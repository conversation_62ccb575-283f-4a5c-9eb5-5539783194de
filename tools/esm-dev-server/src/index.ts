import { serve } from '@hono/node-server'
import { serveStatic } from '@hono/node-server/serve-static';
import { Hono } from 'hono'

const app = new Hono()

app.get('/', (c) => {
  return c.text('Hello Hono!')
});

app.get('/cube/*', serveStatic({
  root: '../../packages/cube-match/dist',
  rewriteRequestPath(path: string) {
    return path.replace(/^\/cube/, '/');
  },
}));
app.get('/dino/*', serveStatic({
  root: '../../packages/dino/dist',
  rewriteRequestPath(path: string) {
    return path.replace(/^\/cube/, '/');
  },
}));
app.get('/masher/*', serveStatic({
  root: '../../packages/keyboard-masher/dist',
  rewriteRequestPath(path: string) {
    return path.replace(/^\/cube/, '/');
  },
}));

const port = 3000
console.log(`Server is running on port ${port}`)

serve({
  fetch: app.fetch,
  port
})
