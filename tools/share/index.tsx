// import React from "preact/compat";
import { useState } from "preact/hooks";
import { Twitter, Facebook, Link, Share2, Check } from 'lucide-react';
import "./index.css";

function httpBuildQuery(obj: Record<string, string>) {
  const qs = new URLSearchParams(obj);
  return qs.toString();
}

export default function Share({ shareTags, shareText }: {
  shareTags: string,
  shareText: string,
}) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const shareUrl = (window as any).og_result_url || location.href;

  const twLink =
    'https://twitter.com/intent/tweet?' +
    httpBuildQuery({
      text: shareText,
      hashtags: shareTags,
      url: shareUrl,
    });

  const fbLink = 'https://www.facebook.com/sharer.php?u=' + shareUrl;

  const [copied, setCopied] = useState(false);

  async function doCopyLink() {
    if (copied) return;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 1e3);
    } catch (error) {
      console.error('Failed to copy: ', error);
    }
  }

  async function doShare() {
    try {
      await navigator.share({
        url: shareUrl,
        text: shareText,
      });
    } catch (error) {
      console.warn('Error: ', error);
    }
  }

  return (
    <div className="social-share-btns">
      <a href={twLink} target="_blank">
        <button type="button" className="social-share-btn tw-btn">
          <Twitter
            className="social-share-icon me-1"
            fill="currentColor"
            strokeWidth="0"
          />
        </button>
      </a>
      <a href={fbLink} target="_blank">
        <button type="button" className="social-share-btn fb-btn">
          <Facebook
            className="social-share-icon me-1"
            fill="currentColor"
            strokeWidth="0"
          />
        </button>
      </a>
      <button
        type="button"
        className="social-share-btn copy-link-btn"
        onClick={doCopyLink}
      >
        {copied ? (
          <Check className="social-share-icon me-1" />
        ) : (
          <Link className="social-share-icon me-1" />
        )}
      </button>
      <button
        type="button"
        className="social-share-btn share-btn"
        onClick={doShare}
      >
        <Share2 className="social-share-icon me-1" />
      </button>
    </div>
  );
}
