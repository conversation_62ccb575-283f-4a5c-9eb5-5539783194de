.social-share-btns {
  width: 80vw;
  background: transparent;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  margin: 0 auto;
  padding: 0.75rem 0.75rem 0.25rem;

  a {
    user-select: none;
    outline: none;
  }

  .social-share-btn {
    cursor: pointer;
    width: 3rem;
    height: 3rem;
    padding: 0;
    border-radius: 20rem;
    outline: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
    font-weight: normal;
    font-size: 0.875rem;
    outline: none;

    &:active,
    &:focus,
    &:focus-visible {
      outline: none;
    }
  }

  .tw-btn:hover {
    background-color: #1371af;
  }

  .fb-btn:hover {
    background-color: #0f4c9b;
  }

  .copy-link-btn:hover,
  .share-btn:hover {
    background-color: #808080;
  }

  .social-share-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  .tw-btn {
    background-color: #009aeb;
  }

  .fb-btn {
    background-color: #0067fb;
  }

  .copy-link-btn,
  .share-btn {
    background-color: #bdc1c7;
  }
}

@media only screen and (max-width: 900px) {
  .social-share-btns {
    width: 90vw;
  }
}

@media only screen and (max-width: 600px) {
  .social-share-btns {
    width: 95vw;
  }
}
