import js from '@eslint/js';
import tsLint from 'typescript-eslint';
import reactRefresh from 'eslint-plugin-react-refresh';
import globals from "globals";

export default [
  {
    ignores: [
      "packages/*/dist/*",
      "packages/hoang-games",
    ],
  },
  {
    languageOptions: {
      ecmaVersion: 'latest',
      globals: {
        ...globals.browser,
        ...globals.node,
        __IS_PUBLISH__: 'readonly',
        __VERSION__: 'readonly',
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
  },
  js.configs.recommended,
  ...tsLint.configs.recommended,
  reactRefresh.configs.recommended,
  {
    files: ["**/*.{js,mjs,cjs,ts,mts,jsx,tsx}"],
    languageOptions: {
      parserOptions: {
        parser: '@typescript-eslint/parser',
        sourceType: "module",
      },
    },
    rules: {
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
];
