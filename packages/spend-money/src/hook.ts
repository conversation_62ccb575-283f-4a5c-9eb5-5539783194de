import { useState, useEffect, useRef } from 'preact/hooks';

export const usePrevious = <T>(value: T): T | undefined => {
  const ref = useRef<T>();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
};

export function useLongPress({
  autoClickingDelay = 200,
  autoClickingCPS = 30,
  minApplyInterval = 50,
  index,
  callback,
}: {
  autoClickingDelay?: number;
  autoClickingCPS?: number;
  minApplyInterval?: number;
  index: number;
  callback: (index: number) => void;
}) {
  const [action, setAction] = useState('');
  const autoClickTimerRef = useRef<ReturnType<typeof setInterval>>();
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const isLongPress = useRef(false);
  const autoClickInterval = 1000 / autoClickingCPS;

  function startPressTimer() {
    isLongPress.current = false;
    timeoutRef.current = setTimeout(() => {
      isLongPress.current = true;
      setAction('longpress');
      autoClickTimerRef.current = setInterval(() => {
        callback(index);
      }, Math.max(autoClickInterval, minApplyInterval));
    }, autoClickingDelay);
  }

  function stopPressTimer() {
    isLongPress.current = false;
    clearTimeout(timeoutRef.current);
    clearInterval(autoClickTimerRef.current);
  }

  function handleOnClick() {
    if (isLongPress.current) {
      return;
    }
    setAction('click');
    callback(index);
  }

  function handleOnMouseDown() {
    startPressTimer();
  }

  function handleOnMouseUp() {
    stopPressTimer();
  }

  function handleOnMouseOut() {
    stopPressTimer();
  }

  function handleOnTouchStart() {
    startPressTimer();
  }

  function handleOnTouchEnd() {
    stopPressTimer();
  }

  function handleOnTouchCancel() {
    stopPressTimer();
  }

  return {
    action,
    handlers: {
      onClick: handleOnClick,
      onMouseDown: handleOnMouseDown,
      onMouseUp: handleOnMouseUp,
      onMouseOut: handleOnMouseOut,
      onTouchStart: handleOnTouchStart,
      onTouchEnd: handleOnTouchEnd,
      onTouchCancel: handleOnTouchCancel,
    },
  };
}
