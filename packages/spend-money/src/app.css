.spend-money-container {
  .game-timer {
    width: 100%;
    color: #989898;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .best-record {
    cursor: pointer;
  }

  .money-progress-bar {
    position: sticky;
    top: 0;
    z-index: 999;
    margin-bottom: 10px;
  }

  .money-progress-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    font-weight: 700;
    color: #fff;
  }

  .money-progress {
    width: 100%;
    position: relative;
    height: 70px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden;
    background-color: #00c796;
    ;
    border: 2px solid #00955d;
    transition: all 0.6s linear;

    &::-webkit-progress-bar {
      background-color: #00c796;
    }

    &::-webkit-progress-value {
      background-color: #00955d;
      border-right: 2px solid #00955d;
      transition: all 0.6s linear;
    }
  }

  .items-container {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-gap: 10px 10px;
  }

  @media only screen and (max-width: 900px) {
    .items-container {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media only screen and (max-width: 600px) {
    .items-container {
      grid-template-columns: repeat(1, 1fr);
    }

    .money-progress-label {
      font-size: 26px;
    }
  }


  .item-wrapper {
    max-width: 100%;
    background-color: #fff;
    text-align: center;
    font-size: 18px;
    padding: 20px 15px 85px;
    width: 100%;
    position: relative;
  }

  .item-img {
    display: block;
    margin: 15px auto;
    height: 120px;
    max-width: 100%;
    object-fit: contain;
    user-select: none;
  }

  .item-name {
    font-size: 22px;
    font-weight: 700;
  }

  .item-cost {
    font-size: 20px;
  }

  .item-controls {
    font-size: 20px;
    font-weight: 700;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px 10px;
    position: absolute;
    bottom: 20px;
    left: 15px;
    right: 15px;
  }

  .item-sell {
    background: #ff0000;
  }

  .item-buy {
    background: #00955d;
  }

  .item-input {
    padding: 9px 12px;
    border-radius: 3px;
    border: 1px solid #b2bec3;
    color: #333;
    width: 100%;
    appearance: none;
    font-size: 16px;
    text-align: center;
    outline: none;
    cursor: auto;
  }

  .receipt-container {
    background: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    max-width: 100%;
    padding: 15px 15px 30px;
    margin-top: 10px;
  }

  .receipt-title {
    font-size: 28px;
    font-weight: 700;
    padding: 15px 15px 25px;
  }

  .receipt-item {
    width: 60%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    display: grid;
    grid-gap: 10px 0;
    font-size: 18px;
    grid-template-columns: 55fr 20fr 25fr;
  }

  .receipt-item-name {
    text-align: left;
    text-overflow: ellipsis;
  }

  .receipt-item-quantity {
    text-align: left;
  }

  .receipt-item-cost {
    color: #24c486;
    text-align: right;
    font-weight: 700;
  }

  .receipt-total {
    font-weight: 700;
    width: 60%;
    border-top: 1px solid #333;
    margin-left: auto;
    margin-right: auto;
    margin-top: 10px;
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
  }

  .receipt-total-cost {
    color: #24c486;
    text-align: right;
  }

  @media only screen and (max-width: 900px) {
    .receipt-item {
      width: 80%;
    }

    .receipt-total {
      width: 85%;
    }
  }

  @media only screen and (max-width: 600px) {
    .receipt-item {
      width: 100%;
    }

    .receipt-total {
      width: 100%;
    }
  }

  .win-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .win-desp {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .continue-btn {
    background: #c1c1c1;
  }

  .restart-btn {
    background: #24c486;
  }


  /* modal style */
  .receipt-modal {
    pointer-events: none;
    position: fixed;
    top: 10px;
    right: 0;
    bottom: 10px;
    left: 0;
    margin: 0;
    display: grid;
    height: calc(100% - 20px);
    max-height: none;
    width: 100%;
    max-width: none;
    justify-items: center;
    align-content: center;
    gap: 10px;
    padding: 0;
    opacity: 0;
    overscroll-behavior: contain;
    z-index: 999;
    background-color: transparent;
    color: inherit;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-property: transform, opacity, visibility;
    overflow-y: hidden;
    border: none;

    &::backdrop {
      background-color: #000c;
      animation: modal-pop 0.2s ease-out;
    }
  }

  :where(.receipt-modal) {
    align-items: center
  }

  .receipt-modal[open] {
    pointer-events: auto;
    visibility: visible;
    opacity: 1;
  }

  :root:has(:is(.receipt-modal[open])) {
    overflow: hidden;
    scrollbar-gutter: stable;
  }

  .receipt-modal-box {
    width: 91.666667%;
    max-width: 80vw;
    max-height: calc(100vh - 5em);
    background-color: #fff;
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    padding: 1.5rem;
    scale: 90% 90%;
  }

  @media only screen and (max-width: 900px) {
    .receipt-modal-box {
      max-width: 90vw;
    }
  }

  @media only screen and (max-width: 600px) {
    .receipt-modal-box {
      max-width: 95vw;
    }
  }

  .receipt-modal[open] .receipt-modal-box {
    scale: 100% 100%;
  }

  .receipt-modal-action {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
  }

  @keyframes modal-pop {
    0% {
      opacity: 0;
    }
  }
}
