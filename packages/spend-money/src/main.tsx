import { render } from 'preact'
import './index.css'
import { App } from './app.tsx'
import type { Config } from './types';

if (!__IS_PUBLISH__) {
  render(<App />, document.getElementById('app')!);
}

const SpendMoney = {
  async init(selector: Element | string, config: Partial<Config>) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    const { root = location.origin } = config;

    // default to show a loading indicator
    const loading = document.createElement('div');
    const bar = document.createElement('div');
    loading.style.cssText = `
      height: 30px;
      width: 60%;
      border: solid 2px #999;
      margin: auto;
    `;
    bar.style.cssText = `
      width: 99%;
      height: 100%;
      background: #999;
      animation: loading 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
    `;
    loading.append(bar);
    el.append(loading);
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes loading {
      0% { width: 0%; }
      100% { width: 99%; }
    }`;
    document.head.appendChild(style);

    const loadCSS = async (url: string) => {
      // load google font
      const googleFont = document.createElement('link');
      googleFont.rel = 'stylesheet';
      googleFont.href =
        'https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap';
      document.head.appendChild(googleFont);

      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'spend-money.css');

    render(<App config={config} />, el);
  }
}

export default SpendMoney;
