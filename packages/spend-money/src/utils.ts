import type { Config } from './types';

export const defaultConfig: Config = {
  minApplyInterval: 50, // 即便使用连点器，1秒之内最多点1000/50=20下；
  autoClickingDelay: 100, // 按下BUY或者SELL之后，如果超过这个毫秒数，则开始进入自动点击状态
  autoClickingCPS: 30, // 自动点击的CPS，即模拟每秒钟30次的点击，这个比上面手动点击的理论最大值要多一点，暂定30
  owner: {
    owner_name: '<PERSON>',
    owner_pic: 'https://placehold.co/400',
    owner_money: 10000000000,
  },
  lcPrefix: 'sm-',
  items: [
    {
      name: 'Plastic Bag',
      price: 0.1,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Big Mac',
      price: 2,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Flip Flops',
      price: 3,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Coca-Cola Pack',
      price: 5,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Movie Ticket',
      price: 12,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Book',
      price: 15,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Pizza',
      price: 25.55,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Lobster Dinner',
      price: 45,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'iPhone',
      price: 500,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Macbook',
      price: 1000,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Bag',
      price: 10000,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Car',
      price: 100000,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'House',
      price: 1000000,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Make a Movie',
      price: 100000000,
      pic: 'https://placehold.co/400',
    },
    {
      name: 'Mona Lisa',
      price: 780000000,
      limit: 1, // 数量有上限，买完就没了
      pic: 'https://placehold.co/400',
    },
    {
      name: 'NBA Team',
      price: 2120000000,
      limit: 30,
      pic: 'https://placehold.co/400',
    },
  ],
  game_ui: {
    best: 'Best:',
    time: 'Time:',
    buy: 'Buy',
    sell: 'Sell',
    receipt: 'Your Receipt',
    total: 'TOTAL',
    time_elapse: 'Time Spend:',
    confirm_reset: 'Do you want to reset the record?',
    'social share':
      "I spent all %OWNER%'s money within %TIME%! Can you do faster than me?",
    win_title: 'Congrats!',
    win_normal:
      "You’ve spend all %OWNER%'s money in %TIME%! Please check your receipt:",
    win_record:
      "You’ve spend all %OWNER%'s money in %TIME%, this is your best record! Please check your receipt:",
    continue: 'Continue Playing',
    restart: 'Restart',
    new_hint: 'Timer starts after first purchase',
  },
  social_share: {
    hashtag: '#SpendMoney',
    text: "I spent all %OWNER%'s money within %TIME%! Can you do faster than me?",
  },
  time_formatter: function (ms: number) {
    if (isNaN(ms) || ms < 0) {
      throw new Error('Input must be a non-negative number');
    }

    // Calculate minutes and seconds
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.min(Math.floor(totalSeconds / 60), 999); // Cap at 999 minutes
    const seconds = Math.min(totalSeconds % 60, 99); // Cap seconds at 99

    // Format time string
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, // 根据语言来确定时间格式，默认时`mm:ss`，这个你实现一个英文的即可。
};

export const formatCurrency = (input: number): string => {
  if (isNaN(input)) {
    throw new Error('Input must be a valid number');
  }

  return '$' + input.toLocaleString('en-US', {
    maximumFractionDigits: 2,
  });
}

export const formatShort = (input: number): string => {
  if (isNaN(input) || input < 0) {
    throw new Error('Input must be a valid non-negative number');
  }

  const formatNumber = (num: number): string => {
    const formatted = num % 1 === 0 ? num.toString() : num.toFixed(2);
    return formatted;
  };

  if (input >= 1e9) {
    // Billion
    return `${formatNumber(input / 1e9)}b`;
  } else if (input >= 1e6) {
    // Million
    return `${formatNumber(input / 1e6)}m`;
  } else if (input >= 1e3) {
    // Thousand
    return `${formatNumber(input / 1e3)}k`;
  } else {
    // Less than 1,000
    return `${formatNumber(input)}`;
  }
};
