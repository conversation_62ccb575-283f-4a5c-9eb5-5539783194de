.spend-money-container {
  *, :after, :before {
    box-sizing: border-box;
    margin: 0;
  }

  font-family: "Roboto Condensed", Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    font-optical-sizing: auto;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  button {
    cursor: pointer;
    padding: 10px 12px;
    border-radius: 3px;
    color: #fff;
    border: none;
    font-size: 16px;
    text-align: center;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: manipulation
  }

  button:hover {
    border-color: #646cff;
  }

  button:focus,
  button:focus-visible {
    outline: none;
  }

  button:disabled {
    cursor: not-allowed;
    background: #666;
  }

  /* remove input number arrow */
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }
}
