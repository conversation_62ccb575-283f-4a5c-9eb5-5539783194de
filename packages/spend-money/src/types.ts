export interface OwnerConfig {
  owner_name: string;
  owner_pic: string;
  owner_money: number;
}

export interface ItemConfig {
  name: string;
  price: number;
  pic: string;
  limit?: number;
}

export interface I18nConfig {
  [key: string]: string;
}

export interface Config {
  root?: string;
  minApplyInterval: number;
  autoClickingDelay: number;
  autoClickingCPS: number;
  lcPrefix: string;
  owner: OwnerConfig;
  items: ItemConfig[];
  game_ui: I18nConfig;
  social_share: {
    hashtag: string | string[];
    text: string;
  },
  time_formatter: (s: number) => string;
}
