import { useState, useEffect, useMemo, useRef } from 'preact/hooks';
import { CountUp } from 'countup.js';
import './app.css'
import type { Config, OwnerConfig, I18nConfig } from './types';
import {
  defaultConfig,
  formatCurrency,
  formatShort,
} from './utils';
import { usePrevious, useLongPress } from './hook';
import Share from '../../../tools/share';

const MoneyLeftProgressBar = ({ progress = 0, max = 100 }) => {
  const prevMoney = usePrevious(progress) as number;
  const moneyRef = useRef<HTMLDivElement | null>(null);
  let countUp: CountUp | null = null;

  const getDuration = (t: number) => {
    return t < 10 ? 0.5 : t < 100 ? 0.8 : t < 1e7 ? 1.3 : 2;
  };

  useEffect(() => {
    if (progress === max) { // when initial, no need animation
      if (moneyRef.current) {
        countUp = new CountUp(moneyRef.current, progress, {
          duration: 0.1,
          startVal: prevMoney || max,
          prefix: '$',
        });
      }
      return;
    }
    if (moneyRef.current) {
      countUp = new CountUp(moneyRef.current, progress, {
        duration: getDuration(Math.abs(prevMoney - progress)),
        startVal: prevMoney || max,
        prefix: '$',
        decimalPlaces: 2,
      });
      if (!countUp.error) {
        countUp.start();
      } else {
        console.error(countUp.error);
      }
    }
  }, [progress]);

  return (
    <div className="money-progress-bar">
      <progress
        className="money-progress"
        role="progressbar"
        value={progress}
        max={max}
      ></progress>
      <div className="money-progress-label" ref={moneyRef}>
        {formatCurrency(progress)}
      </div>
    </div>
  );
};

interface StockItem {
  name: string;
  price: number;
  limit?: number;
  quantity: number;
  pic: string;
}

const Cart = ({ cart, i18n, totalCost, showTitle = true }: { cart: StockItem[], i18n: I18nConfig, totalCost: number, showTitle?: boolean }) => {
  return (
    <div className="receipt-container">
      {showTitle && <div className="receipt-title">{i18n.receipt}</div>}
      {cart.map((item) => (
        <div className="receipt-item" key={item.name}>
          <div className="receipt-item-name">{item.name}</div>
          <div className="receipt-item-quantity">
            x{formatShort(item.quantity)}
          </div>
          <div className="receipt-item-cost">
            ${formatShort(item.price * item.quantity)}
          </div>
        </div>
      ))}
      <div className="receipt-total">
        <div className="receipt-total-label">{i18n.total}</div>
        <div className="receipt-total-cost">{formatCurrency(totalCost)}</div>
      </div>
    </div>
  );
}

export function App({ config = {} }: { config?: Partial<Config> }) {
  config = {
    ...defaultConfig,
    ...config,
  } as Config;

  const owner = config.owner as NonNullable<OwnerConfig>;
  const i18n = config.game_ui as NonNullable<I18nConfig>;
  const shareTags = Array.isArray(config.social_share?.hashtag) ? config.social_share.hashtag.join(',') : config.social_share?.hashtag || '';

  const bestLcKey = config.lcPrefix + 'best';

  const [items, setItems] = useState<StockItem[]>(config.items?.map((item) => ({ ...item, quantity: 0 })) || []);
  const [leftMoney, setLeftMoney] = useState(Number(owner.owner_money));
  const [loaded, setLoaded] = useState(false);
  const [timer, setTimer] = useState(0);
  const [curRec, setCurRec] = useState(0);
  const [record, setRecord] = useState(0);
  const [breakRecord, setBreakRecord] = useState(false);
  const [unlocked, setUnlocked] = useState(1);
  const [played, setPlayed] = useState(false);

  let timerInterval: NodeJS.Timeout | null = null;
  const leftMoneyRef = useRef(leftMoney);
  const timerRef = useRef(0);
  const unlockedRef = useRef(unlocked);
  const receiptRef = useRef<HTMLDialogElement | null>(null);

  const getBuyHandlers = (index: number) => {
    const { handlers } = useLongPress({
      autoClickingDelay: config.autoClickingDelay,
      autoClickingCPS: config.autoClickingCPS,
      minApplyInterval: config.minApplyInterval,
      index,
      callback: (index) => {
        onBuy(index);
      },
    });
    return handlers;
  }

  const getSellHandlers = (index: number) => {
    const { handlers } = useLongPress({
      autoClickingDelay: config.autoClickingDelay,
      autoClickingCPS: config.autoClickingCPS,
      minApplyInterval: config.minApplyInterval,
      index,
      callback: (index) => {
        onSell(index);
      },
    });
    return handlers;
  }

  useEffect(() => {
    if (timerRef.current > 0) {
      timerInterval = setInterval(() => {
        setTimer(prev => prev + 1000);
      }, 1000);
    } else {
      if (timerInterval !== null) {
        clearInterval(timerInterval);
      }
      timerInterval = null;
      setTimer(0);
    }
  }, [timerRef.current]);

  useEffect(() => {
    unlockedRef.current = unlocked;
  }, [unlocked])

  const closeModal = () => {
    receiptRef.current?.close();
  }

  const doRestart = () => {
    setItems(config.items?.map((item) => ({ ...item, quantity: 0 })) || []);
    timerRef.current = 0;
    setTimer(0);
    setCurRec(0);
    setUnlocked(1);
    setBreakRecord(false);
    setPlayed(false);
    closeModal();
  }

  const doReset = () => {
    if (confirm(i18n.confirm_reset)) {
      localStorage.removeItem(bestLcKey);
      setRecord(0);
    }
  }

  const onSell = (index: number) => {
    const newItems = [...items];
    if (newItems[index].quantity === 0) return;
    newItems[index].quantity--;
    setItems(newItems);
    itemOps(index);
  }

  const onBuy = (index: number) => {
    const newItems = [...items];
    if ((newItems[index].limit && newItems[index].quantity === newItems[index].limit)
      || newItems[index].price > leftMoneyRef.current) { // use ref since need updated leftMoney value when long press
      return;
    }
    newItems[index].quantity++;
    setItems(newItems);
    itemOps(index);
  }

  const itemOps = (index: number) => {
    if (timerRef.current === 0) {
      timerRef.current = Date.now();
    }
    if (index === unlocked - 1) {
      setUnlocked(unlocked + 1);
    }
  }

  const cart = useMemo(() => {
    return items.filter((item) => item.quantity > 0);
  }, [items]);

  const totalCost = useMemo(() => {
    return cart.reduce((total, item) => total + item.price * item.quantity, 0);
  }, [cart]);

  useEffect(() => {
    setLeftMoney(Number(owner.owner_money) - totalCost);
    leftMoneyRef.current = Number(owner.owner_money) - totalCost;
  }, [totalCost]);

  useEffect(() => {
    // less than 10% to load confetti component
    if (leftMoney / owner.owner_money <= 0.1 && !loaded) {
      loadConfetti().then(() => {
        setLoaded(true);
      });
    }
    if (leftMoney === 0 && !played) {
      const duration = Date.now() - timerRef.current;
      if (record === 0 || (record > 0 && duration < record)) {
        localStorage.setItem(bestLcKey, String(duration));
        setBreakRecord(true);
        setRecord(duration); // update the best record
        setPlayed(true);
        playFireworks(true);
      } else {
        setBreakRecord(false);
        setPlayed(true);
        playFireworks(false);
      }
      setCurRec(duration);
      timerRef.current = 0;
      receiptRef.current?.showModal();
    }
  }, [leftMoney]);

  useEffect(() => {
    const localRecord = Number(localStorage.getItem(bestLcKey) || 0);
    setRecord(localRecord); // init
  }, []);

  const loadConfetti = async () => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src =
        'https://cdn.jsdelivr.net/npm/@tsparticles/confetti@3.0.3/tsparticles.confetti.bundle.min.js';
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  };

  function playFireworks(breakRecord = false) {
    let count = 10;
    if (breakRecord) {
      count = 50;
    }
    const end = Date.now() + 60;
    // go Buckeyes!
    const colors = ['#ffff00', '#ffffff', 'FF0000'];

    (function frame() {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any)?.confetti({
        particleCount: count,
        angle: 60,
        spread: 55,
        origin: { x: 0 },
        colors: colors,
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any)?.confetti({
        particleCount: count,
        angle: 120,
        spread: 55,
        origin: { x: 1 },
        colors: colors,
      });

      if (Date.now() < end) {
        requestAnimationFrame(frame);
      }
    })();
  }

  const winNormalText = useMemo(() => {
    return i18n.win_normal
      .replace('%OWNER%', owner.owner_name)
      .replace('%TIME%', config.time_formatter?.(curRec) || '');
  }, [curRec]);

  const winRecordText = useMemo(() => {
    return i18n.win_record
      .replace('%OWNER%', owner.owner_name)
      .replace('%TIME%', config.time_formatter?.(curRec) || '');
  }, [curRec]);

  const shareText = useMemo(() => {
    return (config.social_share?.text || '')
      .replace('%OWNER%', owner.owner_name)
      .replace('%TIME%', config.time_formatter?.(curRec) || '');
  }, [curRec]);

  return (
    <div className="spend-money-container">
      <div className="game-timer">
        {timerRef.current === 0 ? (
          <div className="timer-hint">{i18n.new_hint}</div>
        ) : (
          <div>
            {i18n.time}&nbsp;{config.time_formatter?.(timer)}
          </div>
        )}
        {record > 0 && (
          <div className="best-record" onClick={doReset}>
            {i18n.best}&nbsp;{config.time_formatter?.(record)}
          </div>
        )}
      </div>
      <MoneyLeftProgressBar
        progress={leftMoney}
        max={Number(owner.owner_money)}
      />
      <div className="items-container">
        {items.slice(0, unlocked).map((item, idx) => (
          <div className="item-wrapper" key={item.name}>
            <img
              className="item-img"
              src={item.pic}
              alt={item.name}
              height={120}
              width={120}
              loading="lazy"
            />
            <div className="item-name" title={item.name}>
              {item.name}
            </div>
            <div className="item-cost">{formatCurrency(item.price)}</div>
            <div className="item-controls">
              <button
                type="button"
                className="item-sell"
                disabled={item.quantity === 0}
                {...getSellHandlers(idx)}
              >
                {i18n.sell}
              </button>
              <input
                className="item-input"
                type="number"
                min={0}
                value={item.quantity}
                readOnly
              />
              <button
                type="button"
                className="item-buy"
                disabled={
                  item.quantity === item.limit || leftMoney < item.price
                }
                {...getBuyHandlers(idx)}
              >
                {i18n.buy}
              </button>
            </div>
          </div>
        ))}
      </div>

      {cart.length > 0 && (
        <Cart cart={cart} i18n={i18n} totalCost={totalCost} />
      )}

      <dialog
        id="receipt-modal"
        className="receipt-modal"
        ref={receiptRef}
      >
        <div className="receipt-modal-box">
          <div className="win-title">{i18n.win_title}</div>
          <div className="win-desp">
            {breakRecord ? winRecordText : winNormalText}
          </div>
          <Cart
            cart={cart}
            i18n={i18n}
            totalCost={totalCost}
            showTitle={false}
          />
          <div className="receipt-modal-action">
            <button type="button" className="continue-btn" onClick={closeModal}>
              {i18n.continue}
            </button>
            <button type="button" className="restart-btn" onClick={doRestart}>
              {i18n.restart}
            </button>
          </div>
        </div>
        <Share
          shareTags={shareTags}
          shareText={shareText}
        />
      </dialog>
    </div>
  );
}
