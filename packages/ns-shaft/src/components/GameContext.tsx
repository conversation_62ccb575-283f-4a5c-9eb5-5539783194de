import { createContext, ComponentChildren } from 'preact';
import {
  Dispatch,
  StateUpdater,
  useContext,
  useState,
  useRef,
  MutableRef,
} from 'preact/hooks';
import { eventBus } from '../utils/EventBus';
import { GameState, GameStatus, GameResources, GameConfig } from '../types';

interface GameContextType {
  gameState: GameState;
  setGameState: Dispatch<StateUpdater<GameState>>;
  resources: MutableRef<GameResources | null>;
  config: MutableRef<GameConfig>;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

interface GameProviderProps {
  children: ComponentChildren;
  config: GameConfig; // 添加可选的 config 属性
}

export const GameProvider = ({ children, config }: GameProviderProps) => {
  const images = useRef<HTMLDivElement>(null);
  const sounds = useRef<HTMLDivElement>(null);
  const resources = useRef<GameResources | null>(null);
  const gameConfig = useRef<GameConfig>(config);
  let root = config.root || location.origin;
  if (root.at(-1) !== '/') {
    root += '/';
  }
  gameConfig.current.root = root;

  const localBestScore = localStorage.getItem(gameConfig.current.lcPrefix + 'best');

  const [gameState, setGameState] = useState<GameState>({
    soundReady: false,
    soundEnabled: true,
    score: 0,
    bestScore: localBestScore ? Number(localBestScore) : 0,
    life: 10,
    level: 0,
    status: GameStatus.INITIAL,
  });

  // load the resources
  const onLoad = (name: string, type: 'images' | 'sounds', e: Event) => {
    if (!resources.current) {
      resources.current = { images: {}, sounds: {} };
    }
    resources.current[type][name] = e.target as
      | HTMLImageElement
      | HTMLAudioElement;
    eventBus.emit('resourceLoaded', { name, type });
  };

  return (
    <GameContext.Provider
      value={{ gameState, setGameState, resources, config: gameConfig }}
    >
      {children}
      <div id="down-100-resources">
        <div id="down-100-images-resources" ref={images}>
          <img
            id="down-100-bg"
            src={`${root}images/bg.webp`}
            crossorigin="anonymous"
            onLoad={(e) => onLoad('bg', 'images', e)}
          />
          <img
            id="down-100-hero"
            src={`${root}images/hero.png`}
            crossorigin="anonymous"
            onLoad={(e) => onLoad('hero', 'images', e)}
          />
        </div>
        <div id="down-100-audio-resources" ref={sounds}>
          <audio
            id="down-100-normal"
            src={`${root}sounds/normal.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('normal', 'sounds', e)}
          ></audio>
          <audio
            id="down-100-roll"
            src={`${root}sounds/roll.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('roll', 'sounds', e)}
          ></audio>
          <audio
            id="down-100-spring"
            src={`${root}sounds/spring.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('spring', 'sounds', e)}
          ></audio>
          <audio
            id="down-100-lr"
            src={`${root}sounds/lr.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('lr', 'sounds', e)}
          ></audio>
          <audio
            id="down-100-hurt"
            src={`${root}sounds/hurt.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('hurt', 'sounds', e)}
          ></audio>
          <audio
            id="down-100-dead"
            src={`${root}sounds/dead.mp3`}
            crossorigin="anonymous"
            onCanPlayThrough={(e) => onLoad('dead', 'sounds', e)}
          ></audio>
        </div>
      </div>
    </GameContext.Provider>
  );
};

export const useGameContext = (): GameContextType => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGameContext must be used within a GameProvider');
  }
  return context;
};
