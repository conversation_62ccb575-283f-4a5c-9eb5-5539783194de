import { useEffect, useRef } from 'preact/hooks';
import { GameStatus } from '../types';
import { STAGE_HEIGHT, STAGE_WIDTH, TOPBAR_HEIGHT } from '../utils/const';
import { eventBus, type EventMap } from '../utils/EventBus';
import { Overlay } from './Overlay';
import { GameCore } from './GameCore';
import { useGameContext } from './GameContext';
import { useControl } from '../hooks/useControls';

export function Down100Game() {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const ctxRef = useRef<CanvasRenderingContext2D | null>(null);
  const gameCore = useRef<GameCore | null>(null);
  const timerRef = useRef<NodeJS.Timeout>();

  const { gameState, setGameState, resources, config } = useGameContext();
  const soundEnabledRef = useRef<boolean>(gameState.soundEnabled);
  const soundReadyRef = useRef<boolean>(gameState.soundReady);

  useEffect(() => {
    soundEnabledRef.current = gameState.soundEnabled;
  }, [gameState.soundEnabled]);

  useEffect(() => {
    soundReadyRef.current = gameState.soundReady;
  }, [gameState.soundReady]);

  const start = () => {
    gameCore.current?.start();
  };

  const handleLeft = () => {
    gameCore.current?.moveLeft();
  };

  const handleRight = () => {
    gameCore.current?.moveRight();
  };

  const handleStay = () => {
    gameCore.current?.stay();
  };

  const handleSpace = () => {
    switch (gameState.status) {
      case GameStatus.READY:
        // 初始状态，开始游戏
        setGameState(prevState => ({ ...prevState, status: GameStatus.RUNNING }));
        start();
        break;
      case GameStatus.GAMEOVER:
        // 游戏结束状态，重新开始游戏
        setGameState(prevState => ({
          ...prevState,
          status: GameStatus.RUNNING,
          life: 10,
          score: 0,
          level: 0,
        }));
        start();
        break;
      case GameStatus.RUNNING:
        // 游戏运行状态，暂停游戏，可以先用来 debug
        setGameState(prevState => ({ ...prevState, status: GameStatus.PAUSED }));
        gameCore.current?.pause();
        break;
      case GameStatus.PAUSED:
        // 暂停状态，继续游戏
        setGameState(prevState => ({ ...prevState, status: GameStatus.RUNNING }));
        gameCore.current?.resume();
        break;
    }
  };

  useControl({
    onLeft: handleLeft,
    onRight: handleRight,
    onStay: handleStay,
    onSpace: handleSpace,
    // 移动控制只在游戏运行时启用，但空格键在任何状态都可用
    enabled: true
  });

  const playSound = (name: string) => {
    if (!soundReadyRef.current) {
      return;
    }
    const sound = resources.current?.sounds?.[name];
    if (sound) {
      sound.currentTime = 0;
      sound.play();
    }
  };

  const resizeCanvas = () => {
    const container = containerRef.current;
    const canvas = canvasRef.current;
    const ctx = ctxRef.current;

    if (!container || !canvas || !ctx) return;

    const containerWidth = container.clientWidth;
    const zoomRate = containerWidth / STAGE_WIDTH; // make canvas width 100% of container, wider, no need to care about height
    // set canvas size
    canvas.style.width = `${STAGE_WIDTH * zoomRate}px`;
    canvas.style.height = `${STAGE_HEIGHT * zoomRate}px`;
    // make container height same as canvas
    container.style.height = `${STAGE_HEIGHT * zoomRate + TOPBAR_HEIGHT}px`;
    // set resolution
    const ratio = window.devicePixelRatio || 1;
    canvas.width = STAGE_WIDTH * zoomRate * ratio;
    canvas.height = STAGE_HEIGHT * zoomRate * ratio;
    // set transform
    ctx.setTransform(zoomRate * ratio, 0, 0, zoomRate * ratio, 0, 0);
    // re-render if needed
    if (gameCore.current) {
      const timestamp = performance.now(); // current timestamp
      gameCore.current.render(timestamp);
    }
    // console.info('resize rate=' + zoomRate + ", ratio=" + ratio + ", width=" + canvas.width + ", height=" + canvas.height);
  }

  // handle window resize
  useEffect(() => {
    const container = containerRef.current;
    const canvas = canvasRef.current;
    if (!container || !canvas) return;

    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) {
      console.error('Failed to get 2D context');
      return;
    }
    ctxRef.current = ctx;

    const handleResize = () => {
      resizeCanvas();
      // 如果游戏正在运行，确保画布在视图中
      if (gameState.status === GameStatus.RUNNING) {
        canvas.scrollIntoView();
      }
    };

    if (window.ResizeObserver) {
      // will call handleResize on init, can it be avoided
      const resizeObserver = new ResizeObserver(handleResize);

      resizeObserver.observe(container);
    }

    window.addEventListener('resize', handleResize);
    resizeCanvas();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleResourceLoaded = () => {
    // make sure images resources are loaded before game core init
    if (
      ctxRef.current &&
      resources.current?.images?.bg &&
      resources.current?.images?.hero &&
      !gameCore.current
    ) {
      setGameState(prev => ({
        ...prev,
        status: GameStatus.READY,
      }));
      gameCore.current = new GameCore(
        ctxRef.current,
        resources.current,
        config.current?.levelConfig
      );
    }

    if (
      resources.current?.sounds?.normal &&
      resources.current?.sounds?.roll &&
      resources.current?.sounds?.spring &&
      resources.current?.sounds?.lr &&
      resources.current?.sounds?.hurt &&
      resources.current?.sounds?.dead
    ) {
      setGameState((prevState) => ({
        ...prevState,
        soundReady: true,
      }));
    }
  };

  const handleFloorLanding = ({ type }: EventMap['floorLanding']) => {
    if (soundEnabledRef.current) {
      playSound(type);
    }
  };

  const handleGameOver = () => {
    if (soundEnabledRef.current) {
      playSound('dead');
    }
    // 先设置 cooldown，仅显示 GameOver Text
    setGameState((prevState) => ({
      ...prevState,
      status: GameStatus.COOLDOWN,
    }));
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    // 1.5s 后再显示 restart button
    timerRef.current = setTimeout(() => {
      setGameState((prevState) => ({
        ...prevState,
        status: GameStatus.GAMEOVER,
      }));
    }, 1500);
  };

  const handleGamePaused = () => {
    setGameState((prevState) => ({ ...prevState, status: GameStatus.PAUSED }));
  };

  const handleHeroDamaged = ({ life }: EventMap['heroDamaged']) => {
    if (soundEnabledRef.current) {
      playSound('hurt');
    }
    setGameState((prevState) => ({ ...prevState, life }));
  };

  const handleHeroHealed = ({ life }: EventMap['heroHealed']) => {
    setGameState((prevState) => ({ ...prevState, life }));
  };

  const handleScoreUpdate = (score: number) => {
    setGameState((prevState) => {
      if (score > prevState.bestScore) {
        localStorage.setItem(config.current.lcPrefix + 'best', String(score));
      }
      return {
        ...prevState,
        score,
        bestScore: Math.max(score, prevState.bestScore),
      };
    });
  };

  // handle events bus
  useEffect(() => {
    handleResourceLoaded();
    eventBus.on('resourceLoaded', handleResourceLoaded);
    eventBus.on('gameOver', handleGameOver);
    eventBus.on('gamePaused', handleGamePaused);
    eventBus.on('floorLanding', handleFloorLanding);
    eventBus.on('heroDamaged', handleHeroDamaged);
    eventBus.on('heroHealed', handleHeroHealed);
    eventBus.on('scoreUpdate', handleScoreUpdate);

    return () => {
      if (gameCore.current) {
        gameCore.current.destroy();
        gameCore.current = null;
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      eventBus.off('resourceLoaded', handleResourceLoaded);
      eventBus.off('gameOver', handleGameOver);
      eventBus.off('gamePaused', handleGamePaused);
      eventBus.off('floorLanding', handleFloorLanding);
      eventBus.off('heroDamaged', handleHeroDamaged);
      eventBus.off('heroHealed', handleHeroHealed);
      eventBus.off('scoreUpdate', handleScoreUpdate);
    };
  }, []);

  return (
    <div className="down-100-container" ref={containerRef}>
      <canvas ref={canvasRef} className="down-100-canvas"></canvas>
      <Overlay
        onGameStart={start}
        onGameRestart={start}
      />
    </div>
  );
}
