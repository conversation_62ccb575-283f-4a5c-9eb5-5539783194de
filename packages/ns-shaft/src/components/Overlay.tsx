import { useRef } from 'preact/hooks';
import { useGameContext } from './GameContext';
import { GameStatus, I18nStrings } from '../types';

interface OverlayProps {
  onGameStart: () => void;
  onGameRestart: () => void;
  onSoundToggle?: () => void;
}

export const Overlay = ({
  onGameStart,
  onGameRestart,
  onSoundToggle,
}: OverlayProps) => {
  const confirmRef = useRef<HTMLDivElement | null>(null);
  const { gameState, setGameState, config } = useGameContext();
  const i18n = config.current.i18n as NonNullable<I18nStrings>;
  const doSoundToggle = () => {
    setGameState((prevState) => ({
      ...prevState,
      soundEnabled: !prevState.soundEnabled,
    }));
    onSoundToggle?.();
  };

  const doGameStart = () => {
    setGameState((prevState) => ({ ...prevState, status: GameStatus.RUNNING }));
    onGameStart();
  };

  const doGameRestart = () => {
    setGameState((prevState) => ({
      ...prevState,
      status: GameStatus.RUNNING,
      life: 10,
      score: 0,
      level: 0,
    }));
    onGameRestart();
  };

  const closeModal = () => {
    if (confirmRef.current) {
      confirmRef.current.style.opacity = '0';
      confirmRef.current.style.visibility = 'hidden';
    }
  };

  const showModal = () => {
    if (confirmRef.current) {
      confirmRef.current.style.opacity = '1';
      confirmRef.current.style.visibility = 'visible';
    }
  };

  const doResetScore = () => {
    localStorage.removeItem(config.current.lcPrefix + 'best');
    setGameState((prevState) => ({
      ...prevState,
      bestScore: 0,
    }));
    closeModal();
  }

  return (
    <>
      <div className={`down-100-overlay-container ${gameState.status}`}>
        {/* TopBar */}
        <div className="down-100-topbar">
          <div className="down-100-life-container">
            <span className="down-100-life-label">{i18n.life}</span>
            <div className="down-100-life-bar">
              {Array.from({ length: 10 }).map((_, i) => (
                <div
                  key={i}
                  className={`down-100-life-bar-unit ${
                    i < gameState.life ? 'active' : ''
                  }`}
                />
              ))}
            </div>
          </div>

          <div className="down-100-score-container">
            <div className="down-100-score">
              {i18n.score} {gameState.score}
            </div>
            <button
              disabled={!gameState.soundReady}
              className="down-100-sound-toggle"
              onClick={doSoundToggle}
              aria-label={
                gameState.soundEnabled ? `${i18n.soundOn}` : `${i18n.soundOff}`
              }
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 256 256"
                width="18"
                className={`down-100-sound-icon ${gameState.soundEnabled ? 'sound-on' : 'sound-off'}`}
              >
                <g fill="#000" fill-rule="nonzero">
                  <path d="M122 39c5 2 8 7 8 12v154c0 5-3 10-8 12s-10 1-14-3l-49-47H25c-7 0-13-6-13-13v-52c0-7 6-13 13-13h34l49-47c4-4 9-5 14-3ZM187 37c5-5 13-5 18 0a128 128 0 0 1 0 183c-5 5-13 5-18 0-5-6-5-14 0-19a102 102 0 0 0 0-146c-5-5-5-13 0-18Z"></path>
                  <path d="M150 73c5-5 13-5 18 0a78 78 0 0 1 23 55c0 21-8 40-23 54-5 5-13 5-18 0s-5-13 0-18a51 51 0 0 0 0-73c-5-5-5-13 0-18Z"></path>
                </g>
              </svg>
            </button>
          </div>
        </div>

        {gameState.status !== GameStatus.RUNNING && (
          <div className="down-100-game-controls">
            {gameState.status === GameStatus.READY && (
              <>
                <button className="down-100-game-button" onClick={doGameStart}>
                  {i18n.start}
                  <span className="down-100-game-button-hint">
                    {i18n.keyHint}
                  </span>
                </button>
                <div className="down-100-best-score">
                  {i18n.best} {gameState.bestScore}
                </div>
                <div className="down-100-reset-button" onClick={showModal}>
                  {i18n.reset}
                </div>
              </>
            )}
            {gameState.status === GameStatus.PAUSED && (
              <button className="down-100-game-button" onClick={doGameStart}>
                {i18n.continue}
                <span className="down-100-game-button-hint">
                  {i18n.keyHint}
                </span>
              </button>
            )}

            {gameState.status === GameStatus.COOLDOWN && (
              <div className="down-100-game-over-text">{i18n.gameOver}</div>
            )}

            {gameState.status === GameStatus.GAMEOVER && (
              <>
                <button
                  className={`down-100-game-button ${
                    gameState.status === GameStatus.GAMEOVER
                      ? 'restart-visible'
                      : 'restart-hidden'
                  }`}
                  onClick={doGameRestart}
                >
                  {i18n.restart}
                  <span className="down-100-game-button-hint">
                    {i18n.keyHint}
                  </span>
                </button>
                <div className="down-100-best-score">
                  {i18n.best} {gameState.bestScore}
                </div>
                <div className="down-100-reset-button" onClick={showModal}>
                  {i18n.reset}
                </div>
              </>
            )}
          </div>
        )}
      </div>

      <div
        id="down-100-modal-container"
        className="down-100-modal-container"
        ref={confirmRef}
      >
        <div className="down-100-modal-box">
          <div className="down-100-modal-text">{i18n.resetConfirm}</div>
          <div className="down-100-modal-action">
            <button
              type="button"
              className="down-100-modal-btn reset-button"
              onClick={doResetScore}
            >
              {i18n.confirm}
            </button>
            <button
              type="button"
              className="down-100-modal-btn"
              onClick={closeModal}
            >
              {i18n.cancel}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
