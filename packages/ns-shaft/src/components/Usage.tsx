export default function Usage() {
  return (
    <div className="down-100-usage">
      <p>
        First, embed the script. It will load the latest version of the module.
      </p>
      <pre>
        <code>
          {`<script src="https://unpkg.com/@roudanio/down-100@${__VERSION__}/dist/down-100.js"></script>`}
        </code>
      </pre>
      <p>
        Then, initialize the package with specified container element, and
        config options.
      </p>
      <details>
        <summary>Click to expand the code</summary>
        <pre>
          <code>
{`Down100.init(
  document.querySelector('#down-100-container'),  // DOM element or DOM selector
  {
    root: 'https://unpkg.com/@roudanio/down-100@${__VERSION__}/dist/',
    // other options
    lcPrefix: 'down100-',
    levelConfig: [
      {
        level_threshold: 20,
        floor_chances: [10, 3, 1, 1, 1, 2], // [regular, fake, arrow, rollingLeft, rollingRight, spring]
        speed_boost: 1.01, // make all speed goes 1% faster
      },
      {
        level_threshold: 40,
        floor_chances: [7, 3, 1, 1, 1, 2], // fewer regular floors
        speed_boost: 1.04, // make all speed goes 4% faster
      },
      {
        level_threshold: 60,
        floor_chances: [5, 4, 2, 2, 2, 3], // much fewer regular floors
        speed_boost: 1.08, // 8% faster
      },
      {
        level_threshold: 80,
        floor_chances: [3, 5, 3, 3, 3, 3], // very few regular floors
        speed_boost: 1.12, // 12% faster
      },
    ],
    i18n: {
      life: 'HP:',
      score: 'Floor:',
      soundOn: 'Sound: ON',
      soundOff: 'Sound: OFF',
      start: 'Start',
      gameOver: 'Game Over',
      restart: 'Restart',
      continue: 'Continue',
      keyHint: '(Space / Enter)',
      best: 'BEST:',
      reset: 'RESET',
      resetConfirm: 'Are you sure you want to reset the best score?',
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
  }
);
`}
          </code>
        </pre>
      </details>
    </div>
  );
};
