import { eventBus } from '../utils/EventBus';
import { <PERSON> } from './Hero';
import {
  FLOOR_HEIGHT,
  FLOOR_WIDTH,
  SPRING_HEIGHT,
  SPRING_TIME,
  SPRINGING_VELOCITY,
  ROLLING_VELOCITY,
  ARROW_HEIGHT,
  ARROW_WIDTH,
  FAKE_FLOOR_TIME,
  FAKE_FLOOR_TIME2,
} from '../utils/const';
import { roundRect } from '../utils/utils';
import { FloorSeq } from './FloorSeq';
import { FloorType, FloorAudio } from '../types';

const getNewScore = (floorSeq: number) => {
  return Math.floor(floorSeq * 0.2);
}

export abstract class FloorBase {
  public x: number;
  public y: number;
  public seq: number;
  public type: FloorType = FloorType.NORMAL;

  constructor(x: number, y: number, type: FloorType) {
    this.x = x;
    this.y = y;
    this.seq = FloorSeq.get();
    this.type = type;
  }

  // Define common methods
  abstract getHeight(): number;
  abstract draw(ctx: CanvasRenderingContext2D, time?: number): void;
  abstract landing(hero: Hero, velocity?: number, time?: number): void;
  abstract standing(hero: Hero, time?: number): void;
  abstract leaving(hero: Hero, time?: number): void;
}

export class NormalFloor extends FloorBase {
  static pattern: CanvasPattern;

  constructor(x: number, y: number) {
    super(x, y, FloorType.NORMAL);
  }

  draw(ctx: CanvasRenderingContext2D): void {
    ctx.save();
    ctx.translate(this.x, this.y);

    // Create a base64 bitmap pattern for the floor
    if (!NormalFloor.pattern) {
      // Simple checkerboard pattern as base64 image
      const patternImg = new Image();
      patternImg.src =
        'data:image/webp;base64,UklGRkAAAABXRUJQVlA4IDQAAACwAQCdASoIAAgACACkJaACdADzd7iAAPgrrXPLQJHCx7fwucbjCdahXytJpJuCY9EwAAAA';

      // Create pattern once and reuse
      patternImg.onload = function () {
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        if (tempCtx) {
          const pattern = tempCtx.createPattern(patternImg, 'repeat');
          if (pattern) {
            NormalFloor.pattern = pattern;
          }
        }
      };
    }
    if (NormalFloor.pattern) {
      ctx.fillStyle = NormalFloor.pattern;
    } else {
      ctx.fillStyle = '#FFF';
    }
    // Draw the main rectangle
    ctx.fillRect(0, -FLOOR_HEIGHT, FLOOR_WIDTH, FLOOR_HEIGHT);

    // Add 3D effect with different colored borders
    // Top - light
    ctx.fillStyle = '#D3F8FF';
    ctx.fillRect(0, -FLOOR_HEIGHT, FLOOR_WIDTH, 2);
    // Left - light
    ctx.fillRect(0, -FLOOR_HEIGHT, 2, FLOOR_HEIGHT);
    // Bottom - dark
    ctx.fillStyle = '#000E5C';
    ctx.fillRect(0, -2, FLOOR_WIDTH, 2);
    // Right - dark
    ctx.fillRect(FLOOR_WIDTH - 2, -FLOOR_HEIGHT, 2, FLOOR_HEIGHT);

    ctx.restore();
  }

  getHeight(): number {
    return FLOOR_HEIGHT;
  }

  landing(hero: Hero, floorVelocity: number): void {
    hero.vy = floorVelocity;
    eventBus.emit('scoreUpdate', getNewScore(this.seq));
    eventBus.emit('floorLanding', { type: FloorAudio.NORMAL });
  }

  // do nothing
  standing(): void {}
  leaving(): void {}
}

export class SpringFloor extends FloorBase {
  private spring: number = SPRING_HEIGHT;
  private restoring: boolean = false;
  private touchTime: number = 0;
  private leavingTime: number = 0;

  constructor(x: number, y: number) {
    super(x, y, FloorType.SPRING);
  }

  getHeight(): number {
    return this.spring + 4;
  }

  draw(ctx: CanvasRenderingContext2D, time: number): void {
    if (this.restoring) {
      this.restore(time);
    }

    const currentHeight = this.getHeight();
    ctx.save();
    ctx.translate(this.x, this.y);

    // 绘制弹簧基座和弹簧
    ctx.fillStyle = 'green';
    ctx.fillRect(0, -2, FLOOR_WIDTH, 2);
    ctx.fillRect(0, -currentHeight, FLOOR_WIDTH, 2);

    // 绘制弹簧线条
    const gap = 10;
    const width = (FLOOR_WIDTH - gap * 4) / 3;
    ctx.lineWidth = width;
    ctx.strokeStyle = 'grey';
    ctx.setLineDash([1, 2]);

    ctx.beginPath();
    for (let i = 0; i < 3; i++) {
      const x = gap + width * 0.5 + (gap + width) * i;
      ctx.moveTo(x, -currentHeight + 2);
      ctx.lineTo(x, -2);
    }
    ctx.stroke();

    ctx.restore();
  }

  landing(hero: Hero, floorVelocity: number, time: number): void {
    this.touchTime = time;
    this.spring = SPRING_HEIGHT;
    hero.vy = floorVelocity;
    eventBus.emit('scoreUpdate', getNewScore(this.seq));
    eventBus.emit('floorLanding', { type: FloorAudio.SPRING });
  }

  standing(hero: Hero, time: number): void {
    const offset = time - this.touchTime;
    if (offset < SPRING_TIME) {
      this.spring = SPRING_HEIGHT - (offset / SPRING_TIME) * 5;
    } else if (offset < SPRING_TIME * 2) {
      this.spring = SPRING_HEIGHT - 15 + (offset / SPRING_TIME) * 10;
    } else {
      hero.vy = SPRINGING_VELOCITY;
      hero.onFloor = null;
      this.leaving(hero, time);
    }
  }

  leaving(_hero: Hero, time: number): void {
    this.leavingTime = time;
    this.restoring = true;
  }

  restore(time: number): void {
    const offset = time - this.leavingTime;
    const distance = (5 / SPRING_TIME) * offset;

    if (this.spring < SPRING_HEIGHT) {
      this.spring += distance;
      if (this.spring >= SPRING_HEIGHT) {
        this.spring = SPRING_HEIGHT;
        this.restoring = false;
      }
    } else {
      this.spring -= distance;
      if (this.spring <= SPRING_HEIGHT) {
        this.spring = SPRING_HEIGHT;
        this.restoring = false;
      }
    }
  }
}

export class ConveyorFloor extends FloorBase {
  private direction: 'left' | 'right';
  private offset: number;
  private arrowHighlightIndex: number = 0; // Track which arrow should be yellow
  private highlightCounter: number = 0; // Counter for faster highlight animation

  constructor(x: number, y: number, direction: 'left' | 'right') {
    super(x, y, FloorType.CONVEYOR);
    this.direction = direction;
    this.offset = direction === 'left' ? 0 : 20;
  }

  getHeight(): number {
    return FLOOR_HEIGHT;
  }

  draw(ctx: CanvasRenderingContext2D): void {
    // 根据方向更新offset
    if (this.direction === 'left') {
      if (++this.offset >= 20) this.offset = 0;
    } else {
      if (--this.offset < 0) this.offset = 20;
    }
    // 更新高亮箭头，Update highlight more frequently (3x faster than the conveyor animation)
    if (++this.highlightCounter >= 7) {
      // 20/3 ≈ 7 frames per highlight change
      this.highlightCounter = 0;
      this.arrowHighlightIndex = (this.arrowHighlightIndex + 1) % 4; // Cycle through 4 arrows
    }

    ctx.save();
    ctx.translate(this.x, this.y);

    const midH = FLOOR_HEIGHT * 0.5;

    // create gradient fill for the track 传送带的渐变填充
    const gradient = ctx.createLinearGradient(
      0,
      -FLOOR_HEIGHT / 2,
      FLOOR_WIDTH,
      -FLOOR_HEIGHT / 2
    );
    gradient.addColorStop(0, '#FFFFFF'); // White at left
    gradient.addColorStop(0.5, '#000000'); // Black in middle
    gradient.addColorStop(1, '#FFFFFF'); // White at right

    // Draw track background with fill 绘制传送带背景
    ctx.fillStyle = gradient;
    roundRect(
      ctx,
      1,
      -FLOOR_HEIGHT + 2,
      FLOOR_WIDTH - 2,
      FLOOR_HEIGHT - 2,
      midH
    );
    ctx.fill();

    // Add main outline for the entire conveyor belt 绘制主轮廓
    ctx.strokeStyle = '#777';
    ctx.lineWidth = 1;
    ctx.setLineDash([]);
    roundRect(
      ctx,
      1,
      -FLOOR_HEIGHT + 1,
      FLOOR_WIDTH - 2,
      FLOOR_HEIGHT - 2,
      midH
    );
    ctx.stroke();

    // Add shiny metallic highlights along the edges
    // Top curved border - light 绘制上部金属光泽
    ctx.strokeStyle = '#BBB';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(midH, -midH, midH - 1, Math.PI, 1.5 * Math.PI, false);
    ctx.lineTo(FLOOR_WIDTH - midH, -FLOOR_HEIGHT + 1);
    ctx.arc(
      FLOOR_WIDTH - midH,
      -midH,
      midH - 1,
      1.5 * Math.PI,
      2 * Math.PI,
      false
    );
    ctx.stroke();

    // Bottom curved border - dark 绘制下部阴影
    ctx.strokeStyle = '#555';
    ctx.beginPath();
    ctx.arc(midH, -midH, midH - 1, 0.5 * Math.PI, Math.PI, false);
    ctx.lineTo(FLOOR_WIDTH - midH, -1);
    ctx.arc(FLOOR_WIDTH - midH, -midH, midH - 1, 0, 0.5 * Math.PI, false);
    ctx.stroke();

    // Draw dashed lines for the conveyor animation with more contrast 绘制传送带动画线条
    ctx.strokeStyle = '#999';
    ctx.setLineDash([15, 5]);
    ctx.lineWidth = 2.5;

    // Top dashed line - moving left (increasing offset for left, decreasing for right) 上部动画线条
    ctx.lineDashOffset = this.offset;
    ctx.beginPath();
    ctx.moveTo(midH, -FLOOR_HEIGHT + 1);
    ctx.lineTo(FLOOR_WIDTH - midH, -FLOOR_HEIGHT + 1);
    ctx.stroke();

    // Bottom dashed line - moving right (opposite direction with negative offset)  下部动画线条 (相反方向)
    ctx.lineDashOffset = -this.offset;
    ctx.beginPath();
    ctx.moveTo(midH, -1);
    ctx.lineTo(FLOOR_WIDTH - midH, -1);
    ctx.stroke();

    // Add inner line on top and bottom for depth
    ctx.strokeStyle = '#777';
    ctx.setLineDash([]);
    ctx.lineWidth = 1;

    // Top inner line
    ctx.beginPath();
    ctx.moveTo(midH + 2, -FLOOR_HEIGHT + 3);
    ctx.lineTo(FLOOR_WIDTH - midH - 2, -FLOOR_HEIGHT + 3);
    ctx.stroke();

    // Bottom inner line
    ctx.beginPath();
    ctx.moveTo(midH + 2, -3);
    ctx.lineTo(FLOOR_WIDTH - midH - 2, -3);
    ctx.stroke();

    // Draw direction arrows (left pointing) with one highlighted in yellow
    ctx.setLineDash([]);
    ctx.lineWidth = 1.5;

    // Calculate the usable space for arrows
    const arrowAreaStart = FLOOR_WIDTH * 0.2;
    const arrowAreaEnd = FLOOR_WIDTH * 0.8;
    const arrowAreaWidth = arrowAreaEnd - arrowAreaStart;

    // Always draw exactly 4 arrows with equal spacing
    const numArrows = 4;
    const arrowSpacing = arrowAreaWidth / (numArrows - 1);

    for (let i = 0; i < numArrows; i++) {
      // Calculate position for this arrow - opposite order for left-pointing arrows
      const markX = arrowAreaEnd - i * arrowSpacing;

      // Determine if this arrow should be highlighted (yellow)
      const isHighlighted = i === this.arrowHighlightIndex;

      // Set appropriate color
      ctx.strokeStyle = isHighlighted ? '#FFD700' : '#FFF'; // Gold/yellow for highlight, white for others
      ctx.fillStyle = isHighlighted ? '#FFD700' : '#FFF';

      // Create arrow
      ctx.beginPath();
      ctx.moveTo(markX, -midH - 3);
      ctx.lineTo(this.direction === 'left' ? markX - 6 : markX + 6, -midH);
      ctx.lineTo(markX, -midH + 3);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();
    }

    // Draw enhanced bearings at ends
    const bearingGradient = ctx.createRadialGradient(
      midH,
      -midH,
      0,
      midH,
      -midH,
      midH - 3
    );
    bearingGradient.addColorStop(0, '#FFFFFF');
    bearingGradient.addColorStop(0.7, '#AAAAAA');
    bearingGradient.addColorStop(1, '#777777');

    const rightBearingGradient = ctx.createRadialGradient(
      FLOOR_WIDTH - midH,
      -midH,
      0,
      FLOOR_WIDTH - midH,
      -midH,
      midH - 3
    );
    rightBearingGradient.addColorStop(0, '#FFFFFF');
    rightBearingGradient.addColorStop(0.7, '#AAAAAA');
    rightBearingGradient.addColorStop(1, '#777777');

    // Left bearing
    ctx.beginPath();
    ctx.arc(midH, -midH, midH - 3, 0, 2 * Math.PI, false);
    ctx.fillStyle = bearingGradient;
    ctx.fill();
    ctx.strokeStyle = '#555';
    ctx.lineWidth = 1;
    ctx.stroke();

    // Right bearing
    ctx.beginPath();
    ctx.arc(FLOOR_WIDTH - midH, -midH, midH - 3, 0, 2 * Math.PI, false);
    ctx.fillStyle = rightBearingGradient;
    ctx.fill();
    ctx.strokeStyle = '#555';
    ctx.lineWidth = 1;
    ctx.stroke();

    ctx.restore();
  }

  landing(hero: Hero, floorVelocity: number): void {
    hero.vy = floorVelocity;
    hero.vx = this.direction === 'left' ? -ROLLING_VELOCITY : ROLLING_VELOCITY;
    eventBus.emit('scoreUpdate', getNewScore(this.seq));
    eventBus.emit('floorLanding', { type: FloorAudio.LR });
  }

  leaving(hero: Hero): void {
    hero.vx = 0;
  }

  // do nothing
  standing(): void {}
}

export class ArrowFloor extends FloorBase {
  constructor(x: number, y: number) {
    super(x, y, FloorType.ARROW);
  }
  getHeight(): number {
    return FLOOR_HEIGHT;
  }
  draw(ctx: CanvasRenderingContext2D): void {
    ctx.save();
    ctx.translate(this.x, this.y);

    // Draw the base with gradient and borders
    const baseHeight = 10; // Height of the base
    const baseY = -this.getHeight();

    // Create gradient for the base interior
    const baseGradient = ctx.createLinearGradient(
      0,
      baseY + baseHeight / 2,
      FLOOR_WIDTH,
      baseY + baseHeight / 2
    );
    baseGradient.addColorStop(0, '#FFFFFF'); // White at left
    baseGradient.addColorStop(0.5, '#000000'); // Black in middle
    baseGradient.addColorStop(1, '#FFFFFF'); // White at right

    // Draw base interior with gradient
    ctx.fillStyle = baseGradient;
    ctx.fillRect(1, baseY + 1, FLOOR_WIDTH - 2, baseHeight - 2);

    // Draw the borders
    // Top border - light gray #999
    ctx.fillStyle = '#999';
    ctx.fillRect(0, baseY, FLOOR_WIDTH, 1);

    // Left border - light gray #999
    ctx.fillRect(0, baseY, 1, baseHeight);

    // Right border - black #000
    ctx.fillStyle = '#000';
    ctx.fillRect(FLOOR_WIDTH - 1, baseY, 1, baseHeight);

    // Bottom border - black #000
    ctx.fillRect(0, baseY + baseHeight - 1, FLOOR_WIDTH, 1);

    // Draw each spike individually with gradient
    const bottom = baseY;
    const top = bottom - ARROW_HEIGHT;
    const left = 0.5;
    const right = FLOOR_WIDTH - 0.5;

    // Draw each spike individually
    for (let x = left; x < right; x += ARROW_WIDTH * 2) {
      const spikeLeft = x;
      const spikeMiddle = Math.min(x + ARROW_WIDTH, right);
      const spikeRight = Math.min(x + ARROW_WIDTH * 2, right);

      // Create HORIZONTAL gradient for this spike - from left to middle to right
      const gradient = ctx.createLinearGradient(
        spikeLeft,
        top + (bottom - top) / 2,
        spikeRight,
        top + (bottom - top) / 2
      );
      gradient.addColorStop(0, '#333333'); // Dark gray on left side
      gradient.addColorStop(0.5, '#FFFFFF'); // White in the middle
      gradient.addColorStop(1, '#333333'); // Dark gray on right side

      // Draw the spike
      ctx.beginPath();
      ctx.moveTo(spikeLeft, bottom);
      ctx.lineTo(spikeMiddle, top);
      ctx.lineTo(spikeRight, bottom);
      ctx.closePath();

      // Fill with gradient
      ctx.fillStyle = gradient;
      ctx.fill();

      // Add a subtle stroke
      ctx.strokeStyle = '#555';
      ctx.lineWidth = 0.5;
      ctx.stroke();
    }

    ctx.restore();
  }

  landing(hero: Hero, floorVelocity: number, time: number): void {
    hero.vy = floorVelocity;
    hero.hurt(4, time);
    eventBus.emit('scoreUpdate', getNewScore(this.seq));
    eventBus.emit('floorLanding', { type: FloorAudio.HURT });
  }

  // do nothing
  standing(): void {}
  leaving(): void {}
}

export class FakeFloor extends FloorBase {
  private height: number = FLOOR_HEIGHT;
  private restoring: boolean = false;
  private touchTime: number = 0;
  constructor(x: number, y: number) {
    super(x, y, FloorType.FAKE);
  }

  getHeight(): number {
    return this.height;
  }

  draw(ctx: CanvasRenderingContext2D, time: number): void {
    if (this.restoring) {
      this.restore(time);
    }

    ctx.save();
    ctx.translate(this.x, this.y);
    if (this.height >= FLOOR_HEIGHT || this.height <= 0) {
      ctx.fillStyle = '#999';
      ctx.fillRect(0, -FLOOR_HEIGHT, FLOOR_WIDTH, FLOOR_HEIGHT);
    } else {
      const percent = this.height / FLOOR_HEIGHT;
      const colorInc = Math.round(0x66 * percent);
      let color = 0x33 + colorInc;
      ctx.fillStyle = 'rgb(' + color + ',' + color + ',' + color + ')';
      ctx.fillRect(0, -this.getHeight(), FLOOR_WIDTH, this.getHeight());
      color = 0x99 + colorInc;
      ctx.fillStyle = 'rgb(' + color + ',' + color + ',' + color + ')';
      ctx.fillRect(
        0,
        -FLOOR_HEIGHT,
        FLOOR_WIDTH,
        FLOOR_HEIGHT - this.getHeight()
      );
    }
    ctx.restore();
  }

  landing(hero: Hero, floorVelocity: number, time: number): void {
    this.touchTime = time;
    hero.vy = floorVelocity;
    eventBus.emit('scoreUpdate', getNewScore(this.seq));
    eventBus.emit('floorLanding', { type: FloorAudio.ROLL });
  }

  standing(hero: Hero, time: number): void {
    const offset = time - this.touchTime;
    if (offset < FAKE_FLOOR_TIME) {
      this.height = FLOOR_HEIGHT;
    } else if (offset < FAKE_FLOOR_TIME2) {
      this.height =
        (FLOOR_HEIGHT / (FAKE_FLOOR_TIME - FAKE_FLOOR_TIME2)) *
        (offset - FAKE_FLOOR_TIME2);
    } else {
      this.height = 0;
      hero.onFloor = null;
      this.leaving(hero, time);
    }
  }

  leaving(_hero: Hero, time: number): void {
    const offset = time - this.touchTime;
    if (offset >= FAKE_FLOOR_TIME && offset < FAKE_FLOOR_TIME2) {
      this.restoring = true;
    }
  }

  restore(time: number): void {
    const offset = time - this.touchTime;
    if (offset < FAKE_FLOOR_TIME2) {
      this.height =
        (FLOOR_HEIGHT / (FAKE_FLOOR_TIME - FAKE_FLOOR_TIME2)) *
        (offset - FAKE_FLOOR_TIME2);
    } else {
      this.height = 0;
      this.restoring = false;
    }
  }
}

export type Floor = NormalFloor | SpringFloor | ConveyorFloor | ArrowFloor | FakeFloor;
