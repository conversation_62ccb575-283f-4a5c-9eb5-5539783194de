interface FloorSequenceState {
  seq: number;
  running: boolean;
}

class FloorSequence {
  private _state: FloorSequenceState = {
    seq: 0,
    running: false,
  };

  private static instance: FloorSequence;

  private constructor() {}

  static getInstance(): FloorSequence {
    if (!FloorSequence.instance) {
      FloorSequence.instance = new FloorSequence();
    }
    return FloorSequence.instance;
  }

  start(): void {
    this._state.running = true;
  }

  get(): number {
    if (!this._state.running) {
      return 0;
    }
    return this._state.seq++;
  }

  reset(): void {
    this._state = {
      seq: 0,
      running: false,
    };
  }

  current(): number {
    return this._state.seq;
  }

  isRunning(): boolean {
    return this._state.running;
  }

  getState(): Readonly<FloorSequenceState> {
    return { ...this._state };
  }
}

export const FloorSeq = FloorSequence.getInstance();
export type { FloorSequenceState };
