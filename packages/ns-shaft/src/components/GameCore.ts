import { GameResources, LevelConfig } from '../types';
import {
  FLOOR_VELOCITY_BASE,
  CONTROL_VELOCITY,
  MARGIN_TOP,
  STAGE_WIDTH,
  STAGE_HEIGHT,
  WALL_WIDTH,
  ARROW_HEIGHT,
  ARROW_WIDTH,
  HERO_WIDTH,
  FLOOR_DISTANCE,
  PLAY_AREA_WIDTH,
  FLOOR_WIDTH,
  MAX_ACTION_INTERVAL,
  HERO_FEET_OFFSET,
  HERO_FEET_WIDTH,
  GRAVITY_ACC,
} from '../utils/const';
import { Hero } from './Hero';
import {
  type Floor,
  NormalFloor,
  SpringFloor,
  ConveyorFloor,
  ArrowFloor,
  FakeFloor,
} from './Floor';
import { FloorSeq } from './FloorSeq';
import { eventBus } from '../utils/EventBus';

declare global {
  interface Window {
    DEBUG_FLOOR?: number;
    DEBUG?: number;
  }
}

export class GameCore {
  private hero: Hero | null = null;
  private floors: Floor[] = [];
  private score: number = 0;

  private isRunning: boolean = false;
  private isPaused: boolean = false;
  private lastTime: number = 0;
  private animationFrameId: number | null = null;

  private floorVelocity: number = FLOOR_VELOCITY_BASE;
  private level: number = 0;
  private baseFloorVelocity: number = FLOOR_VELOCITY_BASE;

  constructor(
    private ctx: CanvasRenderingContext2D,
    private resources: GameResources,
    private levelConfig: LevelConfig[] = [
      {
        level_threshold: 999999, // Effectively infinite
        floor_chances: [5, 1, 1, 1, 1, 1], // Default distribution
        speed_boost: 1.0, // Default speed
      },
    ]
  ) {
    this.init();
  }

  private init(): void {
    this.drawBg();
    this.resetGameState();
    this.initEventListeners();
  }

  private resetGameState(): void {
    FloorSeq.reset();
    this.floors = [];

    this.hero = new Hero(
      (STAGE_WIDTH - HERO_WIDTH) * 0.5,
      STAGE_HEIGHT - FLOOR_DISTANCE,
      this.resources.images.hero
    );
    this.level = 0;
    this.score = 0;
    this.floorVelocity =
      this.baseFloorVelocity * this.levelConfig[0].speed_boost;
    this.isRunning = false;
    this.lastTime = 0;
  }

  private initEventListeners(): void {
    this.removeEventListeners();
    eventBus.on('scoreUpdate', this.handleScoreUpdate.bind(this));
  }

  private removeEventListeners(): void {
    // 移除所有事件监听器
    eventBus.off('scoreUpdate', this.handleScoreUpdate.bind(this));
  }

  /** =================== Render functions =================== */
  private drawBg(): void {
    if (!this.ctx) {
      throw new Error('Canvas context is not available');
    }

    if (this.resources.images.bg) {
      // draw bg with texture repeat pattern
      const bgPattern = this.ctx.createPattern(
        this.resources.images.bg,
        'repeat'
      );
      if (bgPattern) {
        this.ctx.fillStyle = bgPattern;
        this.ctx.fillRect(
          0,
          MARGIN_TOP,
          STAGE_WIDTH,
          STAGE_HEIGHT - MARGIN_TOP
        );
      }
    }
  }

  private drawBrick(brickHeight: number): void {
    // Fill with the same pattern as floor, it may be white at first
    if (NormalFloor.pattern) {
      this.ctx.fillStyle = NormalFloor.pattern;
    } else {
      this.ctx.fillStyle = '#FFF';
    }
    this.ctx.fillRect(0, 0, WALL_WIDTH, brickHeight);
    // Add the same 3D border effect as floor
    // Top - light
    this.ctx.fillStyle = '#D3F8FF';
    this.ctx.fillRect(0, 0, WALL_WIDTH, 2);

    // Left - light
    this.ctx.fillRect(0, 0, 2, brickHeight);

    // Bottom - dark
    this.ctx.fillStyle = '#000E5C';
    this.ctx.fillRect(0, brickHeight - 2, WALL_WIDTH, 2);

    // Right - dark
    this.ctx.fillRect(WALL_WIDTH - 2, 0, 2, brickHeight);

    this.ctx.restore();
  }

  private drawWalls(): void {
    // using the same pattern and border design as the normal floor
    // Left wall as a series of floor-like rectangles
    const brickHeight = 20;
    for (let y = MARGIN_TOP; y < STAGE_HEIGHT; y += brickHeight) {
      // For each brick in the left wall
      this.ctx.save();
      this.ctx.translate(0, y);
      this.drawBrick(brickHeight);
    }
    // Right wall as a series of floor-like rectangles
    for (let y = MARGIN_TOP; y < STAGE_HEIGHT; y += brickHeight) {
      // For each brick in the right wall
      this.ctx.save();
      this.ctx.translate(STAGE_WIDTH - WALL_WIDTH, y);
      this.drawBrick(brickHeight);
    }
  }

  private drawCeiling(): void {
    // Draw the top spikes with gradients and a matching base
    // First draw the base with gradient and borders
    const ceilingBaseHeight = 10; // Height of the base
    const ceilingBaseY = MARGIN_TOP;

    // Create gradient for the base interior
    const baseGradient = this.ctx.createLinearGradient(
      0,
      ceilingBaseY + ceilingBaseHeight / 2,
      STAGE_WIDTH,
      ceilingBaseY + ceilingBaseHeight / 2
    );
    baseGradient.addColorStop(0, '#FFFFFF'); // White at left
    baseGradient.addColorStop(0.5, '#000000'); // Black in middle
    baseGradient.addColorStop(1, '#FFFFFF'); // White at right
    // Draw base interior with gradient
    this.ctx.fillStyle = baseGradient;
    this.ctx.fillRect(
      1,
      ceilingBaseY + 1,
      STAGE_WIDTH - 2,
      ceilingBaseHeight - 2
    );

    // Draw the borders
    // Top border - light gray #999
    this.ctx.fillStyle = '#999';
    this.ctx.fillRect(0, ceilingBaseY, STAGE_WIDTH, 1);

    // Left border - light gray #999
    this.ctx.fillRect(0, ceilingBaseY, 1, ceilingBaseHeight);

    // Right border - black #000
    this.ctx.fillStyle = '#000';
    this.ctx.fillRect(STAGE_WIDTH - 1, ceilingBaseY, 1, ceilingBaseHeight);

    // Bottom border - black #000
    this.ctx.fillRect(0, ceilingBaseY + ceilingBaseHeight - 1, STAGE_WIDTH, 1);

    // Draw each spike
    for (let x = 0.5; x < STAGE_WIDTH; x += ARROW_WIDTH * 2) {
      const spikeLeft = x;
      const spikeMiddle = x + ARROW_WIDTH;
      const spikeRight = Math.min(x + ARROW_WIDTH * 2, STAGE_WIDTH - 0.5);

      // Create HORIZONTAL gradient for this spike - from left to middle to right
      const gradient = this.ctx.createLinearGradient(
        spikeLeft,
        MARGIN_TOP + ARROW_HEIGHT / 2,
        spikeRight,
        MARGIN_TOP + ARROW_HEIGHT / 2
      );
      gradient.addColorStop(0, '#333333'); // Dark gray on left side
      gradient.addColorStop(0.5, '#FFFFFF'); // White in the middle
      gradient.addColorStop(1, '#333333'); // Dark gray on right side

      // Draw the spike
      this.ctx.beginPath();
      this.ctx.moveTo(spikeLeft, ceilingBaseY + ceilingBaseHeight);
      this.ctx.lineTo(
        spikeMiddle,
        ceilingBaseY + ceilingBaseHeight + ARROW_HEIGHT
      );
      this.ctx.lineTo(spikeRight, ceilingBaseY + ceilingBaseHeight);
      this.ctx.closePath();

      // Fill with gradient
      this.ctx.fillStyle = gradient;
      this.ctx.fill();

      // Add a subtle stroke
      this.ctx.strokeStyle = '#555';
      this.ctx.lineWidth = 0.5;
      this.ctx.stroke();
    }
  }

  /** =================== Update on Frame functions =================== */
  private generateFloor() {
    const firstInit = this.floors.length == 0;
    const floor = this.floors[this.floors.length - 1];
    let postion = (floor && floor.y) || 0;

    // Get current level configuration
    const chances = this.levelConfig[this.level].floor_chances;

    while (postion < STAGE_HEIGHT) {
      postion += FLOOR_DISTANCE;
      const floorY = postion;
      // Calculate floor position within the playable area
      let floorX =
        WALL_WIDTH +
        Math.round(Math.random() * (PLAY_AREA_WIDTH - FLOOR_WIDTH));
      let newFloor: Floor;

      if (firstInit) {
        // make sure can land on a floor at the beginning
        if (floorY > STAGE_HEIGHT - FLOOR_DISTANCE) {
          FloorSeq.start();
          floorX = (STAGE_WIDTH - FLOOR_WIDTH) * 0.5; // Center the first floor
          newFloor = new NormalFloor(floorX, floorY);
          this.floors.push(newFloor);
          continue;
        }
      }

      // Use the floor chances from config to determine floor type
      if (chances && chances.length >= 6) {
        // Calculate the total weight of all floor types
        const totalWeight = chances.reduce((a, b) => a + b, 0);

        // Generate a random value based on the total weight
        const random = Math.random() * totalWeight;
        let weightSum = 0;

        // Determine which floor type to use based on the weights
        if ((weightSum += chances[0]) > random) {
          newFloor = new NormalFloor(floorX, floorY); // Regular floor
        } else if ((weightSum += chances[1]) > random) {
          newFloor = new FakeFloor(floorX, floorY); // Fake floor
        } else if ((weightSum += chances[2]) > random) {
          newFloor = new ArrowFloor(floorX, floorY); // Arrow floor
        } else if ((weightSum += chances[3]) > random) {
          newFloor = new ConveyorFloor(floorX, floorY, 'left'); // Rolling left
        } else if ((weightSum += chances[4]) > random) {
          newFloor = new ConveyorFloor(floorX, floorY, 'right'); // Rolling right
        } else {
          newFloor = new SpringFloor(floorX, floorY); // Spring
        }
      } else {
        // Fallback to original behavior if config is invalid
        const seed = window.DEBUG_FLOOR || Math.random();
        if (seed > 0.5) {
          newFloor = new NormalFloor(floorX, floorY);
        } else if (seed > 0.4) {
          newFloor = new FakeFloor(floorX, floorY);
        } else if (seed > 0.3) {
          newFloor = new ArrowFloor(floorX, floorY);
        } else if (seed > 0.2) {
          newFloor = new ConveyorFloor(floorX, floorY, 'left');
        } else if (seed > 0.1) {
          newFloor = new ConveyorFloor(floorX, floorY, 'right');
        } else {
          newFloor = new SpringFloor(floorX, floorY);
        }
      }

      this.floors.push(newFloor);
    }
  }

  private removeOutboundFloor() {
    let index = 0;
    for (index = 0; index < this.floors.length; index++) {
      const floor = this.floors[index];
      if (floor.y >= MARGIN_TOP) {
        break;
      }
    }

    if (index > 0) {
      this.floors.splice(0, index);
    }
  }

  private updateHeroHorizontalPosition(step: number, time: number) {
    if (!this.hero) return;
    const velocity = this.hero.vx + this.hero.direction * CONTROL_VELOCITY;
    if (velocity !== 0) {
      // Limit hero movement to stay within walls
      this.hero.x = Math.min(
        Math.max(WALL_WIDTH, this.hero.x + velocity * step),
        STAGE_WIDTH - WALL_WIDTH - HERO_WIDTH
      );

      if (this.hero.onFloor) {
        const floor = this.hero.onFloor;
        // Use feet width for floor collision detection
        const feetLeft = this.hero.x + HERO_FEET_OFFSET;
        const feetRight = feetLeft + HERO_FEET_WIDTH;
        if (feetRight <= floor.x || feetLeft >= floor.x + FLOOR_WIDTH) {
          this.hero.onFloor = null; //leaving the floor
          floor.leaving(this.hero, time);
        }
      }
    }
  }

  private updateAllVerticalPositions(step: number, time: number) {
    const floorDistance = step * this.floorVelocity;
    const len = this.floors.length;
    for (let i = 0; i < len; ++i) {
      this.floors[i].y += floorDistance;
    }

    if (!this.hero) return;

    if (this.hero.onFloor) {
      const floor = this.hero.onFloor;
      this.hero.y = floor.y - floor.getHeight();
    } else {
      const heroDistance =
        this.hero.vy * step + 0.5 * GRAVITY_ACC * step * step; // v0t + 1/2gt^2
      const newY = this.hero.y + heroDistance;
      //detect collision
      let hasCollision = false;
      // Calculate the feet position rather than using the full hero width
      const feetLeft = this.hero.x + HERO_FEET_OFFSET;
      const feetRight = feetLeft + HERO_FEET_WIDTH;

      for (let i = 0; i < len; ++i) {
        const floor = this.floors[i];
        // Check if floor overlaps with hero's feet
        if (
          floor.x < feetRight &&
          floor.x + FLOOR_WIDTH > feetLeft &&
          floor.getHeight() > 0
        ) {
          if (
            newY >= floor.y - floor.getHeight() &&
            this.hero.y < floor.y - floor.getHeight() - floorDistance
          ) {
            //collision
            if (window.DEBUG) {
              console.info(
                newY,
                floor.y - floor.getHeight(),
                this.hero.y,
                floor.y - floor.getHeight() - floorDistance
              );
            }
            this.hero.y = floor.y - floor.getHeight();
            this.hero.onFloor = floor;
            floor.landing(this.hero, this.floorVelocity, time);
            hasCollision = true;
            break;
          }
        }
      }
      if (!hasCollision) {
        this.hero.y = newY;
        this.hero.vy += GRAVITY_ACC * step; // v0 + gt;
      }
    }
  }

  private checkHittingTop(time: number) {
    if (!this.hero) return;
    // Arrow
    if (this.hero.y - this.hero.height < MARGIN_TOP) {
      this.hero.y = MARGIN_TOP + this.hero.height;
      this.hero.vy = 0;
      this.hero.hurt(5, time);
      if (this.hero.onFloor) {
        const floor = this.hero.onFloor;
        this.hero.onFloor = null;
        floor.leaving(this.hero, time);
      }
    }
  }

  private checkGameOver(): boolean {
    // 检查以下条件:
    // 1. 英雄不存在
    // 2. 英雄掉出屏幕底部
    // 3. 英雄生命值为 0
    if (
      !this.hero ||
      this.hero.y > STAGE_HEIGHT + this.hero.height ||
      this.hero.life <= 0
    ) {
      return true;
    }
    return false;
  }

  private update(step: number, time: number): boolean {
    if (!this.hero) return true;

    // 更新英雄状态
    if (this.hero.onFloor) {
      this.hero.onFloor.standing(this.hero, time);
    }
    this.hero.checkHealing(time);

    this.generateFloor();
    this.removeOutboundFloor();
    this.updateHeroHorizontalPosition(step, time);
    this.updateAllVerticalPositions(step, time);
    this.checkHittingTop(time);

    // 检查游戏是否结束
    return this.checkGameOver();
  }

  /** =================== Update on Game functions =================== */
  private updateLevel(): void {
    // 检查是否需要提升难度
    while (
      this.level < this.levelConfig.length - 1 &&
      this.score >= this.levelConfig[this.level + 1].level_threshold
    ) {
      this.level++;
      const newConfig = this.levelConfig![this.level];

      // 更新游戏速度
      this.floorVelocity = this.baseFloorVelocity * newConfig.speed_boost;

      console.info(
        `Difficulty increased to level: ${this.level}, at score: ${this.score}, speed boost: ${newConfig.speed_boost}`
      );
    }
  }

  private handleGameOver(): void {
    this.isRunning = false;
    eventBus.emit('gameOver');
  }

  private handleScoreUpdate = (newScore: number): void => {
    if (newScore === this.score) return;
    this.score = newScore;

    // 检查是否需要更新难度
    this.updateLevel();
  };

  private frameLoop = (timestamp: number): void => {
    if (!this.isRunning || !this.hero) return;

    if (!this.lastTime) {
      this.lastTime = timestamp;
    }
    const deltaTime = timestamp - this.lastTime;

    // 如果暂停时间过长，重新开始循环
    if (deltaTime > 2000) {
      console.info('Pause, duration: ' + deltaTime);
      this.pause();
      eventBus.emit('gamePaused');
      return;
    }

    // 更新游戏状态
    let ended = false;
    let remainingTime = deltaTime;

    // 将大的时间步长分解为多个小步长
    while (remainingTime > MAX_ACTION_INTERVAL) {
      ended = this.update(MAX_ACTION_INTERVAL, timestamp - remainingTime);
      if (ended) break;
      remainingTime -= MAX_ACTION_INTERVAL;
    }

    // 处理剩余的时间
    if (!ended) {
      ended = this.update(remainingTime, timestamp);
    }

    // 检查游戏是否结束
    if (ended) {
      this.handleGameOver();
    }

    // 渲染当前帧
    this.render(timestamp);

    // 更新时间戳
    this.lastTime = timestamp;

    // 继续游戏循环
    if (this.isRunning) {
      this.animationFrameId = requestAnimationFrame(this.frameLoop);
    }
  };

  /** =================== Public Functions to Control Game =================== */
  // will call render() on resize, so make it public
  public render(timestamp: number): void {
    // 清空画布
    this.ctx.clearRect(0, 0, STAGE_WIDTH, STAGE_HEIGHT);

    // 绘制背景
    this.drawBg();
    this.drawWalls();

    // 绘制所有地板
    this.floors.forEach((floor) => floor.draw(this.ctx, timestamp));

    // 绘制英雄
    if (this.hero) {
      this.hero.draw(this.ctx, timestamp);
    }

    // 绘制天花板和尖刺, 绘制顺序是需要注意的，ceiling 画在最后，这样 floor 升到顶的时候才会被ceiling 覆盖
    this.drawCeiling();
  }

  public start(): void {
    if (this.isRunning) return;

    if (this.isPaused) {
      this.resume();
    } else {
      // restart
      this.resetGameState();
      this.isRunning = true;
      this.isPaused = false;
      this.lastTime = 0;
      this.animationFrameId = requestAnimationFrame(this.frameLoop);
    }
  }

  public moveLeft(): void {
    if (this.isRunning && this.hero) {
      this.hero.turnLeft();
    }
  }

  public moveRight(): void {
    if (this.isRunning && this.hero) {
      this.hero.turnRight();
    }
  }

  public stay(): void {
    if (this.isRunning && this.hero) {
      this.hero.stay();
    }
  }

  public pause(): void {
    if (this.isRunning) {
      this.isRunning = false;
      this.isPaused = true;
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }
    }
  }

  public resume(): void {
    if (this.isPaused) {
      this.isRunning = true;
      this.isPaused = false;
      this.lastTime = performance.now(); // 重置时间戳，避免大的时间间隔
      this.animationFrameId = requestAnimationFrame(this.frameLoop);
    }
  }

  public destroy(): void {
    this.isRunning = false;
    this.isPaused = false;

    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    this.removeEventListeners();
    this.lastTime = 0;
    this.level = 0;
    this.score = 0;

    this.hero = null;
    this.floors = [];
    FloorSeq.reset();

    if (this.ctx) {
      this.ctx.clearRect(0, 0, STAGE_WIDTH, STAGE_HEIGHT);
    }
  }
}
