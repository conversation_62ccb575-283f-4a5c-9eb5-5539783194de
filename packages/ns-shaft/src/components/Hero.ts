import {
  HERO_WIDTH,
  HERO_FEET_WIDTH,
  HERO_FEET_OFFSET,
  HEAL_DELAY,
  HEAL_INTERVAL,
} from '../utils/const';
import type { Floor } from './Floor';
import { eventBus } from '../utils/EventBus';

export interface HeroSprite {
  standing: {
    middle: number[];
    right: number[];
  };
  falling: {
    middle: number[];
    right: number[];
  };
}

export class Hero {
  x: number;
  y: number;
  width: number;
  height: number;
  heroSprite: HTMLImageElement;
  feetWidth: number; // Store feet width for debugging if needed
  direction: -1 | 0 | 1; // left -1, stay 0, right 1
  onFloor: Floor | null;
  vx: number;
  vy: number;
  life: number;
  lastHurtTime: number; // Time when hero was last hurt
  lastHealTime: number; // Time when hero last healed

  private tintCanvas: HTMLCanvasElement;
  private tintCtx: CanvasRenderingContext2D;
  private frameIndex: number;
  private frameTime: number;
  private hurtTime: number;
  private blinkTime: number;
  private blink: boolean;

  private readonly spritePositions: HeroSprite = {
    standing: {
      middle: [0],
      right: [32, 64, 96], // Walking animation frames
    },
    falling: {
      middle: [128, 160, 192], // Falling animation frames
      right: [128, 160, 192], // Same frames for falling right
    },
  };

  constructor(x: number = 0, y: number = 0, heroSprite: HTMLImageElement) {
    this.heroSprite = heroSprite;
    this.x = x;
    this.y = y;
    this.width = HERO_WIDTH;
    this.height = HERO_WIDTH;
    this.feetWidth = HERO_FEET_WIDTH;
    this.direction = 0;
    this.onFloor = null;
    this.vx = 0;
    this.vy = 0;
    this.life = 10;
    this.lastHurtTime = 0;
    this.lastHealTime = 0;

    // 创建离屏canvas用于受伤效果
    // Create offscreen canvases for tint effect
    this.tintCanvas = document.createElement('canvas');
    this.tintCanvas.width = HERO_WIDTH;
    this.tintCanvas.height = HERO_WIDTH;

    const ctx = this.tintCanvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) {
      throw new Error('Failed to get 2D context for tint canvas');
    }
    this.tintCtx = ctx;

    this.frameIndex = 0;
    this.frameTime = 0;
    this.hurtTime = 0;
    this.blinkTime = 0;
    this.blink = false;
  }

  public turnLeft(): void {
    this.direction = -1;
  }

  public turnRight(): void {
    this.direction = 1;
  }

  public stay(): void {
    this.direction = 0;
  }

  public draw(
    ctx: CanvasRenderingContext2D,
    time: number,
  ): void {
    ctx.save();

    // Disable image smoothing to keep pixel art crisp // 禁用图像平滑以保持像素艺术效果
    ctx.imageSmoothingEnabled = false;

    // 根据方向翻转
    if (this.direction < 0) {
      ctx.scale(-1, 1);
      ctx.translate(-this.x - this.width, this.y);
    } else {
      ctx.translate(this.x, this.y);
    }

    const isHurt =
      this.life < 10 && this.hurtTime > 0 && time - this.hurtTime < 1000;

    // Handle hurt state pulsing 处理受伤状态闪烁
    if (isHurt) {
      if (this.blinkTime < this.hurtTime || time - this.blinkTime >= 100) {
        this.blinkTime = time;
        this.blink = !this.blink;
      }
    } else {
      this.blink = false;
    }

    // 获取当前动画帧
    const state = this.onFloor
      ? this.spritePositions.standing
      : this.spritePositions.falling;
    const frames = this.direction === 0 ? state.middle : state.right;

    // 更新动画帧
    if (time - this.frameTime >= 60) {
      this.frameTime = time;
      ++this.frameIndex;
    }
    this.frameIndex = this.frameIndex % frames.length;

    if (isHurt) {
      this.drawHurtEffect(this.heroSprite, frames[this.frameIndex]);
      ctx.drawImage(
        this.tintCanvas,
        0,
        0,
        this.width,
        this.height,
        0,
        -this.height,
        this.width,
        this.height
      );
    } else {
      // Just draw the normal sprite
      ctx.drawImage(
        this.heroSprite, // @todo, $res.hero, image
        frames[this.frameIndex],
        0,
        32,
        32,
        0,
        -this.height,
        this.width,
        this.height
      );
    }

    ctx.restore();
  }

  private drawHurtEffect(heroSprite: HTMLImageElement, frameX: number): void {
    // 清空临时canvas
    this.tintCtx.clearRect(0, 0, this.width, this.height);

    // 禁用临时canvas的图像平滑
    this.tintCtx.imageSmoothingEnabled = false;

    // 绘制原始精灵 First draw the sprite to get its shape
    this.tintCtx.drawImage(
      heroSprite,
      frameX,
      0,
      32,
      32,
      0,
      0,
      this.width,
      this.height
    );

    // 获取图像数据
    const imageData = this.tintCtx.getImageData(0, 0, this.width, this.height);
    const data = imageData.data;

    // 对非透明像素应用红色色调
    for (let i = 0; i < data.length; i += 4) {
      if (data[i + 3] > 0) {
        // 如果像素不透明
        const intensity = this.blink ? 1.7 : 1.3;
        data[i] = Math.min(255, data[i] * intensity); // 增强红色
        data[i + 1] *= 0.5; // 减少绿色
        data[i + 2] *= 0.5; // 减少蓝色
      }
    }

    // 将修改后的图像数据放回
    this.tintCtx.putImageData(imageData, 0, 0);
  }

  public regain(): void {
    if (this.life < 10) {
      ++this.life;
      eventBus.emit('heroHealed', { life: this.life });
    }
  }

  public hurt(damage: number, time: number): void {
    this.hurtTime = time;
    this.lastHurtTime = time; // Track when hero was last hurt for healing system
    this.life = Math.max(0, this.life - damage);
    eventBus.emit('heroDamaged', { life: this.life });
  }

  public checkHealing(time: number): void {
    // check if hero should heal
    // Only heal if not at full health and enough time has passed since last hurt
    if (this.life < 10 && time - this.lastHurtTime >= HEAL_DELAY) {
      // Check if it's time for another heal tick
      if (time - this.lastHealTime >= HEAL_INTERVAL) {
        this.lastHealTime = time;
        this.regain();
      }
    }
  }

  // // 可选: 用于调试的碰撞箱绘制
  public drawHitbox(ctx: CanvasRenderingContext2D): void {
    ctx.save();
    ctx.translate(this.x, this.y);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 1;
    ctx.strokeRect(HERO_FEET_OFFSET, -5, HERO_FEET_WIDTH, 5);
    ctx.restore();
  }
}
