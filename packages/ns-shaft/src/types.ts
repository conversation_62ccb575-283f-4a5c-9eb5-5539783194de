// 游戏配置类型
export interface GameConfig {
  lcPrefix?: string;
  root?: string;
  levelConfig?: LevelConfig[];
  i18n?: I18nStrings;
}

export interface LevelConfig {
  level_threshold: number;
  floor_chances: number[];
  speed_boost: number;
}

export interface I18nStrings {
  [key: string]: string;
}

export interface GameResources {
  images: Record<string, HTMLImageElement>;
  sounds: Record<string, HTMLAudioElement>;
}

// 游戏状态类型
export enum GameStatus {
  INITIAL = 'initial',
  READY = 'ready',
  RUNNING = 'running',
  PAUSED = 'paused',
  COOLDOWN = 'cooldown',
  GAMEOVER = 'gameover',
}
export interface GameState {
  soundReady: boolean;
  soundEnabled: boolean;
  score: number;
  bestScore: number;
  life: number;
  level: number;
  status: GameStatus;
}

// Floor related
export enum FloorType {
  NORMAL = 'normal',
  SPRING = 'spring',
  CONVEYOR = 'conveyor',
  ARROW = 'arrow',
  FAKE = 'fake',
}

export enum FloorAudio {
  NORMAL = 'normal',
  SPRING = 'spring',
  LR = 'lr',
  HURT = 'hurt',
  ROLL = 'roll',
}
