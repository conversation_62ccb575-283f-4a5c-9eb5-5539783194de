import { useEffect, useCallback, useRef } from 'preact/hooks';

interface ControlState {
  leftPressed: number | null;
  rightPressed: number | null;
  spacePressed: number | null;
}

interface UseControlOptions {
  onLeft?: () => void;
  onRight?: () => void;
  onStay?: () => void;
  onSpace?: () => void;
  onSpaceUp?: () => void;
  enabled?: boolean;
}

export const useControl = ({
  onLeft,
  onRight,
  onStay,
  onSpace,
  onSpaceUp,
  enabled = true,
}: UseControlOptions) => {
  // 使用 ref 存储控制状态，避免重渲染
  const controlState = useRef<ControlState>({
    leftPressed: null,
    rightPressed: null,
    spacePressed: null,
  });

  // 键盘事件处理
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!enabled) return;

      switch (e.keyCode) {
        case 37: // left arrow
          controlState.current.leftPressed = 0;
          onLeft?.();
          e.preventDefault();
          e.stopPropagation();
          break;
        case 39: // right arrow
          controlState.current.rightPressed = 0;
          onRight?.();
          e.preventDefault();
          e.stopPropagation();
          break;
        case 32: // space
        case 13: // enter
          if (!controlState.current.spacePressed) {
            controlState.current.spacePressed = 0;
            onSpace?.();
          }
          e.preventDefault();
          e.stopPropagation();
          break;
      }
    },
    [enabled, onLeft, onRight, onSpace]
  );

  const handleKeyUp = useCallback(
    (e: KeyboardEvent) => {
      if (!enabled) return;

      switch (e.keyCode) {
        case 37: // left arrow
          controlState.current.leftPressed = null;
          if (controlState.current.rightPressed !== null) {
            onRight?.();
          } else {
            onStay?.();
          }
          break;
        case 39: // right arrow
          controlState.current.rightPressed = null;
          if (controlState.current.leftPressed !== null) {
            onLeft?.();
          } else {
            onStay?.();
          }
          break;
        case 32: // space
        case 13: // enter
          if (controlState.current.spacePressed !== null) {
            controlState.current.spacePressed = null;
            onSpaceUp?.();
          }
          break;
      }
    },
    [enabled, onLeft, onRight, onStay, onSpaceUp]
  );

  // 触摸事件处理
  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (!enabled) return;

      const touch = e.changedTouches[0];
      if (touch) {
        if (touch.clientX < window.innerWidth * 0.5) {
          // 左半屏
          controlState.current.leftPressed = touch.identifier;
          onLeft?.();
          e.preventDefault();
          e.stopPropagation();
        } else {
          // 右半屏
          controlState.current.rightPressed = touch.identifier;
          onRight?.();
          e.preventDefault();
          e.stopPropagation();
        }
      }
    },
    [enabled, onLeft, onRight]
  );

  const handleTouchEnd = useCallback(
    (e: TouchEvent) => {
      if (!enabled) return;

      const touch = e.changedTouches[0];
      if (touch) {
        if (touch.identifier === controlState.current.leftPressed) {
          controlState.current.leftPressed = null;
          if (controlState.current.rightPressed !== null) {
            onRight?.();
          } else {
            onStay?.();
          }
        } else if (touch.identifier === controlState.current.rightPressed) {
          controlState.current.rightPressed = null;
          if (controlState.current.leftPressed !== null) {
            onLeft?.();
          } else {
            onStay?.();
          }
        } else if (touch.identifier === controlState.current.spacePressed) {
          controlState.current.spacePressed = null;
          onSpaceUp?.();
        }
      }
    },
    [enabled, onLeft, onRight, onStay, onSpaceUp]
  );

  const handleTouchCancel = useCallback(
    (e: TouchEvent) => {
      if (!enabled) return;

      const touch = e.changedTouches[0];
      if (touch) {
        if (touch.identifier === controlState.current.leftPressed) {
          controlState.current.leftPressed = null;
          if (controlState.current.rightPressed !== null) {
            onRight?.();
          } else {
            onStay?.();
          }
        } else if (touch.identifier === controlState.current.rightPressed) {
          controlState.current.rightPressed = null;
          if (controlState.current.leftPressed !== null) {
            onLeft?.();
          } else {
            onStay?.();
          }
        }
      }
    },
    [enabled, onLeft, onRight, onStay]
  );

  const handleTouchMove = useCallback(
    () => {
      if (!enabled) return;

      // 如果有空格键按下状态，取消它
      if (controlState.current.spacePressed !== null) {
        controlState.current.spacePressed = null;
        onSpaceUp?.();
        // drawAll($ctx, lastTime);
      }
    },
    [enabled, onSpaceUp]
  );
  // 注册和清理事件监听器
  useEffect(() => {
    if (!enabled) return;

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('touchstart', handleTouchStart);
    window.addEventListener('touchend', handleTouchEnd);
    window.addEventListener('touchcancel', handleTouchCancel);
    window.addEventListener('touchmove', handleTouchMove);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);
      window.removeEventListener('touchcancel', handleTouchCancel);
      window.removeEventListener('touchmove', handleTouchMove);
    };
  }, [
    enabled,
    handleKeyDown,
    handleKeyUp,
    handleTouchStart,
    handleTouchEnd,
    handleTouchCancel,
    handleTouchMove,
  ]);

  // 返回当前控制状态
  return controlState.current;
};
