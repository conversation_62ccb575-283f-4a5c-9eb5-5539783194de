.down-100-container {
  text-align: center;
  width: 100%;
  position: relative;
  user-select: none;

  .down-100-canvas {
    display: block;
    margin: 0 auto;
    padding-top: 39px;
  }

  .down-100-overlay-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    background: rgba(65, 65, 65, 0.7);

    &.running {
      background: none;
      bottom: unset;
    }
  }

  .down-100-topbar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    pointer-events: none;
    height: 35px;
    padding: 4px 4px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.9);
    font-family: monospace;
    font-size: 18px;
  }

  @media only screen and (max-width: 500px) {
    .down-100-topbar {
      padding: 4px 10px 0px;
    }
  }

  .down-100-life-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .down-100-life-bar {
    display: flex;
    /* gap: 1px; */
  }

  .down-100-life-bar-unit {
    width: 7px;
    height: 15px;
    border-left: 2px solid #000;
    border-top: 2.5px solid #000;
    border-bottom: 2.5px solid #000;
    background: #fff;
    transition: background-color 0.2s;

    &:last-child {
      border-right: 2px solid #000;
    }

    &.active {
      background: #3f3;
    }

    &.damaged {
      animation: pulse 0.3s ease-in-out;
    }
  }

    /* 生命值变化动画 */
  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.2);
    }

    100% {
      transform: scale(1);
    }
  }

  .down-100-score-container {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
  }

  .down-100-sound-toggle {
    pointer-events: auto; /* 允许点击声音按钮 */
    padding: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: inline-flex;
    justify-content: center;
    align-items: center;

    .sound-off {
      opacity: 0.2;
    }
  }

  .down-100-game-controls {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .down-100-game-button {
    padding: 20px 40px;
    font-size: 24pt;
    font-family: monospace;
    font-weight: bold;
    color: #000;
    background: #fff;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &.restart-hidden {
      visibility: hidden;
    }

    &.restart-visible {
      visibility: visible;
    }
  }

  .down-100-game-button-hint {
    font-size: 10pt;
    margin-top: 8px;
    opacity: 0.7;
  }

  .down-100-game-over-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .down-100-game-over-text {
    font-size: 40pt;
    font-family: monospace;
    font-weight: 900;
    color: #000;
    white-space: nowrap;
    text-shadow:
        3px 3px 0 #fff,
        -3px 3px 0 #fff,
        3px -3px 0 #fff,
        -3px -3px 0 #fff,
        3px 0px 0 #fff,
        -3px 0px 0 #fff,
        0px 3px 0 #fff,
        0px -3px 0 #fff;
    transition: all 0.3s;
  }

  .down-100-best-score, .down-100-reset-button {
    font-size: 18px;
    font-family: monospace;
    color: #fff;
  }

  .down-100-reset-button {
    cursor: pointer;
  }


  /* modal style */
  .down-100-modal-container {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    width: 100%;
    justify-items: center;
    align-content: center;
    opacity: 0;
    visibility: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    background-color: #000c;
    color: inherit;
    animation: modal-pop 0.2s ease-out;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-property: transform, opacity, visibility;
    overflow-y: hidden;
    border: none;
  }

  .down-100-modal-box {
    width: 70%;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    padding: 1.5rem;
    font-family: monospace;
  }

  @media only screen and (max-width: 600px) {
    .down-100-modal-box {
      max-width: 95vw;
    }
  }

  .down-100-modal-text {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .down-100-modal-action {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
  }

  .down-100-modal-btn {
    font-family: monospace;
    cursor: pointer;
    padding: 4px 8px;
    border: none;font-size: 16px;
    text-align: center;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: manipulation;
    border-radius: 3px;
  }

  .reset-button {
    color: #fff;
    background-color: #2e2e4e;
  }

  @keyframes modal-pop {
    0% {
      opacity: 0;
    }
  }
}

#down-100-resources {
  display: none;
}
