import { FloorAudio } from '../types';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type EventCallback = (...args: any[]) => void;

export interface EventMap {
  resourceLoaded: {name: string; type: 'images' | 'sounds'};
  gamePaused: void;
  gameOver: { score: number; bestScore: number };
  scoreUpdate: number;
  floorLanding: { type: FloorAudio };
  heroDamaged: { life: number };
  heroHealed: { life: number };
}

export class EventBus {
  private static instance: EventBus;
  private listeners: Map<keyof EventMap, Set<EventCallback>>;

  private constructor() {
    this.listeners = new Map();
  }

  public static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  public on<T extends keyof EventMap>(
    event: T,
    callback: (payload: EventMap[T]) => void
  ): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)?.add(callback);
  }

  public off<T extends keyof EventMap>(
    event: T,
    callback: (payload: EventMap[T]) => void
  ): void {
    this.listeners.get(event)?.delete(callback);
  }

  public emit<T extends keyof EventMap>(event: T, payload?: EventMap[T]): void {
    this.listeners.get(event)?.forEach((callback) => {
      try {
        callback(payload);
      } catch (error) {
        console.error(`Error in event handler for ${String(event)}:`, error);
      }
    });
  }

  public clear(): void {
    this.listeners.clear();
  }

  // 调试辅助方法
  public getListenerCount(event: keyof EventMap): number {
    return this.listeners.get(event)?.size || 0;
  }

  public getAllEvents(): Array<keyof EventMap> {
    return Array.from(this.listeners.keys());
  }
}

// 导出单例实例
export const eventBus = EventBus.getInstance();
