export const TOPBAR_HEIGHT = 39;
export const MARGIN_TOP = 0;
export const WALL_WIDTH = 10; // Width of the side walls
export const STAGE_WIDTH = 360;
export const STAGE_HEIGHT = 480 + MARGIN_TOP;
export const FLOOR_WIDTH = 100;
export const FLOOR_HEIGHT = 12;
export const FLOOR_DISTANCE = 60;
export const SPRING_HEIGHT = FLOOR_HEIGHT - 4;
export const HERO_WIDTH = 32;
export const HERO_FEET_WIDTH = 26; // Narrower width for collision detection (3px reduction on each side)
export const HERO_FEET_OFFSET = 3; // Offset from the left side of the hero sprite to the feet
export const ARROW_HEIGHT = 15;
export const ARROW_WIDTH = 5; // 钉子尺寸
export const HEAL_DELAY = 2000; // Time to wait after being hurt before healing starts (ms)
export const HEAL_INTERVAL = 1000; // Time between healing ticks (ms)

// Playable area width (accounting for walls)
export const PLAY_AREA_WIDTH = STAGE_WIDTH - WALL_WIDTH * 2;

export const FLOOR_VELOCITY_BASE = -0.1; // 地板上升速度
export const GRAVITY_ACC = 0.0015; // 重力加速度
export const SPRINGING_VELOCITY = -0.5; // 离开弹簧时的初速度
export const SPRING_TIME = 100; // 弹簧压缩时间
export const FAKE_FLOOR_TIME = 100;
export const FAKE_FLOOR_TIME2 = 300; // 虚踏板的停留时间, 虚踏板的转动时间
export const ROLLING_VELOCITY = 0.1; // 传送带速度
export const CONTROL_VELOCITY = 0.2; // 左右操作的速度
export const MAX_ACTION_INTERVAL = 20;
