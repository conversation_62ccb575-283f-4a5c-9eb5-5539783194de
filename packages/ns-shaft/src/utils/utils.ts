import { GameConfig } from "../types";

export const defaultConfig: GameConfig = {
  lcPrefix: 'down100-',
  levelConfig: [
    {
      level_threshold: 20,
      floor_chances: [10, 3, 1, 1, 1, 2], // [regular, fake, arrow, rollingLeft, rollingRight, spring]
      speed_boost: 1.01, // make all speed goes 1% faster
    },
    {
      level_threshold: 40,
      floor_chances: [7, 3, 1, 1, 1, 2], // fewer regular floors
      speed_boost: 1.04, // make all speed goes 4% faster
    },
    {
      level_threshold: 60,
      floor_chances: [5, 4, 2, 2, 2, 3], // much fewer regular floors
      speed_boost: 1.08, // 8% faster
    },
    {
      level_threshold: 80,
      floor_chances: [3, 5, 3, 3, 3, 3], // very few regular floors
      speed_boost: 1.12, // 12% faster
    },
  ],
  i18n: {
    life: 'HP:',
    score: 'Floor:',
    soundOn: 'Sound: ON',
    soundOff: 'Sound: OFF',
    start: 'Start',
    gameOver: 'Game Over',
    restart: 'Restart',
    continue: 'Continue',
    keyHint: '(Space / Enter)',
    best: 'BEST:',
    reset: 'RESET',
    resetConfirm: 'Are you sure you want to reset the best score?',
    confirm: 'Confirm',
    cancel: 'Cancel',
  },
};


export function roundRect(context: CanvasRenderingContext2D, x: number, y: number, w: number, h: number, r: number) {
  if (w < 2 * r) r = w * 0.5;
  if (h < 2 * r) r = h * 0.5;
  context.beginPath();
  context.moveTo(x + r, y);
  context.arcTo(x + w, y, x + w, y + h, r);
  context.arcTo(x + w, y + h, x, y + h, r);
  context.arcTo(x, y + h, x, y, r);
  context.arcTo(x, y, x + w, y, r);
  context.closePath();
}
