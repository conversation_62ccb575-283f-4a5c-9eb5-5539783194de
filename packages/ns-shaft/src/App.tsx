import { GameProvider } from "./components/GameContext";
import { Down100Game } from './components/Game';
import { GameConfig } from './types';
import { defaultConfig } from "./utils/utils";

export default function App({ config = {} }: { config?: Partial<GameConfig> }) {
  config = {
    ...defaultConfig,
    ...config,
  } as GameConfig;

  return (
    <GameProvider config={config}>
      <Down100Game />
    </GameProvider>
  );
}
