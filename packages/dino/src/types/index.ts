export type Position = {
  x: number;
  y: number;
}

export type Dimension = {
  WIDTH: number;
  HEIGHT: number;
}

export interface ISafeSpace {
  xPos: number;
  width: number;
}

/**
 * Collision box object.
 */
export class CollisionBox {
  x: number;
  y: number;
  width: number;
  height: number;

  constructor(x: number, y: number, w: number, h: number) {
    this.x = x;
    this.y = y;
    this.width = w;
    this.height = h;
  }
}

/**
 * Obstacle definitions.
 * minGap: minimum pixel space between obstacles.
 * multipleSpeed: Speed at which multiples are allowed.
 * speedOffset: speed faster / slower than the horizon.
 * minSpeed: Minimum speed which the obstacle can make an appearance.
 *
 */
export type IObstacle = {
  type: string,
  width: number,
  height: number,
  yPos: number,
  multipleSpeed: number,
  minGap: number,
  minSpeed: number,
  collisionBoxes: Array<CollisionBox>,
};

export type GameConfig = {
  lcPrefix?: string,
};
