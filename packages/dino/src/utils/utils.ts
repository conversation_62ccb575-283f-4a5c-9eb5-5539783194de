export const getPercentage = (numerator: number, denominator: number): string => {
  return denominator === 0
    ? 'N/A'
    : ((numerator / denominator) * 100).toFixed(2) + '%';
};

export const RUNNER_STATS = 'dino-cumulative-stats';
export const JUMP_STATS = 'dino-cumulative-jump-stats';

let prefix = '';
export const LOCAL_STORAGE_KEYS = {
  set prefix(value: string) {
    prefix = value || '';
  },
  get RUNNER_STATS() {
    return prefix + RUNNER_STATS;
  },
  get JUMP_STATS() {
    return prefix + JUMP_STATS;
  },
};
