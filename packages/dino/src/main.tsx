import { render } from 'preact';
import <PERSON> from './components/Dino';
import Usage from './components/Usage';
import i18nDefault from './i18n.json';
import type { GameConfig } from "@/types";
import {LOCAL_STORAGE_KEYS} from "@/utils/utils";

if (!__IS_PUBLISH__) {
  render(
    <>
      <Usage />
      <Dino i18n={i18nDefault} />
    </>,
    document.getElementById('dino-game')
  );
}

const DinoGame = {
  async init(selector: Element | string, {
    i18n = i18nDefault,
    root,
    config,
  }: { i18n: Record<string, string>, root?: string, config: GameConfig }) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }
    if (config.lcPrefix) {
      LOCAL_STORAGE_KEYS.prefix = config.lcPrefix;
    }

    if (!root) root = location.origin;

    const loadCSS = async (url) => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'dino-game.css');


    i18n = {
      ...i18nDefault,
      ...i18n,
    };

    render(<Dino i18n={i18n} root={root} />, el);
  }
}

export default DinoGame;
