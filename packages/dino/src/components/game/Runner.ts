import {A11Y_STRINGS, DEFAULT_WIDTH, FPS, HIDDEN_CLASS, IS_HIDPI, IS_IOS, IS_MOBILE, IS_RTL, RESOURCE_POSTFIX} from "@/data";
import {announcePhrase, checkForCollisions, createCanvas, getA11yString, getTimeStamp, vibrate } from "@/utils";
import {loadTimeData} from "@/utils/load-time-data";
import {GAME_TYPE, spriteDefinitionByType} from "@/utils/offline-sprite-definitions";
import { GeneratedSoundFx } from "./SoundFx";
import { Horizon } from "./Horizon";
import { DistanceMeter } from "./DistanceMeter";
import { Trex } from "./Trex";
import {
  getPercentage,
  LOCAL_STORAGE_KEYS,
} from '@/utils/utils';
import { GameOverPanel } from "./GameOverPanel";
import { Obstacle } from "./Obstacle";

/**
 * T-Rex runner.
 * @param {string} outerContainerId Outer containing element id.
 * @param {!Object=} opt_config
 * @constructor
 * @implements {EventListener}
 * @export
 */
export function Runner(outerContainerId: string, opt_config) {
  this.outerContainerEl = document.querySelector(outerContainerId);
  this.containerEl = null;
  this.snackbarEl = null;
  this.statEl = null;
  // A div to intercept touch events. Only set while (playing && useTouch).
  this.touchController = null;

  this.config = opt_config || Object.assign(Runner.config, Runner.normalConfig);
  // Logical dimensions of the container.
  this.dimensions = Runner.defaultDimensions;

  this.gameType = null;
  Runner.spriteDefinition = spriteDefinitionByType.original;

  this.altGameImageSprite = null;
  this.altGameModeActive = false;
  this.altGameModeFlashTimer = null;
  this.fadeInTimer = 0;

  this.canvas = null;
  this.canvasCtx = null;

  this.tRex = null;

  this.distanceMeter = null;
  this.distanceRan = 0;

  this.highestScore = 0;
  this.syncHighestScore = false;

  this.time = 0;
  this.runningTime = 0;
  this.msPerFrame = 1000 / FPS;
  this.currentSpeed = this.config.SPEED;
  Runner.slowDown = false;

  this.obstacles = [];

  this.activated = false; // Whether the easter egg has been activated.
  this.playing = false; // Whether the game is currently in play state.
  this.crashed = false;
  this.paused = false;
  this.inverted = false;
  this.invertTimer = 0;
  this.resizeTimerId_ = null;

  this.playCount = 0;

  // voice control
  this.isUsingVoice = false;

  // long press mode
  this.isFirstKeyDown = 0;
  this.isLongPressMode = false;
  this.longPressStart = -1;
  this.longPressDuration = 0;

  // Sound FX.
  this.audioBuffer = null;

  /** @type {Object} */
  this.soundFx = {};
  this.generatedSoundFx = null;

  // Images.
  this.images = {};
  this.imagesLoaded = 0;

  // Statistics
  this.statistics = {
    voice: 0,
    voiceMiss: 0,
    up: 0,
    upMiss: 0,
    space: 0,
    spaceMiss: 0,
    effective: 0,
    lastOps: null,
  };

  // Jump Statistics
  this.jumpStatistics = {
    jump: 0,
    miss: 0,
    score: 0,
  };

  // Gamepad state.
  this.pollingGamepads = false;
  this.gamepadIndex = undefined;
  this.previousGamepad = null;

  if (this.isDisabled()) {
    this.setupDisabledRunner();
  } else {
    if (Runner.isAltGameModeEnabled()) {
      this.initAltGameType();
      Runner.gameType = this.gameType;
    }
    this.loadImages();

    window['initializeEasterEggHighScore'] =
      this.initializeHighScore.bind(this);
  }
}

/**
 * Default game configuration.
 * Shared config for all  versions of the game. Additional parameters are
 * defined in Runner.normalConfig and Runner.slowConfig.
 */
Runner.config = {
  AUDIOCUE_PROXIMITY_THRESHOLD: 190,
  AUDIOCUE_PROXIMITY_THRESHOLD_MOBILE_A11Y: 250,
  BG_CLOUD_SPEED: 0.2,
  BOTTOM_PAD: 10,
  // Scroll Y threshold at which the game can be activated.
  CANVAS_IN_VIEW_OFFSET: -10,
  CLEAR_TIME: 3000,
  CLOUD_FREQUENCY: 0.5,
  FADE_DURATION: 1,
  FLASH_DURATION: 1000,
  GAMEOVER_CLEAR_TIME: 1200,
  INITIAL_JUMP_VELOCITY: 12,
  INTRO_DURATION: 400,
  INVERT_FADE_DURATION: 12000,
  MAX_BLINK_COUNT: 3,
  MAX_CLOUDS: 6,
  MAX_OBSTACLE_LENGTH: 3,
  MAX_OBSTACLE_DUPLICATION: 2,
  SPEED: 6,
  SPEED_DROP_COEFFICIENT: 3,
  ARCADE_MODE_INITIAL_TOP_POSITION: 35,
  ARCADE_MODE_TOP_POSITION_PERCENT: 0.1,
};

Runner.normalConfig = {
  ACCELERATION: 0.001,
  AUDIOCUE_PROXIMITY_THRESHOLD: 190,
  AUDIOCUE_PROXIMITY_THRESHOLD_MOBILE_A11Y: 250,
  GAP_COEFFICIENT: 0.6,
  INVERT_DISTANCE: 700,
  MAX_SPEED: 13,
  MOBILE_SPEED_COEFFICIENT: 1.2,
  SPEED: 6,
};

Runner.slowConfig = {
  ACCELERATION: 0.0005,
  AUDIOCUE_PROXIMITY_THRESHOLD: 170,
  AUDIOCUE_PROXIMITY_THRESHOLD_MOBILE_A11Y: 220,
  GAP_COEFFICIENT: 0.3,
  INVERT_DISTANCE: 350,
  MAX_SPEED: 9,
  MOBILE_SPEED_COEFFICIENT: 1.5,
  SPEED: 4.2,
};

/**
 * Default dimensions.
 */
Runner.defaultDimensions = {
  WIDTH: DEFAULT_WIDTH,
  HEIGHT: 150,
};

/**
 * CSS class names.
 * @enum {string}
 */
Runner.classes = {
  ARCADE_MODE: 'arcade-mode',
  CANVAS: 'runner-canvas',
  CONTAINER: 'runner-container',
  CRASHED: 'crashed',
  INVERTED: 'inverted',
  SNACKBAR: 'snackbar',
  SNACKBAR_SHOW: 'snackbar-show',
  TOUCH_CONTROLLER: 'touch-controller',
  STATS: 'stats',
};

/**
 * Sound FX. Reference to the ID of the audio tag on interstitial page.
 * @enum {string}
 */
Runner.sounds = {
  BUTTON_PRESS: 'dino-offline-sound-press',
  HIT: 'dino-offline-sound-hit',
  LONG_PRESS: 'dino-offline-sound-long-press',
  SCORE: 'dino-offline-sound-reached',
};

/**
 * Key code mapping.
 * @enum {Object}
 */
Runner.keycodes = {
  JUMP: { 38: 1, 32: 1 }, // Up, spacebar
  DUCK: { 40: 1 }, // Down
  RESTART: { 13: 1 }, // Enter
};

/**
 * Runner event names.
 * @enum {string}
 */
Runner.events = {
  ANIM_END: 'animationend',
  CLICK: 'click',
  KEYDOWN: 'keydown',
  KEYUP: 'keyup',
  POINTERDOWN: 'pointerdown',
  POINTERUP: 'pointerup',
  RESIZE: 'resize',
  TOUCHEND: 'touchend',
  TOUCHSTART: 'touchstart',
  VISIBILITY: 'visibilitychange',
  BLUR: 'blur',
  FOCUS: 'focus',
  LOAD: 'load',
  GAMEPADCONNECTED: 'gamepadconnected',
};

Runner.prototype = {
  /**
   * Initialize alternative game type.
   */
  initAltGameType() {
    if (GAME_TYPE.length > 0) {
      this.gameType =
        loadTimeData && loadTimeData.valueExists('altGameType')
          ? GAME_TYPE[parseInt(loadTimeData.getValue('altGameType'), 10) - 1]
          : '';
    }
  },

  /**
   * Whether the easter egg has been disabled. CrOS enterprise enrolled devices.
   * @return {boolean}
   */
  isDisabled() {
    return loadTimeData && loadTimeData.valueExists('disabledEasterEgg');
  },

  /**
   * For disabled instances, set up a snackbar with the disabled message.
   */
  setupDisabledRunner() {
    this.containerEl = document.createElement('div');
    this.containerEl.className = Runner.classes.SNACKBAR;
    this.containerEl.textContent = loadTimeData.getValue('disabledEasterEgg');
    this.outerContainerEl.appendChild(this.containerEl);

    // Show notification when the activation key is pressed.
    document.addEventListener(
      Runner.events.KEYDOWN,
      function (e) {
        if (Runner.keycodes.JUMP[e.keyCode]) {
          this.containerEl.classList.add(Runner.classes.SNACKBAR_SHOW);
          document.querySelector('.icon').classList.add('icon-disabled');
        }
      }.bind(this)
    );
  },

  /**
   * Setting individual settings for debugging.
   * @param {string} setting
   * @param {number|string} value
   */
  updateConfigSetting(setting, value) {
    if (setting in this.config && value !== undefined) {
      this.config[setting] = value;

      switch (setting) {
        case 'GRAVITY':
        case 'MIN_JUMP_HEIGHT':
        case 'SPEED_DROP_COEFFICIENT':
          this.tRex.config[setting] = value;
          break;
        case 'INITIAL_JUMP_VELOCITY':
          this.tRex.setJumpVelocity(value);
          break;
        case 'SPEED':
          this.setSpeed(/** @type {number} */ (value));
          break;
      }
    }
  },

  /**
   * Creates an on page image element from the base 64 encoded string source.
   * @param {string} resourceName Name in data object,
   * @return {HTMLImageElement} The created element.
   */
  createImageElement(resourceName) {
    const imgSrc =
      loadTimeData && loadTimeData.valueExists(resourceName)
        ? loadTimeData.getString(resourceName)
        : null;

    if (imgSrc) {
      const el = /** @type {HTMLImageElement} */ (
        document.createElement('img')
      );
      el.id = resourceName;
      el.src = imgSrc;
      document.getElementById(RESOURCE_POSTFIX).appendChild(el);
      return el;
    }
    return null;
  },

  /**
   * Cache the appropriate image sprite from the page and get the sprite sheet
   * definition.
   */
  loadImages() {
    let scale = '1x';
    this.spriteDef = Runner.spriteDefinition.LDPI;
    if (IS_HIDPI) {
      scale = '2x';
      this.spriteDef = Runner.spriteDefinition.HDPI;
    }

    Runner.imageSprite =
      /** @type {HTMLImageElement} */
      (document.getElementById(RESOURCE_POSTFIX + '-' + scale));

    if (this.gameType) {
      Runner.altGameImageSprite =
        /** @type {HTMLImageElement} */
        (this.createImageElement('altGameSpecificImage' + scale));
      Runner.altCommonImageSprite =
        /** @type {HTMLImageElement} */
        (this.createImageElement('altGameCommonImage' + scale));
    }
    Runner.origImageSprite = Runner.imageSprite;

    // Disable the alt game mode if the sprites can't be loaded.
    if (!Runner.altGameImageSprite || !Runner.altCommonImageSprite) {
      Runner.isAltGameModeEnabled = () => false;
      this.altGameModeActive = false;
    }

    if (Runner.imageSprite.complete) {
      this.init();
    } else {
      // If the images are not yet loaded, add a listener.
      Runner.imageSprite.addEventListener(
        Runner.events.LOAD,
        this.init.bind(this)
      );
    }
  },

  /**
   * Load and decode base 64 encoded sounds.
   */
  loadSounds() {
    if (!IS_IOS) {
      for (const sound in Runner.sounds) {
        this.soundFx[sound] = document.getElementById(
          Runner.sounds[sound]
        );
      }
      this.soundFx.LONG_PRESS.addEventListener(
        'timeupdate',
        (e: Event) => {
          const audio = e.target as HTMLMediaElement;
          if (audio.currentTime >= audio.duration - 0.01) {
            audio.currentTime = audio.duration - 0.5;
            // 0.2 makes whole audio loop, not only the last sound
            audio.play();
          }
        }
      );
    }
  },

  /**
   * Sets the game speed. Adjust the speed accordingly if on a smaller screen.
   * @param {number=} opt_speed
   */
  setSpeed(opt_speed) {
    const speed = opt_speed || this.currentSpeed;

    // Reduce the speed on smaller mobile screens.
    if (this.dimensions.WIDTH < DEFAULT_WIDTH) {
      const mobileSpeed = Runner.slowDown
        ? speed
        : ((speed * this.dimensions.WIDTH) / DEFAULT_WIDTH) *
        this.config.MOBILE_SPEED_COEFFICIENT;
      this.currentSpeed = mobileSpeed > speed ? speed : mobileSpeed;
    } else if (opt_speed) {
      this.currentSpeed = opt_speed;
    }
  },

  /**
   * Game initialiser.
   */
  init() {
    this.adjustDimensions();
    this.setSpeed();

    const ariaLabel = getA11yString(A11Y_STRINGS.ariaLabel);
    this.containerEl = document.createElement('div');
    this.containerEl.setAttribute('role', IS_MOBILE ? 'button' : 'application');
    this.containerEl.setAttribute('tabindex', '0');
    this.containerEl.setAttribute(
      'title',
      getA11yString(A11Y_STRINGS.description)
    );
    this.containerEl.setAttribute('aria-label', ariaLabel);

    this.containerEl.className = Runner.classes.CONTAINER;

    // Player canvas container.
    this.canvas = createCanvas(
      this.containerEl,
      this.dimensions.WIDTH,
      this.dimensions.HEIGHT
    );

    // Live region for game status updates.
    this.a11yStatusEl = document.createElement('span');
    this.a11yStatusEl.className = 'offline-runner-live-region';
    this.a11yStatusEl.setAttribute('aria-live', 'assertive');
    this.a11yStatusEl.textContent = '';
    Runner.a11yStatusEl = this.a11yStatusEl;

    // Add checkbox to slow down the game.
    this.slowSpeedCheckboxLabel = document.createElement('label');
    this.slowSpeedCheckboxLabel.className = 'slow-speed-option option-label hidden';
    this.slowSpeedCheckboxLabel.textContent = getA11yString(
      A11Y_STRINGS.speedLabel
    );

    this.slowSpeedCheckbox = document.createElement('input');
    this.slowSpeedCheckbox.setAttribute('type', 'checkbox');
    this.slowSpeedCheckbox.setAttribute(
      'title',
      getA11yString(A11Y_STRINGS.speedLabel)
    );
    this.slowSpeedCheckbox.setAttribute('tabindex', '0');
    this.slowSpeedCheckbox.setAttribute('checked', 'checked');

    this.slowSpeedToggleEl = document.createElement('span');
    this.slowSpeedToggleEl.className = 'slow-speed-toggle option-toggle';

    this.slowSpeedCheckboxLabel.appendChild(this.slowSpeedCheckbox);
    this.slowSpeedCheckboxLabel.appendChild(this.slowSpeedToggleEl);

    if (IS_IOS) {
      this.outerContainerEl.appendChild(this.a11yStatusEl);
    } else {
      this.containerEl.appendChild(this.a11yStatusEl);
    }

    this.generatedSoundFx = new GeneratedSoundFx();

    this.canvasCtx = /** @type {CanvasRenderingContext2D} */ (
      this.canvas.getContext('2d')
    );
    this.canvasCtx.fillStyle = '#f7f7f7';
    this.canvasCtx.fill();
    Runner.updateCanvasScaling(this.canvas);

    // Horizon contains clouds, obstacles and the ground.
    this.horizon = new Horizon(
      this.canvas,
      this.spriteDef,
      this.dimensions,
      this.config.GAP_COEFFICIENT
    );

    // Distance meter
    this.distanceMeter = new DistanceMeter(
      this.canvas,
      this.spriteDef.TEXT_SPRITE,
      this.dimensions.WIDTH
    );

    // Draw t-rex
    this.tRex = new Trex(this.canvas, this.spriteDef.TREX);
    this.tRex.on('grounded', this.onTrexGrounded.bind(this));

    this.outerContainerEl.appendChild(this.containerEl);
    // this.outerContainerEl.appendChild(this.slowSpeedCheckboxLabel);

    this.startListening();
    this.update();

    window.addEventListener(
      Runner.events.RESIZE,
      this.debounceResize.bind(this)
    );

    // Handle dark mode
    const darkModeMediaQuery = window.matchMedia(
      '(prefers-color-scheme: dark)'
    );
    this.isDarkMode = darkModeMediaQuery && darkModeMediaQuery.matches;
    darkModeMediaQuery.addListener((e) => {
      this.isDarkMode = e.matches;
    });

    this.setArcadeModeContainerScale();
  },

  /**
   * Create the touch controller. A div that covers whole screen.
   */
  createTouchController() {
    this.touchController = document.createElement('div');
    this.touchController.className = Runner.classes.TOUCH_CONTROLLER;
    this.touchController.addEventListener(Runner.events.TOUCHSTART, this);
    this.touchController.addEventListener(Runner.events.TOUCHEND, this);
    this.outerContainerEl.appendChild(this.touchController);
  },

  /**
   * Debounce the resize event.
   */
  debounceResize() {
    if (!this.resizeTimerId_) {
      this.resizeTimerId_ = setInterval(this.adjustDimensions.bind(this), 250);
    }
  },

  /**
   * Adjust game space dimensions on resize.
   */
  adjustDimensions() {
    clearInterval(this.resizeTimerId_);
    this.resizeTimerId_ = null;

    const boxStyles = window.getComputedStyle(this.outerContainerEl);
    const padding = Number(
      boxStyles.paddingLeft.substr(0, boxStyles.paddingLeft.length - 2)
    );

    // make it can scale by default, not only when arcade mode on
    this.dimensions.WIDTH = Math.min(
      DEFAULT_WIDTH,
      this.outerContainerEl.offsetWidth - padding * 2
    );
    if (this.activated) {
      this.setArcadeModeContainerScale();
    }

    // Redraw the elements back onto the canvas.
    if (this.canvas) {
      this.canvas.width = this.dimensions.WIDTH;
      this.canvas.height = this.dimensions.HEIGHT;

      Runner.updateCanvasScaling(this.canvas);

      this.distanceMeter.calcXPos(this.dimensions.WIDTH);
      this.clearCanvas();
      //console.log('LOG: Resize adjustDimensions: horizon updating');
      this.horizon.update(0, 0, true);
      this.tRex.update(0);

      // Outer container and distance meter.
      if (this.playing || this.crashed || this.paused) {
        this.containerEl.style.width = this.dimensions.WIDTH + 'px';
        this.containerEl.style.height = this.dimensions.HEIGHT + 'px';
        this.distanceMeter.update(0, Math.ceil(this.distanceRan));
        this.stop();
      } else {
        this.tRex.draw(0, 0);
      }

      // Game over panel.
      if (this.crashed && this.gameOverPanel) {
        this.gameOverPanel.updateDimensions(this.dimensions.WIDTH);
        this.gameOverPanel.draw(this.altGameModeActive, this.tRex);
      }
    }
  },

  /**
   * Play the game intro.
   * Canvas container width expands out to the full width.
   */
  playIntro() {
    if (!this.activated && !this.crashed) {
      this.playingIntro = true;
      this.tRex.playingIntro = true;

      // CSS animation definition.
      const keyframes =
        '@-webkit-keyframes intro { ' +
        'from { width:' +
        Trex.config.WIDTH +
        'px }' +
        'to { width: ' +
        this.dimensions.WIDTH +
        'px }' +
        '}';
      document.styleSheets[0].insertRule(keyframes, 0);

      this.containerEl.addEventListener(
        Runner.events.ANIM_END,
        this.startGame.bind(this)
      );

      this.containerEl.style.animation = `intro ${this.config.INTRO_DURATION / 1000}s ease-out 1 both`;
      this.containerEl.style.width = this.dimensions.WIDTH + 'px';

      this.setPlayStatus(true);
      this.activated = true;
      //console.log('LOG: PlayIntro Animation done, should be activated!');
    } else if (this.crashed) {
      this.restart();
    }
  },

  /**
   * Update the game status to started.
   */
  startGame() {
    if (this.isArcadeMode()) {
      this.setArcadeModeContainerScale();
    }
    // this.toggleSpeed();
    this.runningTime = 0;
    this.playingIntro = false;
    this.tRex.playingIntro = false;
    this.containerEl.style.animation = '';
    this.playCount++;
    this.generatedSoundFx.background();

    if (Runner.audioCues) {
      this.containerEl.setAttribute('title', getA11yString(A11Y_STRINGS.jump));
    }

    // Handle tabbing off the page. Pause the current game.
    document.addEventListener(
      Runner.events.VISIBILITY,
      this.onVisibilityChange.bind(this)
    );

    window.addEventListener(
      Runner.events.BLUR,
      this.onVisibilityChange.bind(this)
    );

    window.addEventListener(
      Runner.events.FOCUS,
      this.onVisibilityChange.bind(this)
    );
  },

  clearCanvas() {
    this.canvasCtx.clearRect(
      0,
      0,
      this.dimensions.WIDTH,
      this.dimensions.HEIGHT
    );
  },

  /**
   * Checks whether the canvas area is in the viewport of the browser
   * through the current scroll position.
   * @return boolean.
   */
  isCanvasInView() {
    return (
      this.containerEl.getBoundingClientRect().top >
      Runner.config.CANVAS_IN_VIEW_OFFSET
    );
  },

  /**
   * Enable the alt game mode. Switching out the sprites.
   */
  enableAltGameMode() {
    Runner.imageSprite = Runner.altGameImageSprite;
    Runner.spriteDefinition = spriteDefinitionByType[Runner.gameType];

    if (IS_HIDPI) {
      this.spriteDef = Runner.spriteDefinition.HDPI;
    } else {
      this.spriteDef = Runner.spriteDefinition.LDPI;
    }

    this.altGameModeActive = true;
    this.tRex.enableAltGameMode(this.spriteDef.TREX);
    this.horizon.enableAltGameMode(this.spriteDef);
    this.generatedSoundFx.background();
  },

  /**
   * Update the game frame and schedules the next one.
   */
  update() {
    this.updatePending = false;

    const now = getTimeStamp();
    let deltaTime = now - (this.time || now);

    // Flashing when switching game modes.
    if (this.altGameModeFlashTimer < 0 || this.altGameModeFlashTimer === 0) {
      this.altGameModeFlashTimer = null;
      this.tRex.setFlashing(false);
      this.enableAltGameMode();
    } else if (this.altGameModeFlashTimer > 0) {
      this.altGameModeFlashTimer -= deltaTime;
      this.tRex.update(deltaTime);
      deltaTime = 0;
    }

    this.time = now;

    if (this.playing) {
      this.clearCanvas();

      // Additional fade in - Prevents jump when switching sprites
      if (
        this.altGameModeActive &&
        this.fadeInTimer <= this.config.FADE_DURATION
      ) {
        this.fadeInTimer += deltaTime / 1000;
        this.canvasCtx.globalAlpha = this.fadeInTimer;
      } else {
        this.canvasCtx.globalAlpha = 1;
      }

      if (this.tRex.jumping) {
        this.tRex.updateJump(deltaTime);
      }

      this.runningTime += deltaTime;
      const hasObstacles = this.runningTime > (
        this.isLongPressMode
          ? this.config.INTRO_DURATION : this.config.CLEAR_TIME
      );

      // First jump triggers the intro.
      if (this.tRex.jumpCount === 1 && !this.playingIntro) {
        this.playIntro();
      }

      // The horizon doesn't move until the intro is over.
      if (this.playingIntro) {
        this.horizon.update(0, this.currentSpeed, hasObstacles);
      } else if (!this.crashed) {
        const showNightMode = this.isDarkMode ^ this.inverted;
        deltaTime = !this.activated ? 0 : deltaTime;
        let updateDeltaTime = deltaTime;
        if (this.isLongPressMode) {
          updateDeltaTime = 0;
          if (this.longPressDuration > 0) {
            updateDeltaTime = deltaTime;
            this.longPressDuration = Math.max(this.longPressDuration - deltaTime * 3, 0);
          }
        }
        this.horizon.update(
          updateDeltaTime,
          this.currentSpeed + this.longPressDuration / 500, // move faster when long press
          hasObstacles,
          showNightMode
        );
      }

      // Check for collisions.
      let collision =
        hasObstacles && checkForCollisions(this.horizon.obstacles, this.tRex, undefined, this.isLongPressMode);

      // For a11y, audio cues.
      if (Runner.audioCues && hasObstacles) {
        const jumpObstacle =
          this.horizon.obstacles[0].typeConfig.type != 'COLLECTABLE';

        if (!this.horizon.obstacles[0].jumpAlerted) {
          const threshold = Runner.isMobileMouseInput
            ? Runner.config.AUDIOCUE_PROXIMITY_THRESHOLD_MOBILE_A11Y
            : Runner.config.AUDIOCUE_PROXIMITY_THRESHOLD;
          const adjProximityThreshold =
            threshold +
            threshold * Math.log10(this.currentSpeed / Runner.config.SPEED);

          if (this.horizon.obstacles[0].xPos < adjProximityThreshold) {
            if (jumpObstacle) {
              this.generatedSoundFx.jump();
            }
            this.horizon.obstacles[0].jumpAlerted = true;
          }
        }
      }

      // Activated alt game mode.
      if (
        Runner.isAltGameModeEnabled() &&
        collision &&
        this.horizon.obstacles[0].typeConfig.type == 'COLLECTABLE'
      ) {
        this.horizon.removeFirstObstacle();
        this.tRex.setFlashing(true);
        collision = false;
        this.altGameModeFlashTimer = this.config.FLASH_DURATION;
        this.runningTime = 0;
        this.generatedSoundFx.collect();
      }

      if (!collision) {
        if (!this.isLongPressMode) {
          this.distanceRan += (this.currentSpeed * deltaTime) / this.msPerFrame;

          if (this.currentSpeed < this.config.MAX_SPEED) {
            this.currentSpeed += this.config.ACCELERATION;
          }
        }
      } else if (!this.tRex.isCharging) { // not check collision when charging for long press
        if (this.isLongPressMode) {
          this.jumpStatistics.miss++;
        } else {
          this.statistics.effective =
            this.horizon.obstacleCount - this.horizon.obstacles.length;
        }
        this.gameOver();
      }

      const playAchievementSound = this.distanceMeter.update(
        deltaTime,
        Math.ceil(this.distanceRan)
      );

      if (!Runner.audioCues && playAchievementSound) {
        this.playSound(this.soundFx.SCORE);
      }

      // Night mode.
      if (!Runner.isAltGameModeEnabled()) {
        if (this.invertTimer > this.config.INVERT_FADE_DURATION) {
          this.invertTimer = 0;
          this.invertTrigger = false;
          this.invert(false);
        } else if (this.invertTimer) {
          this.invertTimer += deltaTime;
        } else {
          const actualDistance = this.distanceMeter.getActualDistance(
            Math.ceil(this.distanceRan)
          );
          if (this.isLongPressMode && actualDistance > this.horizon.getCurrentLevelThreshold()) {
            this.horizon.levelUp();
          }

          if (actualDistance > 0) {
            this.invertTrigger = !(
              actualDistance % this.config.INVERT_DISTANCE
            );

            if (this.invertTrigger && this.invertTimer === 0) {
              this.invertTimer += deltaTime;
              this.invert(false);
            }
          }
        }
      }
    }

    if (
      this.playing ||
      (!this.activated && this.tRex.blinkCount < Runner.config.MAX_BLINK_COUNT)
    ) {
      this.tRex.update(deltaTime);
      this.scheduleNextUpdate();
    }
  },

  /**
   * Switch Runner or Jumper Mode
   * @param {boolean} isRunner
   */
  switchMode(isRunner) {
    this.isLongPressMode = !isRunner;
    this.horizon.isLongPressMode = !isRunner;
    this.tRex.isLongPress = !isRunner;
  },

  /**
   * Event handler.
   * @param {Event} e
   */
  handleEvent(e) {
    return function (evtType, events) {
      switch (evtType) {
        case events.KEYDOWN:
        case events.TOUCHSTART:
        case events.POINTERDOWN:
          this.onKeyDown(e);
          break;
        case events.KEYUP:
        case events.TOUCHEND:
        case events.POINTERUP:
          this.onKeyUp(e);
          break;
        case events.GAMEPADCONNECTED:
          this.onGamepadConnected(e);
          break;
      }
    }.bind(this)(e.type, Runner.events);
  },

  /**
   * Initialize audio cues if activated by focus on the canvas element.
   * @param {Event} e
   */
  handleCanvasKeyPress(e) {
    if (!this.activated && !Runner.audioCues) {
      // this.toggleSpeed();
      Runner.audioCues = true;
      this.generatedSoundFx.init();
      Runner.generatedSoundFx = this.generatedSoundFx;
      Runner.config.CLEAR_TIME *= 1.2;
    } else if (e.keyCode && Runner.keycodes.JUMP[e.keyCode]) {
      this.onKeyDown(e);
    }
  },

  /**
   * Prevent space key press from scrolling.
   * @param {Event} e
   */
  preventScrolling(e) {
    if (e.keyCode === 32) {
      e.preventDefault();
    }
  },

  /**
   * Toggle speed setting if toggle is shown.
   */
  toggleSpeed() {
    if (Runner.audioCues) {
      const speedChange = Runner.slowDown != this.slowSpeedCheckbox.checked;

      if (speedChange) {
        Runner.slowDown = this.slowSpeedCheckbox.checked;
        const updatedConfig = Runner.slowDown
          ? Runner.slowConfig
          : Runner.normalConfig;

        Runner.config = Object.assign(Runner.config, updatedConfig);
        this.currentSpeed = updatedConfig.SPEED;
        this.tRex.enableSlowConfig();
        this.horizon.adjustObstacleSpeed();
      }
      if (this.playing) {
        this.disableSpeedToggle(true);
      }
    }
  },

  /**
   * Show the speed toggle.
   * From focus event or when audio cues are activated.
   * @param {Event=} e
   */
  showSpeedToggle(e) {
    const isFocusEvent = e && e.type == 'focus';
    if (Runner.audioCues || isFocusEvent) {
      this.slowSpeedCheckboxLabel.classList.toggle(
        HIDDEN_CLASS,
        isFocusEvent ? false : !this.crashed
      );
    }
  },

  /**
   * Disable the speed toggle.
   * @param {boolean} disable
   */
  disableSpeedToggle(disable) {
    if (disable) {
      this.slowSpeedCheckbox.setAttribute('disabled', 'disabled');
    } else {
      this.slowSpeedCheckbox.removeAttribute('disabled');
    }
  },

  /**
   * Bind relevant key / mouse / touch listeners.
   */
  startListening() {
    // A11y keyboard / screen reader activation.
    this.containerEl.addEventListener(
      Runner.events.KEYDOWN,
      this.handleCanvasKeyPress.bind(this)
    );
    // if (!IS_MOBILE) {
    //   this.containerEl.addEventListener(
    //     Runner.events.FOCUS,
    //     this.showSpeedToggle.bind(this)
    //   );
    // }
    this.canvas.addEventListener(
      Runner.events.KEYDOWN,
      this.preventScrolling.bind(this)
    );
    this.canvas.addEventListener(
      Runner.events.KEYUP,
      this.preventScrolling.bind(this)
    );

    // Keys.
    document.addEventListener(Runner.events.KEYDOWN, this);
    document.addEventListener(Runner.events.KEYUP, this);

    // Touch / pointer.
    this.containerEl.addEventListener(Runner.events.TOUCHSTART, this);
    document.addEventListener(Runner.events.POINTERDOWN, this);
    document.addEventListener(Runner.events.POINTERUP, this);

    if (this.isArcadeMode()) {
      // Gamepad
      window.addEventListener(Runner.events.GAMEPADCONNECTED, this);
    }
  },

  /**
   * Remove all listeners.
   */
  stopListening() {
    document.removeEventListener(Runner.events.KEYDOWN, this);
    document.removeEventListener(Runner.events.KEYUP, this);

    if (this.touchController) {
      this.touchController.removeEventListener(Runner.events.TOUCHSTART, this);
      this.touchController.removeEventListener(Runner.events.TOUCHEND, this);
    }

    this.containerEl.removeEventListener(Runner.events.TOUCHSTART, this);
    document.removeEventListener(Runner.events.POINTERDOWN, this);
    document.removeEventListener(Runner.events.POINTERUP, this);

    if (this.isArcadeMode()) {
      window.removeEventListener(Runner.events.GAMEPADCONNECTED, this);
    }
  },

  /**
   * Process keydown.
   */
  onKeyDown(e: KeyboardEvent) {
    // Prevent native page scrolling whilst tapping on mobile.
    if (IS_MOBILE && this.playing) {
      e.preventDefault();
    }

    if (e.repeat && (this.isFirstKeyDown || this.isLongPressMode)) {
      e.preventDefault();
      return;
    }

    if (!this.isLongPressMode) {
      if (e.keyCode === 38) {
        this.statistics.up++;
        this.statistics.lastOps = 'up';
      } else if (e.keyCode === 32) {
        if (e.useVoice) {
          this.statistics.voice++;
          this.statistics.lastOps = 'voice';
        } else {
          this.statistics.space++;
          this.statistics.lastOps = 'space';
        }
      }
    }

    if (this.isCanvasInView()) {
      // Allow toggling of speed toggle.
      if (
        Runner.keycodes.JUMP[e.keyCode] &&
        e.target == this.slowSpeedCheckbox
      ) {
        return;
      }

      if (!this.crashed && !this.paused) {
        // For a11y, screen reader activation.
        const isMobileMouseInput =
          IS_MOBILE &&
          e.type === Runner.events.POINTERDOWN &&
          e.pointerType == 'mouse' &&
          (e.target == this.containerEl ||
            (IS_IOS &&
              (e.target == this.touchController || e.target == this.canvas)));

        if (
          Runner.keycodes.JUMP[e.keyCode] ||
          (!IS_MOBILE && e.type === Runner.events.POINTERDOWN && e.target == this.canvas) ||  // use mouse to long press on desktop web
          e.type === Runner.events.TOUCHSTART ||
          isMobileMouseInput
        ) {
          e.preventDefault();
          // Starting the game for the first time.
          if (!this.playing) {
            // Started by touch so create a touch controller.
            if (!this.touchController && e.type === Runner.events.TOUCHSTART) {
              this.createTouchController();
            }

            if (isMobileMouseInput) {
              this.handleCanvasKeyPress(e);
            }
            this.loadSounds();
            this.isFirstKeyDown = getTimeStamp();
            this.playSound(this.soundFx.LONG_PRESS);
            if (window.errorPageController) {
              window.errorPageController.trackEasterEgg();
            }
          }

          if (this.isLongPressMode) {
            this.playSound(this.soundFx.LONG_PRESS);
          }
          // Start jump.
          if (!this.tRex.jumping && !this.tRex.ducking) {
            if (this.isLongPressMode) {
              if (this.longPressStart === -1) {
                this.longPressStart = getTimeStamp();
              }
              if (!this.isFirstKeyDown) {
                this.tRex.setDuck(true);
                this.tRex.setCharging(true);
              }
              return;
            }

            if (Runner.audioCues) {
              this.generatedSoundFx.cancelFootSteps();
            } else {
              this.playSound(this.soundFx.BUTTON_PRESS);
            }
            this.tRex.startJump(this.currentSpeed);
          }
        } else if (this.playing && Runner.keycodes.DUCK[e.keyCode]) {
          e.preventDefault();
          if (this.tRex.jumping) {
            // Speed drop, activated only when jump key is not pressed.
            this.tRex.setSpeedDrop();
          } else if (!this.tRex.jumping && !this.tRex.ducking) {
            // Duck.
            this.tRex.setDuck(true);
          }
        }
      }
    }
  },

  /**
   * Process key up.
   * @param {Event} e
   */
  onKeyUp(e) {
    const keyCode = String(e.keyCode);
    const isJumpKey =
      Runner.keycodes.JUMP[keyCode] ||
      e.type === Runner.events.TOUCHEND ||
      e.type === Runner.events.POINTERUP;

    // check for long press mode
    if (this.isFirstKeyDown && isJumpKey) {
      if (this.isLongPressMode) {
        this.horizon.isLongPressMode = true;
      }
      this.isFirstKeyDown = 0;
      this.tRex.isLongPress = this.isLongPressMode;
      this.setPlayStatus(true);
      this.update();
    }
    if (isJumpKey) {
      this.stopSound(this.soundFx.LONG_PRESS);
    }
    if (this.isRunning() && isJumpKey) {
      if (this.isLongPressMode) {
        this.tRex.setDuck(false);
        this.tRex.setCharging(false);
        if (this.longPressStart > -1) {
          this.longPressDuration = getTimeStamp() - this.longPressStart;
        }
        this.longPressStart = -1;
        this.jumpStatistics.jump++;
        this.tRex.startJump(this.longPressDuration);
      } else {
        this.tRex.endJump();
        const tRexX = this.tRex.xPos + this.tRex.config.WIDTH / 2;
        const obstacleX = this.horizon.obstacles[0]?.xPos + this.horizon.obstacles[0]?.width / 2;
        if (obstacleX - tRexX > 100) {
          // when end jump, t-rex should be at the max height, the first obstacle x-pos should not be too far
          // if far more than 100px, then considet this jump as a miss, no obstacles is passed
          this.statistics[this.statistics.lastOps + 'Miss']++;
        }
      }
    } else if (Runner.keycodes.DUCK[keyCode]) {
      this.tRex.speedDrop = false;
      this.tRex.setDuck(false);
    } else if (this.crashed) {
      // Check that enough time has elapsed before allowing jump key to restart.
      const deltaTime = getTimeStamp() - this.time;

      if (
        this.isCanvasInView() &&
        (Runner.keycodes.RESTART[keyCode] ||
          this.isLeftClickOnCanvas(e) ||
          (deltaTime >= this.config.GAMEOVER_CLEAR_TIME &&
            Runner.keycodes.JUMP[keyCode]))
      ) {
        this.handleGameOverClicks(e);
      }
    } else if (this.paused && isJumpKey) {
      // Reset the jump state
      this.tRex.reset();
      this.play();
    }
  },

  /**
   * Process gamepad connected event.
   */
  onGamepadConnected() {
    if (!this.pollingGamepads) {
      this.pollGamepadState();
    }
  },

  /**
   * rAF loop for gamepad polling.
   */
  pollGamepadState() {
    const gamepads = navigator.getGamepads();
    this.pollActiveGamepad(gamepads);

    this.pollingGamepads = true;
    requestAnimationFrame(this.pollGamepadState.bind(this));
  },

  /**
   * Polls for a gamepad with the jump button pressed. If one is found this
   * becomes the "active" gamepad and all others are ignored.
   * @param {!Array<Gamepad>} gamepads
   */
  pollForActiveGamepad(gamepads) {
    for (let i = 0; i < gamepads.length; ++i) {
      if (
        gamepads[i] &&
        gamepads[i].buttons.length > 0 &&
        gamepads[i].buttons[0].pressed
      ) {
        this.gamepadIndex = i;
        this.pollActiveGamepad(gamepads);
        return;
      }
    }
  },

  /**
   * Polls the chosen gamepad for button presses and generates KeyboardEvents
   * to integrate with the rest of the game logic.
   * @param {!Array<Gamepad>} gamepads
   */
  pollActiveGamepad(gamepads) {
    if (this.gamepadIndex === undefined) {
      this.pollForActiveGamepad(gamepads);
      return;
    }

    const gamepad = gamepads[this.gamepadIndex];
    if (!gamepad) {
      this.gamepadIndex = undefined;
      this.pollForActiveGamepad(gamepads);
      return;
    }

    // The gamepad specification defines the typical mapping of physical buttons
    // to button indicies: https://w3c.github.io/gamepad/#remapping
    this.pollGamepadButton(gamepad, 0, 38); // Jump
    if (gamepad.buttons.length >= 2) {
      this.pollGamepadButton(gamepad, 1, 40); // Duck
    }
    if (gamepad.buttons.length >= 10) {
      this.pollGamepadButton(gamepad, 9, 13); // Restart
    }

    this.previousGamepad = gamepad;
  },

  /**
   * Generates a key event based on a gamepad button.
   * @param {!Gamepad} gamepad
   * @param {number} buttonIndex
   * @param {number} keyCode
   */
  pollGamepadButton(gamepad, buttonIndex, keyCode) {
    const state = gamepad.buttons[buttonIndex].pressed;
    let previousState = false;
    if (this.previousGamepad) {
      previousState = this.previousGamepad.buttons[buttonIndex].pressed;
    }
    // Generate key events on the rising and falling edge of a button press.
    if (state !== previousState) {
      const e = new KeyboardEvent(
        state ? Runner.events.KEYDOWN : Runner.events.KEYUP,
        { keyCode: keyCode }
      );
      document.dispatchEvent(e);
    }
  },

  /**
   * Handle interactions on the game over screen state.
   * A user is able to tap the high score twice to reset it.
   * @param {Event} e
   */
  handleGameOverClicks(e) {
    if (e.target != this.slowSpeedCheckbox) {
      e.preventDefault();
      if (this.distanceMeter.hasClickedOnHighScore(e) && this.highestScore) {
        if (this.distanceMeter.isHighScoreFlashing()) {
          // Subsequent click, reset the high score.
          this.saveHighScore(0, true);
          this.distanceMeter.resetHighScore();
        } else {
          // First click, flash the high score.
          this.distanceMeter.startHighScoreFlashing();
        }
      } else {
        this.distanceMeter.cancelHighScoreFlashing();
        this.restart();
      }
    }
  },

  /**
   * Returns whether the event was a left click on canvas.
   * On Windows right click is registered as a click.
   * @param {Event} e
   * @return {boolean}
   */
  isLeftClickOnCanvas(e) {
    return (
      e.button != null &&
      e.button < 2 &&
      e.type === Runner.events.POINTERUP &&
      (e.target === this.canvas ||
        (IS_MOBILE && Runner.audioCues && e.target === this.containerEl))
    );
  },

  /**
   * RequestAnimationFrame wrapper.
   */
  scheduleNextUpdate() {
    if (!this.updatePending) {
      this.updatePending = true;
      this.raqId = requestAnimationFrame(this.update.bind(this));
    }
  },

  /**
   * Whether the game is running.
   * @return {boolean}
   */
  isRunning() {
    return !!this.raqId;
  },

  /**
   * Toggle voice control
   */
  toggleVoiceControl() {
    this.isUsingVoice = !this.isUsingVoice;
    if (this.isUsingVoice) {
      // filter birds
      Obstacle.types = spriteDefinitionByType.original.OBSTACLES.filter(
        (v) => v.type !== 'PTERODACTYL'
      );
    } else {
      Obstacle.types = spriteDefinitionByType.original.OBSTACLES;
    }
  },

  /**
   * Set the initial high score as stored in the user's profile.
   * @param {number} highScore
   */
  initializeHighScore(highScore) {
    this.syncHighestScore = true;
    highScore = Math.ceil(highScore);
    if (highScore < this.highestScore) {
      if (window.errorPageController) {
        window.errorPageController.updateEasterEggHighScore(this.highestScore);
      }
      return;
    }
    this.highestScore = highScore;
    this.distanceMeter.setHighScore(this.highestScore);
  },

  /**
   * Sets the current high score and saves to the profile if available.
   * @param {number} distanceRan Total distance ran.
   * @param {boolean=} opt_resetScore Whether to reset the score.
   */
  saveHighScore(distanceRan, opt_resetScore) {
    this.highestScore = Math.ceil(distanceRan);
    this.distanceMeter.setHighScore(this.highestScore);

    // Store the new high score in the profile.
    if (this.syncHighestScore && window.errorPageController) {
      if (opt_resetScore) {
        window.errorPageController.resetEasterEggHighScore();
      } else {
        window.errorPageController.updateEasterEggHighScore(this.highestScore);
      }
    }
  },

  /**
   * Game over state.
   */
  gameOver() {
    this.playSound(this.soundFx.HIT);
    vibrate(200);

    this.stop();
    this.crashed = true;
    this.distanceMeter.achievement = false;
    if (this.isLongPressMode) {
      this.tRex.setCharging(false);
    }
    this.tRex.update(100, Trex.status.CRASHED);

    // Game over panel.
    if (!this.gameOverPanel) {
      const origSpriteDef = IS_HIDPI
        ? spriteDefinitionByType.original.HDPI
        : spriteDefinitionByType.original.LDPI;

      if (this.canvas) {
        if (Runner.isAltGameModeEnabled) {
          this.gameOverPanel = new GameOverPanel(
            this.canvas,
            origSpriteDef.TEXT_SPRITE,
            origSpriteDef.RESTART,
            this.dimensions,
            origSpriteDef.ALT_GAME_END,
            this.altGameModeActive
          );
        } else {
          this.gameOverPanel = new GameOverPanel(
            this.canvas,
            origSpriteDef.TEXT_SPRITE,
            origSpriteDef.RESTART,
            this.dimensions
          );
        }
      }
    }

    this.gameOverPanel.draw(this.altGameModeActive, this.tRex);

    // Update the high score.
    if (this.distanceRan > this.highestScore) {
      this.saveHighScore(this.distanceRan);
    }
    if (this.isLongPressMode) {
      const score = this.distanceMeter.getActualDistance(this.distanceRan);
      this.jumpStatistics.score = Math.max(score, this.jumpStatistics.score);
    }

    this.showStat();

    // Reset the time clock.
    this.time = getTimeStamp();

    if (Runner.audioCues) {
      this.generatedSoundFx.stopAll();
      announcePhrase(
        getA11yString(A11Y_STRINGS.gameOver).replace(
          '$1',
          this.distanceMeter.getActualDistance(this.distanceRan).toString()
        ) +
        ' ' +
        getA11yString(A11Y_STRINGS.highScore).replace(
          '$1',

          this.distanceMeter.getActualDistance(this.highestScore).toString()
        )
      );
      this.containerEl.setAttribute(
        'title',
        getA11yString(A11Y_STRINGS.ariaLabel)
      );
    }
    // this.showSpeedToggle();
    this.disableSpeedToggle(false);
  },

  showStat() {
    if (!this.statEl) {
      this.statEl = document.createElement('div');
      this.statEl.className = Runner.classes.STATS;
      this.outerContainerEl.appendChild(this.statEl);
    } else {
      this.statEl.classList.remove(HIDDEN_CLASS);
    }

    if (this.isLongPressMode) {
      const localData = localStorage.getItem(LOCAL_STORAGE_KEYS.JUMP_STATS);
      const stats = localData ? JSON.parse(localData) : {
        jump: 0,
        miss: 0,
        score: 0,
      };
      stats.jump += this.jumpStatistics.jump;
      stats.miss += this.jumpStatistics.miss;
      stats.score = Math.max(stats.score, this.jumpStatistics.score);

      localStorage.setItem(LOCAL_STORAGE_KEYS.JUMP_STATS, JSON.stringify(stats));
      this.statEl.innerHTML = `
        <table role="table">
          <thead>
            <tr>
              <th>&nbsp;</th>
              <th>${getA11yString(A11Y_STRINGS.jumpCount)}</th>
              <th>${getA11yString(A11Y_STRINGS.statsMiss)}</th>
              <th>${getA11yString(A11Y_STRINGS.jumpScore)}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>${getA11yString(A11Y_STRINGS.jumpRow)}</td>
              <td>${this.jumpStatistics.jump}</td>
              <td>${this.jumpStatistics.miss}</td>
              <td>${this.jumpStatistics.score}</td>
            </tr>
          </tbody>
        </table>
      `;

      return;
    }

    const localData = localStorage.getItem(LOCAL_STORAGE_KEYS.RUNNER_STATS);
    const stats = localData ? JSON.parse(localData) : {
      space: 0,
      up: 0,
      voice: 0,
      spaceMiss: 0,
      upMiss: 0,
      voiceMiss: 0,
    };

    stats.space += this.statistics.space;
    stats.up += this.statistics.up;
    stats.voice += this.statistics.voice;
    stats.spaceMiss += this.statistics.spaceMiss;
    stats.upMiss += this.statistics.upMiss;
    stats.voiceMiss += this.statistics.voiceMiss;

    localStorage.setItem(LOCAL_STORAGE_KEYS.RUNNER_STATS, JSON.stringify(stats));

    const total = this.statistics.space + this.statistics.up + this.statistics.voice;
    const totalMiss = this.statistics.spaceMiss + this.statistics.upMiss + this.statistics.voiceMiss;
    const totalSuccess = total - totalMiss;

    this.statEl.innerHTML = `
      <table role="table">
        <thead>
          <tr>
            <th>&nbsp;</th>
            <th>${getA11yString(A11Y_STRINGS.statsSuccess)}</th>
            <th>${getA11yString(A11Y_STRINGS.statsMiss)}</th>
            <th>${getA11yString(A11Y_STRINGS.statsAccuracy)}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>${getA11yString(A11Y_STRINGS.statsSpace)}</td>
            <td>${this.statistics.space - this.statistics.spaceMiss}</td>
            <td>${this.statistics.spaceMiss}</td>
            <td>${getPercentage(
      this.statistics.space - this.statistics.spaceMiss,
      this.statistics.space
    )}</td>
          </tr>
          <tr>
            <td>${getA11yString(A11Y_STRINGS.statsUp)}</td>
            <td>${this.statistics.up - this.statistics.upMiss}</td>
            <td>${this.statistics.upMiss}</td>
            <td>${getPercentage(
      this.statistics.up - this.statistics.upMiss,
      this.statistics.up
    )}</td>
          </tr>
          <tr>
            <td>${getA11yString(A11Y_STRINGS.statsVoice)}</td>
            <td>${this.statistics.voice - this.statistics.voiceMiss}</td>
            <td>${this.statistics.voiceMiss}</td>
            <td>${getPercentage(
      this.statistics.voice - this.statistics.voiceMiss,
      this.statistics.voice
    )}</td>
          </tr>
          <tr>
            <td>${getA11yString(A11Y_STRINGS.statsTotal)}</td>
            <td>${totalSuccess}</td>
            <td>${totalMiss}</td>
            <td>${getPercentage(totalSuccess, total)}</td>
          </tr>
        </tbody>
      </table>
    `;
  },

  stop() {
    this.setPlayStatus(false);
    this.paused = true;
    cancelAnimationFrame(this.raqId);
    this.raqId = 0;
    this.generatedSoundFx.stopAll();
  },

  play() {
    if (!this.crashed) {
      this.setPlayStatus(true);
      this.paused = false;
      this.tRex.update(0, Trex.status.RUNNING);
      this.time = getTimeStamp();
      this.update();
      this.generatedSoundFx.background();
    }
  },

  restart() {
    if (!this.raqId) {
      this.playCount++;
      this.runningTime = 0;
      this.setPlayStatus(true);
      // this.toggleSpeed();
      this.paused = false;
      this.crashed = false;
      this.distanceRan = 0;
      this.setSpeed(this.config.SPEED);
      this.time = getTimeStamp();
      this.containerEl.classList.remove(Runner.classes.CRASHED);
      this.clearCanvas();
      this.distanceMeter.reset();
      this.horizon.reset();
      this.tRex.reset();
      this.playSound(this.soundFx.BUTTON_PRESS);
      this.invert(true);
      this.flashTimer = null;
      this.update();
      this.gameOverPanel.reset();
      this.generatedSoundFx.background();
      this.containerEl.setAttribute('title', getA11yString(A11Y_STRINGS.jump));
      announcePhrase(getA11yString(A11Y_STRINGS.started));
      this.statistics = {
        voice: 0,
        voiceMiss: 0,
        up: 0,
        upMiss: 0,
        space: 0,
        spaceMiss: 0,
        effective: 0,
        lastOps: null,
      };
      this.jumpStatistics = {
        jump: 0,
        miss: 0,
        score: 0,
      };
      this.statEl.classList.add(HIDDEN_CLASS);
      this.horizon.currentLevel = 0;
    }
  },

  setPlayStatus(isPlaying) {
    if (this.touchController) {
      this.touchController.classList.toggle(HIDDEN_CLASS, !isPlaying);
    }
    this.playing = isPlaying;
    if (isPlaying) {
      this.longPressStart = -1;
      this.longPressDuration = 0;
    }
    const statusEvent = new CustomEvent('playstatus', {
      bubbles: true,
      detail: isPlaying,
    });
    this.outerContainerEl.dispatchEvent(statusEvent);
  },

  /**
   * Whether the game should go into arcade mode.
   * @return {boolean}
   */
  isArcadeMode() {
    // scale the game to fit the outer container
    return true;
  },

  /**
   * Sets the scaling for arcade mode.
   */
  setArcadeModeContainerScale() {
    const outerHeight = this.outerContainerEl.clientHeight;
    const scaleHeight = outerHeight / this.dimensions.HEIGHT;
    const scaleWidth =
      this.outerContainerEl.clientWidth / this.dimensions.WIDTH;
    const scale = Math.max(1, Math.min(scaleHeight, scaleWidth));
    const scaledCanvasHeight = this.dimensions.HEIGHT * scale;
    // Positions the game container at 10% of the available vertical window
    // height minus the game container height.
    const translateY =
      Math.ceil(
        Math.max(
          0,
          (outerHeight -
            scaledCanvasHeight -
            Runner.config.ARCADE_MODE_INITIAL_TOP_POSITION) *
          Runner.config.ARCADE_MODE_TOP_POSITION_PERCENT
        )
      ) * window.devicePixelRatio;

    const cssScale = IS_RTL ? -scale + ',' + scale : scale;
    this.containerEl.style.transform =
      'scale(' + cssScale + ') translate(' + (IS_RTL ? '100%,' : '0,') + translateY + 'px)';
  },

  onTrexGrounded(distance: number) {
    this.distanceRan += distance;
  },

  /**
   * Pause the game if the tab is not in focus.
   */
  onVisibilityChange(e) {
    if (
      document.hidden ||
      document.webkitHidden ||
      e.type === 'blur' ||
      document.visibilityState !== 'visible'
    ) {
      this.stop();
    } else if (!this.crashed) {
      this.tRex.reset();
      this.play();
    }
  },

  /**
   * Play a sound.
   * @param {HTMLAudioElement} audioElem
   */
  playSound(audioElem: HTMLMediaElement) {
    audioElem?.play();
  },

  stopSound(audioElem: HTMLMediaElement) {
    if (audioElem) {
      audioElem.pause();
      audioElem.currentTime = 0;
    }
  },

  /**
   * Inverts the current page / canvas colors.
   * @param {boolean} reset Whether to reset colors.
   */
  invert(reset) {
    const htmlEl = this.outerContainerEl;

    if (reset) {
      htmlEl.classList.toggle(Runner.classes.INVERTED, false);
      this.invertTimer = 0;
      this.inverted = false;
    } else {
      this.inverted = htmlEl.classList.toggle(
        Runner.classes.INVERTED,
        this.invertTrigger
      );
    }
  },
};

/**
 * Updates the canvas size taking into
 * account the backing store pixel ratio and
 * the device pixel ratio.
 *
 * See article by Paul Lewis:
 * http://www.html5rocks.com/en/tutorials/canvas/hidpi/
 *
 * @param {HTMLCanvasElement} canvas
 * @param {number=} opt_width
 * @param {number=} opt_height
 * @return {boolean} Whether the canvas was scaled.
 */
Runner.updateCanvasScaling = function (canvas, opt_width, opt_height) {
  const context = /** @type {CanvasRenderingContext2D} */ (
    canvas.getContext('2d')
  );

  // Query the various pixel ratios
  const devicePixelRatio = Math.floor(window.devicePixelRatio) || 1;
  /** @suppress {missingProperties} */
  const backingStoreRatio =
    Math.floor(context.webkitBackingStorePixelRatio) || 1;
  const ratio = devicePixelRatio / backingStoreRatio;

  // Upscale the canvas if the two ratios don't match
  if (devicePixelRatio !== backingStoreRatio) {
    const oldWidth = opt_width || canvas.width;
    const oldHeight = opt_height || canvas.height;

    canvas.width = oldWidth * ratio;
    canvas.height = oldHeight * ratio;

    canvas.style.width = oldWidth + 'px';
    canvas.style.height = oldHeight + 'px';

    // Scale the context to counter the fact that we've manually scaled
    // our canvas element.
    context.scale(ratio, ratio);
    return true;
  } else if (devicePixelRatio === 1) {
    // Reset the canvas width / height. Fixes scaling bug when the page is
    // zoomed and the devicePixelRatio changes accordingly.
    canvas.style.width = canvas.width + 'px';
    canvas.style.height = canvas.height + 'px';
  }
  return false;
};

/**
 * Whether events are enabled.
 * @return {boolean}
 */
Runner.isAltGameModeEnabled = function () {
  return loadTimeData && loadTimeData.valueExists('enableAltGameMode');
};

let instance: typeof Runner;
export default function createRunner(outerContainerId: string, opt_config?: any) {
  if (!instance) {
    instance = new Runner(outerContainerId, opt_config);
  }
  return instance;
}
