import { FPS } from "@/data";
import { ISafeSpace } from "@/types";
import {getRandomNum} from "@/utils";

export default class SafeSpace implements ISafeSpace {
  static MAX_WIDTH = 256;
  static MIN_WIDTH = 48;

  remove: boolean = false;
  xPos: number;
  width: number;

  constructor(xPos: number) {
    this.xPos = xPos;
    this.width = getRandomNum(SafeSpace.MIN_WIDTH, SafeSpace.MAX_WIDTH);
  }

  update(deltaTime: number, speed: number) {
    this.xPos -= Math.floor(((speed * FPS) / 1000) * deltaTime);
    this.remove = this.xPos + this.width < 0;
  }
}
