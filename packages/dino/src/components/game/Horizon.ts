import { Obstacle } from "./Obstacle";
import { Runner } from "./Runner";
import { NightMode } from "./NightMode";
import { HorizonLine } from "./HorizonLine";
import { BackgroundEl } from "./BackgroundEl";
import { getRandomNum } from "@/utils";
import { Cloud } from "./Cloud";
import { Dimension, Position } from "@/types";
import SafeSpace from "@/components/game/SafeSpace";


/**
 * Horizon background class.
 */
export class Horizon {
  /**
   * Horizon config.
   * @enum {number}
   */
  static config = {
    BG_CLOUD_SPEED: 0.2,
    BUMPY_THRESHOLD: 0.3,
    CLOUD_FREQUENCY: 0.5,
    HORIZON_HEIGHT: 16,
    MAX_CLOUDS: 6,
  };

  canvas: HTMLCanvasElement;
  canvasCtx: CanvasRenderingContext2D;
  config = Horizon.config;
  dimensions: Dimension;
  horizonOffsets = [0, 0];
  isLongPressMode = false;
  gapCoefficient;
  obstacles: Obstacle[] = [];
  obstacleHistory = [];
  obstacleCount = 0;
  cloudFrequency = Horizon.config.CLOUD_FREQUENCY;
  spritePos: Position;
  nightMode = null;
  altGameModeActive = false;
  runningTime = 0;

  // Cloud
  clouds = [];
  cloudSpeed = Horizon.config.BG_CLOUD_SPEED;

  // Background elements
  backgroundEls = [];
  lastEl = null;
  backgroundSpeed = this.config.BG_CLOUD_SPEED;

  // Horizon
  horizonLine = null;
  horizonLines = [];

  // for longPress mode
  safeSpaces: SafeSpace[] = [];
  currentLevel = 0;
  levelBoost: { scoreThreshold: number; obstacleBound: [number, number] }[] = [
    {
      scoreThreshold: 100,
      obstacleBound: [1, 2],
    },
    {
      scoreThreshold: 400,
      obstacleBound: [2, 4],
    },
    {
      scoreThreshold: 900,
      obstacleBound: [3, 6],
    },
    {
      scoreThreshold: 999999,
      obstacleBound: [4, 8],
    }
  ];

  /**
   * Initialise the horizon. Just add the line and a cloud. No obstacles.
   */
  constructor(
    canvas: HTMLCanvasElement,
    spritePos: Position,
    dimensions: Dimension,
    gapCoefficient: number
  ) {
    this.canvas = canvas;
    this.canvasCtx = canvas.getContext('2d');
    this.dimensions = dimensions;
    this.gapCoefficient = gapCoefficient;
    this.spritePos = spritePos;
    this.addCloud();
    // Multiple Horizon lines
    for (let i = 0; i < Runner.spriteDefinition.LINES.length; i++) {
      this.horizonLines.push(
        new HorizonLine(this.canvas, Runner.spriteDefinition.LINES[i])
      );
    }

    this.nightMode = new NightMode(
      this.canvas,
      this.spritePos.MOON,
      this.dimensions.WIDTH
    );
  }

  /**
   * Update obstacle definitions based on the speed of the game.
   */
  adjustObstacleSpeed() {
    for (let i = 0; i < Obstacle.types.length; i++) {
      if (Runner.slowDown) {
        Obstacle.types[i].multipleSpeed = Obstacle.types[i].multipleSpeed / 2;
        Obstacle.types[i].minGap *= 1.5;
        Obstacle.types[i].minSpeed = Obstacle.types[i].minSpeed / 2;

        // Convert variable y position obstacles to fixed.
        if (typeof Obstacle.types[i].yPos == 'object') {
          Obstacle.types[i].yPos = Obstacle.types[i].yPos[0];
          Obstacle.types[i].yPosMobile = Obstacle.types[i].yPos[0];
        }
      }
    }
  }

  /**
   * Update sprites to correspond to change in sprite sheet.
   * @param {number} spritePos
   */
  enableAltGameMode(spritePos) {
    // Clear existing horizon objects.
    this.clouds = [];
    this.backgroundEls = [];

    this.altGameModeActive = true;
    this.spritePos = spritePos;

    Obstacle.types = Runner.spriteDefinition.OBSTACLES;
    this.adjustObstacleSpeed();

    Obstacle.MAX_GAP_COEFFICIENT = Runner.spriteDefinition.MAX_GAP_COEFFICIENT;
    Obstacle.MAX_OBSTACLE_LENGTH = Runner.spriteDefinition.MAX_OBSTACLE_LENGTH;

    BackgroundEl.config = Runner.spriteDefinition.BACKGROUND_EL_CONFIG;

    this.horizonLines = [];
    for (let i = 0; i < Runner.spriteDefinition.LINES.length; i++) {
      this.horizonLines.push(
        new HorizonLine(this.canvas, Runner.spriteDefinition.LINES[i])
      );
    }
    this.reset();
  }

  update(deltaTime: number, currentSpeed: number, updateObstacles: boolean, showNightMode: boolean) {
    this.runningTime += deltaTime;

    if (this.altGameModeActive) {
      this.updateBackgroundEls(deltaTime);
    }

    //console.log(`LOG: INTERNAL horizon update function: deltaTime: ${deltaTime}, currentSpeed: ${currentSpeed}`);
    for (let i = 0; i < this.horizonLines.length; i++) {
      this.horizonLines[i].update(deltaTime, currentSpeed);
    }

    if (!this.altGameModeActive || Runner.spriteDefinition.HAS_CLOUDS) {
      this.nightMode.update(showNightMode);
      this.updateClouds(deltaTime, currentSpeed);
    }

    if (this.isLongPressMode) {
      this.updateObstaclesInLongPressMode(deltaTime, currentSpeed);
    } else if (updateObstacles) {
      this.updateObstacles(deltaTime, currentSpeed);
    }
  }

  /**
   * Update background element positions. Also handles creating new elements.
   * @param {number} elSpeed
   * @param {Array<Object>} bgElArray
   * @param {number} maxBgEl
   * @param {Function} bgElAddFunction
   * @param {number} frequency
   */
  updateBackgroundEl(elSpeed, bgElArray, maxBgEl, bgElAddFunction, frequency) {
    const numElements = bgElArray.length;

    if (numElements) {
      for (let i = numElements - 1; i >= 0; i--) {
        bgElArray[i].update(elSpeed);
      }

      const lastEl = bgElArray[numElements - 1];

      // Check for adding a new element.
      if (
        numElements < maxBgEl &&
        this.dimensions.WIDTH - lastEl.xPos > lastEl.gap &&
        frequency > Math.random()
      ) {
        bgElAddFunction();
      }
    } else {
      bgElAddFunction();
    }
  }

  /**
   * Update the cloud positions.
   * @param {number} deltaTime
   * @param {number} speed
   */
  updateClouds(deltaTime, speed) {
    const elSpeed = (this.cloudSpeed / 1000) * deltaTime * speed;
    this.updateBackgroundEl(
      elSpeed,
      this.clouds,
      this.config.MAX_CLOUDS,
      this.addCloud.bind(this),
      this.cloudFrequency
    );

    // Remove expired elements.
    this.clouds = this.clouds.filter((obj) => !obj.remove);
  }

  /**
   * Update the background element positions.
   * @param {number} deltaTime
   */
  updateBackgroundEls(deltaTime: number) {
    this.updateBackgroundEl(
      deltaTime,
      this.backgroundEls,
      BackgroundEl.config.MAX_BG_ELS,
      this.addBackgroundEl.bind(this),
      this.cloudFrequency
    );

    // Remove expired elements.
    this.backgroundEls = this.backgroundEls.filter((obj) => !obj.remove);
  }

  /**
   * Update the obstacle positions.
   */
  updateObstacles(deltaTime: number, currentSpeed: number) {
    const updatedObstacles = this.obstacles.slice(0);

    for (let i = 0; i < this.obstacles.length; i++) {
      const obstacle = this.obstacles[i];
      obstacle.update(deltaTime, currentSpeed);

      // Clean up existing obstacles.
      if (obstacle.remove) {
        updatedObstacles.shift();
      }
    }
    this.obstacles = updatedObstacles;

    if (this.obstacles.length > 0) {
      const lastObstacle = this.obstacles[this.obstacles.length - 1];

      if (
        lastObstacle &&
        !lastObstacle.followingObstacleCreated &&
        lastObstacle.isVisible() &&
        lastObstacle.xPos + lastObstacle.width + lastObstacle.gap <
        this.dimensions.WIDTH
      ) {
        this.addNewObstacle(currentSpeed);
        lastObstacle.followingObstacleCreated = true;
      }
    } else {
      // Create new obstacles.
      this.addNewObstacle(currentSpeed);
    }
  }

  updateObstaclesInLongPressMode(deltaTime: number, currentSpeed: number) {
    const updatedObstacles = this.obstacles.slice(0);
    for (const obstacle of this.obstacles) {
      obstacle.update(deltaTime, currentSpeed);
      if (obstacle.remove) {
        updatedObstacles.shift();
      }
    }
    this.obstacles = updatedObstacles;
    const updatedSafeSpaces = this.safeSpaces.slice(0);
    for (const safeSpace of this.safeSpaces) {
      safeSpace.update(deltaTime, currentSpeed);
      if (safeSpace.remove) {
        updatedSafeSpaces.shift();
      }
    }
    this.safeSpaces = updatedSafeSpaces;

    // create safe spaces until out of screen
    let lastSafeSpace = this.safeSpaces[this.safeSpaces.length - 1];
    while (!lastSafeSpace || lastSafeSpace.xPos < this.dimensions.WIDTH) {
      const lastObstacle = this.obstacles[this.obstacles.length - 1];
      // 16 = give some space for start
      lastSafeSpace = new SafeSpace(lastObstacle ? lastObstacle.xPos + lastObstacle.width : 16);
      this.safeSpaces.push(lastSafeSpace);
      // create obstacles before next safe space
      const [min, max] = this.levelBoost[this.currentLevel].obstacleBound;
      let startPos = lastSafeSpace.xPos + lastSafeSpace.width;
      for (let i = 0, len = getRandomNum(min, max); i < len; i++) {
        const obstacleType = Obstacle.types[0];
        const obstacleSpritePos = this.spritePos[obstacleType.type];
        const newObstacle = new Obstacle(
          this.canvasCtx,
          obstacleType,
          obstacleSpritePos,
          this.dimensions,
          this.gapCoefficient,
          currentSpeed,
          startPos - this.dimensions.WIDTH, // just to reuse previous code
          this.altGameModeActive,
          true,
        );
        startPos += newObstacle.width;
        this.obstacles.push(newObstacle);
      }
    }
  }

  getCurrentLevelThreshold() {
    return this.levelBoost[this.currentLevel].scoreThreshold;
  }

  levelUp() {
    this.currentLevel++;
  }

  removeFirstObstacle() {
    this.obstacles.shift();
  }

  /**
   * Add a new obstacle.
   */
  addNewObstacle(currentSpeed: number) {
    const obstacleCount =
      Obstacle.types[Obstacle.types.length - 1].type != 'COLLECTABLE' ||
      (Runner.isAltGameModeEnabled() && !this.altGameModeActive) ||
      this.altGameModeActive
        ? Obstacle.types.length - 1
        : Obstacle.types.length - 2;
    const obstacleTypeIndex =
      obstacleCount > 0 ? getRandomNum(0, obstacleCount) : 0;
    const obstacleType = Obstacle.types[obstacleTypeIndex];

    // Check for multiples of the same type of obstacle.
    // Also check obstacle is available at current speed.
    if (
      (obstacleCount > 0 && this.duplicateObstacleCheck(obstacleType.type)) ||
      currentSpeed < obstacleType.minSpeed
    ) {
      this.addNewObstacle(currentSpeed);
    } else {
      const obstacleSpritePos = this.spritePos[obstacleType.type];
      const newObstacle = new Obstacle(
        this.canvasCtx,
        obstacleType,
        obstacleSpritePos,
        this.dimensions,
        this.gapCoefficient,
        currentSpeed,
        obstacleType.width,
        this.altGameModeActive,
      );
      this.obstacles.push(newObstacle);
      if (
        newObstacle.typeConfig.type !== 'PTERODACTYL'
        || (newObstacle.typeConfig.type === 'PTERODACTYL' && newObstacle.yPos > 50)
      ) {
        // only consider lower birds which need jump to pass
        this.obstacleCount++;
      }

      this.obstacleHistory.unshift(obstacleType.type);

      if (this.obstacleHistory.length > 1) {
        this.obstacleHistory.splice(Runner.config.MAX_OBSTACLE_DUPLICATION);
      }
    }
  }

  /**
   * Returns whether the previous two obstacles are the same as the next one.
   * Maximum duplication is set in config value MAX_OBSTACLE_DUPLICATION.
   * @return {boolean}
   */
  duplicateObstacleCheck(nextObstacleType) {
    let duplicateCount = 0;

    for (let i = 0; i < this.obstacleHistory.length; i++) {
      duplicateCount =
        this.obstacleHistory[i] === nextObstacleType ? duplicateCount + 1 : 0;
    }
    return duplicateCount >= Runner.config.MAX_OBSTACLE_DUPLICATION;
  }

  /**
   * Reset the horizon layer.
   * Remove existing obstacles and reposition the horizon line.
   */
  reset() {
    this.obstacles = [];
    this.safeSpaces = [];
    this.obstacleCount = 0;
    for (let l = 0; l < this.horizonLines.length; l++) {
      this.horizonLines[l].reset();
    }

    this.nightMode.reset();
  }

  /**
   * Update the canvas width and scaling.
   * @param {number} width Canvas width.
   * @param {number} height Canvas height.
   */
  resize(width, height) {
    this.canvas.width = width;
    this.canvas.height = height;
  }

  /**
   * Add a new cloud to the horizon.
   */
  addCloud() {
    this.clouds.push(
      new Cloud(this.canvas, this.spritePos.CLOUD, this.dimensions.WIDTH)
    );
  }

  /**
   * Add a random background element to the horizon.
   */
  addBackgroundEl() {
    const backgroundElTypes = Object.keys(
      Runner.spriteDefinition.BACKGROUND_EL
    );

    if (backgroundElTypes.length > 0) {
      let index = getRandomNum(0, backgroundElTypes.length - 1);
      let type = backgroundElTypes[index];

      // Add variation if available.
      while (type == this.lastEl && backgroundElTypes.length > 1) {
        index = getRandomNum(0, backgroundElTypes.length - 1);
        type = backgroundElTypes[index];
      }

      this.lastEl = type;
      this.backgroundEls.push(
        new BackgroundEl(
          this.canvas,
          this.spritePos.BACKGROUND_EL,
          this.dimensions.WIDTH,
          type
        )
      );
    }
  }
}
