#dino-game-container {
  --background-color: #fff;
  --google-blue-100: rgb(210, 227, 252);
  --google-blue-300: rgb(138, 180, 248);
  --google-blue-600: rgb(26, 115, 232);
  --google-blue-700: rgb(25, 103, 210);
  --google-gray-100: rgb(241, 243, 244);
  --google-gray-300: rgb(218, 220, 224);
  --google-gray-500: rgb(154, 160, 166);
  --google-gray-50: rgb(248, 249, 250);
  --google-gray-600: rgb(128, 134, 139);
  --google-gray-700: rgb(95, 99, 104);
  --google-gray-800: rgb(60, 64, 67);
  --google-gray-900: rgb(32, 33, 36);
  --error-code-color: var(--google-gray-700);
  --heading-color: var(--google-gray-900);
  --text-color: var(--google-gray-700);
  --border-color: var(--google-gray-800);

  box-sizing: border-box;
  background: var(--background-color);
  color: var(--text-color);
  word-wrap: break-word;
  font-size: 1em;
  line-height: 1.55;
  margin: 0 auto;
  padding-top: 75px;
  position: relative;
  width: 100%;
  transition: filter 1.5s cubic-bezier(0.65, 0.05, 0.36, 1), background-color 1.5s cubic-bezier(0.65, 0.05, 0.36, 1);
  will-change: filter, background-color;

  .hidden {
    display: none;
  }

  .main-content {
    margin-top: 205px;
    padding-bottom: 20px;
  }

  .main-message {
    height: 35px;
    margin-top: 96px;
    display: flex;
    justify-content: space-between;
    padding: 0 3px;

    p {
      color: var(--heading-color);
      font-size: 1.25em;
      font-weight: 500;
      margin: 0;
    }
  }

  .show-stats {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 1.15em;
    font-weight: 500;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 999px;
    background: var(--google-gray-50);
    z-index: 4;
    appearance: none;
    outline: none;
    border: none;
    color: var(--text-color);
  }

  &.inverted {
    background-color: #fff;
    filter: invert(1);
  }

  .runner-container {
    direction: ltr;
    height: 150px;
    max-width: 600px;
    overflow: hidden;
    position: absolute;
    top: 10px;
    width: 44px;
    left: 0;
    right: 0;
    transform-origin: left top;
    z-index: 2;

    &:focus {
      outline: none;
    }
  }

  .runner-canvas {
    height: 150px;
    max-width: 600px;
    opacity: 1;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    right: 0;
  }

  .stats {
    position: absolute;
    right: 13px;
    top: 96px;
    background: var(--background-color);
    color: var(--text-color);
    border: 1px solid currentcolor;
    font-size: 11px;
    line-height: 1.1;
    z-index: 10;
    width: fit-content;
    text-align: center;
  }

  .cumulative-stats-modal {
    width: 100%;
    max-width: none;
    height: 100%;
    max-height: none;
    transition: transform .3s ease-out,
      visibility .3s allow-discrete,
      opacity .1s ease-out;
    overscroll-behavior: contain;
    z-index: 999;
    background-color: rgba(0,0,0,60%);
    place-items: center;
    margin: 0;
    padding: 0;
    display: grid;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    visibility: visible;
    pointer-events: auto;
    opacity: 1;
  }

  .cumulative-stats {
    position: absolute;
    top: 30px;
    left: 47%;
    transform: translate(-50%, 0);
    font-size: 15px;
    line-height: 1.5;
    padding: 8px 16px;
    background: var(--background-color);
    color: var(--text-color);
    border: 1px solid currentcolor;
    z-index: 11;
    width: fit-content;
    text-align: center;
  }

  .action-bar {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
  }

  .reset-btn {
    appearance: none;
    outline: none;
    border: none;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 999px;
    background: none;
    text-decoration: underline;
    color: var(--text-color);
    width: 40%;
    align-self: flex-end;
  }

  .ok-btn {
    appearance: none;
    outline: none;
    border: none;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 999px;
    background: var(--google-gray-50);
    color: var(--text-color);
  }

  .offline-runner-live-region {
    bottom: 0;
    clip-path: polygon(0 0, 0 0, 0 0);
    color: var(--background-color);
    display: block;
    font-size: xx-small;
    overflow: hidden;
    position: absolute;
    text-align: center;
    transition: color 1.5s cubic-bezier(0.65, 0.05, 0.36, 1);
    user-select: none;
  }

  .touch-controller {
    height: 100vh;
    left: 0;
    position: absolute;
    top: 0;
    width: 100vw;
    z-index: 9;
  }

  .change-mode {
    width: auto;
    display: flex;
    justify-content: space-between;
    padding: 6px;
  }

  .mode-block {
    appearance: none;
    outline: none;
    border: none;
    cursor: pointer;
    padding: 16px;
    width: 33%;
    text-align: center;
    background-color: var(--google-gray-500);
    color: #fff;
    border-radius: 4px;

    &.active {
      background-color: #8bc44b;
      color: #000;
      border: solid 2px var(--border-color);
    }

    &:disabled {
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.5;
    }
  }

  /* ====== Confirm Modal ====== */
  .dino-modal {
    /* pointer-events: none; */
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: 0;
    width: 100%;
    justify-items: center;
    align-content: center;
    opacity: 0;
    visibility: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    background-color: #000c;
    color: inherit;
    animation: modal-pop 0.2s ease-out;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    transition-property: transform, opacity, visibility;
    overflow-y: hidden;
    border: none;

    &::backdrop {
      background-color: #000c;
      animation: modal-pop 0.2s ease-out;
    }
  }

  .dino-modal-box {
    width: 45%;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    padding: 1.5rem;
  }

  .dino-modal-text {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }
  .dino-modal-action {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
  }
  .dino-modal-btn {
    cursor: pointer;
    padding: 4px 8px;
    border: none;font-size: 16px;
    text-align: center;
    font-weight: 700;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: manipulation;
    border-radius: 3px;
  }
  .reset-button {
    color: #fff;
    background-color: #2e2e4e;
  }
  @keyframes modal-pop {
    0% {
      opacity: 0;
    }
  }

  /* ====== Slow speed option ====== */
  .slow-speed-option {
    margin: 5px auto;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }

  /* ====== Custom Toggle starts ====== */
  .option-label {
    align-items: center;
    background: var(--google-gray-50);
    border-radius: 999px;
    color: var(--error-code-color);
    display: inline-flex;
    font-size: 1em;
    line-height: 1.1em;
    padding: 2px 12px 3px 20px;
    width: max-content;
    z-index: 999;

    &.hidden {
      display: none;
    }

    [type=checkbox] {
      opacity: 0;
      pointer-events: none;
      position: absolute;

      &:disabled~.option-toggle {
        cursor: not-allowed;
      }

      &:checked+.option-toggle::before {
        background-color: #1976d2;
        opacity: 0.5;
      }

      &:checked+.option-toggle::after {
        background-color: var(--google-blue-600);
        transform: translate(calc(2em - 90%), -50%);
      }

      &:disabled+.option-toggle::before {
        opacity: 0.12;
      }

      &:disabled+.option-toggle::after {
        background-color: #f5f5f5;
      }

      &:checked:disabled+.option-toggle::before {
        opacity: 0.12;
      }

      &:checked:disabled+.option-toggle::after {
        background-color: rgb(167, 202, 237);
      }
    }

    .option-toggle {
      cursor: pointer;
      margin-inline-start: 8px;
      padding: 8px 4px;
      position: relative;

      &::before,
      &::after {
        content: '';
        display: block;
        margin: 0 3px;
        transition: all 100ms cubic-bezier(0.4, 0, 1, 1);
      }

      &::before {
        background-color: #000;
        opacity: 0.38;
        border-radius: 0.65em;
        height: 0.9em;
        width: 2em;
        transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }

      &::after {
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 40%);
        height: 1.2em;
        position: absolute;
        top: 51%;
        transform: translate(-20%, -50%);
        width: 1.1em;
      }
    }
  }

  /* ====== Custom Toggle ends ====== */

  /* ====== Snackbar starts, easter egg ====== */
  .snackbar {
    background: #323232;
    border-radius: 2px;
    bottom: 24px;
    box-sizing: border-box;
    color: #fff;
    font-size: .87em;
    left: 24px;
    max-width: 568px;
    min-width: 288px;
    opacity: 0;
    padding: 16px 24px 12px;
    position: fixed;
    transform: translateY(90px);
    will-change: opacity, transform;
    z-index: 999;
  }

  .snackbar-show {
    animation:
      show-snackbar 250ms cubic-bezier(0, 0, 0.2, 1) forwards,
      hide-snackbar 250ms cubic-bezier(0.4, 0, 1, 1) forwards 5s;
  }

  @keyframes show-snackbar {
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes hide-snackbar {
    0% {
      opacity: 1;
      transform: translateY(0);
    }

    100% {
      opacity: 0;
      transform: translateY(90px);
    }
  }

  /* ====== Snackbar ends ====== */
}

html[dir='rtl'] #dino-game-container {
  .runner-container {
    transform-origin: right top;
  }
  .show-stats {
    left: unset;
    right: 13px;
  }
  .stats {
    right: unset;
    left: 13px;
  }
}

/* images and sounds resources */
#dino-offline-resources {
  display: none;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  #dino-game-container {
    --background-color: var(--google-gray-900);
    --error-code-color: var(--google-gray-500);
    --heading-color: var(--google-gray-500);
    --primary-button-fill-color: var(--google-blue-300);
    --primary-button-text-color: var(--google-gray-900);
    --quiet-background-color: var(--background-color);
    --secondary-button-border-color: var(--google-gray-700);
    --secondary-button-fill-color: var(--google-gray-900);
    --secondary-button-hover-fill-color: rgb(48, 51, 57);
    --small-link-color: var(--google-blue-300);
    --text-color: var(--google-gray-500);
    --border-color: #fff;
    background-color: var(--google-gray-900);

    .runner-canvas {
      filter: invert(1);
    }

    &.inverted {
      background-color: #fff;
      filter: invert(0);

      .offline-runner-live-region {
        color: #fff;
      }
    }

    .show-stats, .reset-btn, .ok-btn {
      background: var(--google-gray-800);
    }

    .option-label {
      background: var(--google-gray-800);

      .option-toggle::before {
        background-color: #fff;
        opacity: 0.3;
      }

      [type=checkbox]:disabled+.option-toggle::before {
        opacity: 0.15;
      }

      [type=checkbox]:disabled+.option-toggle::after {
        background-color: #757575;
      }

      [type=checkbox]:checked+.option-toggle::before {
        background-color: #90caf9;
        opacity: 0.5;
      }

      [type=checkbox]:checked+.option-toggle::after {
        background-color: #90caf9;
      }

      [type=checkbox]:checked:disabled+.option-toggle::before {
        opacity: 0.2;
      }

      [type=checkbox]:checked:disabled+.option-toggle::after {
        background-color: #405a70;
      }
    }
  }
}
