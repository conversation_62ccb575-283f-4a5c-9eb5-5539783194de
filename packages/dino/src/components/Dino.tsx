import { useState, useEffect, useRef, useMemo } from 'preact/hooks';
import { loadTimeData } from '@/utils/load-time-data';
import createRunner, {<PERSON>} from '@/components/game/Runner';
import {
  getPercentage,
  LOCAL_STORAGE_KEYS,
} from '@/utils/utils';
import './Dino.css';

interface CustomKeyboardEventInit extends KeyboardEventInit {
  useVoice: boolean;
}

class VoiceKeyboardEvent extends KeyboardEvent {
  useVoice: boolean;
  constructor(type: string, init: CustomKeyboardEventInit) {
    super(type, init);
    this.useVoice = true;
  }
}

function Dino({ i18n, root = '/' }: { i18n: Record<string, string>; root?: string }) {
  const imgs = useRef(null);
  const audios = useRef(null);
  const dinoConfirmRef = useRef<HTMLDivElement | null>(null);
  const [runnerMode, setRunnerMode] = useState(true);
  const [isUsingVoice, setIsUsingVoice] = useState(false);
  const runner = useRef<typeof Runner>();
  const [disabled, setDisabled] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [stats, setStats] = useState<Record<string, number>>();
  let audioContext: AudioContext | null = null;
  if (!root) root = location.origin;

  useEffect(() => {
    loadTimeData.data = {
      ...i18n,
    };
    const runnerInstance = createRunner('.interstitial-wrapper');
    runner.current = runnerInstance;
  }, []);

  let isModuleLoaded = false;
  let micNode = null;
  let volumeMeterNode = null;
  let mediaStream = null;

  const loadGraph = async (context: AudioContext) => {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micNode = context.createMediaStreamSource(mediaStream);
    volumeMeterNode = new AudioWorkletNode(context, 'volume-meter');
    volumeMeterNode.port.onmessage = ({ data }) => {
      const volume = data * 500;
      if (volume > 20) {
        // only dispatch space key press if volume is high enough
        const eDown = new VoiceKeyboardEvent('keydown', {
          key: ' ',
          keyCode: 32,
        } as CustomKeyboardEventInit); // space

        const eUp = new VoiceKeyboardEvent('keyup', {
          key: ' ',
          keyCode: 32,
        } as CustomKeyboardEventInit); // space
        document.dispatchEvent(eDown);
        document.dispatchEvent(eUp);
      }
    };
    micNode.connect(volumeMeterNode).connect(context.destination);
  };

  const startAudio = async (context: AudioContext) => {
    if (!isModuleLoaded) {
      await context.audioWorklet.addModule(root + 'worklet/volume-meter-processor.js');
      isModuleLoaded = true;
    }
    await loadGraph(context);
  };

  const stopAudio = () => {
    if (mediaStream) {
      mediaStream.getTracks().forEach((track) => track.stop());
    }
    if (micNode) {
      micNode.disconnect();
    }
    if (volumeMeterNode) {
      volumeMeterNode.disconnect();
    }
  };

  useEffect(() => {
    if (runner.current) {
      runner.current.toggleVoiceControl();
    }
    if (isUsingVoice) {
      if (!audioContext) {
        // @ts-expect-error I don't know how to define this type
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
      }
      startAudio(audioContext).then(() => {
        audioContext.resume();
      });
    } else {
      if (audioContext) audioContext.suspend();
      stopAudio();
    }

    return () => {
      if (audioContext) audioContext.suspend();
      stopAudio();
    };
  }, [isUsingVoice]);

  const onPlayStatus = (e: CustomEvent) => {
    setDisabled(e.detail);
    if (e.detail) {
      setShowStats(false);
    }
  }

  useEffect(() => {
    window.addEventListener('playstatus', onPlayStatus);

    return () => {
      window.removeEventListener('playstatus', onPlayStatus);
    }
  }, [])

  const doShowStats = () => {
    if (disabled) return;
    const cumulativeStats = runnerMode ? localStorage.getItem(LOCAL_STORAGE_KEYS.RUNNER_STATS) : localStorage.getItem(LOCAL_STORAGE_KEYS.JUMP_STATS);
    if (cumulativeStats) {
      const stats = JSON.parse(cumulativeStats);
      setStats(stats);
    }
    setShowStats(!showStats);
  }

  const total = useMemo(() => {
    if (stats) {
      return stats.space + stats.up + stats.voice;
    }
    return 0
  }, [stats]);

  const totalMiss = useMemo(() => {
    if (stats) {
      return stats.spaceMiss + stats.upMiss + stats.voiceMiss;
    }
    return 0;
  }, [stats]);

  const totalSuccess = useMemo(() => {
    if (stats) {
      return total - totalMiss;
    }
    return 0;
  }, [stats]);

  const onReset = () => {
    // dinoConfirmRef.current?.showModal();
    if (dinoConfirmRef.current) {
      dinoConfirmRef.current.style.opacity = '1';
      dinoConfirmRef.current.style.visibility = 'visible';
    }
  }
  const closeModal = () => {
    // dinoConfirmRef.current?.close();
    if (dinoConfirmRef.current) {
      dinoConfirmRef.current.style.opacity = '0';
      dinoConfirmRef.current.style.visibility = 'hidden';
    }
  }
  const doReset = () => {
    if (runnerMode) {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.RUNNER_STATS);
    } else {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.JUMP_STATS);
    }
    setStats(null);
    closeModal();
    setShowStats(false);
  }

  const doClose = () => {
    setShowStats(false);
  }

  const doSwitchRunner = (isRunner: boolean) => {
    if (runnerMode !== isRunner) {
      setRunnerMode(isRunner);
      runner.current.switchMode(isRunner);
    }
  }

  return (
    <>
      <div id="dino-game-container" className="interstitial-wrapper offline">
        <div className="main-content">
          <div className="main-message">
            <p>{runnerMode ? i18n.pressSpaceToPlay : i18n.jumpPlay}</p>
            {runnerMode && (
              <label className="voice-control-option option-label">
                {i18n.enableVoiceControl}
                <input
                  type="checkbox"
                  title={i18n.enableVoiceControl}
                  tabindex={0}
                  disabled={disabled}
                  checked={isUsingVoice}
                  onChange={() => setIsUsingVoice((c) => !c)}
                />
                <span className="option-toggle" />
              </label>
            )}
          </div>
        </div>
        {!disabled && (
          <button type="button" className="show-stats" onClick={doShowStats}>
            {runnerMode ? i18n.showStats : i18n.jumpStats}
          </button>
        )}
        {showStats && (
          <div className="cumulative-stats-modal">
            <div className="cumulative-stats">
              {runnerMode ? (
                <table role="table">
                  <thead>
                    <tr>
                      <th>&nbsp;</th>
                      <th>{i18n.success}</th>
                      <th>{i18n.miss}</th>
                      <th>{i18n.accuracy}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{i18n.space}</td>
                      <td>{stats ? stats.space - stats.spaceMiss : 0}</td>
                      <td>{stats?.spaceMiss || 0}</td>
                      <td>
                        {getPercentage(
                          stats ? stats.space - stats.spaceMiss : 0,
                          stats?.space || 0
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>{i18n.up}</td>
                      <td>{stats ? stats.up - stats.upMiss : 0}</td>
                      <td>{stats?.upMiss || 0}</td>
                      <td>
                        {getPercentage(
                          stats ? stats.up - stats.upMiss : 0,
                          stats?.up || 0
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>{i18n.voice}</td>
                      <td>{stats ? stats.voice - stats.voiceMiss : 0}</td>
                      <td>{stats?.voiceMiss || 0}</td>
                      <td>
                        {getPercentage(
                          stats ? stats.voice - stats.voiceMiss : 0,
                          stats?.voice || 0
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td>{i18n.total}</td>
                      <td>{totalSuccess}</td>
                      <td>{totalMiss}</td>
                      <td>{getPercentage(totalSuccess, total)}</td>
                    </tr>
                  </tbody>
                </table>
              ) : (
                <table role="table">
                  <thead>
                    <tr>
                      <th>&nbsp;</th>
                      <th>{i18n.jumpCount}</th>
                      <th>{i18n.miss}</th>
                      <th>{i18n.jumpScore}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{i18n.jump}</td>
                      <td>{stats ? stats.jump : 0}</td>
                      <td>{stats ? stats.miss : 0}</td>
                      <td>{stats ? stats.score : 0}</td>
                    </tr>
                  </tbody>
                </table>
              )}

              <div className="action-bar">
                <button type="button" className="reset-btn" onClick={onReset}>
                  {i18n.reset}
                </button>
                <button type="button" className="ok-btn" onClick={doClose}>
                  OK
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="change-mode">
          <button
            className={'mode-block ' + (runnerMode ? 'active' : '')}
            onClick={() => doSwitchRunner(true)}
            disabled={disabled}
          >
            {i18n.dinoRunner}
          </button>
          <button
            className={'mode-block ' + (!runnerMode ? 'active' : '')}
            onClick={() => doSwitchRunner(false)}
            disabled={disabled}
          >
            {i18n.dinoJumper}
          </button>
        </div>
        <div className="dino-modal" ref={dinoConfirmRef}>
          <div className="dino-modal-box">
            <div className="dino-modal-text">{i18n.confirmReset}</div>
            <div className="dino-modal-action">
              <button
                type="button"
                className="dino-modal-btn reset-button"
                onClick={doReset}
              >
                {i18n.confirm}
              </button>
              <button
                type="button"
                className="dino-modal-btn"
                onClick={closeModal}
              >
                {i18n.cancel}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div id="dino-offline-resources">
        <div id="images-resources" ref={imgs}>
          <img
            crossOrigin="anonymous"
            id="dino-offline-resources-1x"
            src={`${root}images/default_100_percent/offline/100-offline-sprite.png`}
          />
          <img
            crossOrigin="anonymous"
            id="dino-offline-resources-2x"
            src={`${root}images/default_200_percent/offline/200-offline-sprite.png`}
          />
        </div>
        <div id="audio-resources" ref={audios}>
          <audio
            id="dino-offline-sound-press"
            src={`${root}sounds/button-press.mp3`}
            crossorigin="anonymous"
          ></audio>
          <audio
            id="dino-offline-sound-hit"
            src={`${root}sounds/hit.mp3`}
            crossorigin="anonymous"
          ></audio>
          <audio
            id="dino-offline-sound-reached"
            src={`${root}sounds/score-reached.mp3`}
            crossorigin="anonymous"
          ></audio>
          <audio
            id="dino-offline-sound-long-press"
            src={`${root}sounds/long-press.mp3`}
            crossOrigin="anonymous"
          />
        </div>
      </div>
    </>
  );
}

export default Dino;
