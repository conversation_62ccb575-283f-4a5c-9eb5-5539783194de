export const HIDDEN_CLASS = 'hidden';

/**
 * Default game width.
 * @const
 */
export const DEFAULT_WIDTH = 600;

/**
 * Frames per second.
 * @const
 */
export const FPS = 60;

/** @const */
export const IS_HIDPI = window.devicePixelRatio > 1;

/** @const */
export const IS_IOS = /CriOS/.test(window.navigator.userAgent);

/** @const */
export const IS_MOBILE = /Android/.test(window.navigator.userAgent) || IS_IOS;

/** @const */
export const IS_RTL = document.querySelector('html').dir == 'rtl';

/** @const */
export const RESOURCE_POSTFIX = 'dino-offline-resources';

/** @const */
export const A11Y_STRINGS = {
  ariaLabel: 'dinoGameA11yAriaLabel',
  description: 'dinoGameA11yDescription',
  gameOver: 'dinoGameA11yGameOver',
  highScore: 'dinoGameA11yHighScore',
  jump: 'dinoGameA11yJump',
  started: 'dinoGameA11yStartGame',
  speedLabel: 'dinoGameA11ySpeedToggle',
  statsSuccess: 'success',
  statsMiss: 'miss',
  statsAccuracy: 'accuracy',
  statsSpace: 'space',
  statsUp: 'up',
  statsVoice: 'voice',
  statsTotal: 'total',
  jumpCount: 'jumpCount',
  jumpScore: 'jumpScore',
  jumpRow: 'jump',
};
