# Dino game

Usage:

```js
import DinoG<PERSON> from 'https://unpkg.com/@roudanio/dino-game@latest/dist/dino-game.js';

DinoGame.init('#dino-game-example', {
  root: 'https://unpkg.com/@roudanio/dino-game@latest/dist/',
});
```

The default config object is like this:
```js
{
  root: location.origin,
  i18n: i18nDefault, // default i18n setting is in i18n.json
}
```

The default i18n is put in file [`./src/i18n.json`](./src/i18n.json).

The content is like this:
```json
{
  "pressSpaceToPlay": "Press SPACE to play",
  "enableVoiceControl": "Enable Voice Control",
  "showStats": "Show Stats",
  "success": "SUCCESS",
  "miss": "MISS",
  "accuracy": "ACCURACY",
  "space": "SPACE",
  "up": "UP",
  "voice": "VOICE",
  "total": "TOTAL",
  "reset": "Reset",
  "confirmReset": "Are you sure you want to reset the game data?",
  "dinoGameA11yAriaLabel": "Dino game, press space to play",
  "dinoGameA11yDescription": "Dino game. A pixelated dinosaur dodges cacti and pterodactyls as it runs across a desolate landscape. When you hear an audio cue, press space to jump over obstacles.",
  "dinoGameA11yGameOver": "Game over, your score is $1.",
  "dinoGameA11yHighScore": "Your highest score is $1.",
  "dinoGameA11yJump": "Jump!",
  "dinoGameA11yStartGame": "Game started.",
  "dinoGameA11ySpeedToggle": "Start slower"
}
```
