{
	"compilerOptions": {
		"target": "ES2020",
		"module": "ESNext",
		"moduleResolution": "bundler",
		"noEmit": true,
		"allowJs": true,
		"checkJs": true,

		/* Preact Config */
		"jsx": "react-jsx",
		"jsxImportSource": "preact",
		"skipLibCheck": true,
		"paths": {
			"@": [
				"./src"
			],
			"@/*": [
				"./src/*"
			],
			"react": ["./node_modules/preact/compat/"],
			"react-dom": ["./node_modules/preact/compat/"]
		}
	},
	"include": ["node_modules/vite/client.d.ts", "**/*"]
}
