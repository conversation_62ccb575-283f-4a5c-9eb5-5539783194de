import { defineConfig } from 'vite';
import preact from '@preact/preset-vite';
import { visualizer } from 'rollup-plugin-visualizer';
import { version } from './package.json';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
	const isProd = command === 'build';
	const isPublish = !!process.env.PUBLISH;

	return {
		define: {
      __IS_PROD__: isProd,
      __IS_PUBLISH__: isPublish,
      __VERSION__: JSON.stringify(version),
    },
		plugins: [
			preact(),
			visualizer({
        filename: 'stats.html', // 报告文件名
        brotliSize: true, // 显示 brotli 大小
      }),
		],
    resolve: {
      alias: {
        '@': '/src',
      },
    },
		...(isPublish && {
      build: {
        assetsInlineLimit: 0,
        target: 'chrome96',
        lib: {
          entry: 'src/main.tsx',
          name: '<PERSON>G<PERSON>',
          fileName: (format) =>
            format === 'es' ? 'dino-game.js' : `dino-game.${format}.js`,
          formats: ['es', 'umd', 'iife'],
        },
      },
    }),
	};
});
