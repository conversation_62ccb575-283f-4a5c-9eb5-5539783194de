import { GameProvider } from './components/TapMeContext';
import CounterPuzzleGame from './components/TapMeGame';
import { GameConfig } from './types';
import { defaultConfig } from './utils/utils';

export default function App({ config = {} }: { config?: Partial<GameConfig> }) {
  const i18n = { ...defaultConfig.i18n, ...(config.i18n || {}) };
  config = {
    ...defaultConfig,
    ...config,
    i18n,
  } as GameConfig;

  return (
    <GameProvider config={config}>
      <CounterPuzzleGame />
    </GameProvider>
  );
}
