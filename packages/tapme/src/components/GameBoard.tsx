import { RefObject } from 'preact';
import { useEffect, useRef, useState } from 'preact/hooks';
import { useGameContext } from './TapMeContext';
import { GamePhase, CellData, PowerUpType } from '../types';
import { useScorePopups } from '../hooks/useScorePopups';
import {
  findAllConnectedGroups,
  applyGravity,
  getEliminatingCells,
  findPath,
  calculateFinalPosition,
  animatePath,
  getHintCell,
  shuffleBoard,
} from '../utils/utils';
import { SoundEvent } from '../utils/SoundManager';
import Cell from './Cell';

interface GameBoardProps {
  containerRef: RefObject<HTMLDivElement>;
  onGameOver?: () => void;
  onGameOverWithUndo?: () => void;
}

const GameBoard = ({
  containerRef,
  onGameOver,
  onGameOverWithUndo,
}: GameBoardProps) => {
  const {
    gameState,
    setGameState,
    phase,
    setPhase,
    gameConfig,
    soundManager,
    stateHistory,
    saveStateToHistory,
  } = useGameContext();
  const { addPopup, renderPopups } = useScorePopups();
  const animationSpeed = gameConfig.current.animationSpeed || 1.25;
  // const animationSpeed = 0.5;
  const powerupTarget = gameConfig.current.powerTarget;

  const boardRef = useRef<HTMLDivElement>(null);
  const connectedGroupsRef = useRef<CellData[][]>([]);
  const targetCellRef = useRef<CellData | null>(null);
  const targetCellRectRef = useRef<DOMRect>();
  const eliminatingCellsRef = useRef<CellData[]>([]);
  const fallPassCounterRef = useRef<number>(0); // add counter for fall as it may needs several times to call
  const comboCountRef = useRef<number>(0); // Track combo count with ref instead of state

  const [lastClickedCell, setLastClickedCell] = useState<CellData | null>(null);
  const [processingGroupIndex, setProcessingGroupIndex] = useState(-1);
  const [processingCellIndex, setProcessingCellIndex] = useState(-1);
  const [hintCell, setHintCell] = useState<CellData | null>(null);
  const [promotedCell, setPromotedCell] = useState<CellData | null>(null);

  /**
   * Add a random powerup
   * This function randomly selects a powerup type and increases its count
   * Each powerup type has a maximum of 3 uses
   */
  const addPowerup = (type: PowerUpType) => {
    // Increase the selected powerup count (max 3)
    setGameState((prev) => {
      const currentCount = prev[`${type}Left`] || 0;

      if (currentCount < 3) {
        return {
          ...prev,
          [`${type}Left`]: currentCount + 1,
        };
      }
      return prev;
    });
  };

  /**
   * handle timeout
   */
  const timeoutRef = useRef<{ [key: string]: number }>({});
  // wrapper of setTimeout to clear previous timeout for same key
  const safeSetTimeout = (callback: () => void, delay: number, key: string) => {
    if (timeoutRef.current[key]) {
      clearTimeout(timeoutRef.current[key]);
    }
    // inactivity timer should not be affected by animation speed
    const adjustedDelay = key.startsWith('inactivity-')
      ? delay
      : Math.max(5, Math.floor(delay / animationSpeed));
    timeoutRef.current[key] = window.setTimeout(callback, adjustedDelay);
    return timeoutRef.current[key];
  };
  const clearAllTimeouts = () => {
    Object.values(timeoutRef.current).forEach((id) => clearTimeout(id));
    timeoutRef.current = {};
  };
  // clear all timers when unmount
  useEffect(() => {
    return () => clearAllTimeouts();
  }, []);

  /**
   * handle game phases, with animations
   */
  // 1. Idle
  //    Start inactivity timer when phase is Idle
  useEffect(() => {
    // Clear inactivity timers when phase is not Idle
    if (phase !== GamePhase.Idle) {
      // Clear any existing inactivity timers
      Object.keys(timeoutRef.current).forEach((key) => {
        if (key.startsWith('inactivity-')) {
          clearTimeout(timeoutRef.current[key]);
          delete timeoutRef.current[key];
        }
      });

      // Also reset hint cell
      setHintCell(null);
    }
    // Only set inactivity timer when in Idle phase
    else if (phase === GamePhase.Idle && gameState.clicksLeft > 0) {
      // Set new timer (5 seconds of inactivity)
      safeSetTimeout(
        () => {
          if (phase === GamePhase.Idle && gameState.clicksLeft > 0) {
            const hintCell = getHintCell(gameState.board);
            if (!hintCell) return;

            setHintCell(hintCell);

            // Auto-hide hint after 2 seconds
            safeSetTimeout(
              () => {
                setHintCell(null);
              },
              5000,
              'inactivity-hide-hint'
            );
          }
        },
        8000,
        'inactivity-timer'
      );
    }
  }, [phase, gameState.clicksLeft]);
  // Idle phase, waiting for click
  const handleCellClick = (cell: CellData) => {
    if (
      (phase !== GamePhase.Idle && phase !== GamePhase.CellClicked) ||
      gameState.clicksLeft <= 0 ||
      cell.value === null
    )
      return;

    // Save current state to history before making a move
    saveStateToHistory();

    // Reset combo count when player clicks a cell
    comboCountRef.current = 0;
    setLastClickedCell(cell); // will highlight the cell for a short time
    soundManager.play(SoundEvent.CELL_CLICK); // Play cell click sound
    setPhase(GamePhase.CellClicked);

    // Reset hint state
    setHintCell(null);
  };

  // 2. CellClicked, update cell + 1, clicksLeft -1
  useEffect(() => {
    if (phase === GamePhase.CellClicked && lastClickedCell) {
      setGameState((prev) => {
        const newBoard = prev.board.map((r) => r.slice());
        newBoard[lastClickedCell.row][lastClickedCell.col] =
          lastClickedCell.value! + 1;

        return {
          ...prev,
          board: newBoard,
          clicksLeft: prev.clicksLeft - 1,
        };
      });

      const timerId = safeSetTimeout(
        () => {
          setPhase(GamePhase.FindGroups);
        },
        500,
        'goto-find-connected-groups'
      );

      return () => {
        clearTimeout(timerId);
      };
    }
  }, [phase, lastClickedCell]);

  // 3. FindGroups, check connected groups
  useEffect(() => {
    if (phase === GamePhase.FindGroups) {
      const groups = findAllConnectedGroups(gameState.board);
      // console.log('FindGroups phase - Board state:', gameState.board);
      // console.log('FindGroups phase - Connected groups found:', groups);

      if (groups.length > 0) {
        connectedGroupsRef.current = groups;
        setProcessingGroupIndex(0);
        setPhase(GamePhase.ProcessGroups); // go to next group
      } else if (gameState.clicksLeft <= 0) {
        setPhase(GamePhase.GameOver);
      } else {
        setPhase(GamePhase.Idle);
      }
    }
  }, [phase]);

  // 4. ProcessGroups, one by one
  useEffect(() => {
    if (
      phase === GamePhase.ProcessGroups &&
      connectedGroupsRef.current.length > 0 &&
      processingGroupIndex >= 0
    ) {
      if (processingGroupIndex >= connectedGroupsRef.current.length) {
        const timerId = safeSetTimeout(
          () => {
            setLastClickedCell(null);
            setPhase(GamePhase.Fall); // processed all groups, go to fall
            connectedGroupsRef.current = [];
            setProcessingGroupIndex(-1);
          },
          50,
          'goto-fall'
        );
        return () => {
          clearTimeout(timerId);
        };
      }

      const curGroup = connectedGroupsRef.current[processingGroupIndex];
      // console.log(
      //   'Processing group:',
      //   processingGroupIndex,
      //   'Group size:',
      //   curGroup.length,
      //   'Group:',
      //   curGroup
      // );

      if (curGroup.length < 3) {
        // skip group with less than 3 cells
        // console.log('Skipping group with less than 3 cells:', curGroup);
        setProcessingGroupIndex((idx) => idx + 1);
        return;
      }

      if (!boardRef.current) return;

      if (comboCountRef.current === 0) {
        // when combo = 0, for the first elimination
        // need to highlight the connected group, later chaining will be highlighted in CheckChain
        curGroup.forEach((cell) => {
          const cellElement = boardRef.current!.querySelector(
            `[data-row="${cell.row}"][data-col="${cell.col}"]`
          ) as HTMLDivElement;

          if (cellElement) {
            cellElement.classList.add('counterpuzzle-cell-connected-group');
          }
        });
      }

      const { targetCell, cellsToClear } = getEliminatingCells(
        curGroup,
        lastClickedCell
      );

      // Increase combo count for each group processed
      comboCountRef.current += 1;

      // Calculate combo multiplier based on updated combo count
      // First combo (comboCountRef.current = 1) has no bonus
      // Each subsequent combo increases the multiplier
      const comboMultiplier =
        comboCountRef.current <= 1
          ? 1
          : // Option 1: Linear growth (1.0, 1.5, 2.0, 2.5, ...)
            // 1 + (comboCountRef.current - 1) * 0.5;

            // Option 2: Exponential growth (1.0, 1.2, 1.44, 1.72, 2.07, 2.48, ...)
            Math.pow(1.2, comboCountRef.current - 1);

      // Option 3: Threshold-based (1.0, 1.5, 2.0, 3.0, 5.0, ...)
      // comboCountRef.current <= 1 ? 1 :
      // comboCountRef.current <= 2 ? 1.5 :
      // comboCountRef.current <= 3 ? 2 :
      // comboCountRef.current <= 5 ? 3 : 5;

      // Apply the multiplier to the score calculation
      const baseScore = targetCell.value! * cellsToClear.length;
      const scoreIncrease = Math.round(baseScore * comboMultiplier);
      const targetCellRect = boardRef.current
        .querySelector(
          `[data-row="${targetCell.row}"][data-col="${targetCell.col}"]`
        )
        ?.getBoundingClientRect();

      if (targetCellRect) {
        addPopup(
          scoreIncrease,
          targetCellRect.left + targetCellRect.width / 2,
          targetCellRect.top - 30,
          comboMultiplier > 1 ? comboMultiplier : undefined // Only pass multiplier if > 1
        );
      }

      // Also play the corresponding hit-N sound for chain reactions, after the animation done
      // Use the combo count (capped at 9) to determine which hit sound to play
      // play hit sound when start processing group, also the time the connected group has border as visual hint
      soundManager.play(
        SoundEvent.CHAIN_HIT,
        Math.min(comboCountRef.current, 9)
      );

      setGameState((prev) => ({
        ...prev,
        score: prev.score + scoreIncrease,
      }));

      targetCellRef.current = targetCell;
      targetCellRectRef.current = targetCellRect;
      eliminatingCellsRef.current = cellsToClear;
      setProcessingCellIndex(0);
      setPhase(GamePhase.ProcessCells);
    }
  }, [phase, processingGroupIndex]);

  // 5. ProcessCells, one by one
  useEffect(() => {
    if (
      phase === GamePhase.ProcessCells &&
      eliminatingCellsRef.current.length > 0 &&
      processingCellIndex >= 0 &&
      targetCellRef.current &&
      targetCellRectRef.current
    ) {
      // completed cells of current group
      if (processingCellIndex >= eliminatingCellsRef.current.length) {
        safeSetTimeout(
          () => {
            const newTargetValue = targetCellRef.current!.value! + 1;

            if (boardRef.current) {
              // remove connected group class for current group only, should not affect other groups
              eliminatingCellsRef.current.forEach((cell) => {
                const cellElement = boardRef.current!.querySelector(
                  `[data-row="${cell.row}"][data-col="${cell.col}"]`
                ) as HTMLDivElement;
                if (cellElement) {
                  cellElement.classList.remove(
                    'counterpuzzle-cell-connected-group'
                  );
                }
              });
              // also need to remove target cell class
              const targetCellElement = boardRef.current!.querySelector(
                `[data-row="${targetCellRef.current!.row}"][data-col="${
                  targetCellRef.current!.col
                }"]`
              ) as HTMLDivElement;
              if (targetCellElement) {
                targetCellElement.classList.remove(
                  'counterpuzzle-cell-connected-group'
                );
              }
            }

            // update target cell value
            setGameState((prev) => {
              const newBoard = prev.board.map((row, i) =>
                row.map((cell, j) =>
                  i === targetCellRef.current!.row &&
                  j === targetCellRef.current!.col
                    ? newTargetValue
                    : cell
                )
              );

              return {
                ...prev,
                board: newBoard,
                clicksLeft: Math.min(prev.clicksLeft + 1, prev.maxClicks),
                maxNumberInGame: Math.max(prev.maxNumberInGame, newTargetValue),
              };
            });

            // we've config for each time target value is created, add powerup item
            if (newTargetValue === powerupTarget?.undo) {
              addPowerup('undo');
            }
            if (newTargetValue === powerupTarget?.shuffle) {
              addPowerup('shuffle');
            }
            if (newTargetValue === powerupTarget?.promote) {
              addPowerup('promote');
            }

            safeSetTimeout(
              () => {
                eliminatingCellsRef.current = [];
                setProcessingCellIndex(-1);
                targetCellRef.current = null;
                targetCellRectRef.current = undefined;
                setPhase(GamePhase.ProcessGroups);
                setProcessingGroupIndex((idx) => idx + 1);
                // processed all cells, back to connected group, will go to next group
              },
              100,
              'cells-complete-goto-next'
            );
          },
          50,
          'cells-animation-complete'
        );
        return () => {
          Object.keys(timeoutRef.current).forEach((key) => {
            if (key.startsWith('cells-')) {
              clearTimeout(timeoutRef.current[key]);
              delete timeoutRef.current[key];
            }
          });
        };
      }

      if (!boardRef.current || !containerRef.current) return;

      const curCell = eliminatingCellsRef.current[processingCellIndex];
      const curCellElement = boardRef.current.querySelector(
        `[data-row="${curCell.row}"][data-col="${curCell.col}"]`
      ) as HTMLDivElement;
      if (!curCellElement) return;
      const curCellRect = curCellElement.getBoundingClientRect();

      const clone = curCellElement.cloneNode(true) as HTMLDivElement;
      clone.classList.add('counterpuzzle-cell-clone');
      clone.style.width = `${curCellRect.width}px`;
      clone.style.height = `${curCellRect.height}px`;
      clone.style.left = `${curCellRect.left}px`;
      clone.style.top = `${curCellRect.top}px`;
      containerRef.current.appendChild(clone);

      const path = findPath(curCell, targetCellRef.current, gameState.board);

      const finalPos = calculateFinalPosition(
        curCell,
        targetCellRef.current,
        targetCellRectRef.current
      );

      // mark current cell as null -> empty
      setGameState((prev) => ({
        ...prev,
        board: prev.board.map((row, i) =>
          row.map((cell, j) =>
            i === curCell.row && j === curCell.col ? null : cell
          )
        ),
      }));

      if (!path) {
        clone.classList.add('counterpuzzle-cell-vanish');
        safeSetTimeout(
          () => {
            containerRef.current!.removeChild(clone);

            safeSetTimeout(
              () => {
                setProcessingCellIndex((idx) => idx + 1);
              },
              50,
              `cell-next-${curCell.row}-${curCell.col}`
            );
          },
          80,
          `cell-vanish-${curCell.row}-${curCell.col}`
        );
        return;
      }

      animatePath(
        clone,
        path,
        finalPos,
        boardRef.current,
        animationSpeed,
        () => {
          containerRef.current!.removeChild(clone);
          safeSetTimeout(
            () => {
              setProcessingCellIndex((idx) => idx + 1);
            },
            50,
            `cell-next-${curCell.row}-${curCell.col}`
          );
        }
      );

      return () => {
        Object.keys(timeoutRef.current).forEach((key) => {
          if (key.startsWith('cell-')) {
            clearTimeout(timeoutRef.current[key]);
            delete timeoutRef.current[key];
          }
        });
      };
    }
  }, [phase, processingCellIndex]);

  // 6. Fall, apply gravity
  useEffect(() => {
    if (phase === GamePhase.Fall) {
      const {
        board: newBoard,
        fallingCells,
        newCells,
        needsAnotherPass,
      } = applyGravity(gameState.board);

      if (
        boardRef.current &&
        (fallingCells.length > 0 || newCells.length > 0)
      ) {
        fallingCells.forEach((move) => {
          if (!move.from || !move.to) return;

          const fromCellElement = boardRef.current!.querySelector(
            `[data-row="${move.from.row}"][data-col="${move.from.col}"]`
          ) as HTMLDivElement;
          const toCellElement = boardRef.current!.querySelector(
            `[data-row="${move.to.row}"][data-col="${move.to.col}"]`
          ) as HTMLDivElement;

          if (!fromCellElement || !toCellElement) return;

          // get position
          const fromRect = fromCellElement.getBoundingClientRect();
          const toRect = toCellElement.getBoundingClientRect();

          // create clone element
          const clone = fromCellElement.cloneNode(true) as HTMLDivElement;
          clone.classList.add('counterpuzzle-cell-falling-clone');
          clone.style.left = `${fromRect.left}px`;
          clone.style.top = `${fromRect.top}px`;
          clone.style.width = `${fromRect.width}px`;
          clone.style.height = `${fromRect.height}px`;
          containerRef.current?.appendChild(clone);

          // update fromCell to null
          const updatedBoard = gameState.board.map((row) => [...row]);
          updatedBoard[move.from.row][move.from.col] = null;
          setGameState((prev) => ({
            ...prev,
            board: prev.board.map((row, i) =>
              row.map((cell, j) =>
                i === move.from.row && j === move.from.col ? null : cell
              )
            ),
          }));

          // start fall animation
          safeSetTimeout(
            () => {
              clone.style.top = `${toRect.top}px`;
              // remove clone after delay
              safeSetTimeout(
                () => {
                  if (
                    containerRef.current &&
                    containerRef.current.contains(clone)
                  ) {
                    containerRef.current.removeChild(clone);
                  }
                  // update to cell value
                  const updatedBoard = gameState.board.map((row) => [...row]);
                  updatedBoard[move.to.row][move.to.col] = move.to.value;
                  setGameState((prev) => {
                    const updatedBoard = prev.board.map((row) => [...row]);
                    updatedBoard[move.to.row][move.to.col] = move.to.value;

                    return {
                      ...prev,
                      board: updatedBoard,
                    };
                  });
                },
                250,
                `fall-end-${move.from.row}-${move.from.col}`
              );
            },
            25,
            `fall-start-${move.from.row}-${move.from.col}`
          );
        });

        // updated new cells only
        setGameState((prev) => ({
          ...prev,
          board: prev.board.map((row, i) => {
            const newRow = row.slice();
            newCells.forEach((cell) => {
              if (cell.row === i) {
                newRow[cell.col] = cell.value;
              }
            });
            return newRow;
          }),
        }));

        safeSetTimeout(
          () => {
            setGameState((prev) => ({
              ...prev,
              board: newBoard,
            }));

            if (needsAnotherPass) {
              fallPassCounterRef.current += 1;
              setPhase(GamePhase.Fall);
            } else {
              fallPassCounterRef.current = 0;
              setPhase(GamePhase.CheckChain);
            }
          },
          300,
          'fall-complete'
        );
      } else {
        setGameState((prev) => ({
          ...prev,
          board: newBoard,
        }));
        fallPassCounterRef.current = 0;
        setPhase(GamePhase.CheckChain);
      }
    }

    return () => {
      Object.keys(timeoutRef.current).forEach((key) => {
        if (key.startsWith('fall-')) {
          clearTimeout(timeoutRef.current[key]);
          delete timeoutRef.current[key];
        }
      });

      document.body
        .querySelectorAll('.counterpuzzle-cell-falling-clone')
        .forEach((el) => {
          el.remove();
        });
    };
  }, [phase, fallPassCounterRef.current]);

  // 7. CheckChain, check if there are connected groups after falling new cells
  useEffect(() => {
    if (phase === GamePhase.CheckChain) {
      // console.log('CheckChain phase - Board state:', gameState.board);
      const groups = findAllConnectedGroups(gameState.board);
      // console.log('CheckChain phase - Connected groups found:', groups);

      if (groups.length > 0) {
        if (!boardRef.current) return;

        // highlight new connected groups for a short time
        groups.forEach((group) => {
          group.forEach((cell) => {
            const cellElement = boardRef.current!.querySelector(
              `[data-row="${cell.row}"][data-col="${cell.col}"]`
            ) as HTMLDivElement;

            if (cellElement) {
              cellElement.classList.add('counterpuzzle-cell-new-connected');
              cellElement.classList.add('counterpuzzle-cell-connected-group');
            }
          });
        });

        // remove highlight after delay
        const timerId = safeSetTimeout(
          () => {
            if (boardRef.current) {
              boardRef.current
                .querySelectorAll('.counterpuzzle-cell-new-connected')
                .forEach((el) => {
                  el.classList.remove('counterpuzzle-cell-new-connected');
                });
            }

            // back to process groups and cells
            // Note: We don't reset combo count here to maintain combo chain
            connectedGroupsRef.current = groups;
            setProcessingGroupIndex(0);
            setPhase(GamePhase.ProcessGroups);
          },
          400,
          'remove-new-connected-highlight'
        );

        return () => {
          clearTimeout(timerId);
        };
      } else if (gameState.clicksLeft <= 0) {
        // console.log('Game over condition detected: no more clicks left');
        comboCountRef.current = 0; // Reset combo count on game over
        setPhase(GamePhase.GameOver);
      } else {
        // Play combo sound only if combo count > 1 (chain reaction exists)
        if (comboCountRef.current > 1) {
          soundManager.play(
            SoundEvent.COMBO,
            Math.min(comboCountRef.current - 1, 8)
          );
        }
        comboCountRef.current = 0; // Reset combo count when returning to idle
        setPhase(GamePhase.Idle);
      }
    }
  }, [phase]);

  // 8. Shuffle - Handle shuffle power-up
  useEffect(() => {
    if (phase === GamePhase.Shuffle) {
      // Decrease shuffles left
      setGameState((prev) => ({
        ...prev,
        shuffleLeft: (prev.shuffleLeft || 0) - 1,
      }));

      // Save current state to history before shuffling, it can be undo
      saveStateToHistory();

      // Add shuffle-out animation to all cells
      if (boardRef.current) {
        const cells = boardRef.current.querySelectorAll('.counterpuzzle-cell');
        cells.forEach((cell) => {
          cell.classList.add('counterpuzzle-cell-shuffle-out');
        });

        // After animation completes, shuffle the board
        safeSetTimeout(
          () => {
            // Shuffle the board
            const shuffledBoard = shuffleBoard(gameState.board);

            // Update the game state with the shuffled board
            setGameState((prev) => ({
              ...prev,
              board: shuffledBoard,
            }));

            // Add shuffle-in animation to all cells
            safeSetTimeout(
              () => {
                const cells = boardRef.current!.querySelectorAll(
                  '.counterpuzzle-cell'
                );
                cells.forEach((cell) => {
                  cell.classList.remove('counterpuzzle-cell-shuffle-out');
                  cell.classList.add('counterpuzzle-cell-shuffle-in');
                });

                // After shuffle-in animation, check for connected groups
                safeSetTimeout(
                  () => {
                    cells.forEach((cell) => {
                      cell.classList.remove('counterpuzzle-cell-shuffle-in');
                    });

                    // Check for connected groups after shuffling
                    setPhase(GamePhase.CheckChain);
                  },
                  300,
                  'shuffle-check-groups'
                );
              },
              50,
              'shuffle-in-animation'
            );
          },
          300,
          'shuffle-board'
        );
      }
    }
  }, [phase]);

  // 9. Undo - Handle undo power-up
  useEffect(() => {
    if (phase === GamePhase.Undo) {
      // if no history, nothing to undo
      if (stateHistory.current.length === 0) {
        setPhase(GamePhase.Idle);
        return;
      }

      // Decrease undo count
      setGameState((prev) => ({
        ...prev,
        undoLeft: (prev.undoLeft || 0) - 1,
      }));

      // Get the last state from history
      if (stateHistory.current.length > 0) {
        // Get the last saved state
        const lastState = stateHistory.current.pop();

        // Restore the state
        if (lastState) {
          setGameState((prev) => ({
            ...lastState,
            // Keep the current power-up counts, just decrease the undo count
            undoLeft: prev.undoLeft,
            shuffleLeft: prev.shuffleLeft,
            promoteLeft: prev.promoteLeft,
          }));
        }
      }

      // Return to idle state
      setPhase(GamePhase.Idle);
    }
  }, [phase]);

  // 10. Promote - Handle random cell promotion
  useEffect(() => {
    if (phase === GamePhase.Promote) {
      // Decrease promote count
      setGameState((prev) => ({
        ...prev,
        promoteLeft: (prev.promoteLeft || 0) - 1,
      }));

      // Save current state to history before promoting, it can be undo
      saveStateToHistory();

      // Find all non-null cells
      const nonNullCells: CellData[] = [];
      gameState.board.forEach((row, rowIndex) => {
        row.forEach((value, colIndex) => {
          if (value !== null) {
            nonNullCells.push({ row: rowIndex, col: colIndex, value });
          }
        });
      });

      if (nonNullCells.length > 0) {
        // Randomly select a cell
        const randomIndex = Math.floor(Math.random() * nonNullCells.length);
        const selectedCell = nonNullCells[randomIndex];

        setPromotedCell(selectedCell);

        safeSetTimeout(
          () => {
            setPromotedCell(null);
            setGameState((prev) => ({
              ...prev,
              board: prev.board.map((row, rowIndex) =>
                row.map((value, colIndex) =>
                  rowIndex === selectedCell.row && colIndex === selectedCell.col
                    ? value! + 1
                    : value
                )
              ),
            }));

            // Add a short delay before checking for groups
            safeSetTimeout(
              () => {
                // Go to FindGroups phase to check if the promotion created any groups
                setPhase(GamePhase.FindGroups);
              },
              300,
              'promote-to-find-groups'
            );
          },
          1500, // promoted animation has 3 pulse, total 1.5s
          'promote-highlight-remove'
        );
      } else {
        // No valid cells to promote, return to idle
        setPhase(GamePhase.Idle);
      }
    }
  }, [phase]);

  // 11. GameOver
  useEffect(() => {
    if (phase === GamePhase.GameOver) {
      if (gameState.undoLeft > 0) {
        onGameOverWithUndo?.();
      } else {
        onGameOver?.();
      }
    }
  }, [phase]);

  // 12. Safety check - ensure game state is consistent
  useEffect(() => {
    // Only run this check when in Idle phase to avoid interference with other phases
    if (phase === GamePhase.Idle) {
      // Check if there are any connected groups that should be eliminated
      const groups = findAllConnectedGroups(gameState.board);

      if (groups.length > 0) {
        // console.log(
        //   'Safety check: Found connected groups in Idle phase:',
        //   groups
        // );
        // If there are connected groups, transition to FindGroups phase
        setPhase(GamePhase.FindGroups);
      } else if (gameState.clicksLeft <= 0) {
        // console.log(
        //   'Safety check: No clicks left in Idle phase, triggering game over'
        // );
        // If no clicks left and no connected groups, game should be over
        setPhase(GamePhase.GameOver);
      }
    }
  }, [gameState.board, gameState.clicksLeft, phase]);

  return (
    <>
      <div
        className={`counterpuzzle-board ${
          phase !== GamePhase.Idle && phase !== GamePhase.CellClicked
            ? 'counterpuzzle-board-disabled'
            : ''
        }`}
        ref={boardRef}
      >
        {gameState.board.map((row, rowIndex) => (
          <>
            {row.map((value, colIndex) => (
              <Cell
                key={`${rowIndex}-${colIndex}`}
                row={rowIndex}
                col={colIndex}
                value={value}
                onClickCell={handleCellClick}
                highlight={
                  lastClickedCell?.row === rowIndex &&
                  lastClickedCell?.col === colIndex
                }
                promoted={
                  promotedCell?.row === rowIndex &&
                  promotedCell?.col === colIndex
                }
                showHint={
                  hintCell?.row === rowIndex &&
                  hintCell?.col === colIndex &&
                  value !== null
                }
              />
            ))}
          </>
        ))}
      </div>

      {/* render score popups: +X */}
      {renderPopups()}
    </>
  );
};

export default GameBoard;
