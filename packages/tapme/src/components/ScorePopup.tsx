import { useEffect, useRef, useState } from 'preact/hooks';

export interface ScorePopupProps {
  id: number;
  score: number;
  x: number;
  y: number;
  comboMultiplier?: number; // Add combo multiplier
  onComplete?: (id: number) => void;
}

const ScorePopup = ({
  id,
  score,
  x,
  y,
  comboMultiplier,
  onComplete,
}: ScorePopupProps) => {
  const [fading, setFading] = useState(false);
  const timeoutRef = useRef<number | null>(null);

  useEffect(() => {
    timeoutRef.current = window.setTimeout(() => {
      setFading(true);
    }, 10);

    const removeTimeout = window.setTimeout(() => {
      onComplete?.(id);
    }, 1010);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      clearTimeout(removeTimeout);
    };
  }, [id, onComplete]);

  return (
    <div
      className={`counterpuzzle-score-popup ${
        fading ? 'counterpuzzle-fade-up' : ''
      } ${
        comboMultiplier && comboMultiplier > 1
          ? 'counterpuzzle-combo-popup'
          : ''
      }`}
      style={{
        left: `${x}px`,
        top: `${y}px`,
      }}
    >
      +{score}
      {comboMultiplier && comboMultiplier > 1 && (
        <span className="counterpuzzle-combo-multiplier">
          ×{comboMultiplier.toFixed(1)}
        </span>
      )}
    </div>
  );
};

export default ScorePopup;
