import { useState, useRef, useEffect } from 'preact/hooks';
import { useGameContext } from './TapMeContext';
import GameControl from './GameControl';
import GameHeader from './GameHeader';
import GameBoard from './GameBoard';
import GameTools from './GameTools';
import Dialog from './Dialog';
import Share from '../../../../tools/share';
import { GamePhase } from '../types';

export default function CounterPuzzleGame() {
  const [isNewRecord, setIsNewRecord] = useState(false);
  const [isGameOverDialogOpen, setIsGameOverDialogOpen] = useState(false);
  const [isRestartConfirmOpen, setIsRestartConfirmOpen] = useState(false);
  const [isGameOverWithUndoOpen, setIsGameOverWithUndoOpen] = useState(false);
  const { gameState, setGameState, gameConfig, resetGame, setPhase } =
    useGameContext();
  const localThemeKey = gameConfig.current.lcPrefix + 'theme';
  const localTheme = localStorage.getItem(localThemeKey) || 'light';
  const [theme, setTheme] = useState(localTheme);
  const i18n = gameConfig.current.i18n!;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    localStorage.setItem(localThemeKey, theme);
  }, [theme]);

  const handleGameOverWithUndo = () => {
    setIsGameOverWithUndoOpen(true);
  };

  const handleGameOver = () => {
    setIsGameOverDialogOpen(true);
    if (
      gameState.score > gameState.highestScore ||
      gameState.maxNumberInGame > gameState.highestNumber
    ) {
      setIsNewRecord(true);
    }
    // update record
    setGameState((prev) => ({
      ...prev,
      highestScore: Math.max(prev.highestScore, prev.score),
      highestNumber: Math.max(prev.highestNumber, prev.maxNumberInGame),
    }));
  };

  const handleRestart = () => {
    setIsGameOverDialogOpen(false);
    setIsNewRecord(false);
    resetGame();
  };

  const handleRestartButtonClick = () => {
    setIsRestartConfirmOpen(true);
  };

  const handleConfirmRestart = () => {
    resetGame();
    setIsNewRecord(false);
    setIsRestartConfirmOpen(false);
  };

  const handleCancelRestart = () => {
    setIsRestartConfirmOpen(false);
  };

  const handleCancelUndo = () => {
    // process to game over
    setIsGameOverWithUndoOpen(false);
    handleGameOver();
  };

  const handleConfirmUndo = () => {
    setIsGameOverWithUndoOpen(false);
    setPhase(GamePhase.Undo);
  };

  const shareTags = Array.isArray(gameConfig.current?.socialShare?.hashtag)
    ? gameConfig.current?.socialShare?.hashtag.join(',')
    : gameConfig.current?.socialShare?.hashtag || '';
  const shareText = (gameConfig.current?.socialShare?.text || '')
    .replace('%NUMBER%', gameState.maxNumberInGame.toString())
    .replace('%SCORE%', gameState.score.toString());

  return (
    <div
      className="counterpuzzle-container"
      data-theme={theme}
      ref={containerRef}
    >
      <GameControl theme={theme} onSwitchTheme={setTheme} />
      <div className="counterpuzzle-inner-container">
        <GameHeader />
        <GameBoard
          containerRef={containerRef}
          onGameOver={handleGameOver}
          onGameOverWithUndo={handleGameOverWithUndo}
        />
        <GameTools onClickRestart={handleRestartButtonClick} />
      </div>

      {/* Game Over with Powerup Dialog */}
      <Dialog
        isOpen={isGameOverWithUndoOpen}
        title={i18n.unusedPowerup}
        className="counterpuzzle-dialog-unused-powerup"
        message={i18n.unusedPowerupMessage}
        cancelText={i18n.cancelGiveIn}
        onCancel={handleCancelUndo}
        confirmText={i18n.confirmUseUndo}
        onConfirm={handleConfirmUndo}
      />

      {/* Game Over Dialog */}
      <Dialog
        isOpen={isGameOverDialogOpen}
        title={i18n.gameOver}
        onConfirm={handleRestart}
        noCancel={true}
        confirmText={i18n.restart}
      >
        <div className="counterpuzzle-current-result">
          <p>
            {i18n.currentScore}:{' '}
            <span className="counterpuzzle-current-result-number">
              {gameState.score}
            </span>
          </p>
          <p>
            {i18n.currentHighestNumber}:{' '}
            <span className="counterpuzzle-current-result-number">
              {gameState.maxNumberInGame}
            </span>
          </p>
        </div>
        {isNewRecord && (
          <>
            <div className="counterpuzzle-congratulation">{i18n.newRecord}</div>
            <p>
              {i18n.newHighestNumber}:{' '}
              <span className="counterpuzzle-current-record-number">
                {gameState.highestNumber}
              </span>
            </p>
            <p>
              {i18n.newHighestScore}:{' '}
              <span className="counterpuzzle-current-record-number">
                {gameState.highestScore}
              </span>
            </p>
          </>
        )}

        <Share shareTags={shareTags} shareText={shareText} />
      </Dialog>

      {/* Restart Confirm Dialog */}
      <Dialog
        isOpen={isRestartConfirmOpen}
        title={i18n.confirmRestart}
        message={i18n.confirmRestartMessage}
        onConfirm={handleConfirmRestart}
        onCancel={handleCancelRestart}
        confirmText={i18n.confirm}
        cancelText={i18n.cancel}
      />
    </div>
  );
}
