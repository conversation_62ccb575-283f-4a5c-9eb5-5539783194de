import { CellData } from '../types';

interface CellProps {
  row: number;
  col: number;
  value: number | null;
  highlight: boolean;
  showHint: boolean;
  promoted: boolean;
  onClickCell: (c: CellData) => void;
}

const Cell = ({
  row,
  col,
  value,
  highlight,
  promoted,
  showHint,
  onClickCell,
}: CellProps) => {
  return (
    <div
      key={`${row}-${col}`}
      className={[
        'counterpuzzle-cell',
        value === null ? 'counterpuzzle-cell-empty' : '',
        highlight ? 'counterpuzzle-cell-highlight' : '',
        promoted ? 'counterpuzzle-cell-promoted' : '',
        showHint && value !== null ? 'counterpuzzle-cell-hint' : '',
      ]
        .filter(Boolean)
        .join(' ')}
      data-value={value}
      data-row={row}
      data-col={col}
      onClick={() => onClickCell({ row, col, value })}
    >
      {value}
    </div>
  );
};

export default Cell;
