import { useGameContext } from './TapMeContext';
import PowerUpItem from './PowerUpItem';

interface GameToolsProps {
  onClickRestart: () => void;
}

const GameTools = ({ onClickRestart }: GameToolsProps) => {
  const { gameConfig, stateHistory } = useGameContext();
  const i18n = gameConfig.current.i18n!;

  const hasUndoAvailable = stateHistory.current.length > 0;

  return (
    <div className="counterpuzzle-tools">
      <PowerUpItem
        type="undo"
        tooltipPosition="top-right"
        notAvailable={!hasUndoAvailable}
        tooltipExtraContent={
          !hasUndoAvailable ? i18n.undoTooltipNoAvailable : ''
        }
      />
      <PowerUpItem type="shuffle" tooltipPosition="top-right" />
      <PowerUpItem type="promote" tooltipPosition="top-right" />

      <button
        type="button"
        className="counterpuzzle-restart-btn"
        onClick={onClickRestart}
      >
        {i18n.restart}
      </button>
    </div>
  );
};

export default GameTools;
