import { useState, useEffect } from 'preact/hooks';
import { Volume2, VolumeOff } from 'lucide-react';
import { useGameContext } from './TapMeContext';

const GameControl = () => {
  const { gameState, gameConfig, soundManager } = useGameContext();
  const i18n = gameConfig.current.i18n!;
  const [soundEnabled, setSoundEnabled] = useState(true);
  const localSoundKey = gameConfig.current.lcPrefix + 'sound';

  // Initialize sound state from local storage or sound manager
  useEffect(() => {
    const localSound = localStorage.getItem(localSoundKey);

    if (localSound !== null) {
      const enabled = localSound === 'true';
      setSoundEnabled(enabled);
      soundManager.setEnabled(enabled);
    } else {
      setSoundEnabled(soundManager.isEnabled());
    }
  }, []);

  const handleSoundToggle = (enabled: boolean) => {
    setSoundEnabled(enabled);
    soundManager.setEnabled(enabled);

    // Save sound setting to local storage
    localStorage.setItem(localSoundKey, enabled.toString());
  };

  return (
    <div className="counterpuzzle-control">
      {/* Records */}
      <div className="counterpuzzle-records">
        <div>
          {i18n.highestNumber}: {gameState.highestNumber}
        </div>
        <div>
          {i18n.highestScore}: {gameState.highestScore}
        </div>
      </div>

      <div className="counterpuzzle-control-buttons">
        {/* Sound toggle */}
        <button
          type="button"
          className="counterpuzzle-sound-button"
          onClick={() => handleSoundToggle(!soundEnabled)}
          title={soundEnabled ? 'Sound On' : 'Sound Off'}
        >
          {soundEnabled ? (
            <Volume2 size={32} strokeWidth={2.5} />
          ) : (
            <VolumeOff size={32} strokeWidth={2.5} />
          )}
        </button>
      </div>
    </div>
  );
};

export default GameControl;
