import { useGameContext } from './TapMeContext';

const GameHeader = () => {
  const { gameState, gameConfig } = useGameContext();
  const i18n = gameConfig.current.i18n!;
  const clicksProgress = (gameState.clicksLeft / gameState.maxClicks) * 100;

  return (
    <div className="counterpuzzle-header">
      {/* Clicks Left, with progress bar */}
      <div className="counterpuzzle-clicks-left">
        <div className="counterpuzzle-clicks-left-label">
          {i18n.clicksLeft}: {gameState.clicksLeft}
        </div>
        <div className="counterpuzzle-clicks-left-progress">
          <div
            className="counterpuzzle-clicks-left-progress-bar"
            style={{
              width: `${clicksProgress}%`,
              backgroundColor:
                clicksProgress <= 20
                  ? '#ff5252'
                  : clicksProgress <= 60
                  ? '#ffd740'
                  : '',
            }}
          ></div>
        </div>
      </div>
      {/* Current Number & Score */}
      <div className="counterpuzzle-current-record">
        <div>
          {i18n.curNumber}: {gameState.maxNumberInGame}
        </div>
        <div>
          {i18n.score}: {gameState.score}
        </div>
      </div>
    </div>
  );
};

export default GameHeader;
