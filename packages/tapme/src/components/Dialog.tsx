import { useRef } from 'preact/hooks';
import type { ComponentChildren } from 'preact';

interface DialogProps {
  isOpen: boolean;
  title: string;
  className?: string;
  message?: string;
  children?: ComponentChildren;
  noConfirm?: boolean;
  noCancel?: boolean;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const Dialog = ({
  isOpen,
  title,
  className = '',
  message,
  children,
  noConfirm = false,
  noCancel = false,
  confirmText = 'OK',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
}: DialogProps) => {
  const dialogRef = useRef<HTMLDivElement>(null);

  return (
    <div
      className={`counterpuzzle-dialog-overlay ${
        isOpen ? 'counterpuzzle-dialog-visible' : ''
      }`}
    >
      <div className={'counterpuzzle-dialog ' + className} ref={dialogRef}>
        <div className="counterpuzzle-dialog-title">{title}</div>
        <div className="counterpuzzle-dialog-content">
          {children || (
            <div
              className="counterpuzzle-dialog-message"
              dangerouslySetInnerHTML={{ __html: message || '' }}
            />
          )}
        </div>
        <div className="counterpuzzle-dialog-buttons">
          {!noCancel && (
            <button className="counterpuzzle-dialog-cancel" onClick={onCancel}>
              {cancelText}
            </button>
          )}
          {!noConfirm && (
            <button
              type="button"
              className="counterpuzzle-dialog-confirm"
              onClick={onConfirm}
            >
              {confirmText}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dialog;
