import { useState, useEffect } from 'preact/hooks';
import { Undo2, Shuffle, Plus } from 'lucide-react';
import { GamePhase, PowerUpType } from '../types';
import { useGameContext } from './TapMeContext';
import Tooltip from './Tooltip';
import { isMobileDevice } from '../utils/utils';

interface PowerUpItemProps {
  type: PowerUpType;
  tooltipPosition?:
    | 'top-left'
    | 'top'
    | 'top-right'
    | 'bottom'
    | 'left'
    | 'right';
  tooltipExtraContent?: string;
  notAvailable?: boolean;
}

const counter = [1, 2, 3]; // each item at most use 3 times

const Icons = {
  undo: Undo2,
  shuffle: Shuffle,
  promote: Plus,
};

const PowerUpItem = ({
  type,
  tooltipPosition,
  tooltipExtraContent,
  notAvailable,
}: PowerUpItemProps) => {
  const { gameState, phase, setPhase, gameConfig } = useGameContext();
  const i18n = gameConfig.current.i18n!;
  const powerTarget = gameConfig.current.powerTarget![type]!;
  const tooltipText = i18n.tooltipContent.replace(
    '%NUM%',
    powerTarget.toString()
  );
  const Icon = Icons[type] || null;
  const count = gameState[`${type}Left`] || 0;
  const isDisabled = count <= 0 || phase !== GamePhase.Idle || notAvailable;

  // Add states for mobile interaction
  const [isMobile, setIsMobile] = useState(false);
  const [tappedOnce, setTappedOnce] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Detect if we're on mobile
  useEffect(() => {
    setIsMobile(isMobileDevice());
  }, []);

  // Reset tapped state after the tooltip auto-hides (3 seconds)
  useEffect(() => {
    if (tappedOnce) {
      const resetTimer = setTimeout(() => {
        setTappedOnce(false);
        setShowTooltip(false);
      }, 3000);

      return () => clearTimeout(resetTimer);
    }
  }, [tappedOnce]);

  const onClickItem = () => {
    if (isDisabled) return;

    // On mobile, first tap shows tooltip, second tap activates
    if (isMobile) {
      if (!tappedOnce) {
        // First tap - show tooltip
        setTappedOnce(true);
        setShowTooltip(true);

        // The tooltip will auto-hide after the autoHideDelay
        // We don't need to set a manual timeout anymore
        return;
      } else {
        // Second tap - activate and reset states
        setTappedOnce(false);
        setShowTooltip(false);
      }
    }

    // Execute the action (for both desktop and mobile second tap)
    switch (type) {
      case 'shuffle':
        setPhase(GamePhase.Shuffle);
        break;
      case 'promote':
        setPhase(GamePhase.Promote);
        break;
      case 'undo':
        setPhase(GamePhase.Undo);
        break;
    }
  };

  // Get tooltip content based on state
  const getTooltipContent = (
    isMobile = false,
    tooltipExtraContent?: string
  ) => {
    return (
      <>
        <div className="counterpuzzle-tooltip-header">
          <span className="counterpuzzle-tooltip-title">{i18n[type]}</span>
          <span>{count + ' ' + i18n.usesLeft}</span>
        </div>
        <div className="counterpuzzle-tooltip-content">{tooltipText}</div>
        {tooltipExtraContent && (
          <div className="counterpuzzle-tooltip-extra-content">
            {tooltipExtraContent}
          </div>
        )}
        {isMobile && !isDisabled && (
          <div className="counterpuzzle-tooltip-tap-again">
            {i18n.tapAgainToUse}
          </div>
        )}
      </>
    );
  };

  return (
    <div className="counterpuzzle-powerup-item-container">
      <Tooltip
        content={getTooltipContent(false, tooltipExtraContent)}
        mobileContent={getTooltipContent(true, tooltipExtraContent)}
        position={tooltipPosition}
        forceVisible={isMobile && tappedOnce && showTooltip}
        autoHideDelay={3000}
      >
        <div
          className={[
            'counterpuzzle-powerup-item',
            isDisabled ? 'item-disabled' : '',
            notAvailable ? 'item-not-available' : '',
            tappedOnce ? 'item-tapped-once' : '',
          ]
            .filter(Boolean)
            .join(' ')}
          onClick={onClickItem}
        >
          <Icon />
        </div>
      </Tooltip>
      <div className="counterpuzzle-powerup-item-counter">
        {counter.map((c) => (
          <div
            key={c}
            className={`counterpuzzle-powerup-item-counter-one ${
              c > 3 - count ? 'counter-active' : ''
            }`}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default PowerUpItem;
