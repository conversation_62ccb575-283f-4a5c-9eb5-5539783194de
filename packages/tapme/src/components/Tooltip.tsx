import { useState, useRef, useEffect } from 'preact/hooks';
import { ComponentChildren } from 'preact';
import { isMobileDevice } from '../utils/utils';

interface TooltipProps {
  content: ComponentChildren;
  children: ComponentChildren;
  position?: 'top-left' | 'top' | 'top-right' | 'bottom' | 'left' | 'right';
  forceVisible?: boolean; // Add prop to control visibility from parent
  mobileContent?: ComponentChildren; // Optional different content for mobile tap
  autoHideDelay?: number; // Delay in ms before auto-hiding the tooltip
}

const Tooltip = ({
  content,
  children,
  position = 'top',
  forceVisible = false,
  mobileContent,
  autoHideDelay = 0,
}: TooltipProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const childRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const autoHideTimerRef = useRef<number | null>(null);

  // Detect if we're on mobile using the utility function
  useEffect(() => {
    setIsMobile(isMobileDevice());
  }, []);

  // Clear any existing timer when component unmounts
  useEffect(() => {
    return () => {
      if (autoHideTimerRef.current) {
        clearTimeout(autoHideTimerRef.current);
      }
    };
  }, []);

  // Set up auto-hide timer when forceVisible changes to true
  useEffect(() => {
    if (forceVisible && autoHideDelay > 0) {
      // Clear any existing timer
      if (autoHideTimerRef.current) {
        clearTimeout(autoHideTimerRef.current);
      }

      // Set a new timer to hide the tooltip after the delay
      autoHideTimerRef.current = setTimeout(() => {
        setIsVisible(false);
      }, autoHideDelay) as unknown as number;
    }
  }, [forceVisible, autoHideDelay]);

  const showTooltip = () => {
    setIsVisible(true);
  };

  const hideTooltip = () => {
    if (!forceVisible) {
      setIsVisible(false);
    }
  };

  useEffect(() => {
    if (isVisible && tooltipRef.current && childRef.current) {
      const childRect = childRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      let top = 0;
      let left = 0;

      switch (position) {
        case 'top-left':
          top = -tooltipRect.height - 8;
          left = childRect.width - tooltipRect.width;
          break;
        case 'top':
          top = -tooltipRect.height - 8;
          left = (childRect.width - tooltipRect.width) / 2;
          break;
        case 'top-right':
          top = -tooltipRect.height - 8;
          left = 0;
          break;
        case 'bottom':
          top = childRect.height + 8;
          left = (childRect.width - tooltipRect.width) / 2;
          break;
        case 'left':
          top = (childRect.height - tooltipRect.height) / 2;
          left = -tooltipRect.width - 8;
          break;
        case 'right':
          top = (childRect.height - tooltipRect.height) / 2;
          left = childRect.width + 8;
          break;
      }

      tooltipRef.current.style.top = `${top}px`;
      tooltipRef.current.style.left = `${left}px`;
    }
  }, [isVisible, position]);

  return (
    <div
      className="counterpuzzle-tooltip-container"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onTouchStart={showTooltip}
      onTouchEnd={hideTooltip}
      ref={childRef}
    >
      {children}
      {(isVisible || forceVisible) && (
        <div
          className={`counterpuzzle-tooltip counterpuzzle-tooltip-${position}`}
          ref={tooltipRef}
        >
          {isMobile && mobileContent ? mobileContent : content}
        </div>
      )}
    </div>
  );
};

export default Tooltip;
