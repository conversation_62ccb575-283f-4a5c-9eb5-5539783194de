* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background-color: #f4f4f4;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #333;
}

#app {
  width: 90%;
  max-width: 600px;
  min-height: 858px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.counterpuzzle-usage {
  width: 95%;
  margin: 0 auto 30px;

  pre {
    white-space: pre-wrap;
    padding: 10px 12px;
    background-color: #291334;
    color: #d0cad3;
    word-wrap: break-word;
  }

  details>summary {
    cursor: pointer;
  }

  .copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 4px 8px;
    background: #e0e0e0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
}
