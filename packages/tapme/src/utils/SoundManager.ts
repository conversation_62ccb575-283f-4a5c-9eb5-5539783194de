/**
 * Sound Manager for CounterPuzzle game
 * Handles loading and playing sound effects
 */

// Sound categories and their corresponding files
export enum SoundCategory {
  HIT = 'hit',
  COMBO = 'combo',
}

// Sound events in the game
export enum SoundEvent {
  CELL_CLICK = 'cell_click',
  COMBO = 'combo',
  CHAIN_HIT = 'chain_hit', // New event for chain reaction hits
}

export class SoundManager {
  private sounds: Map<string, HTMLAudioElement> = new Map();
  private enabled: boolean = true;
  private initialized: boolean = false;
  private root: string;

  constructor(root: string = '') {
    this.root = root;
  }

  /**
   * Initialize the sound manager by loading all sound files
   */
  public async init(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.loadSoundCategory(SoundCategory.HIT, 10); // hit-0 to hit-9
      await this.loadSoundCategory(SoundCategory.COMBO, 8); // 1 to 8

      this.initialized = true;
      // console.log('Sound manager initialized');
    } catch (error) {
      console.error('Failed to initialize sound manager:', error);
    }
  }

  /**
   * Load all sound files in a category
   */
  private async loadSoundCategory(
    category: SoundCategory,
    count: number
  ): Promise<void> {
    const loadPromises: Promise<void>[] = [];

    if (category === SoundCategory.HIT) {
      // Load hit sounds (hit-0 to hit-9)
      for (let i = 0; i < count; i++) {
        loadPromises.push(
          this.loadSound(`${category}/hit-${i}.mp3`, `${category}_${i}`)
        );
      }
    } else if (category === SoundCategory.COMBO) {
      // Load combo sounds (1 to 8)
      for (let i = 1; i <= count; i++) {
        loadPromises.push(
          this.loadSound(`${category}/${i}.mp3`, `${category}_${i}`)
        );
      }
    }

    await Promise.all(loadPromises);
  }

  /**
   * Load a single sound file
   */
  private async loadSound(path: string, key: string): Promise<void> {
    try {
      const audio = new Audio(`${this.root}sounds/${path}`);
      audio.volume = 0.5; // Fixed volume
      this.sounds.set(key, audio);

      // Preload the audio
      return new Promise((resolve) => {
        audio.addEventListener('canplaythrough', () => resolve(), {
          once: true,
        });
        audio.load();
      });
    } catch (error) {
      console.error(`Failed to load sound: ${path}`, error);
    }
  }

  /**
   * Play a sound for a specific game event
   */
  public play(event: SoundEvent, value?: number): void {
    if (!this.initialized || !this.enabled) return;

    let sound: HTMLAudioElement | undefined;
    let soundKey: string;

    switch (event) {
      case SoundEvent.CELL_CLICK: {
        // Always play hit-0 for cell click
        soundKey = `${SoundCategory.HIT}_0`;
        sound = this.sounds.get(soundKey);
        break;
      }

      case SoundEvent.COMBO: {
        // Play combo sound based on combo size (capped at 8)
        const comboSize = Math.min(value || 3, 8);
        soundKey = `${SoundCategory.COMBO}_${comboSize}`;
        sound = this.sounds.get(soundKey);
        break;
      }

      case SoundEvent.CHAIN_HIT: {
        // Play hit-N sound for chain reactions (capped at 9)
        const hitIndex = Math.min(value || 1, 9);
        soundKey = `${SoundCategory.HIT}_${hitIndex}`;
        sound = this.sounds.get(soundKey);
        break;
      }
    }

    if (sound) {
      // Clone the audio to allow overlapping sounds
      const clone = sound.cloneNode() as HTMLAudioElement;
      clone.volume = 0.5; // Fixed volume
      clone.play().catch((err) => console.error('Error playing sound:', err));
    }
  }

  /**
   * Enable or disable sounds
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Check if sounds are enabled
   */
  public isEnabled(): boolean {
    return this.enabled;
  }
}

export default SoundManager;
