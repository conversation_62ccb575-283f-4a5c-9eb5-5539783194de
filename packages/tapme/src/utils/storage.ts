import { GameState } from '../types';

export const createStorage = (key: string) => {
  return {
    saveGameData(state: Partial<GameState>): void {
      try {
        localStorage.setItem(key, JSON.stringify(state));
      } catch (e) {
        console.error('Failed to save CounterPuzzle game data:', e);
      }
    },

    loadGameData(): Partial<GameState> | null {
      try {
        const savedData = localStorage.getItem(key);
        if (!savedData) return null;

        return JSON.parse(savedData);
      } catch (e) {
        console.error('Failed to load CounterPuzzle game data:', e);
        return null;
      }
    },

    resetGameData(): void {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        console.error('Failed to reset CounterPuzzle game state:', e);
      }
    },
  };
};
