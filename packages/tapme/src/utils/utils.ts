import { GameConfig, GameBoardData, CellData } from '../types';

export const defaultConfig: GameConfig = {
  lcPrefix: 'counterpuzzle-',
  i18n: {
    gameTitle: 'Counter Puzzle',
    highestNumber: 'Highest Number',
    highestScore: 'Highest Score',
    darkMode: 'Dark Mode',
    clicksLeft: 'Clicks Left',
    curNumber: 'Count',
    score: 'Score',
    restart: 'Restart',
    gameOver: 'Game Over',
    currentScore: '🟢 Score',
    currentHighestNumber: '🟢 Max Number',
    newRecord: '🎉 Congratulations! New record! ',
    newHighestNumber: '🏆 New High Number',
    newHighestScore: '🏆 New High Score',
    confirmRestart: 'Are you sure to restart the game?',
    confirmRestartMessage: 'Your current progress will be lost.',
    confirm: 'Confirm',
    cancel: 'Cancel',
    shuffle: 'Shuffle', // New i18n for shuffle button
    promote: 'Promote',
    undo: 'Undo',
    usesLeft: 'Uses Left',
    tooltipContent: 'Make a %NUM% to get more',
    // undoTooltip: 'Make a 7 to get more',
    // shuffleTooltip: 'Make a 8 to get more',
    // promoteTooltip: 'Make a 9 to get more',
    tapAgainToUse: 'Tap again to use',
    undoTooltipNoAvailable:
      "There's nothing to undo. Please make your first move!",
    unusedPowerup: 'Unused Powerup',
    unusedPowerupMessage:
      'Game is over, but luckily you have got an <i>Undo</i> Powerup. Do you want to save yourself?',
    cancelGiveIn: "Cancel, I'd give in.",
    confirmUseUndo: 'Yes, undo to save me!',
  },
  powerTarget: {
    undo: 7,
    shuffle: 8,
    promote: 9,
  },
  boardSize: 5,
  initialClicks: 5,
  maxNumber: 20,
  animationSpeed: 1.25,
  socialShare: {
    hashtag: 'CounterPuzzle',
    text: 'I have reached the Number %NUMBER% and scored %SCORE%. Can you do better?',
  },
};

export const isMobileDevice = () => {
  return (
    'ontouchstart' in window &&
    /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent)
  );
};

export const initBoard = (size: number): GameBoardData => {
  let board: GameBoardData;
  do {
    board = generateBoard(size);
  } while (hasConnectedGroups(board));

  return board;
};

export const generateBoard = (size: number): GameBoardData => {
  return Array(size)
    .fill(null)
    .map(() => {
      return Array(size)
        .fill(null)
        .map(() => Math.floor(Math.random() * 5) + 1);
    });
};

export const findAllConnectedGroups = (board: GameBoardData): CellData[][] => {
  const size = board.length;
  const visited: boolean[][] = Array(size)
    .fill(false)
    .map(() => Array(size).fill(false));
  const groups: CellData[][] = [];

  const checkCell = (
    row: number,
    col: number,
    value: number,
    group: CellData[]
  ) => {
    if (
      row < 0 ||
      row >= size ||
      col < 0 ||
      col >= size ||
      visited[row][col] ||
      board[row][col] !== value
    ) {
      return;
    }

    visited[row][col] = true;
    group.push({ row, col, value: board[row][col] });

    // Check all four adjacent cells (up, down, left, right)
    checkCell(row - 1, col, value, group); // up
    checkCell(row + 1, col, value, group); // down
    checkCell(row, col - 1, value, group); // left
    checkCell(row, col + 1, value, group); // right
  };

  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      if (!visited[i][j] && board[i][j] !== null) {
        const group: CellData[] = [];
        checkCell(i, j, board[i][j]!, group);
        if (group.length >= 3) {
          groups.push(group);
        }
      }
    }
  }

  return groups;
};

export const hasConnectedGroups = (board: GameBoardData): boolean => {
  return findAllConnectedGroups(board).length > 0;
};

type FallingCell = {
  from: CellData;
  to: CellData;
};

export const applyGravity = (
  board: GameBoardData
): {
  board: GameBoardData;
  fallingCells: FallingCell[];
  newCells: CellData[];
  needsAnotherPass: boolean;
} => {
  const size = board.length;
  const newBoard = board.map((row) => row.slice());
  const fallingCells: FallingCell[] = [];
  const newCells: CellData[] = [];

  // check from bottom to top
  for (let col = 0; col < size; col++) {
    for (let row = size - 1; row > 0; row--) {
      if (newBoard[row][col] === null) {
        let sourceRow = row - 1;
        while (sourceRow >= 0 && newBoard[sourceRow][col] === null) {
          sourceRow--;
        }

        if (sourceRow >= 0) {
          fallingCells.push({
            from: { row: sourceRow, col, value: newBoard[sourceRow][col] },
            to: { row, col, value: newBoard[sourceRow][col] },
          });

          newBoard[row][col] = newBoard[sourceRow][col];
          newBoard[sourceRow][col] = null;
        }
      }
    }
  }

  // fill up above cells
  for (let col = 0; col < size; col++) {
    if (newBoard[0][col] === null) {
      const newValue = Math.floor(Math.random() * 5) + 1;
      newBoard[0][col] = newValue;
      newCells.push({
        row: 0,
        col: col,
        value: newValue,
      });
    }
  }

  let needsAnotherPass = false;
  for (let row = 0; row < size; row++) {
    for (let col = 0; col < size; col++) {
      if (newBoard[row][col] === null) {
        needsAnotherPass = true;
        break;
      }
    }
    if (needsAnotherPass) break;
  }

  return { board: newBoard, fallingCells, newCells, needsAnotherPass };
};

export function getEliminatingCells(
  group: CellData[],
  lastClickedCell: CellData | null
): {
  targetCell: CellData;
  cellsToClear: CellData[];
} {
  const clickedInGroup = group.some(
    (cell) =>
      cell.row === lastClickedCell?.row && cell.col === lastClickedCell?.col
  );

  let cellsToClear: CellData[] = [];
  let targetCell: CellData | undefined;

  if (clickedInGroup) {
    // clicked cell is in the group, use it as target
    targetCell = group.find(
      (cell) =>
        cell.row === lastClickedCell?.row && cell.col === lastClickedCell?.col
    );
  } else {
    // choose max row / col cell
    targetCell = group[0];
    for (const cell of group) {
      if (
        cell.row > targetCell.row ||
        (cell.row === targetCell.row && cell.col < targetCell.col)
      ) {
        targetCell = cell;
      }
    }
  }

  cellsToClear = group.filter(
    (cell) => cell.row !== targetCell?.row || cell.col !== targetCell?.col
  );

  return {
    targetCell: targetCell!,
    cellsToClear,
  };
}

export function findPath(
  startCell: CellData,
  targetCell: CellData,
  board: GameBoardData
): { row: number; col: number }[] | null {
  const directions = [
    { row: -1, col: 0 }, // up
    { row: 0, col: 1 }, // right
    { row: 1, col: 0 }, // down
    { row: 0, col: -1 }, // left
  ];

  // start a queue from start cell
  const queue: {
    row: number;
    col: number;
    path: { row: number; col: number }[];
  }[] = [{ row: startCell.row, col: startCell.col, path: [] }];

  const boardSize = board.length;

  // array to mark visited cells
  const visited = Array(boardSize)
    .fill(null)
    .map(() => Array(boardSize).fill(false));
  visited[startCell.row][startCell.col] = true;

  // BFS
  while (queue.length > 0) {
    const current = queue.shift()!;

    // if reach the target, return path
    if (
      (Math.abs(current.row - targetCell.row) === 1 &&
        current.col === targetCell.col) ||
      (Math.abs(current.col - targetCell.col) === 1 &&
        current.row === targetCell.row)
    ) {
      return [...current.path, { row: current.row, col: current.col }];
    }

    for (const dir of directions) {
      const newRow = current.row + dir.row;
      const newCol = current.col + dir.col;

      // check boundary
      if (
        newRow >= 0 &&
        newRow < boardSize &&
        newCol >= 0 &&
        newCol < boardSize &&
        !visited[newRow][newCol]
      ) {
        // check new position is null or is target
        if (
          board[newRow][newCol] === null ||
          (newRow === targetCell.row && newCol === targetCell.col)
        ) {
          visited[newRow][newCol] = true;

          // push to queue
          queue.push({
            row: newRow,
            col: newCol,
            path: [...current.path, { row: current.row, col: current.col }],
          });
        }
      }
    }
  }

  return null;
}

export function calculateFinalPosition(
  cell: CellData,
  targetCell: CellData,
  targetRect: DOMRect
): { x: number; y: number } {
  // offset when vanish
  const offset = 20;

  if (cell.row < targetCell.row) {
    // above target cell, vanish up
    return {
      x: targetRect.left + targetRect.width / 2,
      y: targetRect.top - offset,
    };
  } else if (cell.row > targetCell.row) {
    // below target cell, vanish down
    return {
      x: targetRect.left + targetRect.width / 2,
      y: targetRect.bottom + offset,
    };
  } else if (cell.col < targetCell.col) {
    // left target cell, vanish left
    return {
      x: targetRect.left - offset,
      y: targetRect.top + targetRect.height / 2,
    };
  } else {
    // right target cell, vanish right
    return {
      x: targetRect.right + offset,
      y: targetRect.top + targetRect.height / 2,
    };
  }
}

export function animatePath(
  element: HTMLElement,
  path: { row: number; col: number }[],
  finalPos: { x: number; y: number },
  boardElement: HTMLElement,
  animationSpeed: number = 1,
  callback: () => void
) {
  // const duration = 25;
  const duration = Math.max(5, Math.floor(25 / animationSpeed));
  let step = 0;

  // get coordinates of each point in the path
  const positions = path.map((point) => {
    const cell = boardElement.querySelector(
      `[data-row="${point.row}"][data-col="${point.col}"]`
    ) as HTMLDivElement;
    const rect = cell.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2,
    };
  });

  // add final position
  positions.push(finalPos);

  // move cell to next position step by step
  function nextStep() {
    if (step < positions.length) {
      const pos = positions[step];
      const elementWidth = parseInt(element.style.width);
      const elementHeight = parseInt(element.style.height);

      element.style.left = `${pos.x - elementWidth / 2}px`;
      element.style.top = `${pos.y - elementHeight / 2}px`;

      step++;
      setTimeout(nextStep, duration);
    } else {
      // reach final position, vanish
      element.classList.add('counterpuzzle-cell-vanish');
      setTimeout(callback, Math.max(10, Math.floor(80 / animationSpeed)));
    }
  }

  setTimeout(nextStep, Math.max(1, Math.floor(5 / animationSpeed)));
}

export function getHintCell(board: GameBoardData): CellData | null {
  let eliminableCell: CellData | null = null;

  const rows = board.length;
  const cols = board[0].length;

  // Check each cell with a value using for loops for early breaking
  for (let rowIndex = 0; rowIndex < rows; rowIndex++) {
    for (let colIndex = 0; colIndex < cols; colIndex++) {
      const value = board[rowIndex][colIndex];
      if (value === null) continue; // Skip empty cells

      // Create a simulated board with this cell incremented by 1
      const simulatedBoard = board.map((r) => r.slice());
      simulatedBoard[rowIndex][colIndex] = value + 1;

      // Check if this would create any connected groups
      const groups = findAllConnectedGroups(simulatedBoard);

      // If there are connected groups, this cell can trigger elimination
      if (groups.length > 0) {
        eliminableCell = {
          row: rowIndex,
          col: colIndex,
          value,
        } as CellData;

        // If we only need one hint cell, we can break early
        // Return after finding the first eliminable cell
        return eliminableCell;
      }
    }
  }

  return eliminableCell;
}

/**
 * Shuffle the board while preserving all cell values
 * This is used for the shuffle power-up
 */
export function shuffleBoard(board: GameBoardData): GameBoardData {
  const size = board.length;
  const newBoard = Array(size)
    .fill(null)
    .map(() => Array(size).fill(null));

  // Collect all non-null values from the board
  const values: number[] = [];
  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      if (board[i][j] !== null) {
        values.push(board[i][j]!);
      }
    }
  }

  // Shuffle the values array
  for (let i = values.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [values[i], values[j]] = [values[j], values[i]];
  }

  // Place the shuffled values back into the board
  let valueIndex = 0;
  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      if (board[i][j] !== null) {
        newBoard[i][j] = values[valueIndex++];
      }
    }
  }

  return newBoard;
}
