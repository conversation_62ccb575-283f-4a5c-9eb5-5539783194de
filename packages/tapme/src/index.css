.counterpuzzle-container {
  --bg-color: #f4f4f4;
  --container-bg: white;
  --text-color: #333;
  --heading-color: #4a4a4a;
  --counter-color: #555;
  --button-bg: #4caf50;
  --button-hover-bg: #388e3c;
  --button-text: white;
  --button-secondary-bg: #e0e0e0;
  --button-secondary-hover-bg: #d0d0d0;
  --button-secondary-text: #333;
  --cell-bg: #e0e0e0;
  --cell-shadow: rgba(0, 0, 0, 0.1);
  --highlight-color: #ffeb3b;
  --progress-bar-bg: rgba(0, 0, 0, 0.1);
  --tooltip-bg: #333;
  --tooltip-corner: rgba(0, 0, 0, 0.8);
  --tooltip-text: #9f9f9f;
  --tooltip-title: #fff;

  &[data-theme="dark"] {
    --bg-color: #121212;
    --container-bg: #1e1e1e;
    --text-color: #e0e0e0;
    --heading-color: #f0f0f0;
    --counter-color: #b0b0b0;
    --button-bg: #2e7d32;
    --button-hover-bg: #1b5e20;
    --button-text: #e0e0e0;
    --button-secondary-bg: #424242;
    --button-secondary-hover-bg: #535353;
    --button-secondary-text: #e0e0e0;
    --cell-bg: #2a2a2a;
    --cell-shadow: rgba(0, 0, 0, 0.3);
    --highlight-color: #ffd600;
    --progress-bar-bg: rgba(172, 172, 172, 0.3);

    --tooltip-bg: #2d2d2d;
    --tooltip-corner: rgba(54, 54, 54, 0.8);
    --tooltip-text: #a0a0a0;
    --tooltip-title: #4caf50;
  }


  font-family: "Fjalla One",
  sans-serif;
  font-weight: normal;
  /* background-color: var(--container-bg);
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
  transition: background-color 0.3s ease; */
  margin-left: auto;
  margin-right: auto;
  position: relative;

  .counterpuzzle-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
  }

  .counterpuzzle-control-buttons {
    display: flex;
    gap: 15px;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    /* Sound button, Theme Switch button */
    button {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      padding: 0;
      opacity: 0.7;
      transition: opacity 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        opacity: 1;
      }

      &:focus {
        outline: none;
      }
    }
  }

  .counterpuzzle-inner-container {
    width: 100%;
    background-color: var(--container-bg);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    transition: background-color 0.3s ease;
  }

  .counterpuzzle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
  }

  /* Records */
  .counterpuzzle-records {
    opacity: 0.7;
    text-align: left;
    color: #555;
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 0;
  }

  /* Theme switch */
  .counterpuzzle-theme-switch {
    display: flex;
    align-items: center;
    margin-top: 2px;
  }

  /* Current Number & Score */
  .counterpuzzle-current-record {
    display: inline-flex;
    flex-direction: column;
    align-items: start;
    gap: 5px;
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
  }

  /* Clicks Left */
  .counterpuzzle-clicks-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    font-size: 18px;
  }

  .counterpuzzle-clicks-left-label {
    font-weight: bold;
    color: var(--counter-color);
    transition: color 0.3s ease;
  }

  .counterpuzzle-clicks-left-progress {
    width: 120px;
    height: 10px;
    background-color: var(--progress-bar-bg);
    border-radius: 5px;
    overflow: hidden;
  }

  .counterpuzzle-clicks-left-progress-bar {
    height: 100%;
    width: 100%;
    background-color: var(--button-bg);
    border-radius: 5px;
    transition: width 0.3s ease;
  }

  /* Game Board */
  .counterpuzzle-board {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 10px;
    width: 350px;
    height: 350px;
    margin: 0 auto 20px;
    position: relative;
    user-select: none;
  }

  .counterpuzzle-board-disabled .counterpuzzle-cell {
    cursor: not-allowed;
    pointer-events: none;
  }

  .counterpuzzle-cell {
    background-color: var(--cell-bg);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.05s ease-out, background-color 0.15s ease-out;
    box-shadow: 0 2px 5px var(--cell-shadow);
    will-change: transform, background-color;

    &:hover {
      transform: scale(1.05);
    }
  }

  @media (max-width: 480px) {
    .counterpuzzle-board {
      width: 300px;
      height: 300px;
      gap: 8px;
    }

    .counterpuzzle-cell {
      font-size: 20px;
    }
  }

  .counterpuzzle-cell-empty {
    background-color: transparent;
    box-shadow: none;
  }

  .counterpuzzle-cell-clone {
    position: fixed;
    z-index: 100;
    pointer-events: none;
    will-change: left, top;
    transition: left 0.1s ease-in-out, top 0.1s ease-in-out;
  }

  .counterpuzzle-cell-falling-clone {
    position: fixed;
    z-index: 100;
    pointer-events: none;
    will-change: top;
    transition: top 0.25s ease-in;
  }

  .counterpuzzle-cell-highlight {
    animation: counterpuzzlePulse 0.15s ease-in-out;
    background-color: var(--highlight-color);
    will-change: transform;
  }

  .counterpuzzle-cell-promoted {
    animation: counterpuzzlePulse 0.5s ease-in-out 3;
    background-color: var(--highlight-color);
    will-change: transform;
  }

  .counterpuzzle-cell-vanish {
    animation: counterpuzzleVanish 0.15s ease-in-out;
    will-change: transform, opacity;
  }

  /* new-connected only applies scale pulse animation when chaining  */
  .counterpuzzle-cell-new-connected {
    animation: counterpuzzleNew 0.4s ease-in-out;
    z-index: 5;
    will-change: transform;
  }

  /* add border for connected-group */
  &[data-theme="light"] .counterpuzzle-cell-connected-group {
    box-shadow: 0 0 6px rgba(255, 152, 0, 0.9);
    border: 1px solid rgba(253, 87, 34, 0.7);
  }

  &[data-theme="dark"] .counterpuzzle-cell-connected-group {
    box-shadow: 0 0 6px rgba(255, 235, 59, 0.7);
    border: 1px solid rgba(255, 235, 59, 0.9);
  }

  /* Hint effect for clickable cells */
  .counterpuzzle-cell-hint {
    animation: counterpuzzleHint 1.5s ease-in-out 3;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.8);
    z-index: 5;
    will-change: transform, box-shadow;
  }

  /* shuffle animation */
  .counterpuzzle-cell-shuffle-out {
    animation: counterpuzzleShuffleOut 0.3s ease-out forwards;
  }

  .counterpuzzle-cell-shuffle-in {
    animation: counterpuzzleShuffleIn 0.3s ease-out forwards;
  }


  /* cell color for 1-20 under light theme */
  .counterpuzzle-cell {
    &[data-value="1"] {
      background: linear-gradient(135deg, #fefcea, #f1da36);
      color: #5c4b00;
    }

    &[data-value="2"] {
      background: linear-gradient(135deg, #ffe5ec, #ffc2d1);
      color: #602030;
    }

    &[data-value="3"] {
      background: linear-gradient(135deg, #d0f4de, #a9def9);
      color: #1c3b40;
    }

    &[data-value="4"] {
      background: linear-gradient(135deg, #caffbf, #9bf6ff);
      color: #124247;
    }

    &[data-value="5"] {
      background: linear-gradient(135deg, #ffadad, #ffd6a5);
      color: #66342e;
    }

    &[data-value="6"] {
      background: linear-gradient(135deg, #ffd6a5, #fdffb6);
      color: #665013;
    }

    &[data-value="7"] {
      background: linear-gradient(135deg, #cdb4db, #ffc8dd);
      color: #4f2f55;
    }

    &[data-value="8"] {
      background: linear-gradient(135deg, #b5ead7, #c7ceea);
      color: #2b4a4a;
    }

    &[data-value="9"] {
      background: linear-gradient(135deg, #ff9aa2, #ffb7b2);
      color: #602a2e;
    }

    &[data-value="10"] {
      background: linear-gradient(135deg, #f6d186, #f6abb6);
      color: #5c3a33;
    }

    &[data-value="11"] {
      background: linear-gradient(135deg, #a0ced9, #f7d9d9);
      color: #2a474f;
    }

    &[data-value="12"] {
      background: linear-gradient(135deg, #fdffab, #ffc3a0);
      color: #554400;
    }

    &[data-value="13"] {
      background: linear-gradient(135deg, #c0fdfb, #e2f0cb);
      color: #2e4a3d;
    }

    &[data-value="14"] {
      background: linear-gradient(135deg, #b0efeb, #f8f3d4);
      color: #33534f;
    }

    &[data-value="15"] {
      background: linear-gradient(135deg, #f3c5ff, #c2f0fc);
      color: #4c3263;
    }

    &[data-value="16"] {
      background: linear-gradient(135deg, #e4c1f9, #a9def9);
      color: #3b3b66;
    }

    &[data-value="17"] {
      background: linear-gradient(135deg, #f7b7a3, #fcd5ce);
      color: #5b3530;
    }

    &[data-value="18"] {
      background: linear-gradient(135deg, #a3c4f3, #b5ead7);
      color: #2e3f66;
    }

    &[data-value="19"] {
      background: linear-gradient(135deg, #fbc4ab, #ffdab9);
      color: #663e2f;
    }

    &[data-value="20"] {
      background: linear-gradient(135deg, #ffafcc, #cdb4db);
      color: #5b2b44;
    }
  }

  /* Score popup: +X*/
  .counterpuzzle-score-popup {
    position: fixed;
    font-size: 1.5rem;
    font-weight: bold;
    color: #4caf50;
    z-index: 200;
    pointer-events: none;
    opacity: 1;
    transition: transform 1s ease-out,
      opacity 1s ease-out;
    will-change: transform, opacity;
    display: flex;
    flex-direction: column;
    align-items: center;

    &.counterpuzzle-fade-up {
      transform: translateY(-50px);
      opacity: 0;
    }

    /* Combo multiplier styling */
    &.counterpuzzle-combo-popup {
      color: #ff9800;
      /* Orange color for combo popups */
    }

    .counterpuzzle-combo-multiplier {
      font-size: 1rem;
      color: #ff5722;
      /* Deeper orange for multiplier */
      margin-top: -5px;
      animation: counterpuzzleComboFlash 0.5s ease-in-out infinite alternate;
    }
  }

  /* GameTools: Shuffle,undo,promote + Restart  */
  .counterpuzzle-tools {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .counterpuzzle-powerup-item-container {
    display: flex;
    flex-direction: row;
    gap: 6px;
  }

  .counterpuzzle-powerup-item {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 5px;
    background-color: var(--button-bg);
    color: var(--button-text);
    transition: transform 0.2s, background-color 0.3s;

    &:hover:not(.item-disabled) {
      transform: scale(1.1);
      background-color: var(--button-hover-bg);
    }

    &.item-disabled {
      cursor: not-allowed;
    }

    &.item-not-available {
      opacity: 0.5;
    }

    &.item-tapped-once {
      transform: scale(1.1);
      background-color: var(--button-hover-bg);
      box-shadow: 0 0 8px var(--highlight-color);
      animation: counterpuzzlePulse 1s infinite;
    }
  }

  .counterpuzzle-powerup-item-counter {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    gap: 2px;
  }

  .counterpuzzle-powerup-item-counter-one {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ccc;
    opacity: 0.5;
    transition: background-color 0.3s ease, opacity 0.3s ease;
    will-change: background-color;

    &.counter-active {
      opacity: 1;
      background-color: var(--button-bg);
    }
  }

  /* Restart button */
  .counterpuzzle-restart-btn {
    font-family: inherit;
    padding: 10px 20px;
    background-color: var(--button-bg);
    color: var(--button-text);
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    will-change: background-color;

    &:hover {
      background-color: var(--button-hover-bg);
    }
  }

  /* Dialog */

  .counterpuzzle-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    will-change: opacity, visibility;

    &.counterpuzzle-dialog-visible {
      opacity: 1;
      visibility: visible;

      .counterpuzzle-dialog {
        transform: translateY(0);
      }
    }
  }

  .counterpuzzle-dialog {
    background-color: var(--container-bg);
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    width: 300px;
    max-width: 90%;
    padding: 20px;
    text-align: center;
    transform: translateY(-20px);
    transition: transform 0.3s;
    will-change: transform;
  }

  .counterpuzzle-dialog-content {
    margin-bottom: 25px;
  }

  .counterpuzzle-dialog-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: var(--heading-color);
  }

  .counterpuzzle-dialog-message {
    font-size: 14px;
    margin-bottom: 20px;
    color: var(--text-color);
    line-height: 1.5;
  }

  .counterpuzzle-dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
  }

  .counterpuzzle-dialog-cancel {
    padding: 10px 20px;
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
    font-size: 14px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    will-change: background-color;

    &:hover {
      background-color: var(--button-secondary-hover-bg);
    }
  }

  .counterpuzzle-dialog-confirm {
    padding: 10px 20px;
    background-color: var(--button-bg);
    color: var(--button-text);
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    will-change: background-color;

    &:hover {
      background-color: var(--button-hover-bg);
    }
  }

  .counterpuzzle-current-result {
    margin: 30px 0;
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-color);
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .counterpuzzle-current-result-number {
    font-weight: bold;
    color: var(--button-bg);
  }

  .counterpuzzle-congratulation {
    text-align: center;
    color: #ff9800;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 25px 0;
    animation: counterpuzzlePulse 1s infinite alternate;
  }

  .counterpuzzle-current-record-number {
    font-weight: bold;
    color: #ff9800;
  }

  .counterpuzzle-dialog-unused-powerup .counterpuzzle-dialog-buttons {
    flex-direction: column;
  }

  /* fix social share component styles */
  .counterpuzzle-dialog-content .social-share-btns {
    width: 100%;
    box-shadow: none;
  }

  .counterpuzzle-dialog-content .social-share-btn {
    border: none;
    color: #fff;
  }

  /* Tooltip styles */
  .counterpuzzle-tooltip-container {
    position: relative;
    display: inline-block;
  }

  .counterpuzzle-tooltip {
    width: 170px;
    position: absolute;
    background-color: var(--tooltip-bg);
    color: var(--tooltip-text);
    padding: 5px;
    border-radius: 6px;
    font-size: 14px;
    text-align: left;
    z-index: 1000;
    pointer-events: none;
    animation: tooltipFadeIn 0.2s ease-in-out;

    &::after {
      content: '';
      position: absolute;
      border-width: 5px;
      border-style: solid;
    }
  }

  .counterpuzzle-tooltip-top::after {
    border-color: var(--tooltip-corner) transparent transparent transparent;
    top: 100%;
    left: 50%;
    margin-left: -5px;
  }

  .counterpuzzle-tooltip-top-left::after {
    border-color: var(--tooltip-corner) transparent transparent transparent;
    top: 100%;
    left: 87%;
    margin-left: -5px;
  }

  .counterpuzzle-tooltip-top-right::after {
    border-color: var(--tooltip-corner) transparent transparent transparent;
    top: 100%;
    right: 83%;
    margin-left: -5px;
  }

  .counterpuzzle-tooltip-bottom::after {
    border-color: transparent transparent var(--tooltip-corner) transparent;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
  }

  .counterpuzzle-tooltip-left::after {
    border-color: transparent transparent transparent var(--tooltip-corner);
    top: 50%;
    left: 100%;
    margin-top: -5px;
  }

  .counterpuzzle-tooltip-right::after {
    border-color: transparent var(--tooltip-corner) transparent transparent;
    top: 50%;
    right: 100%;
    margin-top: -5px;
  }

  .counterpuzzle-tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px
  }

  .counterpuzzle-tooltip-title {
    font-weight: bold;
    color: var(--tooltip-title);
  }

  .counterpuzzle-tooltip-content {
    font-size: 13px;
  }

  .counterpuzzle-tooltip-extra-content {
    margin-top: 5px;
    font-size: 13px;
    color: #ffeb3b;
  }

  .counterpuzzle-tooltip-tap-again {
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    text-align: center;
  }
}

@keyframes counterpuzzlePulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

@keyframes counterpuzzleShuffleIn {
  0% {
    transform: scale(0.8) rotate(-10deg);
    opacity: 0;
  }

  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes counterpuzzleShuffleOut {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }

  100% {
    transform: scale(0.8) rotate(10deg);
    opacity: 0;
  }
}

@keyframes counterpuzzleVanish {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0);
    opacity: 0;
  }
}

@keyframes counterpuzzleHint {
  0% {
    transform: scale(1);
    box-shadow: 0 0 4px rgba(33, 150, 243, 0.4);
  }

  50% {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(33, 150, 243, 0.8);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 4px rgba(33, 150, 243, 0.4);
  }
}

@keyframes counterpuzzleNew {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes counterpuzzleComboFlash {
  0% {
    opacity: 0.7;
    transform: scale(0.95);
  }

  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
