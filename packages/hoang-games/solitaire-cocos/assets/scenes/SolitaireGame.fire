[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "2913ec0b-fe2e-4fac-bed9-222c79be09c8"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}, {"__id__": 183}], "_active": true, "_components": [{"__id__": 189}, {"__id__": 190}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "01FoM/o9FEpYGrziSXMGnj"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 760}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 250.28134169370279, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cbin4CGUBDvpEZJYRCSqWK"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 0, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "9cbyo5jxNDJJTJkYXW28Ls"}, {"__type__": "5e0740n6tRBDo1Dg9KYej0X", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "eventName": "shake-camera", "_id": "17m+/2FxBHg6NpNzvzNgP/"}, {"__type__": "cc.Node", "_name": "Gameplay", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}, {"__id__": 10}, {"__id__": 48}, {"__id__": 72}, {"__id__": 74}, {"__id__": 84}, {"__id__": 106}, {"__id__": 171}], "_active": true, "_components": [{"__id__": 182}, {"__id__": 186}, {"__id__": 187}, {"__id__": 188}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6e68/+4MBKtY2iLX3yIYzz"}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c0TCvzbfNEC54E5BqQQzwh"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e6c58b8b-8e53-4325-b8b5-44587fe2a9f3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "525TuLkeFCQo+CDJ99S7UO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "02NCwEzdZEu4pQDTSWXfOU"}, {"__type__": "cc.Node", "_name": "FinalStacks", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 11}, {"__id__": 16}, {"__id__": 21}, {"__id__": 26}, {"__id__": 31}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 968.4999999999999, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 218.793, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "47Z6qHD+5GU6X+RlBXLmrd"}, {"__type__": "cc.Node", "_name": "col_1", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}], "_active": true, "_components": [{"__id__": 14}, {"__id__": 15}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 139}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-27.80000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "39uWzs/aVDMriC02GU18rJ"}, {"__type__": "cc.Node", "_name": "boder_highlight", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": false, "_components": [{"__id__": 13}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e65ttYqNlN6Y10NeEj1PRd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29277900-f23b-420d-896d-ebb343d7f6e3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b8b3RGh/tB+azLkkuB4Jqq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e9a4cd6-8d5a-4162-8376-9b1f318b06ca"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "06ar3ewgFFSrrwaBqrOJeK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "01jguaB8hMaode8IjhWyoZ"}, {"__type__": "cc.Node", "_name": "col_2", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 17}], "_active": true, "_components": [{"__id__": 19}, {"__id__": 20}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 139}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [122.49999999999996, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "45nmFkQMVIHq633YUbS6wI"}, {"__type__": "cc.Node", "_name": "boder_highlight", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 18}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "49wYN8TBdOXKNuhhQjhGwP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29277900-f23b-420d-896d-ebb343d7f6e3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "661FlW1iFLTII3lRLPcvDy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e9a4cd6-8d5a-4162-8376-9b1f318b06ca"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "38SDjRgJZOW7KxtwJG8H0U"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "a8WSTjZgpIvq6JxWM5/CoX"}, {"__type__": "cc.Node", "_name": "col_3", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 22}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 139}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [272.79999999999995, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "67gb4B/mZFWbA4pZe8Uuio"}, {"__type__": "cc.Node", "_name": "boder_highlight", "_objFlags": 0, "_parent": {"__id__": 21}, "_children": [], "_active": false, "_components": [{"__id__": 23}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "05WXSucJ5FiaRDb9kibpJa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29277900-f23b-420d-896d-ebb343d7f6e3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "08J8f74glL/4mTwD8j++Uy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e9a4cd6-8d5a-4162-8376-9b1f318b06ca"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4754OJX8FB6q3aFbkR4knY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "77QAWoCulAMKNl2UfUJ1k9"}, {"__type__": "cc.Node", "_name": "col_4", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 27}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 139}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [423.0999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "59ccW061NEephWSUIsabLt"}, {"__type__": "cc.Node", "_name": "boder_highlight", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": false, "_components": [{"__id__": 28}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e7dFgrzjBOIKZRRqBkvBhL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29277900-f23b-420d-896d-ebb343d7f6e3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "d7AZADDp1L4bd7bXd/ZGJW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e9a4cd6-8d5a-4162-8376-9b1f318b06ca"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dc38ARC9lMJpMqTCm/z+C6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "89UFGS+35M5oN+9RnQ4SRn"}, {"__type__": "cc.Node", "_name": "col_6", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 32}, {"__id__": 37}], "_active": true, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 139}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-423.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "98tDKl4kpCCb1eI7pIPVhE"}, {"__type__": "cc.Node", "_name": "refresh", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [{"__id__": 33}], "_active": true, "_components": [{"__id__": 35}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 81.5, "height": 119.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.666, 0.9, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d80NjS4aNEpLU5iWxI+osW"}, {"__type__": "cc.Node", "_name": "empty_card", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 116}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3di2n8KlJDKLf74/BBoxJZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1e5f2ba5-998c-492d-b8d6-2b9e161393cd"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2eclUdiAdPTIV0d22gqyun"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.02, "clickEvents": [{"__id__": 36}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": "c6odFqNC1Fo6Kv7oT7e9Pb"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "7d50ezs4TpNbqWv2ZmzvaOX", "handler": "resetCards", "customEventData": ""}, {"__type__": "cc.Node", "_name": "boder_highlight", "_objFlags": 0, "_parent": {"__id__": 31}, "_children": [], "_active": false, "_components": [{"__id__": 38}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 155}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "07cf2VaDdDn7bM3ckyJ2XN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "29277900-f23b-420d-896d-ebb343d7f6e3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "68MKhRmD9D8rMn0Zg3cTh8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7e9a4cd6-8d5a-4162-8376-9b1f318b06ca"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "43KhmLERZB7YnzQJ7YF994"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "6bZiPtmDhFIJvyvBA9F73K"}, {"__type__": "cc.Node", "_name": "col_5", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 42}], "_active": true, "_components": [{"__id__": 45}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 205, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-225.45000000000005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e4XnFxJPtHA7Ew14oR9UoS"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_active": false, "_components": [{"__id__": 43}, {"__id__": 44}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 175, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2c+/SZSgNKJ4UfO4WvY8JO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3brrxfPs5Dxa5ZoLGsFQ4e"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 20, "_right": 10, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_id": "a6Qvnsm5xC57+nXW6luIWU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 0, "_left": 0, "_right": 0, "_top": -25, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "2eyTbscKJK1pdoH0E5Mzpp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 295.75000000000006, "_right": 295.75000000000006, "_top": 66.20700000000002, "_bottom": 700, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 280, "_id": "f6SjluBzBD8JgGxCj2IilV"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 968.4999999999999, "height": 150}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 6, "_N$paddingRight": 6, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 40, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "2dhi3kr89M3q0xHQfzAe3o"}, {"__type__": "cc.Node", "_name": "TableauStacks", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 49}, {"__id__": 52}, {"__id__": 55}, {"__id__": 58}, {"__id__": 61}, {"__id__": 64}, {"__id__": 67}], "_active": true, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1095.5, "height": 519.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -141.70700000000005, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d3y4WkacJL5q68AxHc9uMW"}, {"__type__": "cc.Node", "_name": "col_1", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 51}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-481.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "eeArRUEVJGe5xCd/stlECE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c11Cn/W69O7rCsb2hkM1Se"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "25P+kjkkVJ4rHvTBmM5etw"}, {"__type__": "cc.Node", "_name": "col_2", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-321, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bfwHBRbr9HRa+zaNHFw9Nc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "7f/UKpfOVDTo2BKjwwQGla"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "06DKX9RhtPTJqrhJZNZknb"}, {"__type__": "cc.Node", "_name": "col_3", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 56}, {"__id__": 57}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "96aBd4DjNEuZn4QII4IORu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f6YNghFuhLIozIAHLmlyWE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "3b5RZDgYlDQIvXB4joPwqq"}, {"__type__": "cc.Node", "_name": "col_4", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5aRlJlX3NK6pTVekRzNRyK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "70ocAmnQxEMLAwSCyAhM5E"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "df0m8+kiJH8I3j1YVBstCK"}, {"__type__": "cc.Node", "_name": "col_5", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "abQhEYUnRBqpT0Sz2lF3GT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "5agPRx4sBCcKJsG9Kw0D2a"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "76/WOi1EdG5pDU5QtS+mo8"}, {"__type__": "cc.Node", "_name": "col_6", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [321, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c9FBUKOetDnLglBio2NiQo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "4bZMkR6X9Eq45coMojlauy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "cf8PsqMfpDVZjKbkZriZNA"}, {"__type__": "cc.Node", "_name": "col_7", "_objFlags": 0, "_parent": {"__id__": 48}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 120.5, "height": 160.75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [481.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "60UWJD3NtJ3qi08uM58uN5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d855584c-4e45-410f-b4f0-e41a769b5e1e"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "06IoccqAdNSYQDeKaG4LIV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 2.5, "_bottom": -25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 200, "_id": "b4HTXIGjpOLZjoK1cMAUcp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 44, "_left": 232.25, "_right": 232.25, "_top": 141.909, "_bottom": -41.40700000000001, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "acGhBD57JOK5JhaetTEM5M"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 1095.5, "height": 519.4}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 6, "_N$paddingRight": 6, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 40, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "24Dp824BpD7rP41y0UWLAG"}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 73}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "879YzT4sFL5rBh46qsp9cU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "2be70knidCfYLLf1hr+UXT"}, {"__type__": "cc.Node", "_name": "SolvedBtn", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 75}, {"__id__": 80}], "_active": false, "_components": [{"__id__": 82}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 57.3, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-312.524, 222.082, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "57UIKVON9B/pPE8RQMjL4x"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 74}, "_children": [{"__id__": 76}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 79}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 57.3, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6d5IZiie5PQa5IfyKm6sc7"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 46.3, "height": 25.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.285, -12.301, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6dRZuPa6xGhb6zrlo0ARij"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Solved", "_N$string": "Solved", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 2, "_N$cacheMode": 1, "_id": "24JaMdNa5BgqbuOrMiHKzC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "58f55ba6-9c08-477a-ac16-c111c2ab3f77"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dal2QQG1dNxo29JzrlT/Lg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "b8xAWs7BVGhrWJLtnr6N3r"}, {"__type__": "cc.Node", "_name": "icon_tick", "_objFlags": 512, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 81}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.279, 9.399, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4bGkCjUi1DWIs8zo8iUb8y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a11e1fd7-7f6f-4e51-bdc3-075042d01603"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "cctD8PcP9HIr/79nGJhusB"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 83}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "58f55ba6-9c08-477a-ac16-c111c2ab3f77"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 75}, "_id": "ceR8UHDtdAjJh8fzon8/By"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "7d50ezs4TpNbqWv2ZmzvaOX", "handler": "onSolved", "customEventData": ""}, {"__type__": "cc.Node", "_name": "BannerScore", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 85}, {"__id__": 91}, {"__id__": 97}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 104}, {"__id__": 105}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 338, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0ct8TEoMtH5KccZEEGvA8T"}, {"__type__": "cc.Node", "_name": "HighScore", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 86}, {"__id__": 88}], "_active": true, "_components": [{"__id__": 90}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140.82, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-337.263, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d9l0Irg6pL3aDxEF55WCmE"}, {"__type__": "cc.Node", "_name": "highscore_64_update", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 87}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20.999, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e7AGbIitJH6ZNEtcre5deD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "11160426-1c48-48ad-a484-5f5b8f3a680d"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "abpWjTYl9ImZ68uz2oRakF"}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_active": true, "_components": [{"__id__": 89}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 236, "g": 255, "b": 14, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [147.987, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9fj16cbiNJzpa1t5UlTwxX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "e8vn1W3gRO2LiXjEOJeZwk"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "High Score:", "_N$string": "High Score:", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "ca6oeRYcRBOphdEyEPupdX"}, {"__type__": "cc.Node", "_name": "Score", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 92}, {"__id__": 94}], "_active": true, "_components": [{"__id__": 96}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 75.32, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11.634, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2d+iL8kkZDhKM9bQ3SPjHe"}, {"__type__": "cc.Node", "_name": "star_3", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 93}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-21.687, -1.098, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -5}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bbeVM65CxJUpaozQjdiNxa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f2a6d8d1-92d2-4fcc-a42c-457561fd3a26"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0aCTsq6CBCEYvUC4DksOe7"}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 95}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 236, "g": 255, "b": 14, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [81.923, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "adCYO4+GVBHITsX/a9QLii"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "a2zhSuKOxP7pdMGoYQFtbr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Score:", "_N$string": "Score:", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "d3Ggq+8x5CA5mXFkNl7R8p"}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 98}, {"__id__": 100}], "_active": true, "_components": [{"__id__": 102}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 66.42, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [270.091, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "506jpBLEpChbUEQ4bOBNWE"}, {"__type__": "cc.Node", "_name": "clock_33", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 99}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-17.587, -0.601, 0, 0, 0, -0.06104853953485687, 0.9981347984218669, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -7}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cdPh/dqP1DzYHwubm1wPKp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0bcce177-7cad-4f7e-b9bb-71423c8342ba"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "79gEGgDjVLgry/x8wkJmlg"}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 97}, "_children": [], "_active": true, "_components": [{"__id__": 101}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 236, "g": 255, "b": 14, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 15.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [72.145, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3bJSWU0dpHGrvvulIIzbab"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "8agi6xIMBMyYzVAc6HWEyQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Time:", "_N$string": "Time:", "_fontSize": 25, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "24pcCcTFxGH45wEXQ7ICXZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "46f5d06a-e545-4bbd-92f3-22326e6d2a67"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "fePNnzw+5BYqTrhTjb+Fld"}, {"__type__": "57ac4SQLsZIrIQBu5aHJob8", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "highScore": {"__id__": 89}, "score": {"__id__": 95}, "timer": {"__id__": 101}, "_id": "93u/dUu3JJk7Yayw6SEWYq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 41, "_left": 0, "_right": 0, "_top": -4.5, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1560, "_originalHeight": 0, "_id": "51uKWf4vFEK4DvUGJIu10o"}, {"__type__": "cc.Node", "_name": "MenuGame", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 107}, {"__id__": 136}, {"__id__": 147}, {"__id__": 158}], "_active": true, "_components": [{"__id__": 169}, {"__id__": 170}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1biEi77bZL86FbUtxlQaeB"}, {"__type__": "cc.Node", "_name": "layoutSetting", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 108}, {"__id__": 116}, {"__id__": 125}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}, {"__id__": 135}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 88.2, "height": 402.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4828634539268461, "y": 0.5018120890756209}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [734.3885566363479, -161.4, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0dDh+uXt9E0olMIIlOGVBR"}, {"__type__": "cc.Node", "_name": "gameRuleBtn", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [{"__id__": 109}], "_active": true, "_components": [{"__id__": 114}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.49444981000549204, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.416, -57.963, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "56bdgYZcxKPIhhRa2DyKfu"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 108}, "_children": [{"__id__": 110}], "_active": true, "_components": [{"__id__": 112}, {"__id__": 113}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.2053570297967937, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bbxJZevARKLoSOo7c5JsWL"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54.58, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -38.962, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7f3t6xuwdIWqjRMF8U7ypG"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Rules", "_N$string": "Rules", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "e1d2e9fd-3131-43b1-b9d3-ee4c5e4876c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 2, "_id": "25/xmYiulBPK/rGpvZ18xo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bd4cce52-d544-4c0d-8efb-a413cfc01331"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "0esKHat29NBLVrpeGOh/j2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 0.5, "_top": 0.5, "_bottom": 0.5, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "665GwvlZpBbaJXlk9Uqjby"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 115}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 109}, "_id": "54Tl9toq9MNpxqkeyBkDU0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 106}, "component": "", "_componentId": "147b6fFzCdNhL4rC3VJZ1og", "handler": "openGameRule", "customEventData": ""}, {"__type__": "cc.Node", "_name": "btnSound", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [{"__id__": 117}, {"__id__": 119}, {"__id__": 121}], "_active": true, "_components": [{"__id__": 123}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.539, 45.289, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "06hHsbhz1A9pksuFWxbky/"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 116}, "_children": [], "_active": false, "_components": [{"__id__": 118}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "7fMiE5oGRDNr3Wb8qlRNjW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 117}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0d252c55-4085-44cf-8c5f-0ac168667b61"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "62VMO325RCvrtuJLhNJ+hI"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 120}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "caILqbASNEOJVgD7uiWd86"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "62322061-886d-48a8-9629-fed46e792603"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "063wXQpCNFhK3veDDvdrmI"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 122}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64.12, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.6, -38.455, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "12e8BeTeZBj4kiHqlxjzW0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Sound", "_N$string": "Sound", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "e1d2e9fd-3131-43b1-b9d3-ee4c5e4876c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 2, "_id": "606GqqSdZBcLMlLIPFvOC6"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": {"__uuid__": "0d252c55-4085-44cf-8c5f-0ac168667b61"}, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 117}, "_N$isChecked": true, "toggleGroup": null, "checkMark": {"__id__": 120}, "checkEvents": [{"__id__": 124}], "_id": "2c/PqjiHxBzLs0/O5+cU85"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 106}, "component": "", "_componentId": "147b6fFzCdNhL4rC3VJZ1og", "handler": "onSoundToggleCheck", "customEventData": ""}, {"__type__": "cc.Node", "_name": "btnNew", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [{"__id__": 126}], "_active": true, "_components": [{"__id__": 131}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 48.6, "height": 61.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.721, 153.414, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d5kN22cepM46FZX8JmrFAt"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 125}, "_children": [{"__id__": 127}], "_active": true, "_components": [{"__id__": 129}, {"__id__": 130}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 4.937000000000005, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "00EUZ6xAJOibuVrNi9KSjw"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": true, "_components": [{"__id__": 128}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 44.34, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.129, -42.999, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "43LO0LjGlO/L6+OudMGqzC"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 127}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "New", "_N$string": "New", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "e1d2e9fd-3131-43b1-b9d3-ee4c5e4876c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": "27hqvPgSBABIiViXI6yVQz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d5e8644-c5aa-4fbf-b5cb-4c3d0702c2b2"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "69YzK7YaROOazTWSnOqNfg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": -3.6999999999999993, "_right": -3.6999999999999993, "_top": -2.3370000000000033, "_bottom": 7.537000000000006, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "94agpXXw5FS6KzQJZ6q5L6"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 125}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 132}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 126}, "_id": "51cmEiD5RA6Z07NubwSJzb"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 106}, "component": "", "_componentId": "147b6fFzCdNhL4rC3VJZ1og", "handler": "openNewPlayKlondike", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1a66eca6-ee52-45b1-ba6e-8df23f9ef6bf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "665FD7npxIm6TMETu9d9lq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 36, "_left": 0, "_right": 0, "_top": 0, "_bottom": -3.1786410173072, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "e3o1yD4KNLdb0PtgnUEHhM"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 973, "height": 92}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 180, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "70cX0S/dBAfrQFM23IxcVS"}, {"__type__": "cc.Node", "_name": "btnMore", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 137}, {"__id__": 142}], "_active": true, "_components": [{"__id__": 144}, {"__id__": 146}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 87.6, "height": 77}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [736.2, -322.3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d6vO5ZlSpMHYFYv5t1DWpP"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 136}, "_children": [{"__id__": 138}], "_active": true, "_components": [{"__id__": 140}, {"__id__": 141}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 87.6, "height": 77}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b4uTiibX5FCo3kzCPcYL1p"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 137}, "_children": [], "_active": false, "_components": [{"__id__": 139}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "43fTbYA4FLLZkW7ubXTqA7"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "button", "_N$string": "button", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "87NzR4G9pDlq3BQV+B+mgr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "58f55ba6-9c08-477a-ac16-c111c2ab3f77"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "bdpJM9zjxJ3J1HlCyDOIVf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "21Xkhv0DpJ4Y5jBPpX8iSR"}, {"__type__": "cc.Node", "_name": "iconMore", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 143}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 43.12, "height": 21.56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8eFKaH2PVOWL1N1aSCHTdB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b9b1f9b3-3614-410c-b8b4-53518f0fcdd2"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "68S2qmir1OXqrIv6B3aOwg"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 145}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 137}, "_id": "1cRaf0R/tKi4njtQJCj+xG"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 106}, "component": "", "_componentId": "147b6fFzCdNhL4rC3VJZ1og", "handler": "openMenu", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 36, "_left": 0, "_right": 0, "_top": 0, "_bottom": -0.8000000000000114, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "5bVxHgfZlNhKMy8D6a6dfq"}, {"__type__": "cc.Node", "_name": "btnHint", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 148}, {"__id__": 150}], "_active": true, "_components": [{"__id__": 155}, {"__id__": 157}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-733.5, 265.504, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fcdn+pnwJN7amjXgWSQ+KB"}, {"__type__": "cc.Node", "_name": "banner_bot", "_objFlags": 512, "_parent": {"__id__": 147}, "_children": [], "_active": false, "_components": [{"__id__": 149}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91.1, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "96Za6XjCpCCJl1u70ojtpP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1a66eca6-ee52-45b1-ba6e-8df23f9ef6bf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2cpv5DxgVOk7rGHpj+v4Qs"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 147}, "_children": [{"__id__": 151}], "_active": true, "_components": [{"__id__": 153}, {"__id__": 154}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 11.744, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f2t+J/d2lMkIYVAewlg8R8"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 150}, "_children": [], "_active": true, "_components": [{"__id__": 152}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 41.18, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.6, -38.284, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0biNT5DIZALJoGMV8TUWcW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 151}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Hint", "_N$string": "Hint", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": "44CGqxnapJg5p2uQQN6MPk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "66583c01-15ac-477b-a38d-4d3c60d8442f"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "8dLnxJovJAaqz5igV94SAH"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 150}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 30.5, "_right": 30.5, "_top": 11.756, "_bottom": 35.244, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "f8NUqxsjZLnZIkCiw6PPTg"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 156}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 150}, "_id": "b2BAgu0i5ITYcr9prdR0+L"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "7d50ezs4TpNbqWv2ZmzvaOX", "handler": "showHint", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 9, "_left": 1, "_right": 0, "_top": 48.99599999999998, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "22GhNUVVtAcJsUoqJpH7U7"}, {"__type__": "cc.Node", "_name": "btnUndo", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 159}, {"__id__": 161}], "_active": true, "_components": [{"__id__": 166}, {"__id__": 168}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [734.5, 265.523, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b2Ye8YNLNDJah0dnHJFdaV"}, {"__type__": "cc.Node", "_name": "banner_bot", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [], "_active": false, "_components": [{"__id__": 160}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 91.1, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.2, 0.384, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edu8nTR/hDNb99C1ogfB9F"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "1a66eca6-ee52-45b1-ba6e-8df23f9ef6bf"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "b23ASFdbtJeZpNsTLtcRoy"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 158}, "_children": [{"__id__": 162}], "_active": true, "_components": [{"__id__": 164}, {"__id__": 165}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.37400000000000233, 10.79, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "afz00NIvZA0rFa7BgoLRZN"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 161}, "_children": [], "_active": true, "_components": [{"__id__": 163}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 52.46, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.6, -36.489, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "efmxDDBUdAFLgA98TsglDX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 162}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Undo", "_N$string": "Undo", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": {"__uuid__": "1206669b-e68e-4fb7-9a43-d671b32275c1"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": "658uy4yLFLW6c4Pv/Bw1V1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0b454db6-1605-4d1a-bd51-362d8a171b54"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "09MolJquJKAY6Z66Z0hQOy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 161}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 22.874000000000002, "_right": 22.125999999999998, "_top": 14.21, "_bottom": 35.79, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "43cCse6EBJIKfZr/GDzgwc"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "_normalMaterial": {"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}, "_grayMaterial": {"__uuid__": "3a7bb79f-32fd-422e-ada2-96f518fed422"}, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 167}], "_N$interactable": true, "_N$enableAutoGrayEffect": true, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": null, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 161}, "_id": "1b6BD2pS5AGbH+WfLWahnM"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 6}, "component": "", "_componentId": "7d50ezs4TpNbqWv2ZmzvaOX", "handler": "undo", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 0, "_top": 48.476999999999975, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "fa/VQG5lxFX5E1CoI5e1ct"}, {"__type__": "147b6fFzCdNhL4rC3VJZ1og", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "btnMore": {"__id__": 136}, "btnHint": {"__id__": 147}, "btnUndo": {"__id__": 158}, "layoutButton": {"__id__": 107}, "layoutLeft": null, "soundToggle": {"__id__": 123}, "_id": "d0n+BrhApKAItUiomgJLED"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": "85VUPoDttDE7dkBuwSxSND"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 172}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}], "_active": true, "_components": [{"__id__": 180}, {"__id__": 181}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edQXqtuAZGdJ3XW/xZpBLp"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 171}, "_prefab": {"__id__": 173}, "_name": "SelectModeGamePopup", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11p+GhD3lOoKWFeI+jmzya"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 172}, "asset": {"__uuid__": "a5519649-53f9-4aa6-a9f0-21d913d3dd84"}, "fileId": "", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 171}, "_prefab": {"__id__": 175}, "_name": "NotificationPopup", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c5EnQUZUpG/5EakSLP3hmX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 174}, "asset": {"__uuid__": "f22f93a8-fcbf-4e85-ac2a-7130f5aef2f5"}, "fileId": "", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 171}, "_prefab": {"__id__": 177}, "_name": "GameRule", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d7ot+KLVdHsrQC9sqWKuST"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 176}, "asset": {"__uuid__": "1fec3de1-6aeb-4c91-9b69-855aee640786"}, "fileId": "", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 171}, "_prefab": {"__id__": 179}, "_name": "ResultPopup", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7ebuRdb8xOlLD0gUcVajLS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 178}, "asset": {"__uuid__": "22b5fc7a-ecbd-40ac-b813-6e789ffb000b"}, "fileId": "", "sync": true}, {"__type__": "6f25d/atJdJnKdZPn1S43YK", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "resultPopup": {"__id__": 178}, "gameRule": {"__id__": 176}, "selectMode": {"__id__": 172}, "notificationPopup": {"__id__": 174}, "informationPopup": null, "_id": "ee++Xhb4VMPrzRlQYGU6F/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 171}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "b0CnzxK95I24AKLcgr4c8u"}, {"__type__": "7d50ezs4TpNbqWv2ZmzvaOX", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "finalStacks": {"__id__": 10}, "tableauStacks": {"__id__": 48}, "container": {"__id__": 72}, "cardPrefab": {"__uuid__": "*************-4cfc-9003-2c28704a55d1"}, "boardScore": {"__id__": 84}, "blockTouch": {"__id__": 183}, "btnSolved": {"__id__": 74}, "btnUndo": {"__id__": 166}, "_id": "db/9ZLOfJFv4G2z1wJZQ49"}, {"__type__": "cc.Node", "_name": "BlockInput", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 184}, {"__id__": 185}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "907skVjDJBh5NfEl2K7Xvc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": "7d7p6pBl1ED4wcTC0WJDTl"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 183}, "_enabled": true, "_id": "9czi4s0vlPyLFkd4S6GwqG"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": "03KnM8v8RH/ItRNkj7P+rk"}, {"__type__": "e465cQRkatGQYPqV+2UHdun", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "followCanvasNodes": [{"__id__": 6}], "_id": "73Cp7GoQBMlq6ys5cZoP2x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1560, "_originalHeight": 720, "_id": "90MWe1IOFFabvQ+3Ba/I7r"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1560, "height": 720}, "_fitWidth": false, "_fitHeight": true, "_id": "807dlIgjpGVIKsRXmM+hAi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "c6SNvhrnhAHYC8KWAknuu1"}]