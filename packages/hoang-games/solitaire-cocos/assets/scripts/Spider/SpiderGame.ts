import PopupManager from "../UI/Popup/PopupManager";
import BoardScore from "../UI/menu/BoardScore";
import { EventType, Events } from "../events/Events";
import SoundManager from "../sounds/SoundManager";
import { ResultData, SuitMode } from "../utils/Gameinterface";
import Utils from "../utils/Utils";
import CardSpider from "./CardSpider";

const { ccclass, property } = cc._decorator;

const enum Stacks {
  A1,
  A2,
  A3,
  A4,
  A5,
}

const COLUMNS = 10;
const DOUBLE_CLICK_TIME = 0.6;
const KEY_HIGH_SCORE_GAME = "Spider_HighScore";
@ccclass
export default class SpiderGame extends cc.Component {
  @property(cc.Node)
  winStacks: cc.Node = null;

  @property(cc.Node)
  deckStacks: cc.Node = null;

  @property(cc.Node)
  tableauStacks: cc.Node = null;

  @property(cc.Node)
  container: cc.Node = null;

  @property(cc.Prefab)
  cardPrefab: cc.Prefab = null;

  @property(cc.Node)
  boardScore: cc.Node = null;

  @property(cc.Node)
  blockTouch: cc.Node = null;

  @property(cc.Node)
  btnPowerUp: cc.Node = null;

  @property(cc.Node)
  highlightStacks: cc.Node = null;

  @property([cc.SpriteFrame])
  powerSprite: cc.SpriteFrame[] = [];

  @property(cc.Sprite)
  btnUndo: cc.Sprite = null;

  @property([cc.SpriteFrame])
  undoSprite: cc.SpriteFrame[] = [];

  private undoStacks = [];
  private deckCards: Array<cc.Node> = [];
  private drawedCardStacks: Array<cc.Node> = [];
  private tableauCardStacks: Array<cc.Node> = [];
  private gameMode: SuitMode = SuitMode.ONE;
  private timer: number = 0;
  private score: number = 500;
  private listCardWin: Array<cc.Node> = [];
  private isWin: boolean = false;
  private isShowHint: boolean = false;
  private resetTime: number = 0;
  private noMovePredicts: boolean = false;
  private currentClickCard: number = 0;
  private lockTimerClickCard: number = 1;
  private isSolved: boolean = false;
  private isStartGame: boolean = false;
  private ignoreRule: boolean = false;
  private timePowerUp: number = 0;
  private isTimePowerUp: boolean = false;
  private isTimeActive: boolean = false;
  private isHighlightStacks: boolean = false;
  private timeActivePower: number = 15;
  private listTimeStamp: number[] = [];
  private drawRemainingTimes = 5;
  private isTimeOut: boolean = false;

  private originArr = null;

  protected onLoad(): void {
    Events.On(EventType.SELECT_SUIT, this.setUpGamePlay.bind(this));
    Events.On(EventType.POWER_ACTION, this.powerCardEnd.bind(this));
  }

  powerCardEnd(data) {
    if (!data.parent) {
      this.tableauCardStacks.forEach((stackNode, index) => {
        if (stackNode === data.node) {
          this.tableauCardStacks[index] = data.child;
        }
      });
    }

    this.highlightCard();
    this.undoStacks.push(data);
  }

  start() {
    //this.setUpGamePlay();
    // PopupManager.inst.openSelectMode();
    
    PopupManager.inst.openSuitMode(true);
  }

  getDeckCards(): Array<string> {
    const pattern = [];
    let newDeck = [];
    const selectors = [];
    const suit = ["spades", "hearts", "clubs", "diamonds"];

    for (var i = 1; i <= 13; i++) {
      pattern[i] = i.toString().padStart(2, "0");
    }

    for (i = 0; i < this.gameMode; i++) {
      pattern.forEach(function (item) {
        newDeck.push(suit[i] + "_" + item);
      });
    }

    while (104 / newDeck.length > 1) {
      newDeck = newDeck.concat(newDeck);
    }

    // newDeck.sort(function () {
    //   return 0.5 - Math.random();
    // });

    let k = newDeck.length;
    let j, t;
    while (k) {
      j = Math.floor(k-- * Math.random());
      t = newDeck[k];
      newDeck[k] = newDeck[j];
      newDeck[j] = t;
    }

    // cc.log("selectors", selectors);
    return newDeck;
  }

  setUpGamePlay(mode: SuitMode = SuitMode.ONE) {
    this.btnPowerUp
      .getChildByName("Background")
      .getComponent(cc.Sprite).spriteFrame = this.powerSprite[1];
    this.isTimePowerUp = false;
    this.ignoreRule = false;
    this.isStartGame = true;
    this.isWin = false;
    this.noMovePredicts = false;
    this.isSolved = false;
    this.isHighlightStacks = false;
    this.deckCards.length = 0;
    this.listCardWin.length = 0;
    this.drawedCardStacks.length = 0;
    this.tableauCardStacks.length = 0;
    this.listTimeStamp.length = 0;
    this.undoStacks.length = 0;
    this.drawRemainingTimes = 5;
    this.timer = 0;
    this.score = 500;
    this.boardScore
      .getComponent(BoardScore)
      .setScore(this.score, KEY_HIGH_SCORE_GAME);
    this.blockTouch.active = false;
    this.container.removeAllChildren();
    this.gameMode = mode;
    this.isShowHint = false;
    this.isTimeActive = false;
    this.btnUndo.spriteFrame = this.undoSprite[0];
    this.deckStacks.children.forEach((child, idx) => {
      child.x = this.deckStacks.children[Stacks.A5].x;
      child.active = true;
    });
    this.btnPowerUp
      .getChildByName("Background")
      .getComponent(cc.Sprite).spriteFrame = this.powerSprite[1];
    this.btnPowerUp
      .getChildByName("Background")
      .getChildByName("Label").active = false;
    this.timePowerUp = 0;
    this.generateMap();
  }

  async generateMap() {
    this.originArr = this.getDeckCards();
    // cc.log("originArr", this.originArr);

    let startPosition = this.convertToCardsNodePosition(
      this.getStartStackPosition(4)
    );

    let nodeArray: cc.Node[] = [];
    for (let i = 0; i < this.originArr.length; i++) {
      let node = cc.instantiate(this.cardPrefab);
      nodeArray.push(node);
    }

    nodeArray.forEach((cardNode, index) => {
      cardNode.parent = this.container;
      cardNode.position = cc.v3(startPosition.x, startPosition.y);
      let card = cardNode.getComponent(CardSpider);
      cardNode.zIndex = index;
      card.setData(this.originArr[index], this.container);
      card.setTouchCallback(
        this.cardTouchStart.bind(this),
        this.cardTouchEnd.bind(this),
        this.cardDoubleClick.bind(this),
        this.drawCard.bind(this),
        this.popUndoStack.bind(this),
        (() => {
          if (this.isTimeOut) {
            card.resetPowerUp();
            this.isTimeOut = false;
          }
          return this.isTimePowerUp;
        }).bind(this)
      );
    });
    this.deckCards = nodeArray;

    this.dealCard();
  }

  dealCard() {
    this.blockTouch.active = true;
    for (let i = 0; i < 44; i++) {
      const col = i % 10;
      let toPosition = this.convertToCardsNodePosition(
        this.getTableauStackPosition(col)
      );
      let cardNode = this.deckCards.pop();

      cc.tween(cardNode)
        .delay(0.1 * i)
        .parallel(
          cc.tween().to(0.15, {
            position: cc.v3(
              toPosition.x,
              toPosition.y + Math.floor(i / 10) * -30
            ),
          }),
          cc.tween().call(() => {
            SoundManager.inst.playEffect("DEAL_CARD");
            cardNode.zIndex = i;
          })
        )
        .call(() => {
          cardNode.zIndex = 1;

          if (i < 10) {
            this.tableauCardStacks.push(cardNode);
          } else {
            let parent = this.tableauCardStacks[col].getComponent(CardSpider);
            parent.setBottomChild(cardNode);
          }
        })
        .start();

      // cardNode.position = cc.v3(
      //   toPosition.x,
      //   toPosition.y + Math.floor(i / 10) * -30
      // );
    }

    for (let i = 0; i < 10; i++) {
      let toPosition = this.convertToCardsNodePosition(
        this.getTableauStackPosition(i)
      );

      let cardNode = this.deckCards.pop();

      cc.tween(cardNode)
        .delay(0.1 * (44 + i))
        .parallel(
          cc.tween().to(0.15, {
            position: cc.v3(
              toPosition.x,
              toPosition.y + Math.floor((44 + i) / 10) * -30
            ),
          }),
          cc.tween().call(() => {
            SoundManager.inst.playEffect("DEAL_CARD");
            cardNode.zIndex = i;
          })
        )
        .call(() => {
          cardNode.zIndex = 1;
          if (i === 9) {
            this.blockTouch.active = false;
          }
          let parent = this.tableauCardStacks[i].getComponent(CardSpider);
          parent.setBottomChild(cardNode);
          cardNode.getComponent(CardSpider).turnToFront();
        })
        .start();

      // cardNode.position = cc.v3(
      //   toPosition.x,
      //   toPosition.y + Math.floor((44 + i) / 10) * -30
      // );
      // cardNode.zIndex = 1;

      // let parent = this.tableauCardStacks[i].getComponent(Card);
      // parent.setBottomChild(cardNode);
      // cardNode.getComponent(Card).turnToFront();
    }

    this.deckStacks.children.forEach((child, idx) => {
      cc.tween(child)
        .delay(0.1 * 55)
        .to(0.2, {
          x: this.deckStacks.children[Stacks.A5].x - (5 - idx - 1) * 15,
        })
        .start();
    });
  }

  cardTouchStart(node: cc.Node) {
    this.isTimeOut = false;
    let world = cc.Vec3.ZERO;
    node.parent.convertToWorldSpaceAR(node.position, world);

    let stackIndex;
    let fromStack;

    stackIndex = this.getTableauStackIndex(world.x);
    //cc.warn("from tableau", stackIndex, node.getComponent(CardSpider).point);
    fromStack = "tableau";

    let parent = node.parent.parent ?? null;
    this.undoStacks.push({
      node,
      idx: stackIndex,
      from: fromStack,
      parentStatus:
        fromStack === "tableau" && parent
          ? parent.getComponent(CardSpider)?.isFront
          : false,
    });
  }

  cardTouchMove(node: cc.Node) {}
  async cardTouchEnd(node: cc.Node) {
    if (
      !this.undoStacks[this.undoStacks.length - 1] ||
      this.undoStacks[this.undoStacks.length - 1].node !== node
    ) {
      return;
    }
    //this.isTouch = false;
    let world = cc.Vec3.ZERO;
    node.parent.convertToWorldSpaceAR(node.position, world);

    let stackIndex = this.getTableauStackIndex(world.x);

    if (!this.tableauCardStacks[stackIndex]) {
      this.moveCardToEmptyStack(node, stackIndex);
    } else if (node === this.tableauCardStacks[stackIndex]) {
      this.popUndoStack();
      node.getComponent(CardSpider).reset();
    } else {
      this.moveCard(node, stackIndex, this.ignoreRule);
      node.getComponent(CardSpider).isMoving = false;
      const isComplete = this.completeSuit(stackIndex);
      //cc.warn({ isComplete });
    }

    this.highlightCard();
  }

  private highlightCard() {
    this.tableauCardStacks.forEach((stack) => {
      stack && stack.getComponent(CardSpider).calcHeight();
      if (stack) {
        const lastCard = stack.getComponent(CardSpider).getBottomCard();

        let parent = null;
        let currCard = lastCard;
        let active = true;
        currCard.getComponent(CardSpider).setEnable();

        for (
          let i = 0;
          i <= stack.getComponent(CardSpider).getBottomCount() - 1;
          i++
        ) {
          parent = currCard.parent.parent;
          if (
            active &&
            (!parent.getComponent(CardSpider) ||
              parent.getComponent(CardSpider).suit !==
                currCard.getComponent(CardSpider).suit ||
              parent.getComponent(CardSpider).point !==
                currCard.getComponent(CardSpider).point + 1)
          ) {
            active = false;
          }

          currCard = parent;
          active
            ? currCard.getComponent(CardSpider).setEnable()
            : currCard.getComponent(CardSpider).setDisable();
        }
      }
    });
  }

  private completeSuit(stackIdx: number) {
    const lastCard = this.tableauCardStacks[stackIdx]
      .getComponent(CardSpider)
      .getBottomCard();

    if (lastCard.getComponent(CardSpider).point !== 1) return false;

    let parent = null;
    let currCard = lastCard;
    const completedSuit = [];
    completedSuit.push(currCard);
    for (let i = 0; i < 12; i++) {
      parent = currCard.parent.parent;
      if (
        !parent.getComponent(CardSpider) ||
        parent.getComponent(CardSpider).suit !==
          currCard.getComponent(CardSpider).suit ||
        parent.getComponent(CardSpider).point !==
          currCard.getComponent(CardSpider).point + 1
      ) {
        return false;
      }
      currCard = parent;
      completedSuit.push(currCard);
    }

    this.undoStacks.push({
      from: "reset",
      parentStatus: currCard.getComponent(CardSpider).isFront,
      idx: stackIdx,
      completedSuit,
    });

    this.moveToWin(completedSuit);
    return true;
  }

  moveToWin(cards: cc.Node[]) {
    const winSlotIdx = Math.max(
      this.winCardStack.findIndex((child) => child === null),
      0
    );

    cards.forEach((card, idx) => {
      card.getComponent(CardSpider).isInWin = true;

      cc.tween(card)
        .delay(0.1 * idx)
        .call(() => {
          SoundManager.inst.playEffect("MOVE_WIN");
          card.getComponent(CardSpider).zIndex = 54 + idx;
          card
            .getComponent(CardSpider)
            .moveToWorldPosition(
              cc.v2(this.getWinStackPosition(winSlotIdx)),
              () => {
                this.moveCardToWinStack(card, winSlotIdx);

                if (idx === cards.length - 1) {
                  this.openLastCardInStack();
                  this.highlightCard();
                  // win
                  if (winSlotIdx === 7) {
                    this.blockTouch.active = false;
                    this.isStartGame = false;
                    this.isWin = true;
                    const data: ResultData = {
                      score: this.score,
                      highScore: this.score,
                      time: Utils.toMMSS(this.timer),
                    };
                    console.log(this.listTimeStamp);
                    PopupManager.inst.openResult(
                      true,
                      data,
                      "Spider_HighScore"
                    );
                  }
                }
              }
            );
        })
        .start();
    });
    this.listTimeStamp.push(parseFloat(this.timer.toFixed(2)));
    this.score += 100;
    this.boardScore
      .getComponent(BoardScore)
      .setScore(this.score, KEY_HIGH_SCORE_GAME);
  }

  cardDoubleClick(node: cc.Node) {
    //cc.warn("double");

    this.blockTouch.active = true;
    this.lockTimerClickCard = DOUBLE_CLICK_TIME;

    let possibleMoves = [];

    const result = this.detectPosibleMove(node);
    if (result.length > 0) possibleMoves.push(...result);

    if (possibleMoves.length > 0) {
      possibleMoves
        .sort(function () {
          return 0.5 - Math.random();
        })
        .sort((a, b) => b.priority - a.priority)
        .sort((a, b) => b.countCard - a.countCard);

      //cc.log(possibleMoves);

      const move = possibleMoves[0];

      //cc.log("hint", move);

      if (move.to === "card") {
        node
          .getComponent(CardSpider)
          .moveToWorldPosition(
            cc.v2(
              this.tableauCardStacks[move.idx]
                .getComponent(CardSpider)
                .getBottomWorldPosition()
            ),
            () => {
              this.moveCard(node, move.idx);
              this.blockTouch.active = false;
              const isComplete = this.completeSuit(move.idx);
              this.highlightCard();
            }
          );
      } else {
        node
          .getComponent(CardSpider)
          .moveToWorldPosition(
            cc.v2(this.getTableauStackPosition(move.idx)),
            () => {
              this.moveCardToEmptyStack(node, move.idx);
              this.blockTouch.active = false;
              this.highlightCard();
            }
          );
      }

      return true;
    }

    this.popUndoStack();
    //cc.warn("pop", this.undoStacks);
    this.blockTouch.active = false;
  }

  private hintCount = 0;

  drawCard(node: cc.Node) {
    // if (this.showHint(false)) {
    //   this.hintCount++;
    // }
    if (this.tableauCardStacks.some((card) => card === null)) {
      if (this.isHighlightStacks) return;
      this.isHighlightStacks = true;
      this.tableauCardStacks.forEach((cr, idx) => {
        if (cr === null) {
          cc.tween(this.highlightStacks.children[idx])
            .set({ active: true })
            .to(0.5, { scale: 1.5, opacity: 0 })
            .call(() => {
              this.highlightStacks.children[idx].active = false;
              this.highlightStacks.children[idx].scale = 1;
              this.highlightStacks.children[idx].opacity = 255;
              this.isHighlightStacks = false;
            })
            .start();
        }
      });
      if (PopupManager.inst.isPopupShowing()) return;
      const content = "You cannot deal a new row while any columns are empty.";
      PopupManager.inst.openInformation(content);
      return;
    }

    const listDeal = [];
    this.blockTouch.active = true;
    for (let i = 0; i < 10; i++) {
      let toPosition = this.convertToCardsNodePosition(
        this.getTableauStackPosition(i)
      );

      let cardNode = this.deckCards.pop();
      listDeal.push(cardNode);
      cc.tween(cardNode)
        .delay(0.1 * i)
        .parallel(
          cc.tween().to(0.2, {
            position: cc.v3(
              toPosition.x,
              toPosition.y +
                (this.tableauCardStacks[i]
                  .getComponent(CardSpider)
                  .getBottomCount() -
                  1) *
                  -30
            ),
          }),
          cc.tween().call(() => {
            SoundManager.inst.playEffect("DEAL_CARD");
            cardNode.zIndex = 54 + i;
          })
        )
        .call(() => {
          cardNode.zIndex = 1;

          let parent = this.tableauCardStacks[i].getComponent(CardSpider);
          parent.setBottomChild(cardNode);
          cardNode.getComponent(CardSpider).turnToFront();
          this.completeSuit(i);
          if (i === 9) {
            this.drawRemainingTimes--;
            if (this.drawRemainingTimes >= 0) {
              this.deckStacks.children[this.drawRemainingTimes].active = false;
            }

            this.deckCards.forEach((card) => {
              card.x -= 15;
            });
            this.blockTouch.active = false;
            this.highlightCard();
          }
        })
        .start();
    }
    this.undoStacks.push({ node, from: "deck", listDeal });
    const num = this.undoStacks.length > 0 ? 1 : 0;
    this.btnUndo.spriteFrame = this.undoSprite[num];
  }

  getStartStackPosition(idx: number): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.deckStacks.convertToWorldSpaceAR(
      this.deckStacks.children[idx].position,
      world
    );
    return world;
  }

  getWinStackPosition(index: number): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.winStacks.convertToWorldSpaceAR(
      this.winStacks.children[index].position,
      world
    );
    return world;
  }

  getWinStackIndex(worldX: number): number {
    let result = 0;
    let distance = 0;
    let minDistance = 0;
    let stackWorldX = 0;
    for (let i = 0; i < 4; i++) {
      stackWorldX = this.getWinStackPosition(i).x;
      distance = Math.abs(stackWorldX - worldX);
      if (i === 0 || minDistance > distance) {
        minDistance = distance;
        result = i;
      }
    }
    return result;
  }

  getTableauStackPosition(index: number): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.tableauStacks.convertToWorldSpaceAR(
      this.tableauStacks.children[index].position,
      world
    );
    return world;
  }

  getTableauStackIndex(worldX: number): number {
    let result = 0;
    let distance = 0;
    let minDistance = 0;
    let stackWorldX = 0;
    for (let i = 0; i < COLUMNS; i++) {
      stackWorldX = this.getTableauStackPosition(i).x;
      distance = Math.abs(stackWorldX - worldX);
      if (i === 0 || minDistance > distance) {
        minDistance = distance;
        result = i;
      }
    }
    return result;
  }

  convertToCardsNodePosition(world: cc.Vec3): cc.Vec3 {
    let nodePosition = cc.Vec3.ZERO;
    this.container.convertToNodeSpaceAR(world, nodePosition);
    return nodePosition;
  }

  moveCardToEmptyStack(node: cc.Node, toStackIndex: number) {
    let card = node.getComponent(CardSpider);

    node.parent = this.container;
    this.tableauCardStacks[toStackIndex] = node;

    let toPosition = this.convertToCardsNodePosition(
      this.getTableauStackPosition(toStackIndex)
    );
    node.position = toPosition;
    node.zIndex = card.point;
    this.tableauCardStacks.forEach((stackNode, index) => {
      if (stackNode === node && index !== toStackIndex) {
        this.tableauCardStacks[index] = null;
      }
    });

    this.openLastCardInStack();
  }

  private winCardStack = [null, null, null, null, null, null, null, null];
  moveCardToWinStack(node: cc.Node, toStackIndex: number) {
    node.parent = this.container;
    this.winCardStack[toStackIndex] = node;

    let toPosition = this.convertToCardsNodePosition(
      this.getWinStackPosition(toStackIndex)
    );

    node.position = toPosition;
    node.getComponent(CardSpider).zIndex = 1;
    this.tableauCardStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.tableauCardStacks[index] = null;
      }
    });
  }

  moveCard(node: cc.Node, toStackIndex: number, ignoreRule: boolean = false) {
    let stackCard =
      this.tableauCardStacks[toStackIndex].getComponent(CardSpider);
    this.score--;
    this.boardScore
      .getComponent(BoardScore)
      .setScore(this.score, KEY_HIGH_SCORE_GAME);
    if (stackCard) {
      if (stackCard.setBottomChild(node, ignoreRule)) {
        //cc.warn("moveCard::Point::", node.getComponent(CardSpider).point);

        this.drawedCardStacks = this.drawedCardStacks.filter(
          (item) => item !== node
        );

        if (this.drawedCardStacks.length - 1 >= 0) {
          this.drawedCardStacks[this.drawedCardStacks.length - 1].getComponent(
            CardSpider
          ).isUnder = false;
        }
        const num = this.undoStacks.length > 0 ? 1 : 0;
        this.btnUndo.spriteFrame = this.undoSprite[num];
      } else {
        // cc.warn("pop");
        this.popUndoStack();
      }
    }

    this.tableauCardStacks.forEach((stackNode, index) => {
      stackNode && stackNode.getComponent(CardSpider).calcHeight();

      if (stackNode === node) {
        this.tableauCardStacks[index] = null;
      }
    });

    this.openLastCardInStack();
  }

  openLastCardInStack(excludeIdx?: number) {
    this.tableauCardStacks.forEach((stackNode, idx) => {
      if (stackNode && idx !== excludeIdx) {
        let card = stackNode.getComponent(CardSpider);
        if (card) {
          card.turnLastBottomChildToFront();
          //this.checkSolve();
        }
      }
    });
  }

  update(dt) {
    if (PopupManager.inst.isPopupShowing()) {
      if (this.tableauCardStacks.findIndex((card) => card === null) < 0) {
        PopupManager.inst.closeInformation();
      }
    }
    if (this.isWin || !this.isStartGame) return;
    this.timer += dt;
    this.boardScore.getComponent(BoardScore).setTimer(Utils.toMMSS(this.timer));

    if (this.currentClickCard > 0) {
      this.lockTimerClickCard -= dt;
      if (this.lockTimerClickCard <= 0) {
        this.currentClickCard = 0;
        this.lockTimerClickCard = DOUBLE_CLICK_TIME;
      }
    }
    this.timePowerUp -= dt;
    if (this.timePowerUp > 0) {
      this.isTimeActive = true;
      this.btnPowerUp
        .getChildByName("Background")
        .getComponent(cc.Sprite).spriteFrame = this.powerSprite[2];
      this.btnPowerUp
        .getChildByName("Background")
        .getChildByName("Label").active = true;
      this.btnPowerUp.getComponentInChildren(cc.Label).string = Utils.toMMSS(
        this.timePowerUp
      );
      this.btnPowerUp
        .getChildByName("Background")
        .getChildByName("Label").color = new cc.Color().fromHEX("#FF3A5A");
    } else {
      this.isTimeActive = false;
      this.btnPowerUp
        .getChildByName("Background")
        .getComponent(cc.Sprite).spriteFrame = this.powerSprite[1];
      this.btnPowerUp
        .getChildByName("Background")
        .getChildByName("Label").active = false;
    }
    if (!this.isTimePowerUp) return;
    this.timeActivePower -= dt;
    if (this.timeActivePower > 0) {
      this.btnPowerUp
        .getChildByName("Background")
        .getComponent(cc.Sprite).spriteFrame = this.powerSprite[0];
      this.btnPowerUp
        .getChildByName("Background")
        .getChildByName("Label").active = true;
      this.btnPowerUp.getComponentInChildren(cc.Label).string = Utils.toMMSS(
        this.timeActivePower
      );
    } else {
      this.isTimeOut = true;
      this.isTimePowerUp = false;
      this.ignoreRule = false;
      this.btnPowerUp
        .getChildByName("Background")
        .getComponent(cc.Sprite).spriteFrame = this.powerSprite[1];
      this.timePowerUp = 60;
    }
  }

  showHint(playAnim: boolean = true) {
    SoundManager.inst.playEffect("CLICK_BTN");
    if (this.isShowHint) return;
    this.isShowHint = playAnim;
    if (this.noMovePredicts) {
      SoundManager.inst.playEffect("NO_HINT");
      // const data: ResultData = {
      //   score: this.score,
      //   highScore: this.score,
      //   time: Utils.toMMSS(this.timer),
      // };
      // this.isStartGame = false;
      // PopupManager.inst.openResult(false, data);
      return;
    }

    const remainingCards = this.container
      .getComponentsInChildren(CardSpider)
      .filter((card) => card.isFront && !card.isDisable && !card.isInWin);

    //cc.log({ remainingCards });

    const possibleMoves = [];

    for (const card of remainingCards) {
      if (!card) continue;
      const result = this.detectPosibleMove(card.node);
      if (result.length > 0) possibleMoves.push(...result);
    }

    if (possibleMoves.length > 0) {
      possibleMoves
        .sort(function () {
          return 0.5 - Math.random();
        })
        .sort((a, b) => b.priority - a.priority);

      //cc.log(possibleMoves);

      const move = possibleMoves[0];

      //cc.log("hint", move);

      if (move.to === "card") {
        if (playAnim) {
          this.highlightNode(move.card.node, move.parent);
        }
      } else {
        if (playAnim) {
          this.highlightNode(
            move.card.node,
            this.tableauStacks.children[move.idx]
          );
        }
      }

      return true;
    }

    if (playAnim) {
      cc.tween(
        this.deckCards[this.deckCards.length - 1] //.getChildByName("boder_highlight")
      )
        .set({ active: true })
        .blink(1, 2)
        .call(() => {
          this.isShowHint = false;
        })
        .start();
    }

    return false;
  }

  detectPosibleMove(node: cc.Node) {
    let card = node.getComponent(CardSpider);

    const moves = [];
    for (let i = 0; i < this.tableauCardStacks.length; i++) {
      if (!this.tableauCardStacks[i]) {
        if (
          card.getParentCount(true) > 0 ||
          (card.node.active && card.isFront)
        ) {
          const priority = 1;

          moves.push({ to: "empty", idx: i, priority, card });
        }
      } else {
        let tableauCard = this.tableauCardStacks[i].getComponent(CardSpider);
        if (tableauCard && tableauCard.isBottomChild(node)) {
          if (
            card.node.active &&
            card.isFront &&
            !card.isDisable &&
            !card.isInWin
          ) {
            const priority =
              tableauCard.getBottomCard().getComponent(CardSpider).suit ===
              node.getComponent(CardSpider).suit
                ? 2
                : 0;
            moves.push({
              to: "card",
              parent: tableauCard.getBottomCard(),
              card,
              idx: i,
              priority,
              countCard: tableauCard
                .getBottomCard()
                .getComponent(CardSpider)
                .getParentEnableCount(),
            });
          }
        }
      }
    }

    return moves;
  }

  highlightNode(from: cc.Node, dest: cc.Node) {
    // if (
    //   from.getComponent(CardSpider).isDisable ||
    //   dest.getComponent(CardSpider).getBottomCard().getComponent(CardSpider)
    //     .isDisable
    // ) {
    //   this.showHint();
    //   return;
    // }
    cc.tween(from)
      .blink(1, 2)
      .call(() => {
        this.isShowHint = false;
      })
      .start();

    if (dest.getComponent(CardSpider)) {
      cc.tween(dest.getComponent(CardSpider).getBottomCard())
        .blink(1, 2)
        .call(() => {
          this.isShowHint = false;
        })
        .start();
    } else if (dest.getChildByName("boder_highlight")) {
      cc.tween(dest.getChildByName("boder_highlight"))
        .set({ active: true })
        .blink(1, 2)
        .call(() => {
          dest.getChildByName("boder_highlight").active = false;
          this.isShowHint = false;
        })
        .start();
    } else {
      cc.tween(dest)
        .blink(1, 2)
        .call(() => {
          this.isShowHint = false;
        })
        .start();
    }
  }

  checkCardWin(card) {
    const findCard = this.listCardWin.find((pocker) => pocker === card);
    if (!findCard) {
      this.listCardWin.push(card);
      this.score += 100;
      this.boardScore
        .getComponent(BoardScore)
        .setScore(this.score, KEY_HIGH_SCORE_GAME);
    }
  }

  undo() {
    SoundManager.inst.playEffect("CLICK_BTN");
    const record = this.popUndoStack();
    const num = this.undoStacks.length > 0 ? 1 : 0;
    this.btnUndo.spriteFrame = this.undoSprite[num];
    if (record) {
      switch (record.from) {
        case "tableau":
          let stackCard = this.tableauCardStacks[record.idx];
          if (stackCard) {
            {
              this.moveCard(record.node, record.idx, true);
              const parent = record.node.parent.parent;
              if (parent && !record.parentStatus)
                record.node.parent.parent.getComponent(CardSpider).turnToBack();
            }
            //cc.warn("here", record.idx);
          } else {
            // cc.warn("here", record.idx);
            this.removeCardFromOldStack(record.node);
            this.moveCardToEmptyStack(record.node, record.idx);
          }
          break;
        case "deck":
          // this.blockTouch.active = this.gameMode === SuitMode.ONE;
          let startPosition = this.convertToCardsNodePosition(
            this.getStartStackPosition(this.drawRemainingTimes)
          );

          //cc.warn(record.listDeal);
          while (record.listDeal.length > 0) {
            let cardNo = record.listDeal.pop();
            if (!cardNo) return;

            cardNo.parent = this.container;
            // cardNo.zIndex = ;
            cardNo.position = cc.v3(startPosition.x, startPosition.y);
            this.deckCards.push(cardNo);
            let card = cardNo.getComponent(CardSpider);
            card.turnToBack();
          }

          this.deckStacks.children[this.drawRemainingTimes].active = true;
          this.drawRemainingTimes++;
          this.deckCards.forEach((card) => {
            card.position = startPosition;
          });

          this.removeCardFromOldStack(record.node);
          break;
        case "reset":
          record.completedSuit.reverse();
          record.completedSuit.forEach((card, idx) => {
            card.getComponent(CardSpider).isInWin = false;
            card.getComponent(CardSpider).turnToFront();
            let stackCard = this.tableauCardStacks[record.idx];
            if (stackCard) {
              this.moveCard(card, record.idx, true);

              if (idx === 0 && card.parent.parent && !record.parentStatus) {
                card.parent.parent.getComponent(CardSpider).turnToBack();
              }
              this.removeCardFromOldStack(card);
            } else {
              this.removeCardFromOldStack(card);
              this.moveCardToEmptyStack(card, record.idx);
            }
          });
          this.undo();
          break;
        default: // case power
          record.node.parent = this.container;
          record.child.parent = this.container;

          if (record.parent) {
            record.parent
              .getComponent(CardSpider)
              .setBottomChild(record.node, true);
          } else {
            record.node.position = record.child.position;

            this.tableauCardStacks.forEach((stackNode, index) => {
              if (stackNode === record.child) {
                this.tableauCardStacks[index] = record.node;
              }
            });
          }

          record.node
            .getComponent(CardSpider)
            .setBottomChild(record.child, true);

          this.highlightCard();
      }

      this.highlightCard();
    }
  }

  removeCardFromOldStack(node: cc.Node) {
    this.winCardStack.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.winCardStack[index] = null;
      }
    });

    this.tableauCardStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.tableauCardStacks[index] = null;
      }
    });
  }

  popUndoStack() {
    //cc.warn("pop", this.undoStacks.slice(), this.undoStacks);
    return this.undoStacks.pop();
  }

  // checkSolve() {
  //   const cardIsBottom = this.container
  //     .getComponentsInChildren(CardSpider)
  //     .filter((card) => !card.isFront);
  //   this.btnSolved.active =
  //     this.drawedCardStacks.length === 0 &&
  //     cardIsBottom.length === 0 &&
  //     this.deckCards.length === 0;
  // }

  onPowerUp() {
    if (this.isTimePowerUp) return;
    if (this.isTimeActive) return;
    SoundManager.inst.playEffect("CLICK_BTN");
    this.btnPowerUp
      .getChildByName("Background")
      .getComponent(cc.Sprite).spriteFrame = this.powerSprite[0];
    this.btnPowerUp.getComponentInChildren(cc.Label).string = Utils.toMMSS(
      this.timeActivePower
    );
    this.btnPowerUp.getChildByName("Background").getChildByName("Label").color =
      new cc.Color().fromHEX("#A0FF82");
    // this.ignoreRule = true;
    this.timeActivePower = 15;
    this.isTimePowerUp = true;
  }
}
