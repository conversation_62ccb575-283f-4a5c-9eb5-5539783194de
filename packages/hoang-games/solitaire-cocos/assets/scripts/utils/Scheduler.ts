export class Scheduler {


    /**
     * Call a function after a duration time using app frame update
     * @param time delay time in millisecond
     * @param callback callback function will be called after time duration
     */
    public static setTimeout(time: number, callback: Function, component: cc.Component): Function {
        component.scheduleOnce(callback, time / 1000);
        return callback;
    }

    /**
     * Clear a timeout event by a key
     * @param key
     */
    public static clearTimeout(key: Function, component: cc.Component) {
        component.unschedule(key);
    }

}