const { ccclass, property } = cc._decorator;
// import { GameOrientationInterface } from "../../../build-templates/web-mobile/gameconfig";
export interface GameOrientationInterface {
    isPortraitOnly: boolean,
    isLandscapeOnly: boolean,
    isFixedScreen: boolean,
    isSupportBlurBg: boolean,
    designScreenWidth: number,
    designScreenHeight: number,
    supportScreenWidth?: number,
    supportScreenHeight?: number,
}
@ccclass

export default class resizeCanvas extends cc.Component {
    /**  it will fixed screen or resizeAble base on designScreenWidth
   */

    @property(cc.Node)
    followCanvasNodes: cc.Node[] = [];

    gameOrientationConfig: GameOrientationInterface = null;

    onLoad() {

        this.gameOrientationConfig = window['gameOrientationConfig'];
        if (!this.gameOrientationConfig) {
            if (!this.gameOrientationConfig) {
                this.gameOrientationConfig = {
                    'isPortraitOnly': false,
                    'isLandscapeOnly': true,
                    'isFixedScreen': false,
                    'isSupportBlurBg': true,
                    'designScreenWidth': 1280,
                    'designScreenHeight': 720,
                    'supportScreenWidth': 1560,
                    'supportScreenHeight': 1560
                };
            }
        }
        cc.view.setResizeCallback(() => {
            this.resizeCanvas();
        });
        this.resizeCanvas();

    }

    resizeCanvas() {
        if (this.gameOrientationConfig.isFixedScreen) {
            return;
        }

        let width, height = 0;

        const canvas = cc.find('Canvas');
        let fitSCale;

        const curRatio = window.innerWidth / window.innerHeight;
        let targetRatio = this.gameOrientationConfig.designScreenWidth / this.gameOrientationConfig.designScreenHeight;

        if (this.gameOrientationConfig.isLandscapeOnly) {
            const scaleHeight = this.gameOrientationConfig.designScreenHeight / window.innerHeight;
            const mustWidth = cc.misc.clampf(
                scaleHeight * window.innerWidth,
                this.gameOrientationConfig.designScreenWidth,
                this.gameOrientationConfig.supportScreenWidth
            );
            targetRatio = mustWidth / this.gameOrientationConfig.designScreenHeight;
        } else {
            const scaleWidth = this.gameOrientationConfig.designScreenWidth / window.innerWidth;
            const mustHeight = cc.misc.clampf(
                scaleWidth * window.innerHeight,
                this.gameOrientationConfig.supportScreenHeight,
                this.gameOrientationConfig.designScreenHeight
            );
            targetRatio = this.gameOrientationConfig.designScreenWidth / mustHeight;
        }

        if (curRatio > targetRatio) {
            width = window.innerHeight * targetRatio;
            height = window.innerHeight;
        } else {
            width = window.innerWidth;
            height = window.innerWidth / targetRatio;
        }

        if (this.gameOrientationConfig.isLandscapeOnly) {
            fitSCale = this.gameOrientationConfig.designScreenHeight / height;
        } else {
            fitSCale = this.gameOrientationConfig.designScreenWidth / width;
        }

        width = fitSCale * width;
        height = fitSCale * height;

        canvas.width = width;
        canvas.height = height;

        /*
        This trick to swap width heigh cuz we using rotate for mainNode
        Must remove when we go with portrait offical.
    */
        if (!this.gameOrientationConfig.isLandscapeOnly) {
            const temp = width;
            width = height;
            height = temp;
        }

        this.followCanvasNodes.forEach((node) => {
            if (node) {
                node.width = width;
                node.height = height;
            }
        });
    }
}
