import { EventType, Events } from "../events/Events";


const numeral = (window as any).numeral;

export default class Utils {
    /**
     * RGB to HEX
     * The formula for combining color values into a number is: red + 256*green + 256*256*blue
     */
    public static colorIntToHex(intValue: number): string {
        return '#' + intValue.toString(16).substring(0, 6);
    }

    public static convertToMoneyFormat(amount: number, format: string = '0,0.[00]'): string {
        return numeral(amount).format(format);
    }

    public static waitToEventEmit(type: EventType): Promise<void> {
        return new Promise((resolve) => {
            Events.Once(type, resolve);
        });
    }

    public static randomRange(min: number, max: number, int: boolean = false) {
        const delta = max - min;
        const rnd = Math.random();
        let result = min + rnd * delta;

        if (int) {
            result = Math.round(result);
        }

        return result;
    }

    public static waitForSeconds(delayTime: number, context: cc.Component): Promise<void> {
        return new Promise<void>((resolve) => {
            context.scheduleOnce(resolve.bind(context), delayTime);
        });
    }

    public static getAnimDuration(ske: sp.Skeleton, animName: string): number {
        const anim: sp.spine.Animation = ske ? ske.findAnimation(animName) : null;
        return anim ? anim.duration : 0.0;
    }

    public static resetSkeleton(ske: sp.Skeleton): void {
        if (!ske.isAnimationCached()) {
            ske.clearTrack(0);
        }
        ske.setToSetupPose();
    }

    public static prefSaveBoolean(key: string, value: boolean): void {
        cc.sys.localStorage.setItem(key, value ? '1' : '0');
    }

    public static prefGetBoolean(key: string, defaultValue: boolean = false): boolean {
        const savedCfg = cc.sys.localStorage.getItem(key);
        if (savedCfg) {
            return savedCfg === '1';
        }
        return defaultValue;
    }

    public static safeGetProperty<T>(obj: Object, property: string, defaultValue: T): T {
        return property in obj ? obj[property] : defaultValue;
    }

    public static cartesianToPolar(cartesian: cc.Vec2): cc.Vec2 {
        const [x, y] = [cartesian.x, cartesian.y];
        const angle = Math.atan2(y, x);

        return new cc.Vec2(this.getLength(cartesian), angle);
    }

    public static polarToCartesian(polar: cc.Vec2): cc.Vec2 {
        const [len, angle] = [polar.x, polar.y];

        return new cc.Vec2(len * Math.cos(angle), len * Math.sin(angle));
    }

    public static getLength(vector: cc.Vec2): number {
        return Math.sqrt(vector.x * vector.x + vector.y * vector.y);
    }

    public static getDateFromTime(unix_timestamp: number, needYear: boolean = false): string {
        const date = new Date(unix_timestamp);
        const day = ('0' + date.getUTCDate()).substr(-2);
        const month = ('0' + (date.getMonth() + 1)).substr(-2);
        const year = date.getUTCFullYear();

        const yearStr = (needYear) ? '/' + year : '';
        return `${day}/${month}` + yearStr;
    }

    public static getTimeUnixDate(unix_timestamp: number) {
        const date = new Date(unix_timestamp);
        const hours = date.getHours();
        const minutes = ('0' + date.getMinutes()).substr(-2);
        const seconds = ('0' + date.getSeconds()).substr(-2);

        return hours + ':' + minutes + ':' + seconds;
    }

    public static setTintNode(node: cc.Node, tint: number) {
        const color = 0xff & tint;
        const dimColor = ((color << 16 & 0xff0000) | (color << 8 & 0x00ff00) | (color & 0x0000ff));
        node.color = new cc.Color().fromHEX(Utils.colorIntToHex(dimColor));
    }

    /**
     *
     * @param normalMatrix
     * @returns Reel id which is start to play near win animation
     */
    public static checkReelNearWinActive(normalMatrix: string[]): number[] {
        // let scatterCount = 0;
        // const reelCanPayNearWin = JSON.parse(JSON.stringify(GameConfig.REEL_NEAR_WIN_CAN_ACTIVE));
        // for (let i = 0; i < GameConfig.NUMBER_OF_REELS; i++) {
        //     if (normalMatrix[i].includes(GameConfig.SymbolType.SymbolScatter)) {
        //         scatterCount++;
        //     }

        //     if (reelCanPayNearWin.includes(i)) {
        //         reelCanPayNearWin.splice(reelCanPayNearWin.indexOf(i), 1);
        //     }

        //     if (scatterCount > 1 && i < GameConfig.NUMBER_OF_REELS - 1) {
        //         return reelCanPayNearWin;
        //     }
        // }
        return [];
    }

    public static getURLParam(name: string): string {
        if (cc.sys.isNative) {
            return '';
        }
        const url = new URL(window.location.href);
        return url.searchParams.get(name);
    }

    public static toMMSS(seconds, useHour = false) {
        const h = Math.floor((seconds % (3600 * 24)) / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = Math.floor(seconds % 60);

        const hDisplay = h + ':';
        let mDisplay;
        if (useHour) {
            mDisplay = m > 9 ? m + ':' : '0' + m + ':';
        } else {
            mDisplay = m + ':';
        }
        const sDisplay = s > 9 ? s + '' : '0' + s;

        if (useHour) {
            return hDisplay + mDisplay + sDisplay;
        }
        return mDisplay + sDisplay;
    }
}
