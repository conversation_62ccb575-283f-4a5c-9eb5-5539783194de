/**
 * Based from Parallel Extension Extras and other ObjectPool implementations.
 * Uses .add(T) and .take():T
 */

const ABSOLUTE_MAX_SIZE = 65536;

export class ObjectPool<T> {
    private _pool: T[];
    private _localAbsMaxSize: number;

    constructor(
        private _maxSize: number,
        private _generator?: (...args: any[]) => T,
        private _recycler?: (o: T) => void,
        private _name?: string,
        private _isDebug?: boolean,
    ) {
        if (isNaN(_maxSize) || _maxSize < 1) {
            throw new Error('Must be at valid number least 1');
        }
        if (_maxSize > ABSOLUTE_MAX_SIZE) {
            throw new Error(`Must be less than or equal to ${ABSOLUTE_MAX_SIZE}`);
        }

        this._localAbsMaxSize = Math.min(_maxSize * 2, ABSOLUTE_MAX_SIZE);

        this._pool = [];
    }

    public get maxSize(): number {
        return this._maxSize;
    }

    public get count(): number {
        const p = this._pool;
        return p ? p.length : 0;
    }

    public clear(): void {
        this._pool.length = 0;
    }

    public toArrayAndClear(): T[] {
        const p = this._pool;
        this._pool = [];
        return p;
    }

    public add(object: T): void {
        if (this._pool.length >= this._localAbsMaxSize) {
            cc.warn(`[POOL] Overhead of pool '${this._name}', status: ${this._pool.length}/${this._maxSize}`);
        } else {
            if (this._recycler) {
                this._recycler(object);
            }
            this._pool.push(object);
            if (this._isDebug) {
                cc.warn(`[POOL] ${this._name} add, new size: ${this.count}`);
            }
        }
    }

    public take(factory?: () => T): T {
        if (!this._generator && !factory) {
            throw new Error('Must provide a factory if on was not provided at construction time');
        }
        if (this._isDebug) {
            cc.warn(`[POOL] ${this._name} take, current size: ${this.count}`);
        }
        return this._pool.pop() || (factory && factory()) || this._generator();
    }

    public prepare(count: number) {
        if (this._pool.length < count) {
            const diff = count - this._pool.length;
            for (let i = 0; i < diff; i++) {
                this._pool.push(this._generator());
            }
            if (this._isDebug) {
                cc.warn(`[POOL] ${this._name} create ${diff} item to prepare, size: ${this.count}`);
            }
        }
    }
}
