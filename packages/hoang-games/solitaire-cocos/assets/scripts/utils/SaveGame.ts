/* eslint-disable no-restricted-syntax */
export default class SaveGame {
  private static _instance: SaveGame = null;
  /**
   *
   */
  constructor() {
    SaveGame._instance = this;
  }

  public static getInstance(): SaveGame {
    return SaveGame._instance;
  }

  public static init() {
    if (SaveGame.getInstance() == null) {
      new SaveGame();
    }
  }

  public async setData(key: string, value: any) {
    cc.sys.localStorage.setItem(key, value);
  }

  public getData(key: string) {
    const data = {};
    data[key] = cc.sys.localStorage.getItem(key);
    return data[key];
  }

  public async setDataAsync(data: object) {
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const element = data[key];
        cc.sys.localStorage.setItem(key, element);
      }
    }
    return true;
  }

  public async getDataAsync(keys: Array<string>) {
    const data = {};
    keys.forEach((key) => {
      const value = cc.sys.localStorage.getItem(key);
      data[key] = value;
    });
    return data;
  }

  public async removeData(keys: Array<string>) {
    keys.forEach((key) => {
      cc.sys.localStorage.removeItem(key);
    });
  }
}
