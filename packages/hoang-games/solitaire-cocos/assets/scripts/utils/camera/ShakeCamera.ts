import ShakeAction from "../../UI/actions/ShakeAction";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ShakeCamera extends cc.Component {
  @property(cc.String)
  eventName: string = "shake-camera";

  onLoad() {
    cc.systemEvent.on(this.eventName, this._onEventTriggered.bind(this));
  }

  start() {}

  private _onEventTriggered(...args: any[]): void {
    const action = ShakeAction.create(this.node);
    const shakeTime = Number.parseFloat(args[0]);
    if (!Number.isNaN(shakeTime)) {
      action.duration = shakeTime;
    }
    const magnitudeTime = Number.parseFloat(args[1]);
    if (!Number.isNaN(magnitudeTime)) {
      action.magnitude = magnitudeTime;
    }
    // cc.warn(action);
    this.node.runAction(action);
  }
}
