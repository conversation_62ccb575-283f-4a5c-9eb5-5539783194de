const { ccclass, property } = cc._decorator;

@ccclass
export default class FPSInfo extends cc.Component {

    @property(cc.Label)
    fpsLabel: cc.Label = null;

    private _curCounter = 0;
    private _curTimer = 0;

    // onLoad () {}
    // start () {}
    update(dt) {
        this._curCounter ++;
        this._curTimer += dt;

        if (this._curTimer > 1) {
            this.fpsLabel.string = `FPS: ${this._curCounter}`;
            this._curCounter = 0;
            this._curTimer %= 1;
        }
    }
}
