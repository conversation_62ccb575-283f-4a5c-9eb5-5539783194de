export default class BooleanFuser {
    private _dataMap: Map<string, boolean> = new Map();
    private _currentValue: boolean = true;
    private _onValueChanged: (value: boolean) => void | undefined;

    constructor(onValueChanged?: (value: boolean) => void) {
        this._onValueChanged = onValueChanged;
    }

    setValue(value: boolean, name: string = 'default'): boolean {
        this._dataMap.set(name, value);

        this._updateValue();

        return this._currentValue;
    }

    getValue(): boolean {
        return this._currentValue;
    }

    clearData(): void {
        this._dataMap.clear();
    }

    setCallback(onValueChanged: (value: boolean) => void): void {
        this._onValueChanged = onValueChanged;
    }

    private _calculateNewValue(): boolean {
        let result = true;

        this._dataMap.forEach((value) => {
            result &&= value;
        });

        return result;
    }

    private _updateValue(): void {
        const newValue = this._calculateNewValue();

        if (this._currentValue === newValue) {
            return;
        }

        this._currentValue = newValue;
        this.onValueChanged(this._currentValue);
    }

    private onValueChanged(value: boolean): void {
        if (typeof this._onValueChanged === 'function') {
            this._onValueChanged(value);
        }
    }
}