
type RenderNode = {
    node: cc.Node,
    groupId: number,
    orderId: number,
};

export default class ArrangeRenderOrder {

    private _nodes: Map<number, RenderNode[]> = new Map();

    public addNode(node: cc.Node, groupId: number, orderId: number = 0): void {
        const newNode: RenderNode = { node, groupId, orderId };
        if (this._nodes.has(groupId)) {
            const nodes = this._nodes.get(groupId);
            nodes.push(newNode);
            nodes.sort((a, b) => a.orderId - b.orderId);
        } else {
            this._nodes.set(groupId, [newNode]);
        }
    }

    public sort(): void {
        const groupIds = Array.from(this._nodes.keys()).sort((a, b) => a - b);
        let siblingIdx = 0;
        for (let i = 0; i < groupIds.length; i++) {
            const groupId = groupIds[i];
            const nodes: RenderNode[] = this._nodes.get(groupId);
            nodes.forEach((sortNode, idx) => {
                sortNode.node.setSiblingIndex(siblingIdx++);
            });
        }
    }
}
