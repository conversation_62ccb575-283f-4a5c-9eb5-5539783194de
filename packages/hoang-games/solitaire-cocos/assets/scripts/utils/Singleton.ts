export class Singleton<T> {
    public static inst<T>(clazz: { new (): T }): T {
        return this._instance || new clazz();
    }

    private static _instance = null;
}

export abstract class SingletonComponent<T> extends cc.Component {
    constructor() {
        super();
    }

    protected static _setupInstance<T>(clazz: { new (): T }): T {
        return this._instance || new clazz();
    }

    protected onLoad(): void {
        if (SingletonComponent._instance) {
            cc.warn(`There are several ${this.constructor.name} construction!`);
            return;
        }
        SingletonComponent._instance = this;
    }

    private static _instance = null;
}
