import Utils from './Utils';

const { ccclass, property } = cc._decorator;

@ccclass
export default class AttachNode extends cc.Component {

    @property(cc.Node)
    attachTo: cc.Node = null;

    private _originalParent: cc.Node = null;
    private _originalPosition: cc.Vec2 = null;
    private _originalPolarVector: cc.Vec2 = null;
    private _isAttaching: boolean = false;

    protected lateUpdate(dt: number): void {
        if (this._isAttaching) {
            const lPos = this._originalParent.getPosition();
            const cartesianVector = Utils.polarToCartesian(this._originalPolarVector);
            this.node.setPosition(lPos.add(cc.v2(cartesianVector.x, cartesianVector.y)));
        }
    }

    public attach() {
        this._isAttaching = true;
        this._originalPosition = this.node.getPosition();
        this._originalParent = this.node.parent;
        this._originalPolarVector = Utils.cartesianToPolar(cc.v2(this.node.position.x, this.node.position.y));
        this.node.parent = this.attachTo;
        this._originalParent.on('detach', () => {
            this._detach();
        });
    }

    private _detach() {
        this._isAttaching = false;
        this.node.parent = this._originalParent;
        this.node.setPosition(this._originalPosition);

    }

    // update (dt) {}
}
