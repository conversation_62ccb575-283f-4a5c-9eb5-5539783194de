/**
 * Event class
 */
export class Event<Data> {
    /**
     * Function to run when emitted
     */
    private readonly callback: Callback<Data>;

    /**
     * Number of times to run callback left
     */
    private count: number = 0;

    /**
     * Creates an event
     * @param callback Function to run when emitted
     * @param count Number of times to run callback
     */
    public constructor(callback: Callback<Data>, count: number) {
        this.callback = callback;
        this.count = Math.max(count, 0);
    }

    /**
     * Runs callback if any run left
     * @param args1 Data to use in callback
     */
    public Emit(args1?: Data, args2?, args3?, args4?, args5?): boolean {
        if (this.count > 0) {
            try {
                this.callback(args1, args2, args3, args4, args5);
                --this.count;
            } catch (err) {
                console.error(err);
            }
        }

        return this.count > 0;
    }
}

/**
 * Event callback type
 */
export type Callback<Data> = (args1?: Data, args2?, args3?, args4?, args5?) => void;
