import { Callback, Event } from "./Event";

/**
 * Event type enumeration
 */
export enum EventType {
  /**
   * New Game
   */
  NEW_GAME = "NEW_GAME",
  /**
   * Draw Mode
   */
  DRAW_MODE = "DRAW_MODE",
  SELECT_SUIT = "SELECT_SUIT",
  /**
   *Reset Gameover
   */
  RESET_GAMEOVER = "RESET_GAMEOVER",
  POWER_ACTION = "POWER_ACTION",
}

/**
 * Events class
 */
export abstract class Events {
  /**
   * All events separated by type
   */
  private static readonly events: EventsTree = Events.generateEventsTree();

  /**
   * Next added event's id
   */
  private static nextId: number = 0;

  /**
   * Adds event and returns it's id
   * @param type Event type
   * @param callback Event callback
   * @param count Event count
   */
  public static On<Data extends EventData>(
    type: EventType,
    callback: Callback<Data>,
    count: number = Number.POSITIVE_INFINITY
  ): number {
    const id: number = Events.nextId++;
    // Casting Data to EventData is needed for storing events
    // This is a limitation of Typescript language
    if (!(type in Events.events)) {
      Events.events[type] = {};
    }
    Events.events[type][id] = new Event<EventData>(
      callback as Callback<EventData>,
      count
    );

    return id;
  }

  /**
   * Adds event to be run once and returns it's id
   * @param type Event type
   * @param callback Event callback
   */
  public static Once<Data extends EventData>(
    type: EventType,
    callback: Callback<Data>
  ): number {
    return Events.On<Data>(type, callback, 1);
  }

  /**
   * Removes event if it exists
   * @param type Event type
   * @param id Event id
   */
  public static Off(type: EventType, id: number): boolean {
    if (type in Events.events && Events.events[type][id]) {
      delete Events.events[type][id];

      return true;
    }

    return false;
  }

  /**
   * Runs all events of wanted type if any exist
   * @param type Event type
   * @param args1 Event data
   */
  public static Emit<Data extends EventData>(
    type: EventType,
    args1?: Data,
    args2?: Data,
    args3?: Data,
    args4?: Data,
    args5?: Data
  ): boolean {
    if (!(type in Events.events)) {
      cc.log(`[ES] Event without subscribers: ${type}`);
      return false;
    }
    const ids: number[] = Object.keys(Events.events[type]).map(Number);
    for (const id of ids) {
      // Casting EventData back to Data is needed for using events
      // Always use the type used when adding events of wanted type
      const event: Event<Data> = Events.events[type][id] as Event<Data>;
      if (!event.Emit(args1, args2, args3, args4, args5)) {
        delete Events.events[type][id];
      }
    }

    return ids.length > 0;
  }

  /**
   * Generates events tree
   */
  private static generateEventsTree(): EventsTree {
    const trees = {};
    // create holder for subscribers
    /* Object.keys(EventType).forEach(eventType => {
            trees[eventType] = {};
        }); */
    return trees;
  }
}

/**
 * Event data type union
 */
type EventData = any;

/**
 * Events tree type
 */
type EventsTree = { [key in EventType]?: EventTree };

/**
 * Event tree type
 */
type EventTree = { [key: number]: Event<EventData> };

// window.addEventListener('error', (event: ErrorEvent) => Events.Emit<ErrorEvent>(EventType.WindowError, event));
// window.addEventListener('load', () => {
// 	Events.Emit(EventType.WindowLoad);
// });
// window.addEventListener('beforeunload', (event: BeforeUnloadEvent) =>
// 	Events.Emit<BeforeUnloadEvent>(EventType.WindowBeforeUnload, event),
// );
// window.addEventListener('unload', () => Events.Emit(EventType.WindowUnload));
// window.addEventListener('resize', () => Events.Emit(EventType.WindowResize));
// window.addEventListener('orientationchange', () => Events.Emit(EventType.WindowOrientationChange));
// window.addEventListener('webkitfullscreenchange', () => Events.Emit(EventType.WindowFullScreenChange));
// window.addEventListener('mozfullscreenchange', () => Events.Emit(EventType.WindowFullScreenChange));
// window.addEventListener('msfullscreenchange', () => Events.Emit(EventType.WindowFullScreenChange));
// window.addEventListener('fullscreenchange', () => Events.Emit(EventType.WindowFullScreenChange));
// window.addEventListener('webkitfullscreenerror', () => Events.Emit(EventType.WindowFullScreenError));
// window.addEventListener('mozfullscreenerror', () => Events.Emit(EventType.WindowFullScreenError));
// window.addEventListener('msfullscreenerror', () => Events.Emit(EventType.WindowFullScreenError));
// window.addEventListener('fullscreenerror', () => Events.Emit(EventType.WindowFullScreenError));
// window.addEventListener('focus', () => Events.Emit(EventType.WindowFocus));
// window.addEventListener('blur', () => Events.Emit(EventType.WindowBlur));
// window.addEventListener('keydown', (event: KeyboardEvent) =>
// 	Events.Emit<KeyboardEvent>(EventType.WindowKeyDown, event),
// );
// window.addEventListener('contextmenu', (event: MouseEvent) => {
// 	event.preventDefault();
// });
