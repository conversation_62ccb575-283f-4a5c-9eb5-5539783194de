import SoundManager from "../../sounds/SoundManager";
import Utils from "../../utils/Utils";
import PopupManager from "../Popup/PopupManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class MenuSetting extends cc.Component {
  @property(cc.Node)
  btnMore: cc.Node = null;

  @property(cc.Node)
  btnHint: cc.Node = null;

  @property(cc.Node)
  btnUndo: cc.Node = null;

  @property(cc.Node)
  layoutButton: cc.Node = null;

  @property(cc.Node)
  layoutLeft: cc.Node = null;

  @property(cc.Toggle)
  soundToggle: cc.Toggle = null;

  private _iconButtonMore: cc.Node;
  private _isshowAnimPopup: boolean = false;
  private _mainNode: cc.Node = null;
  private _timePowerUp = 60;
  private _timeActive = 10;

  async onLoad() {
    this._iconButtonMore = this.btnMore.getChildByName("iconMore");
    this._mainNode = cc.find("Canvas/Gameplay");
    if (this._mainNode) {
      this._mainNode.on(cc.Node.EventType.SIZE_CHANGED, () => {
        this._onCanvasSizeChanged();
      });
      this._onCanvasSizeChanged();
    }
    this._loadSoundSetting();
  }

  private _loadSoundSetting(): void {
    if (SoundManager.inst.isMusicOn()) {
      this.soundToggle.check();
      this.soundToggle.node.getChildByName("Background").active = false;
    }
  }

  private _onCanvasSizeChanged() {
    this.node.width = this._mainNode.width;
    this.layoutButton.x =
      this._mainNode.width / 2 - this.layoutButton.width / 2;
    this.btnMore.x = this._mainNode.width / 2 - this.btnMore.width / 2;
    if (this.layoutLeft) {
      this.layoutLeft.x =
        -this._mainNode.width / 2 + this.layoutLeft.width / 1.5;
      this.btnUndo.x = this._mainNode.width / 2 - this.btnUndo.width;
    } else {
      this.btnHint.x = -this._mainNode.width / 2 + this.btnHint.width / 2;
      this.btnUndo.x = this._mainNode.width / 2 - this.btnUndo.width / 2;
    }
  }

  public openMenu() {
    SoundManager.inst.playEffect("CLICK_BTN");
    if (this._isshowAnimPopup) return;
    this._isshowAnimPopup = true;
    cc.tween(this.layoutButton)
      .by(0.3, {
        x: this.layoutButton.width * this.btnMore.scaleX,
      })
      .call(() => {
        this._isshowAnimPopup = false;
        this.btnMore.scaleX *= -1;
      })
      .start();
  }

  openNewPlayKlondike() {
    SoundManager.inst.playEffect("CLICK_BTN");
    PopupManager.inst.openSelectMode();
  }

  openNewPlaySpider() {
    SoundManager.inst.playEffect("CLICK_BTN");
    PopupManager.inst.openSuitMode();
  }

  public onSoundToggleCheck(sender: cc.Toggle, customEventData): void {
    const bgSprite = this.soundToggle.node.getChildByName("Background");
    bgSprite.active = !sender.isChecked;
    SoundManager.inst.setSoundOption(sender.isChecked, "music");
    SoundManager.inst.setSoundOption(sender.isChecked, "sfx");
    SoundManager.inst.playEffect("CLICK_BTN");
  }

  openGameRule() {
    SoundManager.inst.playEffect("CLICK_BTN");
    PopupManager.inst.openGameRule();
  }
}
