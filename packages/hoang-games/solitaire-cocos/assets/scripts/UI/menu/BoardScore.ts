import { EventType, Events } from "../../events/Events";
import { DrawMode } from "../../utils/Gameinterface";
import SaveGame from "../../utils/SaveGame";
import PopupManager from "../Popup/PopupManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BoardScore extends cc.Component {
  @property(cc.Label)
  highScore: cc.Label = null;

  @property(cc.Label)
  score: cc.Label = null;

  @property(cc.Label)
  timer: cc.Label = null;

  private _iconButtonMore: cc.Node;
  private _isshowAnimPopup: boolean = false;
  private _highScore: number = 0;

  onLoad() {
    SaveGame.init();
    Events.On(EventType.NEW_GAME, this.reset.bind(this));
  }

  public setTimer(time: string) {
    this.timer.string = `${time}`;
  }

  public setScore(score: number, key: string) {
    const dataSave = SaveGame.getInstance().getData(key);
    if (!dataSave || dataSave < score) {
      this.highScore.string = `${score}`;
      SaveGame.getInstance().setData(key, score);
    }
    this.score.string = `${score}`;
  }

  reset(key: string) {
    const dataSave = SaveGame.getInstance().getData(key);
    if (dataSave) {
      this._highScore = dataSave;
      this.highScore.string = `${this._highScore}`;
    } else {
      this._highScore = 0;
      this.highScore.string = `0`;
    }
  }
}
