/**
 * Ref: https://discuss.cocos2d-x.org/t/tutorial-cocos2d-camera-shake-effect-from-unity-updates/27215
 */
import Utils from '../../utils/Utils';

const { ccclass, property } = cc._decorator;

//TODO: Use tween instead of Action
export default class ShakeAction extends cc.ActionInterval {
    private _interval: number = 1 / 60;
    private _duration: number;
    private _speed: number;
    private _magnitude: number;

    public static create(target: cc.Node, duration: number = 0.8, speed: number = 6, magnitude: number = 4): ShakeAction {
        const action = new ShakeAction(1 / 60, duration, speed, magnitude);
        action.setTarget(target);
        return action;
    }

    constructor(interval: number, duration: number, speed: number, magnitude: number) {
        super();
        // this._interval = interval;
        this._duration = duration;
        this._speed = speed;
        this._magnitude = magnitude;
    }

    private interpolate(a: number, b: number, x: number): number {
        const ft = x * 3.1415927;
        const f = (1.0 - Math.cos(ft)) * 0.5;
        return a * (1.0 - f) + b * f;
    }

    private findnoise(x: number, y: number): number {
        let n = Number.parseInt(x + '') + Number.parseInt(y + '') * 57;
        n = (n << 13) ^ n;
        const nn = (n * (n * n * 60493 + 19990303) + 1376312589) & 0x7fffffff;
        return 1.0 - (Number.parseFloat(nn + '') / 1073741824.0);
    }

    private lerp(start: number, end: number, t: number): number {
        return start * (1 - t) + end * t;
    }

    private noise(x: number, y: number): number {
        const floorx = Number.parseInt(x + ''); //This is kinda a cheap way to floor a double integer.
        const floory = Number.parseInt(y + '');
        let s, t, u, v; //Integer declaration
        s = this.findnoise(floorx, floory);
        t = this.findnoise(floorx + 1, floory);
        u = this.findnoise(floorx, floory + 1); //Get the surrounding pixels to calculate the transition.
        v = this.findnoise(floorx + 1, floory + 1);

        const int1 = this.lerp(s, t, x - floorx);
        const int2 = this.lerp(u, v, x - floorx);
        return this.lerp(int1, int2, y - floory);
    }

    private clamp(x: number, min: number, max: number): number {
        if (x < min) {
            x = min;
        } else if (x > max) {
            x = max;
        }
        return x;
    }

    private static _elapsed = 0.0;
    private update(dt: number): void {
        const randomStart = Utils.randomRange(-1000.0, 1000.0);

        if (this.getTarget()) {
            const target = this.getTarget();
            const orgPos = target.getPosition();
            ShakeAction._elapsed += dt;

            const percentComplete = ShakeAction._elapsed / this._duration;

            // We want to reduce the shake from full power to 0 starting half way through
            const damper = 1.0 - this.clamp(2.0 * percentComplete - 1.0, 0.0, 1.0);

            // Calculate the noise parameter starting randomly and going as fast as speed allows
            const alpha = randomStart + this._speed * percentComplete;

            // map noise to [-1, 1]
            let x = this.noise(alpha, 0.0) * 2.0 - 1.0;
            let y = this.noise(0.0, alpha) * 2.0 - 1.0;

            x *= this._magnitude * damper;
            y *= this._magnitude * damper;
            target.setPosition(x, y);//Here is where the magic goes
            // cc.warn("Elapsed : ", ShakeAction._elapsed);
            if (ShakeAction._elapsed >= this._duration) {
                ShakeAction._elapsed = 0;
                // target.unscheduleUpdate();
                target.setPosition(orgPos);
            }
        }
    }

    public set duration(value: number) {
        this._duration = value;
    }

    public set magnitude(value: number) {
        this._magnitude = value;
    }
}
