import SoundManager from "../../sounds/SoundManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class InformationPopup extends cc.Component {
  @property(cc.Label)
  textInformation: cc.Label = null;

  private _mainNode: cc.Node = null;
  public isShowing: boolean = false;

  onLoad() {
    this._mainNode = cc.find("Canvas/Gameplay");
    if (this._mainNode) {
      this._mainNode.on(cc.Node.EventType.SIZE_CHANGED, () => {
        this._onCanvasSizeChanged();
      });
      this._onCanvasSizeChanged();
    }
  }

  private _onCanvasSizeChanged() {
    this.node.width = this._mainNode.width;
  }

  start() {}

  open(content: string) {
    this.textInformation.string = content;
    this.node.active = true;
    this.isShowing = true;
    this.node.setPosition(0, -760);
    cc.tween(this.node)
      .to(0.2, {
        position: cc.v3(this.node.position.x, this.node.position.y + 760),
      })
      .start();
  }

  close() {
    this.isShowing = false;
    SoundManager.inst.playEffect("CLICK_BTN");
    cc.tween(this.node)
      .to(0.2, {
        position: cc.v3(this.node.position.x, this.node.position.y - 760),
      })
      .call(() => {
        this.node.active = false;
      })
      .start();
  }
}
