import { EventType, Events } from "../../events/Events";
import SoundManager from "../../sounds/SoundManager";
import { ResultData } from "../../utils/Gameinterface";
import PopupManager from "./PopupManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class notificationPopup extends cc.Component {
  @property(cc.Sprite)
  blurBg: cc.Sprite = null;

  @property(cc.Label)
  titleLbl: cc.Label = null;

  @property(cc.Label)
  content: cc.Label = null;

  private _waitPromise = null;

  onLoad() {
    // this.confirmBtn.node.on(cc.Node.EventType.TOUCH_START, this.onClickOkButton.bind(this));
  }

  start() {}

  public show(content: string): Promise<void> {
    this.titleLbl.string = "No Moves Remaining";
    this.content.string = content;
    this.node.active = true;
    return new Promise<void>((resolve) => {
      this._waitPromise = resolve;
    });
  }

  onClickOkButton(): void {
    SoundManager.inst.playEffect("CLICK_BTN");
    this.node.active = false;
    this._waitPromise && this._waitPromise();
    PopupManager.inst.openSelectMode();
  }

  onClickCountinueButton(): void {
    SoundManager.inst.playEffect("CLICK_BTN");
    Events.Emit(EventType.RESET_GAMEOVER);
    this.node.active = false;
  }
}
