import { EventType, Events } from "../../events/Events";
import { ResultData } from "../../utils/Gameinterface";
import GameRule from "./GameRule";
import InformationPopup from "./InformationPopup";
import NotificationPopup from "./NotificationPopup";
import ResultPopup from "./ResultPopup";
import resultPopup from "./ResultPopup";

import SelectGamePopup from "./SelectGamePopup";
import SelectSuitPopup from "./SelectSuitPopup";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PopupManager extends cc.Component {
  public static inst: PopupManager = null!;

  @property(cc.Node)
  resultPopup: cc.Node = null;

  @property(cc.Node)
  gameRule: cc.Node = null;

  @property(cc.Node)
  selectMode: cc.Node = null;

  @property(cc.Node)
  notificationPopup: cc.Node = null;

  @property(cc.Node)
  informationPopup: cc.Node = null;

  onLoad() {
    if (PopupManager.inst) {
      cc.log("There are several PopupManager construction!!!");
    }
    PopupManager.inst = this;

    globalThis.PopupManager = this;
  }

  public openGameRule() {
    this.gameRule.getComponent(GameRule).open();
  }

  public openSelectMode(isStart: boolean = false) {
    this.selectMode.getComponent(SelectGamePopup).open(isStart);
  }

  public openSuitMode(isStart: boolean = false) {
    this.selectMode.getComponent(SelectSuitPopup).open(isStart);
  }

  public async openResult(isWin: boolean, resultData: ResultData, key: string) {
    const popupHandler = this.resultPopup.getComponent(ResultPopup);
    await popupHandler.show(isWin, resultData, key);
  }

  public async openNotification() {
    const popupHandler = this.notificationPopup.getComponent(NotificationPopup);
    const content = "You can undo some steps to win the game.";
    await popupHandler.show(content);
  }

  public openInformation(content: string) {
    this.informationPopup.getComponent(InformationPopup).open(content);
  }

  public closeInformation() {
    this.informationPopup.getComponent(InformationPopup).close();
  }

  isPopupShowing(): any {
    switch (true) {
      case this.informationPopup?.getComponent(InformationPopup).isShowing:
        return true;
      default:
        return false;
    }
  }
}
