import { EventType, Events } from "../../events/Events";
import SoundManager from "../../sounds/SoundManager";
import { DrawMode, SuitMode } from "../../utils/Gameinterface";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SelectSuitPopup extends cc.Component {
  @property(cc.Sprite)
  blurBg: cc.Sprite = null;

  @property(cc.Button)
  confirmBtn: cc.Button = null;

  @property(cc.Node)
  btnClose: cc.Node = null;

  private _waitPromise = null;
  private _clickBtnCallback = null;
  private modeGame: SuitMode = SuitMode.ONE;

  public open(isStart: boolean): Promise<void> {
    this.btnClose.active = !isStart;
    this.node.active = true;
    return new Promise<void>((resolve) => {
      this._waitPromise = resolve;
    });
  }

  onSelectDraw(sender, idx) {
    const mode = parseInt(idx);
    switch (mode) {
      case 1:
        this.modeGame = SuitMode.ONE;
        break;
      case 2:
        this.modeGame = SuitMode.TWO;
        break;
      case 4:
        this.modeGame = SuitMode.FOUR;
        break;
    }
  }

  onClickNewPlay(): void {
    SoundManager.inst.playEffect("CLICK_BTN");
    this._clickBtnCallback && this._clickBtnCallback();
    this.node.active = false;
    this._waitPromise && this._waitPromise();
    Events.Emit(EventType.SELECT_SUIT, this.modeGame);
    Events.Emit(EventType.NEW_GAME, "Spider_HighScore");
  }

  onClose() {
    SoundManager.inst.playEffect("CLICK_BTN");
    this.node.active = false;
  }
}
