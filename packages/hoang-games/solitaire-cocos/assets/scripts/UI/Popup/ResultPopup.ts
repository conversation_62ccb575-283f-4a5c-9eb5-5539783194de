import { EventType, Events } from "../../events/Events";
import SoundManager from "../../sounds/SoundManager";
import { ResultData } from "../../utils/Gameinterface";
import SaveGame from "../../utils/SaveGame";
import PopupManager from "./PopupManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ResultPopup extends cc.Component {
  @property(cc.Sprite)
  blurBg: cc.Sprite = null;

  @property(cc.Label)
  titleLbl: cc.Label = null;

  @property(cc.Label)
  score: cc.Label = null;

  @property(cc.Label)
  highScore: cc.Label = null;

  @property(cc.Label)
  timer: cc.Label = null;

  private _waitPromise = null;

  onLoad() {
    SaveGame.init();
    // this.confirmBtn.node.on(cc.Node.EventType.TOUCH_START, this.onClickOkButton.bind(this));
  }

  start() {}

  public show(isWin: boolean, content: ResultData, key: string): Promise<void> {
    const title = isWin ? "Congratulations!" : "Game Over";
    if (isWin) {
      SoundManager.inst.playEffect("WIN");
    }
    this.titleLbl.string = title.toUpperCase();
    this.node.active = true;
    this.score.string = `Score: ${content.score}`;
    this.timer.string = `Time: ${content.time}`;
    let getData = SaveGame.getInstance().getData(key);
    if (!getData) {
      getData = content.score;
    }
    this.highScore.string = `High score: ${getData}`;
    return new Promise<void>((resolve) => {
      this._waitPromise = resolve;
    });
  }

  onClickPlayKlondike(): void {
    SoundManager.inst.playEffect("CLICK_BTN");
    this.node.active = false;
    this._waitPromise && this._waitPromise();
    PopupManager.inst.openSelectMode();
  }

  onClickPlaySpider(): void {
    SoundManager.inst.playEffect("CLICK_BTN");
    this.node.active = false;
    this._waitPromise && this._waitPromise();
    PopupManager.inst.openSuitMode();
  }
}
