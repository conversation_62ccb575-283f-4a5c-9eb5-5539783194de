import SoundManager from "../../sounds/SoundManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameRule extends cc.Component {
  private _scrollView: cc.ScrollView = null;
  private _mainNode: cc.Node = null;

  onLoad() {
    this._scrollView = this.getComponentInChildren(cc.ScrollView);
    this._mainNode = cc.find("Canvas/Gameplay");
    if (this._mainNode) {
      this._mainNode.on(cc.Node.EventType.SIZE_CHANGED, () => {
        this._onCanvasSizeChanged();
      });
      this._onCanvasSizeChanged();
    }
  }

  private _onCanvasSizeChanged() {
    this.node.width = this._mainNode.width;
  }

  start() {}

  open() {
    this.node.active = true;
    this.node.setPosition(0, -760);
    cc.tween(this.node)
      .to(0.2, {
        position: cc.v3(this.node.position.x, this.node.position.y + 760),
      })
      .start();
  }

  close() {
    SoundManager.inst.playEffect("CLICK_BTN");
    this.stopScroll();

    cc.tween(this.node)
      .to(0.2, {
        position: cc.v3(this.node.position.x, this.node.position.y - 760),
      })
      .call(() => {
        this.node.active = false;
      })
      .start();
  }

  stopScroll() {
    this._scrollView?.stopAutoScroll();
    this._scrollView?.scrollToTop(0);
  }
}
