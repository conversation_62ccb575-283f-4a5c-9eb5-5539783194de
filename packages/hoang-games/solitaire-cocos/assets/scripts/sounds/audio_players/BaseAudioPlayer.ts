import { SoundId } from '../AudioResources';
import { AudioSystemData, AudioPlayerType, AudioConfig } from './AudioPlayerData';
import { AudioSource } from './AudioSource';

const { ccclass, property } = cc._decorator;

export type LoadProgressCallback = (finish: number, total: number) => void;
export type LoadCompleteCallback = (isSuccess: boolean) => void;
export type AudioStartCallback = (sId: SoundId, info: AudioConfig, source: AudioSource, pId: number, duration: number) => void;
export type AudioEndCallback = (sId: SoundId, pId: number) => void;

export default abstract class BaseAudioPlayer {
    protected _ctx: cc.Component;
    protected _type: AudioPlayerType;
    protected _shareVars: AudioSystemData;
    protected _wasCleanUp: boolean = false;

    constructor(context: cc.Component, shareVars: AudioSystemData) {
        this._ctx = context;
        this._shareVars = shareVars;
    }

    public cleanUp(): void {
        this._wasCleanUp = true;
    }

    public abstract onLoadAudio(packName: string, clip: cc.AudioClip, howlOpts: unknown): void;
    public async onVerifyResources(totalOfAssets: number, progress?: LoadProgressCallback, complete?: LoadCompleteCallback): Promise<boolean> { return true; }
    public async onLoadAudioComplete(totalOfAssets: number): Promise<boolean> { return true; }
    public abstract init(): void;
    public abstract pause(sId: SoundId): void;
    public abstract resume(sId: SoundId): boolean;
    public abstract resumeBySource(source: AudioSource, vol: number, isReplay: boolean): number;
    public abstract stop(sId: SoundId): void;
    public abstract stopByPlayId(pId: number): boolean;
    public abstract playing(sId: SoundId, includePause?: boolean): boolean;
    public abstract playingByPlayId(pId: number): boolean;
    public abstract playAudio(sId: SoundId, info: AudioConfig, vol: number, loop: boolean, startCb: AudioStartCallback, endCb: AudioEndCallback, isBgm: boolean): number;
    public abstract fade(
        sId: SoundId,
        from: number | 'AUTO',
        to: number | 'AUTO',
        duration: number,
        stopAfterEnd: boolean,
        endCallback?: Function,
        nearEndCallback?: Function,
        nearFactor?: number,
        playId?: number
    ): number;

    public abstract getPlayingAudio(sId: SoundId): AudioSource[];
    public abstract getPlayingMusic(sId: SoundId): AudioSource;
    public abstract onChangeSoundOption(isOn: boolean, isMusicSetting: boolean): void;

    public onPause(isPaused: boolean): void {
        if (isPaused) {
            this._onInterrupt();
        } else {
            this._onGameResume();
        }
    }

    /* public async waitStopImportantAudios(): Promise<void> {
        return new Promise<void>((resolve) => {
            const checkPlaying = window.setInterval(() => {
                let hasPlayAudio = false;
                this._shareVars.importantSounds.forEach((sId) => {
                    if(this.playing(sId)) {
                        hasPlayAudio = true;
                    }
                });
                if(!hasPlayAudio) {
                    window.clearInterval(checkPlaying);
                    resolve();
                }
            }, 100);
        });
    } */

    public isWebPlayer(): boolean {
        return this._type === AudioPlayerType.Web;
    }

    public getType(): AudioPlayerType {
        return this._type;
    }

    protected abstract _onInterrupt(): void;
    protected abstract _onGameResume(): void;

    protected _isVolumeValid(vol: number | 'AUTO'): boolean {
        if ((vol && vol === 'AUTO') || (vol >= 0 && vol <= 1)) {
            return true;
        }
        return false;
    }
}
