import { SoundId } from '../AudioResources';
import { AudioConfig, AudioPlayerType, ChannelData } from './AudioPlayerData';
import { AudioEndCallback } from './BaseAudioPlayer';
import { Howl, HowlOptions } from 'howler';

export type AudioPendingEndCallback = {
    owner: AudioSource;
    playId: number;
    callback: AudioEndCallback;
};

export type FadingTween = {
    handler: cc.Tween<AudioSource>;
    from: number;
    to: number;
    debugMsg?: string;
};

export class AudioSource {
    public audioId: SoundId = 'NONE';
    public playId: number = -1;
    public info: AudioConfig = null!;
    public clip: cc.AudioClip = null!;

    public fadingHandler: FadingTween = null!;
    public endCallback: AudioPendingEndCallback = null!;

    public isMusic: boolean = false;
    public isFading: boolean = false;
    public isAutoStop: boolean = false;
    public isLockVolume: boolean = false;

    public player: Howl = null!;

    private _type: AudioPlayerType;

    constructor(pType: AudioPlayerType, channelData: ChannelData) {
        this._type = pType;
        this.clip = channelData.clip;
        this.clip.addRef();
        this.player = channelData.howl;
    }

    public reset(): void {
        if (this.playId === -1) {
            return;
        }

        --this.info.numberOfPlaying;
        this.playId = -1;
        this.audioId = 'NONE';
        this.isMusic = false;
        this.isAutoStop = false;
        this.info = null;
    }

    public howl(): Howl {
        return this.player;
    }

    public isWebAudio(): boolean {
        return this._type === AudioPlayerType.Web;
    }

    public cleanUp(): void {
        //TODO: handle clean up
        this.clip.decRef();
    }

    public clearFadeInfo(): void {
        this.fadingHandler = null;
        this.isFading = false;
    }

    private initHowl(howlOpts: HowlOptions): void {
        this.player = new Howl(howlOpts);
    }

    /* Generators */
    /* public static createWebAudioSource(clip: cc.AudioClip, howlOpts: HowlOptions): AudioSource {
        const source = new AudioSource(AudioPlayerType.Web, clip);
        source.initHowl(howlOpts);
        return source;
    } */
}
