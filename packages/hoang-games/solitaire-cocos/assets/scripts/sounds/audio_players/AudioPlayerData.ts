import { Howl } from 'howler';
import { AudioPackId, SoundId } from '../AudioResources';

export enum AudioPlayerType {
    None = 0,
    Native,
    Web,
}

export type ChannelData = {
    howl: Howl,
    clip: cc.AudioClip
};

export interface AudioConfig {
    pack: AudioPackId;
    sprite: string;
    loop: boolean;
    volume: number;
    isMusic: boolean;

    numberOfPlaying: number;
}

export interface AudioSystemData {
    audioConfigs: Map<SoundId, AudioConfig>;
    audioPacks: string[];
    totalOfPacks: number;

    bgmId: SoundId;
    isMusicOn: boolean;
    isEffectOn: boolean;

    maxAudioAtTimes: number;
    totalOfPlayingAudios: number;
    isGamePaused: boolean;

    debug: boolean;
}
