import BaseAudioPlayer, { AudioEndCallback, AudioStartCallback, LoadCompleteCallback, LoadProgressCallback } from './BaseAudioPlayer';
import { AudioFormat, AudioPackId, SoundId, MusicIds } from '../AudioResources';
import { AudioConfig, ChannelData as AudioHost, AudioPlayerType, AudioSystemData } from './AudioPlayerData';
import { AudioSource } from './AudioSource';
import { HowlOptions, Howl } from 'howler';
import Utils from '../../utils/Utils';

type AudioPlayingInfo = {
    sId: SoundId;
    source: AudioSource;
    playIds: number[];
    interrupt: boolean;
};
type LoadStatus = { finish: number; total: number };

export default class WebAudioPlayer extends BaseAudioPlayer {
    private readonly _SUSPEND_CONTEXT_DEVICES: string[] = [
        'SM-N970', // Galaxy Note 10
    ];

    private _players: Map<AudioPackId, AudioHost> = null!;
    private _playingAudios: Map<SoundId, AudioPlayingInfo> = null!;
    private _pendingEndCallbacks: Map<SoundId, AudioSource> = null!;
    private _savedVolumeCfg: Map<number, number> = null!;
    private _loadStatus: LoadStatus = null!;
    private _isSuspendContext: boolean = false;

    constructor(context: cc.Component, shareVars: AudioSystemData) {
        super(context, shareVars);
        this._type = AudioPlayerType.Web;
    }

    public cleanUp(): void {
        Howler.mute(true);
        this._playingAudios.forEach((playInfo) => {
            const howl = playInfo.source.howl();
            playInfo.playIds.forEach((pId) => {
                if (howl.playing(pId)) {
                    howl.stop(pId);
                }
            });
        });
        // clean audio
        /* this._players.forEach((audioPlayer) => {
            audioPlayer.cleanUp();
        }); */

        this._playingAudios.clear();
        this._players.clear();
        this._pendingEndCallbacks.clear();
        this._savedVolumeCfg.clear();
        this._SUSPEND_CONTEXT_DEVICES.length = 0;

        super.cleanUp();
    }

    public onLoadAudio(packName: AudioPackId, clip: cc.AudioClip, howlOpts: HowlOptions): void {
        howlOpts.preload = false;
        howlOpts.format = AudioFormat;
        howlOpts.src = clip.nativeUrl;
        const howl: Howl = new Howl(howlOpts);
        // this._players.set(packName, AudioSource.createWebAudioSource(clip, howlOpts));
        this._players.set(packName, { howl, clip });
    }

    public async onVerifyResources(totalOfAssets: number, progress?: LoadProgressCallback, complete?: LoadCompleteCallback): Promise<boolean> {
        // On web platform, we need to wait for the howler to init (download audio)
        return new Promise<boolean>((resolve, reject) => {
            this._loadStatus.total = totalOfAssets;
            this._players.forEach((player, packName) => {
                const howl = player.howl;
                howl.on('load', () => {
                    if (this._shareVars.debug) {
                        cc.warn(`[SOUND] Howler Load: ${packName}`);
                    }
                    const finishTask = ++this._loadStatus.finish;
                    progress && progress(finishTask, this._loadStatus.total);
                    if (finishTask >= this._loadStatus.total) {
                        if (this._shareVars.debug) {
                            cc.warn('[SOUND] Howler Load << DONE!');
                        }
                        complete && complete(true);
                        resolve(true);
                    }
                });
                howl.on('loaderror', (nativeId: number, error: unknown) => {
                    cc.warn(`[SOUND] Howler Load failed: ${packName}`);
                    reject(error);
                });
                howl.on('playerror', (nativeId: number, error: unknown) => {
                    cc.warn(`[SOUND] Howler Play error: ${packName}`);
                });
                howl.load();
            });
        });
    }

    public init(): void {
        this._players = new Map();
        this._playingAudios = new Map();
        this._pendingEndCallbacks = new Map();
        this._savedVolumeCfg = new Map<number, number>();
        this._loadStatus = { finish: 0, total: -1 };

        // cc.log('[SOUND] User agent: ' + navigator.userAgent);
        let isLoop: boolean = true;
        for (let i = 0; i < this._SUSPEND_CONTEXT_DEVICES.length && isLoop; i++) {
            const device = this._SUSPEND_CONTEXT_DEVICES[i];
            if (navigator.userAgent.indexOf(device) !== -1) {
                this._isSuspendContext = true;
                isLoop = false;
                // cc.log('[SOUND] Use Suspend Context');
            }
        }

        // disable auto suspend AudioContext after 30s
        Howler.autoSuspend = false;
        Howler.volume(1);
        Howler.mute(false);
    }

    public pause(sId: SoundId): void {
        const playInfo = this._playingAudios.get(sId);
        if (playInfo) {
            playInfo.playIds.forEach((pId) => {
                playInfo.source.howl().pause(pId);
            });
        }
    }

    public resume(sId: SoundId): boolean {
        if (this._shareVars.isGamePaused) {
            cc.warn(`[SOUND] ${sId} is resume during game pause, skip!`);
            return false;
        }
        let result: boolean = false;
        const playInfo = this._playingAudios.get(sId);
        if (playInfo) {
            let isLoop: boolean = true;
            for (let i = 0; i < playInfo.playIds.length && isLoop; i++) {
                const pId = playInfo.playIds[i];
                if ((playInfo.source.isMusic && this._shareVars.isMusicOn) || (!playInfo.source.isMusic && this._shareVars.isEffectOn)) {
                    playInfo.source.howl().play(pId);
                    result = true;
                    isLoop = false;
                }
            }
        }
        return result;
    }

    public resumeBySource(source: AudioSource, vol: number, isReplay: boolean): number {
        if ((source.isMusic && !this._shareVars.isMusicOn) || (!source.isMusic && !this._shareVars.isEffectOn)) {
            return -1;
        }

        const howl = source.howl();
        const pId = source.playId;
        if (isReplay === true) {
            howl.volume(0, pId);
            howl.seek(0, pId);
        }
        howl.volume(vol, pId);
        howl.play(pId);

        return pId;
    }

    public stop(sId: SoundId): void {
        const playInfo = this._playingAudios.get(sId);
        if (playInfo) {
            playInfo.playIds.forEach((pId) => {
                playInfo.source.howl().stop(pId);
                // cc.warn(`[SOUND] ${sId}(${pId}) stopped!`);
            });
        }
    }

    public stopByPlayId(pId: number): boolean {
        let isStop: boolean = false;
        const playInfo = this._getPlayAudioByPlayId(pId);
        if (playInfo) {
            playInfo.playIds.forEach((playingId) => {
                playInfo.source.howl().stop(playingId);
                // cc.warn(`[SOUND] ${playInfo.sId}(${playingId}) stopped!`);
                isStop = true;
            });
        }
        return isStop;
    }

    public playing(sId: SoundId, includePause: boolean = false): boolean {
        let isPlaying: boolean = false;
        const playInfo = this._playingAudios.get(sId);
        if (playInfo) {
            let isLoop: boolean = true;
            for (let i = 0; i < playInfo.playIds.length && isLoop; i++) {
                const pId = playInfo.playIds[i];
                if (playInfo.source.howl().playing(pId) || (includePause && this._howlIsPaused(playInfo.source, pId))) {
                    isPlaying = true;
                    isLoop = false;
                }
            }
        }
        return isPlaying;
    }

    public playingByPlayId(pId: number): boolean {
        let isPlaying: boolean = false;
        const playInfo: AudioPlayingInfo = this._getPlayAudioByPlayId(pId);
        if (playInfo) {
            let isLoop: boolean = true;
            for (let i = 0; i < playInfo.playIds.length && isLoop; i++) {
                const pId = playInfo.playIds[i];
                if (playInfo.source.howl().playing(pId)) {
                    isPlaying = true;
                    isLoop = false;
                }
            }
        }
        return isPlaying;
    }

    public playAudio(sId: SoundId, info: AudioConfig, vol: number, loop: boolean, startCb: AudioStartCallback, endCb: AudioEndCallback, isBgm: boolean): number {
        const { pack, sprite } = info;
        const playerData = this._players.get(pack);
        const source: AudioSource = new AudioSource(AudioPlayerType.Web, playerData);
        const howl = source.howl();
        if (howl.state() !== 'loaded') {
            cc.warn(`[SOUND] ${pack}(${howl.state()}) not load yet!`);
            return -1;
        }
        const pId = howl.play(sprite);
        source.audioId = sId;
        source.playId = pId;
        source.isMusic = info.isMusic && isBgm;
        source.info = info;
        howl.loop(loop, pId);
        howl.volume(vol, pId);

        const audioEndListener = (endId) => {
            if (!this._wasCleanUp) {
                // check sound in playing list to avoid error when play sound silence
                if (this._playingAudios.has(sId)) {
                    const playInfo = this._playingAudios.get(sId);
                    if (playInfo.playIds.length > 0) {
                        const idxOfEndId = playInfo.playIds.indexOf(endId);
                        if (idxOfEndId > -1) {
                            playInfo.playIds.splice(idxOfEndId, 1);
                            if (playInfo.playIds.includes(endId)) {
                                cc.error(`[SOUND] Remove ${sId}(${pId}) failed!`);
                            }
                        } else {
                            cc.warn(`[SOUND] ${sId}(${pId}) not found in playing audios!`);
                        }
                        if (playInfo.playIds.length === 0) {
                            this._playingAudios.delete(sId);
                        }
                    }
                } else {
                    cc.warn(`[SOUND] Stop non-play audio ${sId}(${pId})`);
                }
            }

            howl.off('end', null, endId);
            howl.off('stop', null, endId);

            if (endCb) {
                if (source.isFading) {
                    source.endCallback = { owner: source, playId: pId, callback: endCb };
                    this._pendingEndCallbacks.set(sId, source);
                } else {
                    endCb(sId, endId);
                }
            }

            --this._shareVars.totalOfPlayingAudios;
            source.reset();
        };
        howl.once('stop', audioEndListener.bind(this), pId);
        if (!loop) {
            howl.once('end', audioEndListener.bind(this), pId);
        }

        if (this._playingAudios.has(sId)) {
            const playInfo = this._playingAudios.get(sId);
            playInfo.playIds.push(pId);
            if (playInfo.source && playInfo.source.howl() !== howl) {
                cc.warn(`[SOUND] Diff player of audio ${sId}(${pId})`);
            }
        } else {
            this._playingAudios.set(sId, {
                sId,
                source,
                playIds: [pId],
                interrupt: false,
            });
        }
        ++info.numberOfPlaying;
        ++this._shareVars.totalOfPlayingAudios;

        // cc.warn(`[SOUND] PlayAudio: ${sId}(${pId})`);

        const playCallback = () => {
            if (howl.playing(pId) && howl.seek(pId) >= 0) {
                // just to get id for fade music
                if (vol === 0) {
                    howl.seek(0, pId);
                    howl.pause(pId);
                    // cc.warn(`[SOUND] ${sId}(${pId}), Vol: 0, pause to fading!`);
                } else {
                    // validate volume config in late update
                    if (howl.volume(pId) === 0 && (source.isMusic && this._shareVars.isMusicOn || !source.isMusic && this._shareVars.isEffectOn)) {
                        howl.volume(vol, pId);
                        cc.warn(`[SOUND] ${sId}(${pId}), set vol turn 2: ${howl.volume(pId)}, queue`, howl['_queue'].slice(-1));
                    }
                }
                startCb && startCb(sId, info, source, pId, howl.duration(pId));
                this._ctx.unschedule(playCallback);
            }/*  else {
                cc.warn(`[SOUND] ${sId}(${pId}) wait to ready...`);
            } */
        };
        this._ctx.schedule(playCallback, 0.001, cc.macro.REPEAT_FOREVER);
        return pId;
    }

    public fade(
        sId: SoundId,
        from: number | 'AUTO',
        to: number | 'AUTO',
        duration: number,
        stopAfterEnd: boolean,
        endCallback?: Function,
        nearEndCallback?: Function,
        nearFactor?: number,
        playId?: number
    ): number {
        let source: AudioSource;
        if (playId !== -1) {
            source = this.getPlayingAudioByPlayId(playId);
            if (!sId && source) {
                sId = source.audioId;
            }
        } else {
            const sources = this.getPlayingAudio(sId);
            if (sources.length > 0) {
                source = sources[0];
                playId = source.playId;
            } else {
                const callbackOwner = this._pendingEndCallbacks.get(sId);
                if (callbackOwner && callbackOwner.endCallback) {
                    const endEvent = callbackOwner.endCallback;
                    if (endEvent.callback) {
                        endEvent.callback(sId, endEvent.playId);
                    } else {
                        cc.warn(`[SOUND] ${sId} missing callback for pending callback`);
                    }
                    callbackOwner.endCallback = null;
                    this._pendingEndCallbacks.delete(sId);
                } else {
                    cc.warn(`[SOUND] ${sId} not play, can't fade!`);
                }
                return -1;
            }
        }

        if (!source) {
            cc.error(`[SOUND] ${sId} (${playId}) is null, recheck this!`);
            return -1;
        } else if (playId === -1) {
            cc.warn(`[SOUND] ${sId} not play, cant fade!`);
            return -1;
        } else if (source.howl().state() !== 'loaded') {
            cc.warn(`[SOUND] ${sId} not loaded yet, cant fade!`);
            return -1;
        }

        // if(playInfo.ids.length > 1 && playInfo.howl.isMusic) {
        //     warn(`[SOUND] Check case fading sound has multiple playing channels!\nCurrent fading info: ${soundName}(${playId1})`);
        // }

        // get sound config
        const info = this._shareVars.audioConfigs.get(sId);
        const howl = source.howl();
        const parsedFrom = Number.parseFloat(from + '');
        const parsedTo = Number.parseFloat(to + '');
        from = !Number.isNaN(parsedFrom) ? parsedFrom : <number>howl.volume(playId);
        to = !Number.isNaN(parsedTo) ? parsedTo : info.volume;

        const fadeInfo = `${sId} (${playId}: ${from} => ${to}) ${duration}s`;
        if (!this._isVolumeValid(from) || !this._isVolumeValid(to)) {
            cc.error(`[SOUND] ${fadeInfo}, error volume range!`);
            return -1;
        }
        if (duration >= 5) {
            cc.warn(`[SOUND] ${fadeInfo}, time fade too long!`);
        }

        // check correct fading journey
        const isAudioGroupDisabled = (info.isMusic && !this._shareVars.isMusicOn) || (!info.isMusic && !this._shareVars.isEffectOn);
        if (from === to) {
            cc.warn(`[SOUND] ${fadeInfo} fade duration is zero, recheck this!`);
            if (to === 0 && !isAudioGroupDisabled) {
                // skip fading from 0 -> 0 during sound off
                if (stopAfterEnd) {
                    // warn(`[SOUND] ${sId} stop!`);
                    howl.stop(playId);
                    this._savedVolumeCfg.delete(playId);
                    playId = -1;
                } else {
                    // warn(`[SOUND] ${sId} pause!`);
                    howl.pause(playId);
                }
            }
            if (nearEndCallback) {
                nearEndCallback();
            }
            if (endCallback) {
                endCallback();
            }
            return playId;
        }
        // just fade if enable sound
        if (isAudioGroupDisabled) {
            if (info.isMusic && to > 0) {
                this._shareVars.bgmId = sId;
            }
            // source.volume = to;
            // source.currentTime = 0;
            if (stopAfterEnd) {
                // warn(`[SOUND] ${sId} stop!`);
                howl.stop(playId);
                //TODO: reset current music id
            } else {
                if (isAudioGroupDisabled) {
                    source.isLockVolume = true;
                }
                howl.pause(playId);
                // warn(`[SOUND] ${sId} pause, ${source.volume}, ${source.stateStr()}`);
            }
            let isInvokeCallback = false;
            if (nearEndCallback) {
                isInvokeCallback = true;
                nearEndCallback();
            }
            if (endCallback && !isInvokeCallback) {
                endCallback();
            }
            return playId;
        }

        // clear current fading callback
        if (source.isFading) {
            source.isFading = false;
            howl.off('fade', null, playId);
            source.fadingHandler = null;
        }

        if ((to > 0 && info && info.isMusic) || MusicIds.indexOf(sId) > -1) {
            this._shareVars.bgmId = sId;
        }

        // setup fade event and callback
        const fadeEventHandler = {
            onStart: () => {
                source.isFading = true;
                if (!howl.playing(playId)) {
                    howl.play(playId);
                }
            },
            onComplete: (triggerId) => {
                // cc.warn(`[SOUND] ${fadeInfo}, vol: ${source.howl().volume(playId)}`);
                // check this fade is fade down, to stop or pause sound
                if (from > to) {
                    if (stopAfterEnd && to === 0) {
                        howl.stop(triggerId);
                        if (this._shareVars.bgmId === sId) {
                            this._shareVars.bgmId = 'NONE';
                        }
                    } else {
                        howl.pause(triggerId);
                    }
                }
                if (endCallback) {
                    endCallback();
                }
                source.clearFadeInfo();
            },
        };
        if (nearEndCallback) {
            const parseNearFactor = Number.parseFloat(nearFactor + '');
            nearFactor = !Number.isNaN(parseNearFactor) ? nearFactor : 0.75;
            if (nearFactor >= 0 && nearFactor <= 1) {
                this._ctx.scheduleOnce(nearEndCallback, nearFactor * duration);
            } else {
                cc.error(`[SOUND] ${fadeInfo}, nearFactor must in range [0, 1]!`);
            }
        }
        // start fade
        howl.volume(from, playId);
        howl.once('fade', fadeEventHandler.onComplete.bind(this), playId);
        howl.fade(from, to, duration * 1000, playId);
        fadeEventHandler.onStart();
        source.fadingHandler = { from, to, handler: null };
        // cc.warn(`[SOUND] ${fadeInfo}, seek: ${howl.seek(playId).toFixed(1)}/${howl.duration(playId).toFixed(1)} = ${(howl.seek(playId)/howl.duration(playId)).toFixed(1)}%`);

        return playId;
    }

    public getPlayingAudio(sId: SoundId): AudioSource[] {
        const playInfo = this._playingAudios.get(sId);
        const result: AudioSource[] = [];
        if (playInfo) {
            playInfo.playIds.forEach((pId) => {
                result.push(playInfo.source);
            });
        }
        return result;
    }

    public getPlayingAudioByPlayId(pId: number): AudioSource {
        if (pId < 0) return null;
        const playInfo: AudioPlayingInfo = this._getPlayAudioByPlayId(pId);
        return playInfo ? playInfo.source : null;
    }

    public getPlayingMusic(sId: SoundId): AudioSource {
        let result: AudioSource = null;
        const playInfo = this._playingAudios.get(sId);
        if (playInfo) {
            let canLoop: boolean = true;
            for (let i = 0; i < playInfo.playIds.length && canLoop; i++) {
                const pId = playInfo.playIds[i];
                if (!playInfo.source.howl().playing(pId)) {
                    result = playInfo.source;
                    canLoop = false;
                }
            }
        }
        return result;
    }

    public onChangeSoundOption(isOn: boolean, isMusicSetting: boolean): void {
        if (isOn) {
            this._playingAudios.forEach((playInfo) => {
                const howl = playInfo.source.howl();
                const playId = playInfo.source.playId;
                const audioId = playInfo.source.audioId;
                const isMusic = playInfo.source.isMusic;
                if (!isMusic && this._savedVolumeCfg.has(playId)) {
                    if (howl.playing(playId)) {
                        const lastVolume = this._savedVolumeCfg.get(playId);
                        howl.volume(lastVolume, playId);
                        this._savedVolumeCfg.delete(playId);
                    }
                }

                // check and resume music
                if (isMusicSetting && isMusic && audioId === this._shareVars.bgmId) {
                    howl.play(playId);
                    if (howl.volume(playId) === 0) {
                        const info = this._shareVars.audioConfigs.get(audioId);
                        const originVol = info ? info.volume : 1;
                        // check and reset current time when user play game during sound off
                        if (playInfo.source.isLockVolume) {
                            playInfo.source.isLockVolume = false;
                            howl.seek(0, playId);
                        }
                        howl.volume(originVol, playId);
                        // cc.warn(`[SOUND] Setting On, check volume: ${audioId}, newVol: ${originVol}, time: ${howl.seek(playId)}`);
                    }/*  else {
                        cc.warn(`[SOUND] Setting On: ${audioId}, playing: ${howl.playing(playId)}`);
                    } */
                }
            });
            this._savedVolumeCfg.clear();
        } else {
            this._playingAudios.forEach((playInfo) => {
                // cc.warn(`${playInfo.source}`);
                if ((playInfo.source.isMusic && isMusicSetting) || (!playInfo.source.isMusic && !isMusicSetting)) {
                    const howl = playInfo.source.howl();
                    const audioId = playInfo.source.audioId;
                    playInfo.playIds.forEach((pId) => {
                        if (howl.playing(pId)) {
                            const vol = <number>howl.volume(pId);
                            this._savedVolumeCfg.set(pId, vol);
                            // cc.warn(`[SOUND] Setting Off: ${audioId}(${pId}), pause!`);
                            if (playInfo.source.isMusic) {
                                howl.pause(pId);
                            } else {
                                howl.volume(0, pId);
                            }
                        } else {
                            cc.error(`[SOUND] Cant pause: ${audioId}(${pId}), playing: ${howl.playing(pId)}`);
                        }
                    });
                }
            });
        }
    }

    protected _onInterrupt(): void {
        this._playingAudios.forEach((playInfo, sId) => {
            let isPaused: boolean = false;
            playInfo.playIds.forEach((pId) => {
                const howl = playInfo.source.howl();
                if (howl.playing(pId)) {
                    howl.pause(pId);
                    isPaused = true;
                    // cc.warn(`[SOUND] ${sId}(${pId}) pause!`);
                }
            });
            playInfo.interrupt = isPaused;
        });
        Howler.mute(true);
        this._suspendAudioContext();
    }

    protected _onGameResume(): void {
        this._resumeAudioContext()
            .then(() => {
                this._playingAudios.forEach((playInfo, sId) => {
                    if (!this._shareVars.isMusicOn && (playInfo.sId === this._shareVars.bgmId || playInfo.source.isMusic)) {
                        return;
                    }
                    if (playInfo.interrupt) {
                        playInfo.playIds.forEach((pId) => {
                            playInfo.source.howl().play(pId);
                            // cc.warn(`[SOUND] ${sId}(${pId}) resume!`);
                        });
                        playInfo.interrupt = false;
                    }
                });
                Howler.mute(false);
            })
            .catch((reason) => {
                Howler.mute(false);
                cc.error('[SOUND] Cant Resume Context! ' + reason);
            });
    }

    private _resumeAudioContext(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            if (Howler.ctx) {
                if (Howler.ctx.state !== 'running') {
                    Howler.ctx
                        .resume()
                        .then(() => {
                            // cc.warn('[SOUND] ResumeAC: Resume success!');
                            resolve();
                        })
                        .catch((reason) => {
                            cc.error(`[SOUND] ResumeAC: Resume failed! (${reason})`);
                            reject(reason);
                        });
                } else {
                    // cc.warn(`[SOUND] ResumeAC: Skip (${Howler.ctx.state})`);
                    resolve();
                }
            } else {
                if (this._playingAudios.size > 0) {
                    cc.error('[SOUND] ResumeAC: AC null');
                    reject('AudioContext is null');
                } else {
                    // No sound need to resume, on start game, skip
                    cc.warn('[SOUND] ResumeAC: AC null on start game, skip');
                    resolve();
                }
            }
        });
    }

    private _suspendAudioContext(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            if (Howler.ctx) {
                if (this._isSuspendContext) {
                    Howler.ctx
                        .suspend()
                        // #!if ENV === 'development'
                        .then(() => {
                            cc.log('[SOUND] InterruptAC: Manual Suspended!');
                            resolve();
                        })
                        // #!endif
                        .catch((reason) => {
                            cc.warn('[SOUND] InterruptAC: Manual Suspended, Error ' + reason);
                            reject(reason);
                        });
                } else {
                    cc.log('[SOUND] InterruptAC: ' + Howler.ctx.state);
                    resolve();
                }
            } else {
                cc.warn('[SOUND] InterruptAC: Context is null!');
                resolve();
            }
        });
    }

    // Check correct player status from web native player
    private _howlIsPaused(source: AudioSource, pId: number): boolean {
        const nativeSounds: Object[] = source.howl()['_sounds'].filter((x) => x._id === pId);
        if (nativeSounds.length > 0) {
            const isPaused = nativeSounds.map((x) => x['_paused']).reduce((prev, next) => prev || next);
            // cc.warn(`[SOUND] ${source.audioId} isHowlPaused: ${isPaused}`);
            return isPaused;
        } else {
            cc.error(`[SOUND] ${source.audioId} Can\'t check howl paused!`);
        }
        return false;
    }

    private _getPlayAudioByPlayId(pId: number): AudioPlayingInfo {
        for (const entry of Array.from(this._playingAudios.entries())) {
            const playInfo = entry[1];
            if (playInfo.playIds.includes(pId)) {
                return playInfo;
            }
        }
    }
}
