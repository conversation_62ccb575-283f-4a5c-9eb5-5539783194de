/* auto generate by sound config */
export const AudioFormat = [ 'mp3' ];
export const PreLoadPacks: string[] = [ ];
export const InGamePacks: string[] = [ 'spk0', ];
export const AudioPacks = [ 'spk0', ] as const;
export type AudioPackId = typeof AudioPacks[number];
export const SoundNames = ['CLICK_BTN', 'CARD', 'WIN', 'CARD_CLICK', 'CARD_FLIP', 'NO_HINT', 'MOVE_WIN', 'DEAL_CARD', 'NONE' ] as const;
export type SoundId = typeof SoundNames[number];
export const MusicIds: SoundId[] = [ ];
export const RawConfigs = [
    { p: 'spk0', s: 'click-btn' },
    { p: 'spk0', s: 'card' },
    { p: 'spk0', s: 'win' },
    { p: 'spk0', s: 'card_click' },
    { p: 'spk0', s: 'card_flip' },
    { p: 'spk0', s: 'no_hint' },
    { p: 'spk0', s: 'move_win' },
    { p: 'spk0', s: 'deal_card', v: 0.6 },
];