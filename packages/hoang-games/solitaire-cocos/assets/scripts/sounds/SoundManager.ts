import { AudioSystemData, AudioConfig } from './audio_players/AudioPlayerData';
import BaseAudioPlayer, { LoadCompleteCallback, LoadProgressCallback } from './audio_players/BaseAudioPlayer';
import WebAudioPlayer from './audio_players/WebAudioPlayer';
import { SoundId, InGamePacks, PreLoadPacks, SoundNames, RawConfigs, AudioPackId } from './AudioResources';
import { Howl, HowlOptions, Howler } from 'howler';
import Utils from '../utils/Utils';
import { AudioSource } from './audio_players/AudioSource';
const { ccclass, property } = cc._decorator;

// const Howl = (window as any).Howl;

export enum SoundMgrState {
    INIT = 0,
    LOAD_ASSET,
    LOAD_ASSET_DONE,
    CANCEL_LOAD,
    NONE,
}

type AUTO_CFG = 'AUTO';
type AudioStartCbRepeater = (inst?: SoundManager, sId?: SoundId, info?: AudioConfig, duration?: number, pId?: number, source?: AudioSource) => void;
type AudioEndCbRepeater = (inst?: SoundManager, sId?: SoundId, pId?: number) => void;

@ccclass
export default class SoundManager extends cc.Component {
    private readonly _PREF_BGM_SETTING = 'enableBgm';
    private readonly _PREF_EFFECT_SETTING = 'enableSfx';

    public static inst: SoundManager = null!;

    private static _state: SoundMgrState = SoundMgrState.NONE;

    @property({ displayName: 'Debug', tooltip: 'Enable debug logs' })
    private isDebug: boolean = false;

    @property({ displayName: 'Is Persistent', tooltip: 'Keep instance when switch scene' })
    private isPersistent: boolean = true;

    /* turn off audio bundle
    @property(cc.String)
    private audioBundle: string = 'audio'; */

    @property(cc.String)
    private rootAudioPath: string = 'audio';

    @property({ type: cc.JsonAsset, displayName: 'Resource Config' })
    private resourceConfig: cc.JsonAsset = null;

    private _shareVars: AudioSystemData = null;
    private _audioPlayer: BaseAudioPlayer = null;

    protected onLoad(): void {
        if (SoundManager.inst) {
            cc.warn(`There are several ${this.name} construction!`);
            return;
        }
        SoundManager.inst = this;

        if (this.isPersistent) {
            cc.game.addPersistRootNode(this.node);
        }

        this._registerEvents();
        this._init();
    }

    public async onQuitGame(): Promise<void> {
        if (SoundManager._state === SoundMgrState.LOAD_ASSET) {
            SoundManager._state = SoundMgrState.CANCEL_LOAD;
        }
        // await this._audioPlayer.waitStopImportantAudios();
        if (this.isPersistent) {
            cc.game.removePersistRootNode(this.node);
        }
        this.node.destroy();
    }

    protected onDestroy(): void {
        this.cleanUp();
    }

    public cleanUp(): void {
        this._audioPlayer.cleanUp();

        this._unRegisterEvents();
        this.unscheduleAllCallbacks();

        this._shareVars.audioConfigs.clear();

        if (this && this === SoundManager.inst) {
            SoundManager.inst = null;
            SoundManager._state = SoundMgrState.NONE;
        }
    }

    private async _loadAudioClip(progress?: LoadProgressCallback, complete?: LoadCompleteCallback): Promise<boolean> {
        /* turn off audio bundle
        const bundle = await this._loadBundle(); */
        return new Promise<boolean>((resolve) => {
            if (SoundManager._state === SoundMgrState.LOAD_ASSET_DONE) {
                progress && progress(1, 1);
                complete && complete(true);
                resolve(true);
            }

            const configJson = this.resourceConfig.json;
            if (!configJson) {
                cc.error('[SOUND] Can\' read config!');
                resolve(false);
            }

            const audioPacks = [...PreLoadPacks, ...InGamePacks];
            this._shareVars.audioPacks = audioPacks;
            this._shareVars.totalOfPacks = audioPacks.length;
            if (this._shareVars.totalOfPacks > 0) {
                SoundManager._state = SoundMgrState.LOAD_ASSET;
                const chunkStatus: number[] = [];
                let chunkCounter: number = 0;
                let finish: number = 0;
                let hasError: boolean = false;
                audioPacks.forEach((packName) => {
                    const howlOpts: HowlOptions = configJson[packName];
                    const chunkId: number = chunkCounter++;
                    cc.resources.load(`${this.rootAudioPath}/${packName}`, cc.AudioClip, (finish: number, total: number, item: cc.AssetManager.RequestItem) => {
                        chunkStatus[chunkId] = finish / total;
                        const summaryStatus = chunkStatus.reduce((prev, cur) => prev + cur);
                        progress && progress(summaryStatus, this._shareVars.totalOfPacks);
                    }, (err, clip) => {
                        if (!err && clip) {
                            this._audioPlayer.onLoadAudio(packName, <cc.AudioClip>clip, howlOpts);
                        } else {
                            hasError = true;
                            cc.error(`[SOUND] CCLoad: ${packName} <= FAIL!`);
                            resolve(false);
                        }

                        if (this._shareVars.debug) {
                            cc.warn(`[SOUND] CCLoad ${packName} <= ${!hasError ? 'DONE' : 'FAIL'}!`);
                        }
                        if (++finish >= this._shareVars.totalOfPacks) {
                            if (this._shareVars.debug) {
                                cc.warn(`[SOUND] CCLoad Sounds <= ${!hasError ? 'DONE' : 'FAIL'}!`);
                            }
                            SoundManager._state = SoundMgrState.LOAD_ASSET_DONE;
                            complete && complete(true);
                            resolve(!hasError);
                        }
                    }
                    );
                });
            } else {
                cc.warn('[SOUND] Empty sound config!!!');
                resolve(false);
            }
        });
    }

    private async _verifyResources(progress?: LoadProgressCallback, complete?: LoadCompleteCallback): Promise<boolean> {
        const isSuccess: boolean = await this._audioPlayer.onVerifyResources(this._shareVars.totalOfPacks, progress, complete);
        return isSuccess;
    }

    public async loadAudioResources(progress?: LoadProgressCallback, complete?: LoadCompleteCallback): Promise<boolean> {
        let isSuccess: boolean = false;
        isSuccess = await this._loadAudioClip(progress, null);
        isSuccess = await this._verifyResources(null, complete);
        await this._audioPlayer.onLoadAudioComplete(this._shareVars.totalOfPacks);
        cc.warn('[SOUND] Load Audio Done!');
        return isSuccess;
    }

    /* turn off audio bundle
    private _loadBundle(): Promise<cc.AssetManager.Bundle> {
        return new Promise<cc.AssetManager.Bundle>((resolve) => {
            cc.assetManager.loadBundle(this.audioBundle, null, (err, bundle) => {
                if(err) {
                    cc.error('[SOUND] Load bundle error!', err);
                }
                resolve(bundle);
            });
        })
    } */

    public playBgm(sId: SoundId, vol: 'FADE' | AUTO_CFG = 'AUTO', startCb?: AudioStartCbRepeater, endCb?: AudioEndCbRepeater, replayExist?: boolean): number {
        let playId = -1;
        const curBgmId = this._shareVars.bgmId;
        if (curBgmId && curBgmId !== 'NONE') {
            const bgmSources: AudioSource[] = this._audioPlayer.getPlayingAudio(curBgmId);
            if (bgmSources.length > 0) {
                const source = bgmSources[0];
                if (!source.isAutoStop && (!source.fadingHandler || source.fadingHandler.to !== 0)) {
                    cc.warn(`[SOUND] Pause music ${sId}, because don\'t fadeout!`);
                    source.howl().pause(source.playId);
                }
            }
        }

        const info = this._shareVars.audioConfigs.get(sId);
        const bgmSource = this._audioPlayer.getPlayingMusic(sId);
        this._shareVars.bgmId = sId;
        if (bgmSource) {
            const initVol = vol === 'FADE' ? 0 : info.volume;
            const isReplay = [true, false].includes(replayExist) ? replayExist : false;
            playId = this._audioPlayer.resumeBySource(bgmSource, initVol, isReplay);
            startCb && startCb(this, sId, info, bgmSource.howl().duration(playId), playId, bgmSource);
        } else {
            const initVol = vol === 'FADE' || this._shareVars.isGamePaused || !this._shareVars.isMusicOn ? 0 : vol;
            playId = this._playAudio(sId, info, initVol, 'AUTO', startCb, endCb, true);
        }
        if (this._shareVars.debug) {
            cc.warn(`[SOUND] Play bgm: ${sId}, SoundOn: ${this._shareVars.isMusicOn}`);
        }
        return playId;
    }

    public playEffect(sId: SoundId, vol?: number | AUTO_CFG, loop?: boolean | AUTO_CFG, startCb?: AudioStartCbRepeater, endCb?: AudioEndCbRepeater): number {
        if (this._shareVars.isGamePaused) {
            cc.warn(`[SOUND] ${sId} play during game pause, skip!`);
            return -1;
        }
        const info = this._shareVars.audioConfigs.get(sId);
        if (info) {
            if (info.isMusic) {
                if (!this._shareVars.isMusicOn) {
                    return -1;
                }
                cc.warn(`[SOUND] ${sId} use effect channel!`);
            } else if (!this._shareVars.isEffectOn) {
                return -1;
            }
        }
        return this._playAudio(sId, info, vol, loop, startCb, endCb, false);
    }

    private _playAudio(sId: SoundId, info: AudioConfig, vol: number | AUTO_CFG, loop: boolean | AUTO_CFG, startCb: AudioStartCbRepeater, endCb: AudioEndCbRepeater, isBgm: boolean): number {
        info = info ?? this._shareVars.audioConfigs.get(sId);
        if (!info) {
            cc.error(`[SOUND] ${sId} not found config!`);
            return -1;
        }
        const parseVol = Number.parseFloat(vol + '');
        const volCfg = !Number.isNaN(parseVol) ? parseVol : info.volume;
        const loopCfg = loop === true || loop === false ? loop : info.loop;
        const startCbRepeater = (sId: SoundId, info: AudioConfig, source: AudioSource, pId: number, duration: number) => {
            startCb && startCb(this, sId, info, duration, pId, source);
        };
        const endCbRepeater = (sId: SoundId, pId: number) => {
            endCb && endCb(this, sId, pId);
        };
        return this._audioPlayer.playAudio(sId, info, volCfg, loopCfg, startCbRepeater, endCbRepeater, isBgm);
    }

    public pause(sId: SoundId): void {
        return this._audioPlayer.pause(sId);
    }

    public resume(sId: SoundId): boolean {
        return this._audioPlayer.resume(sId);
    }

    public stop(sId: SoundId): void {
        return this._audioPlayer.stop(sId);
    }

    public stopByPlayId(pId: number): boolean {
        return this._audioPlayer.stopByPlayId(pId);
    }

    public playing(sId: SoundId, includePause?: boolean): boolean {
        return this._audioPlayer.playing(sId, includePause);
    }

    public playingByPlayId(pId: number): boolean {
        return this._audioPlayer.playingByPlayId(pId);
    }

    /**
     * Fade in/out audio
     * @param sId id of sound
     * @param from from volume, `'AUTO'` to get current volume
     * @param to target volume, `'AUTO'` to load setting from config
     * @param duration fading time in second
     * @param stopAfterEnd if true, will stop sound when fade ending, otherwise will pause, just use this for fade volume to 0 only
     * @param endCallback function will be invoked when fade end
     * @param nearEndCallback function when the near fade end, at point the fade process got nearFactor * 100 percent
     * @param nearFactor factor to trigger `nearEndCallback`, in range [0, 1]
     */
    public fade(
        sId: SoundId,
        from: number | AUTO_CFG,
        to: number | AUTO_CFG,
        duration: number = 5,
        stopAfterEnd: boolean = true,
        endCallback?: Function,
        nearEndCallback?: Function,
        nearFactor: number = 0.75,
        playId: number = -1
    ): number {
        return this._audioPlayer.fade(sId, from, to, duration, stopAfterEnd, endCallback, nearEndCallback, nearFactor, playId);
    }

    public transitionToMusic(fromId: SoundId | 'AUTO', targetId: SoundId, outDuration: number = 0.5, isOutStop: boolean = false, inDuration: number = 0.5, mixingPoint: number = 0.75) {
        const playTarget = () => {
            if (!this.playing(targetId)) {
                this.playBgm(targetId, 'FADE', (inst, sId, info, duration, pId) => {
                    inst.fade(sId, 0, 'AUTO', inDuration, false, undefined, undefined, undefined, pId);
                });
            } else {
                this.fade(targetId, 0, 'AUTO', inDuration, false);
            }
        };

        fromId = fromId !== 'AUTO' ? fromId : this._shareVars.bgmId;
        if (fromId !== targetId && !this.playing(fromId)) {
            playTarget();
        } else {
            this.fade(fromId, 'AUTO', 0, outDuration, isOutStop, null, playTarget.bind(this), mixingPoint);
        }
    }

    public setSoundOption(isOn: boolean, type: 'music' | 'sfx', saveSetting: boolean = true) {
        const isMusicType: boolean = type === 'music';
        if (isMusicType) {
            this._shareVars.isMusicOn = isOn;
        } else {
            this._shareVars.isEffectOn = isOn;
        }
        this._audioPlayer.onChangeSoundOption(isOn, isMusicType);
        if (saveSetting) {
            this._saveSettings();
        }
    }

    public loadSoundSetting(): void {
        this._shareVars.isMusicOn = Utils.prefGetBoolean(this._PREF_BGM_SETTING, true);
        this._shareVars.isEffectOn = Utils.prefGetBoolean(this._PREF_EFFECT_SETTING, true);
    }

    private _init(): void {
        this._shareVars = {
            audioConfigs: new Map<SoundId, AudioConfig>(),
            audioPacks: null,
            totalOfPacks: 0,

            bgmId: 'BGM_NORMAL',
            isMusicOn: true,
            isEffectOn: true,

            maxAudioAtTimes: 15,
            totalOfPlayingAudios: 0,
            isGamePaused: false,

            debug: this.isDebug,
        };

        SoundNames.forEach((strId, idx) => {
            const sId: SoundId = <SoundId>strId;
            if (sId !== 'NONE') {
                const rawCfg = RawConfigs[idx];
                const audioCfg: AudioConfig = {
                    pack: <AudioPackId>rawCfg.p,
                    sprite: Utils.safeGetProperty<string>(rawCfg, 's', undefined),
                    loop: Utils.safeGetProperty<number>(rawCfg, 'l', 0) === 1,
                    volume: Utils.safeGetProperty<number>(rawCfg, 'v', 1),
                    isMusic: Utils.safeGetProperty<number>(rawCfg, 'm', 0) === 1,
                    numberOfPlaying: 0,
                };
                this._shareVars.audioConfigs.set(sId, audioCfg);
            }
        });

        this._audioPlayer = this._createAudioPlayer();
        this.loadSoundSetting();
    }

    private _registerEvents(): void {
        if (cc.sys.isBrowser) {
            document.addEventListener('visibilitychange', this._onVisibilityChange);
        } else {
            cc.error('This platform is not support!');
        }
    }

    private _unRegisterEvents(): void {
        if (cc.sys.isBrowser) {
            document.removeEventListener('visibilitychange', this._onVisibilityChange);
        } else {
            cc.error('This platform is not support!');
        }
    }

    private _onVisibilityChange = () => {
        const isVisible = document.visibilityState === 'visible';
        if (this.node.active) {
            if (isVisible) {
                this._onResume();
            } else {
                this._onPause();
            }
        }
    };

    private _createAudioPlayer(): BaseAudioPlayer {
        if (!cc.sys.isBrowser) {
            cc.warn('[SOUND] Don\'t support this platform!');
        }
        const player = new WebAudioPlayer(this, this._shareVars);
        player.init();
        return player;
    }

    public setBgmId(sId: SoundId): void {
        this._shareVars.bgmId = sId;
    }

    public getBgmId(): SoundId {
        return this._shareVars.bgmId;
    }

    public isMusicOn(): boolean {
        return this._shareVars.isMusicOn;
    }

    public isEffectOn(): boolean {
        return this._shareVars.isEffectOn;
    }

    private _onResume = () => {
        this._shareVars.isGamePaused = false;
        this._audioPlayer.onPause(false);
    };

    private _onPause = () => {
        this._shareVars.isGamePaused = true;
        this._audioPlayer.onPause(true);
    };

    private _saveSettings(): void {
        Utils.prefSaveBoolean(this._PREF_BGM_SETTING, this._shareVars.isMusicOn);
        Utils.prefSaveBoolean(this._PREF_EFFECT_SETTING, this._shareVars.isEffectOn);
    }
}
