import SoundManager from "../sounds/SoundManager";

const { ccclass, property } = cc._decorator;

const SUITS = ["diamonds", "clubs", "hearts", "spades"];
const JQKNAMES = ["jack", "queen", "king"];

@ccclass
export default class Card extends cc.Component {
  @property(cc.Node)
  bgNode: cc.Node = null;

  @property(cc.Node)
  fgNode: cc.Node = null;

  @property(cc.Node)
  centerContainer: cc.Node = null;

  @property(cc.Node)
  rightContainer: cc.Node = null;

  @property(cc.Node)
  bottomContainer: cc.Node = null;

  public point: number = 0;
  public suit: number = 0;
  public cardMoveContainer: cc.Node = null;
  public isFront: boolean = false;
  public isInWin: boolean = false;

  private positionTemp: cc.Vec3 = cc.Vec3.ZERO;
  private zIndexTemp: number = 0;

  private touchStartCallback: Function = null;
  private touchEndCallback: Function = null;
  private drawCallback: Function = null;
  private doubleClickCallback: Function = null;
  private popUndo: Function = null;

  private lastClickTime: number = 0;
  private currentClickTime: number = 0;

  public isMoving: boolean = false;
  private hasMoved = false;

  start() {
    this.openTouch();
  }

  onEnable() {
    this.turnToBack();
  }

  onDestroy() {
    this.closeTouch();
  }

  public setData(data: number, cardMoveContainer: cc.Node) {
    this.cardMoveContainer = cardMoveContainer;
    this.suit = Math.floor((data - 1) / 13);
    this.point = (data - this.suit * 13) % 13;

    if (this.point === 0) this.point = 13;

    this.updateUI();
  }

  private updateUI() {
    const suit = SUITS[this.suit];
    let number = "";
    if (this.point > 10) {
      number = JQKNAMES[this.point - 10 - 1];
    } else {
      number = this.point == 1 ? "ace" : this.point.toString().padStart(2, "0");
    }

    cc.resources.load(
      `Cards/${suit}_${number}`,
      cc.SpriteFrame,
      (err, asset: cc.SpriteFrame) => {
        if (err) {
          cc.error(`${"url"} error ${err}`);
        } else {
          this.fgNode.getComponent(cc.Sprite).spriteFrame = asset;
        }
      }
    );
  }

  private openTouch(): void {
    this.node.on(
      cc.Node.EventType.TOUCH_START,
      this.touchStart.bind(this),
      this
    );
    this.node.on(
      cc.Node.EventType.TOUCH_MOVE,
      this.touchsMove.bind(this),
      this
    );
    this.node.on(cc.Node.EventType.TOUCH_END, this.touchEnd.bind(this), this);
    this.node.on(
      cc.Node.EventType.TOUCH_CANCEL,
      this.touchEnd.bind(this),
      this
    );
  }

  private closeTouch(): void {
    this.node.off(
      cc.Node.EventType.TOUCH_START,
      this.touchStart.bind(this),
      this
    );
    this.node.off(
      cc.Node.EventType.TOUCH_MOVE,
      this.touchsMove.bind(this),
      this
    );
    this.node.off(cc.Node.EventType.TOUCH_END, this.touchEnd.bind(this), this);
    this.node.off(
      cc.Node.EventType.TOUCH_CANCEL,
      this.touchEnd.bind(this),
      this
    );
  }

  public isUnder: boolean = false;
  private get canTouch(): Boolean {
    // return false if is not front or is shown in draw 3
    if (!this.isFront || this.rightContainer.childrenCount > 0 || this.isUnder)
      return false;
    return true;
  }

  private touchStart(e: cc.Event.EventTouch): void {
    e.stopPropagation();

    this.lastClickTime = this.currentClickTime;
    this.currentClickTime = Date.now();
    if (this.isMoving) return;
    if (!this.canTouch) return;
    this.positionTemp = this.node.position;
    this.zIndexTemp = this.zIndex;

    this.zIndex = 100;

    if (this.touchStartCallback) {
      this.touchStartCallback(this.node);
    }
  }

  private touchsMove(e: cc.Event.EventTouch): void {
    e.stopPropagation();
    if (!this.canTouch) return;
    if (!this.hasMoved) {
      SoundManager.inst.playEffect("CARD");
    }
    this.hasMoved = true;
    let delta = e.getDelta();
    this.node.position = cc.v3(delta.add(cc.v2(this.node.position)));
  }

  private touchEnd(e: cc.Event.EventTouch): void {
    e.stopPropagation();
    if (this.hasMoved) {
      this.zIndex = this.zIndexTemp;

      if (this.touchEndCallback) {
        if (SoundManager.inst.playing("CARD")) {
          SoundManager.inst.fade("CARD", "AUTO", 0, 0.1, true);
        }
        SoundManager.inst.playEffect("CARD_FLIP");
        this.touchEndCallback(this.node);
      }

      this.hasMoved = false;
      return;
    }
    if (this.isMoving) return;
    if (!this.canTouch) {
      // Draw card
      if (
        this.drawCallback &&
        this.bottomContainer.childrenCount <= 0 &&
        // this.currentClickTime - this.lastClickTime > 600 &&
        this.rightContainer.childrenCount <= 0 &&
        !this.isUnder
      ) {
        this.zIndex = this.zIndexTemp;
        cc.log("single click");
        this.drawCallback(this.node);
      }
      return;
    }
    SoundManager.inst.playEffect("CARD_CLICK");
    if (this.currentClickTime - this.lastClickTime <= 600) {
      this.zIndex = this.zIndexTemp;

      if (this.doubleClickCallback) {
        this.doubleClickCallback(this.node);
        return;
      }
    }

    this.zIndex = this.zIndexTemp;
    this.popUndo();
  }

  public turnToFront() {
    if (this.isFront) return;

    this.isFront = true;
    this.bgNode.opacity = 0;
  }

  public turnToBack() {
    this.isFront = false;
    this.bgNode.opacity = 255;
  }

  public reset() {
    cc.warn("reset", this.point);
    cc.tween(this.node)
      .to(0, { position: this.positionTemp })
      .call(() => {
        this.zIndex = this.zIndexTemp;
      })
      .start();
  }

  public setTouchCallback(
    touchStartCallback: Function,
    touchEndCallback: Function,
    drawCallback: Function,
    doubleClickCallback: Function,
    popUndo: Function
  ) {
    this.touchStartCallback = touchStartCallback;
    this.drawCallback = drawCallback;
    this.touchEndCallback = touchEndCallback;
    this.doubleClickCallback = doubleClickCallback;
    this.popUndo = popUndo;
  }

  public getBottomCard() {
    if (this.bottomContainer.childrenCount > 0) {
      return this.bottomContainer.children[0]
        .getComponent(Card)
        .getBottomCard();
    } else {
      return this.node;
    }
  }

  public getRightCard() {
    if (this.rightContainer.childrenCount > 0) {
      return this.rightContainer.children[0].getComponent(Card).getRightCard();
    } else {
      return this.node;
    }
  }

  public getParentCount(
    includedNotFront: boolean = false,
    accumulator: number = 0
  ) {
    let card = this.node.parent.parent.getComponent(Card);

    if (card && card.isFront === !includedNotFront) {
      accumulator++;
      return card.getParentCount(includedNotFront, accumulator);
    }

    return accumulator;
  }

  public setBottomChild(child: cc.Node, ignoreRule: boolean = true): boolean {
    if (this.bottomContainer.childrenCount > 0) {
      return this.bottomContainer.children[0]
        .getComponent(Card)
        .setBottomChild(child, ignoreRule);
    } else if (child == this.node) {
      this.reset();
      return false;
    } else {
      if (ignoreRule) {
        child.parent = this.bottomContainer;
        child.position = cc.v3(0, 0);
        return true;
      } else {
        let childCard = child.getComponent(Card);
        let absSuit = Math.abs(childCard.suit - this.suit);
        if (
          childCard.point == this.point - 1 &&
          (absSuit == 1 || absSuit == 3)
        ) {
          child.parent = this.bottomContainer;
          child.position = cc.v3(0, 0);
          return true;
        } else {
          childCard.reset();
          return false;
        }
      }
    }
  }

  public isBottomChild(child: cc.Node): boolean {
    if (this.bottomContainer.childrenCount > 0) {
      return this.bottomContainer.children[0]
        .getComponent(Card)
        .isBottomChild(child);
    } else if (child === this.node) {
      return false;
    } else {
      let childCard = child.getComponent(Card);
      let absSuit = Math.abs(childCard.suit - this.suit);
      if (childCard.point == this.point - 1 && (absSuit == 1 || absSuit == 3)) {
        return true;
      } else {
        return false;
      }
    }
  }

  public turnLastBottomChildToFront() {
    if (this.bottomContainer.childrenCount > 0) {
      this.bottomContainer.children[0]
        .getComponent(Card)
        .turnLastBottomChildToFront();
    } else {
      this.turnToFront();
    }
  }

  public setRightChild(child: cc.Node) {
    if (this.rightContainer.childrenCount > 0) {
      this.rightContainer.children[0].getComponent(Card).setRightChild(child);
    } else {
      child.parent = this.rightContainer;
      child.position = cc.v3(0, 0);
      return true;
    }
  }

  public async setCenterChild(
    child: cc.Node,
    isSkipReset?: boolean
  ): Promise<boolean> {
    if (this.centerContainer.childrenCount > 0) {
      return this.centerContainer.children[0]
        .getComponent(Card)
        .setCenterChild(child);
    } else if (child == this.node) {
      this.reset();
      return false;
    } else {
      let childCard = child.getComponent(Card);
      if (childCard.point == this.point + 1 && childCard.suit == this.suit) {
        if (!isSkipReset) {
          child.parent = this.centerContainer;
          child.position = cc.v3(0, 0);
        } else {
          await new Promise((res, rej) => {
            cc.tween(child)
              .to(0.1, {
                position: child.parent.convertToNodeSpaceAR(
                  this.centerContainer.parent.convertToWorldSpaceAR(
                    this.centerContainer.position
                  )
                ),
              })
              .call(() => {
                child.parent = this.centerContainer;
                child.position = cc.v3(0, 0);
                res(true);
              });
          });
        }
        return true;
      } else {
        childCard.reset();

        return false;
      }
    }
  }

  public isCenterChild(child: cc.Node): boolean {
    if (this.centerContainer.childrenCount > 0) {
      return this.centerContainer.children[0]
        .getComponent(Card)
        .isCenterChild(child);
    } else if (child == this.node) {
      return false;
    } else {
      let childCard = child.getComponent(Card);
      if (childCard.point == this.point + 1 && childCard.suit == this.suit) {
        return true;
      } else {
        return false;
      }
    }
  }

  public get zIndex() {
    let card = this.node.parent.parent.getComponent(Card);
    if (card) {
      return card.zIndex;
    } else {
      return this.node.zIndex;
    }
  }

  public set zIndex(zIndex: number) {
    let card = this.node.parent.parent.getComponent(Card);
    if (card) {
      card.zIndex = zIndex;
    } else {
      this.node.zIndex = zIndex;
    }
  }

  public getBottomWorldPosition() {
    let world = cc.Vec3.ZERO;
    if (this.bottomContainer.childrenCount > 0) {
      world = this.bottomContainer.children[0]
        .getComponent(Card)
        .getBottomWorldPosition();
    } else {
      this.bottomContainer.parent.convertToWorldSpaceAR(
        this.bottomContainer.position,
        world
      );
    }
    return world;
  }

  public getCenterWorldPosition() {
    let world = cc.Vec3.ZERO;
    if (this.centerContainer.childrenCount > 0) {
      world = this.centerContainer.children[0]
        .getComponent(Card)
        .getCenterWorldPosition();
    } else {
      this.centerContainer.parent.convertToWorldSpaceAR(
        this.centerContainer.position,
        world
      );
    }
    return world;
  }

  public moveToWorldPosition(world: cc.Vec2, finishCallback: Function) {
    cc.warn("moveToWorld::Point::", this.node.getComponent(Card).point);
    let nodePosition = cc.Vec2.ZERO;
    this.node.parent.convertToNodeSpaceAR(world, nodePosition);
    cc.tween(this.node)
      .to(0.1, { position: cc.v3(nodePosition) })
      .call(() => {
        if (finishCallback) {
          finishCallback();
        }
      })
      .start();
  }

  public shake() {
    this.isMoving = true;
    let x = this.node.position.x;
    let y = this.node.position.y;
    cc.tween(this.node)
      .to(0.018, { position: cc.v3(x + 5, y + 7) })
      .to(0.018, { position: cc.v3(x - 6, y + 7) })
      .to(0.018, { position: cc.v3(x - 13, y + 3) })
      .to(0.018, { position: cc.v3(x + 3, y - 6) })
      .to(0.018, { position: cc.v3(x - 5, y + 5) })
      .to(0.018, { position: cc.v3(x + 2, y - 8) })
      .to(0.018, { position: cc.v3(x - 8, y - 10) })
      .to(0.018, { position: cc.v3(x + 3, y + 10) })
      .to(0.018, { position: cc.v3(x + 0, y + 0) })
      .call(() => {
        if (this.node.parent.parent.getComponent(Card))
          this.node.position = cc.v3(0, 0);
        this.isMoving = false;
      })
      .start();
  }
}
