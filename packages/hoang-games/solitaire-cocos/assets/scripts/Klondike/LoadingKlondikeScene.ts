import SoundManager from "../sounds/SoundManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LoadingScene extends cc.Component {
  @property(cc.Node)
  loading: cc.Node = null;

  @property(cc.Label)
  text: cc.Label = null;

  start() {
    this.loading.active = true;
    this.text.string = "Loading";
    cc.tween(this.loading)
      .repeatForever(cc.tween().by(0.01, { angle: -5 }))
      .start();
    this.preLoadGameScene();
  }

  async preLoadGameScene() {
    cc.resources.preloadDir("Cards");
    await SoundManager.inst.loadAudioResources(null, async (isSuccess) => {
      cc.director.loadScene("SolitaireGame");
    });
  }
}
