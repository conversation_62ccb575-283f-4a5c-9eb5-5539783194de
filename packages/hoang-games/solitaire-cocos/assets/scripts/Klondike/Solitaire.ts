import PopupManager from "../UI/Popup/PopupManager";
import BoardScore from "../UI/menu/BoardScore";
import { EventType, Events } from "../events/Events";
import SoundManager from "../sounds/SoundManager";
import { DrawMode, ResultData } from "../utils/Gameinterface";
import Utils from "../utils/Utils";
import Card from "./Card";

const { ccclass, property } = cc._decorator;

const enum Stacks {
  A1,
  A2,
  A3,
  A4,
  DECK,
  SHOW,
}

const COLUMNS = 7;
const DOUBLE_CLICK_TIME = 0.6;
const KEY_HIGH_SCORE_GAME = "Klondike_HighScore";
@ccclass
export default class Solitaire extends cc.Component {
  @property(cc.Node)
  finalStacks: cc.Node = null;

  @property(cc.Node)
  tableauStacks: cc.Node = null;

  @property(cc.Node)
  container: cc.Node = null;

  @property(cc.Prefab)
  cardPrefab: cc.Prefab = null;

  @property(cc.Node)
  boardScore: cc.Node = null;

  @property(cc.Node)
  blockTouch: cc.Node = null;

  @property(cc.Node)
  btnSolved: cc.Node = null;

  @property(cc.Button)
  btnUndo: cc.Button = null;

  private undoStacks = [];
  private deckCards: Array<cc.Node> = [];
  private drawedCardStacks: Array<cc.Node> = [];
  private tableauCardStacks: Array<cc.Node> = [];
  private winStacks: Array<cc.Node> = [null, null, null, null];
  private drawMode: DrawMode = DrawMode.ONE;
  private timer: number = 0;
  private score: number = 0;
  private listCardWin: Array<cc.Node> = [];
  private isWin: boolean = false;
  private listCardPlayer: Array<cc.Node> = [];
  private isShowHint: boolean = false;
  private resetTime: number = 0;
  private noMovePredicts: boolean = false;
  private currentClickCard: number = 0;
  private lockTimerClickCard: number = 1;
  private isMovedInWin: boolean = false;
  private isSolved: boolean = false;
  private isStartGame: boolean = false;

  private currentCardMove: number = -1;
  private originArr = null;
  private cardDraw;
  private hintCount: number = 0;
  private listTimeStamp: number[] = [];
  protected onLoad(): void {
    Events.On(EventType.DRAW_MODE, this.setUpGamePlay.bind(this));
    Events.On(EventType.RESET_GAMEOVER, this.resetCheckGameOver.bind(this));
  }

  start() {
    //this.setUpGamePlay();
    PopupManager.inst.openSelectMode(true);
  }

  getRandomArray(): Array<number> {
    let result: Array<number> = [];
    for (let i = 0; i < 52; i++) {
      result[i] = i + 1;
    }

    result.sort(function () {
      return 0.5 - Math.random();
    });

    this.originArr = result;
    return result;

    // return [
    //   8, 21, 46, 31, 22, 4, 52, 18, 36, 11, 35, 33, 3, 47, 32, 26, 30, 38, 25,
    //   51, 15, 28, 42, 12, 41, 10, 9, 20, 49, 24, 39, 2, 5, 19, 14, 34, 44, 1,
    //   27, 48, 40, 13, 43, 45, 7, 50, 6, 23, 17, 37, 29, 16,
    // ];
  }

  setUpGamePlay(mode: DrawMode = DrawMode.ONE) {
    this.isStartGame = true;
    this.isWin = false;
    this.noMovePredicts = false;
    this.btnSolved.active = false;
    this.isSolved = false;
    this.deckCards.length = 0;
    this.listCardWin.length = 0;
    this.drawedCardStacks.length = 0;
    this.tableauCardStacks.length = 0;
    this.listTimeStamp.length = 0;
    this.undoStacks.length = 0;
    // this.currentCardMove = -1;
    this.winStacks = [null, null, null, null];
    this.timer = 0;
    this.score = 0;
    this.boardScore
      .getComponent(BoardScore)
      .setScore(this.score, KEY_HIGH_SCORE_GAME);
    this.blockTouch.active = false;
    this.container.removeAllChildren();
    this.drawMode = mode;
    this.isShowHint = false;
    this.btnUndo.interactable = false;

    this.generateMap();
  }

  async generateMap() {
    if (true) {
      let dataArray = this.getRandomArray();
      //cc.log(dataArray);
      let startPosition = this.convertToCardsNodePosition(
        this.getStartStackPosition()
      );

      let nodeArray: Array<cc.Node> = [];
      for (let i = 0; i < 52; i++) {
        let node: cc.Node = cc.instantiate(this.cardPrefab);
        nodeArray.push(node);
      }

      nodeArray.forEach((cardNode, index) => {
        cardNode.parent = this.container;
        cardNode.position = cc.v3(
          startPosition.x,
          startPosition.y + index * 0.5
        );
        let card = cardNode.getComponent(Card);
        cardNode.zIndex = index;
        card.setData(dataArray[index], this.container);
        card.setTouchCallback(
          this.cardTouchStart.bind(this),
          this.cardTouchEnd.bind(this),
          this.drawCard.bind(this),
          this.cardDoubleClick.bind(this),
          this.popUndoStack.bind(this)
        );
      });
      this.deckCards = nodeArray;
      this.dealCard();
    }
  }

  dealCard() {
    let count = 0;
    for (let i = 0; i < COLUMNS; i++) {
      for (let j = i; j < COLUMNS; j++) {
        count++;
        let toPosition = this.convertToCardsNodePosition(
          this.getTableauStackPosition(j)
        );
        let cardNode = this.deckCards.pop();
        cardNode.position = cc.v3(toPosition.x, toPosition.y + i * -30);
        cardNode.zIndex = i + j;
        cardNode.zIndex = 1;

        if (i === 0) {
          this.tableauCardStacks.push(cardNode);
        } else {
          let parent = this.tableauCardStacks[j].getComponent(Card);
          parent.setBottomChild(cardNode);
        }

        if (i === j) {
          cardNode.getComponent(Card).turnToFront();
        }
      }
    }
  }

  cardTouchStart(node: cc.Node) {
    let world = cc.Vec3.ZERO;
    node.parent.convertToWorldSpaceAR(node.position, world);

    let stackIndex;
    let fromStack;
    if (world.y - 100 > this.getTableauStackPosition(0).y) {
      if (world.x - 150 > this.getWinStackPosition(Stacks.SHOW).x) {
        stackIndex = this.getTableauStackIndex(world.x);
        fromStack = "win";
        cc.warn("from Win", stackIndex);
      } else {
        fromStack = "drawedCards";
        cc.warn("from drawedCards", stackIndex, node.getComponent(Card).point);
      }
    } else {
      stackIndex = this.getTableauStackIndex(world.x);
      cc.warn("from tableau", stackIndex, node.getComponent(Card).point);
      fromStack = "tableau";
    }

    let parent = node.parent.parent ?? null;
    this.undoStacks.push({
      node,
      idx: stackIndex,
      from: fromStack,
      parentStatus:
        fromStack === "tableau" && parent
          ? parent.getComponent(Card)?.isFront
          : false,
    });
  }

  cardTouchMove(node: cc.Node) {}
  async cardTouchEnd(node: cc.Node) {
    if (
      !this.undoStacks[this.undoStacks.length - 1] ||
      this.undoStacks[this.undoStacks.length - 1].node !== node
    ) {
      return;
    }

    this.isMovedInWin = false;
    let world = cc.Vec3.ZERO;
    node.parent.convertToWorldSpaceAR(node.position, world);

    let stackIndex = this.getTableauStackIndex(world.x);

    if (world.y - 100 > this.getTableauStackPosition(0).y) {
      cc.log(world.x - 150, this.getWinStackPosition(Stacks.DECK).x);
      if (world.x - 150 > this.getWinStackPosition(Stacks.SHOW).x) {
        stackIndex = this.getWinStackIndex(world.x);
        cc.log(stackIndex);
        this.moveCardToWinInStack(node, stackIndex);
        node.getComponent(Card).isMoving = false;
      } else {
        this.popUndoStack();
        node.getComponent(Card).reset();
      }
    } else {
      if (!this.tableauCardStacks[stackIndex]) {
        this.moveCardToEmptyStack(node, stackIndex);
      } else if (node === this.tableauCardStacks[stackIndex]) {
        this.popUndoStack();
        node.getComponent(Card).reset();
      } else {
        this.moveCard(node, stackIndex);
        node.getComponent(Card).isMoving = false;
      }
    }
  }

  cardDoubleClick(node: cc.Node) {
    cc.warn("double");

    this.blockTouch.active = true;
    this.lockTimerClickCard = DOUBLE_CLICK_TIME;

    if (!this.detectPosibleMove(node, false, true)) {
      node.getComponent(Card).shake();
      this.popUndoStack();
      cc.warn("pop", this.undoStacks);
      this.blockTouch.active = false;
    }

    cc.warn(this.undoStacks);
  }

  drawCard(node: cc.Node) {
    if (this.drawMode === DrawMode.ONE) {
      this.drawOneCard();
    } else {
      this.drawThreeCards();
    }
    this.undoStacks.push({ node, from: "deck" });

    if (this.showHint(false)) {
      this.hintCount++;
    }

    this.btnUndo.interactable = this.undoStacks.length > 0;
  }

  drawOneCard() {
    // if(this.drawedCardStacks.length === 0){
    //   this.currentCardDraw = 0;
    // }
    this.blockTouch.active = true;

    let toPosition = this.convertToCardsNodePosition(
      this.getDrawStackPosition()
    );

    let cardNode = this.deckCards.pop();
    if (!cardNode) {
      this.blockTouch.active = false;
      this.popUndoStack();
      return;
    }
    const index =
      this.drawedCardStacks.length + 1 >= 3 ? 2 : this.drawedCardStacks.length;
    cc.tween(cardNode)
      .set({
        zIndex:
          this.drawedCardStacks.slice(-1).length > 0
            ? this.drawedCardStacks.slice(-1)[0].zIndex + 1
            : 52,
      })
      .to(0.15, {
        position: cc.v3(toPosition.x - 22 + (index - 1) * 27, toPosition.y, 0),
      })
      .call(() => {
        cc.log("zi", cardNode.zIndex);
        this.blockTouch.active = false;
        cardNode.getComponent(Card).turnToFront();
      })
      .start();

    this.hideFirstCard();

    this.drawedCardStacks.forEach((card) => {
      card.getComponent(Card).isUnder = true;
    });
    this.drawedCardStacks.push(cardNode);
  }

  hideFirstCard() {
    if (this.drawMode !== DrawMode.ONE) return;

    let toPosition = this.convertToCardsNodePosition(
      this.getDrawStackPosition()
    );

    if (this.drawedCardStacks.length >= 3) {
      let idx = 0;
      this.blockTouch.active = true;

      for (
        let i = this.drawedCardStacks.length - 2;
        i < this.drawedCardStacks.length;
        i++
      ) {
        cc.tween(this.drawedCardStacks[i])
          .to(0.15, {
            position: cc.v3(
              toPosition.x - 22 + (idx - 1) * 27,
              toPosition.y,
              0
            ),
          })
          .call(() => {
            this.blockTouch.active = false;
            this.drawedCardStacks[i].getComponent(Card).isUnder = true;
          })
          .start();
        idx++;
      }
    } else {
      this.blockTouch.active = false;
    }
  }

  showHidingDrawedCard() {
    if (this.drawMode !== DrawMode.ONE) return;

    let toPosition = this.convertToCardsNodePosition(
      this.getDrawStackPosition()
    );
    const lastThreeCard = this.drawedCardStacks.slice(-3);
    if (lastThreeCard.length === 0) {
      this.blockTouch.active = false;
    }
    for (let i = 0; i < lastThreeCard.length; i++) {
      cc.tween(lastThreeCard[i])
        .set({
          zIndex: 52 + this.drawedCardStacks.length - lastThreeCard.length + i,
        })
        .to(0.15, {
          position: cc.v3(toPosition.x - 22 + (i - 1) * 27, toPosition.y, 0),
        })
        .call(() => {
          cc.log(
            "zi",
            lastThreeCard[i].zIndex,
            52 + this.drawedCardStacks.length - lastThreeCard.length + i
          );

          if (i === lastThreeCard.length - 1) {
            lastThreeCard[i].getComponent(Card).isUnder = false;
            this.blockTouch.active = false;
          }
        })
        .start();
    }
  }

  drawThreeCards() {
    this.drawedCardStacks.forEach((card) => {
      // card.removeFromParent(false);
      card.active = false;
    });

    let firstNode: cc.Node = null;
    for (let i = 0; i < 3; i++) {
      this.blockTouch.active = true;

      let toPosition = this.convertToCardsNodePosition(
        this.getDrawStackPosition()
      );

      let cardNode = this.deckCards.pop();
      if (!cardNode) {
        //cc.log("break");

        this.blockTouch.active = false;
        break;
      }

      cc.tween(cardNode)
        .delay(0.1 * i)
        .parallel(
          cc.tween().to(0.15, {
            position: cc.v2(toPosition.x - 22 + i * 27, toPosition.y),
          }),
          cc
            .tween()
            .delay(0.1)
            .set({ zIndex: 52 + i })
        )
        .call(() => {
          if (i == 0) {
            firstNode = cardNode;
          } else {
            firstNode.getComponent(Card).setRightChild(cardNode);
          }
          cardNode.getComponent(Card).turnToFront();
          if (i === 2) {
            this.blockTouch.active = false;
          }
        })
        .start();

      this.drawedCardStacks.push(cardNode);
    }
  }

  resetCards() {
    cc.log(this.drawedCardStacks.map((item) => Object.assign({}, item)));

    this.undoStacks.push({
      from: "reset",
      drawedCardStacks: this.drawedCardStacks.map((item) =>
        Object.assign({}, item)
      ),
    });

    let startPosition = this.convertToCardsNodePosition(
      this.getStartStackPosition()
    );
    let count = 0;
    while (this.drawedCardStacks.length > 0) {
      let cardNo = this.drawedCardStacks.pop();
      cardNo.parent = this.container;
      cardNo.active = true;
      count++;
      cardNo.zIndex = 1;
      cardNo.position = cc.v3(startPosition.x, startPosition.y + count * 0.5);
      this.deckCards.push(cardNo);
      let card = cardNo.getComponent(Card);
      card.isUnder = false;
      card.turnToBack();
    }

    if (!this.showHint(false)) {
      this.resetTime++;
    }
    if (this.resetTime > 1 && this.hintCount === 0) {
      ////cc.log("noMovePredicts");
      this.noMovePredicts = true;
    }
    this.hintCount = 0;
  }

  getStartStackPosition(): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.finalStacks.convertToWorldSpaceAR(
      this.finalStacks.children[Stacks.DECK].position,
      world
    );
    return world;
  }

  getDrawStackPosition(): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.finalStacks.convertToWorldSpaceAR(
      this.finalStacks.children[Stacks.SHOW].position,
      world
    );
    return world;
  }

  getWinStackPosition(index: number): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.finalStacks.convertToWorldSpaceAR(
      this.finalStacks.children[index].position,
      world
    );
    return world;
  }

  getWinStackIndex(worldX: number): number {
    let result = 0;
    let distance = 0;
    let minDistance = 0;
    let stackWorldX = 0;
    for (let i = 0; i < 4; i++) {
      stackWorldX = this.getWinStackPosition(i).x;
      distance = Math.abs(stackWorldX - worldX);
      if (i === 0 || minDistance > distance) {
        minDistance = distance;
        result = i;
      }
    }
    return result;
  }

  getTableauStackPosition(index: number): cc.Vec3 {
    let world = cc.Vec3.ZERO;
    this.tableauStacks.convertToWorldSpaceAR(
      this.tableauStacks.children[index].position,
      world
    );
    return world;
  }

  getTableauStackIndex(worldX: number): number {
    let result = 0;
    let distance = 0;
    let minDistance = 0;
    let stackWorldX = 0;
    for (let i = 0; i < COLUMNS; i++) {
      stackWorldX = this.getTableauStackPosition(i).x;
      distance = Math.abs(stackWorldX - worldX);
      if (i === 0 || minDistance > distance) {
        minDistance = distance;
        result = i;
      }
    }
    return result;
  }

  convertToCardsNodePosition(world: cc.Vec3): cc.Vec3 {
    let nodePosition = cc.Vec3.ZERO;
    this.container.convertToNodeSpaceAR(world, nodePosition);
    return nodePosition;
  }

  async moveCardToWinInStack(
    node: cc.Node,
    toWinIndex: number,
    skipPopUndo: boolean = false
  ) {
    let card = node.getComponent(Card);

    if (!this.winStacks[toWinIndex]) {
      // if card is ACE
      if (card.point === 1) {
        //cc.log("here", card.node.parent);

        this.isMovedInWin = true;
        this.winStacks.forEach((stackNode, index) => {
          if (stackNode === node) {
            this.winStacks[index] = null;
          }
        });

        this.tableauCardStacks.forEach((stackNode, index) => {
          if (stackNode === node) {
            this.tableauCardStacks[index] = null;
          }
        });

        node.parent = this.container;
        this.winStacks[toWinIndex] = node;

        let toPosition = this.convertToCardsNodePosition(
          this.getWinStackPosition(toWinIndex)
        );

        if (this.isSolved) {
          await new Promise((res, rej) => {
            cc.tween(node)
              .to(0.1, { position: toPosition })
              .call(() => {
                card.isInWin = true;
                res(true);
              })
              .start();
          });
        } else {
          node.position = cc.v3(toPosition.x, toPosition.y);
          card.isInWin = true;
        }

        this.removeFromDrawedStack(node);
        this.showHidingDrawedCard();
        if (this.drawedCardStacks.length - 1 >= 0) {
          this.drawedCardStacks[this.drawedCardStacks.length - 1].getComponent(
            Card
          ).isUnder = false;
        }
        this.checkCardWin(node);
        // if (this.isSolved) {
        //   this.scheduleOnce(()=>{
        //     this.onSolved();
        //   }, 1)

        // }
        this.btnUndo.interactable = this.undoStacks.length > 0;
      } else if (!skipPopUndo) {
        cc.warn("pop");
        this.popUndoStack();
        node.getComponent(Card).reset();
      }
    } else if (this.winStacks[toWinIndex] === node) {
      cc.warn("pop");
      this.popUndoStack();
      node.getComponent(Card).reset();
    } else {
      let stackCard = this.winStacks[toWinIndex].getComponent(Card);
      if (
        stackCard &&
        node.getChildByName("BottomContainer").childrenCount === 0
      ) {
        if (await stackCard.setCenterChild(node, this.isSolved)) {
          this.isMovedInWin = true;
          this.removeFromDrawedStack(node);
          this.showHidingDrawedCard();
          if (this.drawedCardStacks.length - 1 >= 0) {
            this.drawedCardStacks[
              this.drawedCardStacks.length - 1
            ].getComponent(Card).isUnder = false;
          }
          this.checkCardWin(node);
          this.tableauCardStacks.forEach((stackNode, index) => {
            if (stackNode === node) {
              this.tableauCardStacks[index] = null;
            }
          });

          card.isInWin = true;
          // if (this.isSolved) {
          //   this.scheduleOnce(()=>{
          //     this.onSolved();
          //   }, 1)
          // }
        }
      }
    }
  }

  removeFromDrawedStack(node: cc.Node) {
    this.resetTime = 0;
    // const drawedCardStacks =  this.drawedCardStacks.length;
    this.drawedCardStacks = this.drawedCardStacks.filter(
      (item) => item !== node
    );
    // if(drawedCardStacks > this.drawedCardStacks.length){
    //   let toPosition = this.convertToCardsNodePosition(
    //     this.getDrawStackPosition()
    //   );
    //   this.currentCardDraw--;
    //   if(this.currentCardDraw > 3){
    //   this.drawedCardStacks[this.drawedCardStacks.length - 1].position = cc.v3(toPosition.x - 22 + 27, toPosition.y, 0)
    //   this.drawedCardStacks[this.drawedCardStacks.length - 2].position = cc.v3(toPosition.x - 22 , toPosition.y, 0)
    //   }
    // }
    this.openLastCardInStack();
    this.checkSolve();
  }

  moveCardToEmptyStack(
    node: cc.Node,
    toStackIndex: number,
    force: boolean = false
  ) {
    let card = node.getComponent(Card);

    // king card
    if (card.point === 13 || force) {
      node.parent = this.container;
      this.tableauCardStacks[toStackIndex] = node;

      let toPosition = this.convertToCardsNodePosition(
        this.getTableauStackPosition(toStackIndex)
      );
      node.position = toPosition;
      node.zIndex = card.point;
      this.tableauCardStacks.forEach((stackNode, index) => {
        if (stackNode === node && index !== toStackIndex) {
          this.tableauCardStacks[index] = null;
        }
      });

      this.removeFromDrawedStack(node);
      this.showHidingDrawedCard();
      if (this.drawedCardStacks.length - 1 >= 0) {
        this.drawedCardStacks[this.drawedCardStacks.length - 1].getComponent(
          Card
        ).isUnder = false;
      }
      this.btnUndo.interactable = this.undoStacks.length > 0;
    } else {
      cc.warn("pop");
      this.popUndoStack();
      node.getComponent(Card).reset();
    }
  }

  moveCard(node: cc.Node, toStackIndex: number, ignoreRule: boolean = false) {
    let stackCard = this.tableauCardStacks[toStackIndex].getComponent(Card);
    if (stackCard) {
      if (stackCard.setBottomChild(node, ignoreRule)) {
        cc.warn("moveCard::Point::", node.getComponent(Card).point);

        this.drawedCardStacks = this.drawedCardStacks.filter(
          (item) => item !== node
        );

        this.showHidingDrawedCard();
        if (this.drawedCardStacks.length - 1 >= 0) {
          this.drawedCardStacks[this.drawedCardStacks.length - 1].getComponent(
            Card
          ).isUnder = false;
        }
        stackCard.isInWin = false;
        this.btnUndo.interactable = this.undoStacks.length > 0;
      } else {
        // cc.warn("pop");
        this.popUndoStack();
      }
    }

    this.tableauCardStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.tableauCardStacks[index] = null;
      }
    });

    this.winStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.winStacks[index] = null;
      }
    });

    this.openLastCardInStack();
  }

  openLastCardInStack(excludeIdx?: number) {
    this.tableauCardStacks.forEach((stackNode, idx) => {
      if (stackNode && idx !== excludeIdx) {
        let card = stackNode.getComponent(Card);
        if (card) {
          card.turnLastBottomChildToFront();
          this.checkSolve();
        }
      }
    });
  }

  update(dt) {
    if (this.isWin || !this.isStartGame) return;
    this.timer += dt;
    this.boardScore.getComponent(BoardScore).setTimer(Utils.toMMSS(this.timer));

    if (this.currentClickCard > 0) {
      this.lockTimerClickCard -= dt;
      if (this.lockTimerClickCard <= 0) {
        this.currentClickCard = 0;
        this.lockTimerClickCard = DOUBLE_CLICK_TIME;
      }
    }
  }

  resetCheckGameOver() {
    this.noMovePredicts = false;
    this.resetTime = 0;
    this.isStartGame = true;
  }

  showHint(playAnim: boolean = true) {
    SoundManager.inst.playEffect("CLICK_BTN");
    if (this.isShowHint) return;
    this.isShowHint = playAnim;
    if (this.noMovePredicts) {
      this.isStartGame = false;
      PopupManager.inst.openNotification();
      return;
    }

    const remainingCards = this.container
      .getComponentsInChildren(Card)
      .filter(
        (card) =>
          card.isFront &&
          !card.isInWin &&
          !this.drawedCardStacks.includes(card.node)
      );

    for (const card of remainingCards) {
      if (!card) continue;
      const result = this.detectPosibleMove(card.node, playAnim);
      if (result) return true;
    }

    if (this.drawedCardStacks[this.drawedCardStacks.length - 1]) {
      const drawableCard =
        this.drawMode === DrawMode.ONE
          ? this.drawedCardStacks[this.drawedCardStacks.length - 1]
              .getComponent(Card)
              .getBottomCard()
          : this.drawedCardStacks[this.drawedCardStacks.length - 1]
              .getComponent(Card)
              .getRightCard();

      cc.warn({ drawableCard });
      const result = this.detectPosibleMove(drawableCard, playAnim);
      if (result) return true;
    }

    if (playAnim) {
      cc.tween(
        this.finalStacks.children[Stacks.DECK].getChildByName("boder_highlight")
      )
        .set({ active: true })
        .blink(1, 2)
        .call(() => {
          this.finalStacks.children[Stacks.DECK].getChildByName(
            "boder_highlight"
          ).active = false;
          this.isShowHint = false;
        })
        .start();
    }

    return false;
  }

  detectPosibleMove(
    node: cc.Node,
    playAnim: boolean,
    runMove: boolean = false
  ) {
    let card = node.getComponent(Card);

    if (card.bottomContainer.childrenCount === 0) {
      for (let i = 0; i < this.winStacks.length; i++) {
        if (!this.winStacks[i]) {
          if (card.point === 1) {
            if (playAnim) {
              this.highlightNode(node, this.finalStacks.children[i]);
            }

            if (runMove) {
              card.moveToWorldPosition(
                cc.v2(this.getWinStackPosition(i)),
                () => {
                  this.moveCardToWinInStack(node, i);
                  this.blockTouch.active = false;
                }
              );
            }
            return true;
          }
        } else {
          let winCard = this.winStacks[i].getComponent(Card);
          if (winCard && winCard.isCenterChild(node)) {
            if (playAnim) {
              this.highlightNode(
                node,
                this.winStacks[i] ?? this.finalStacks.children[i]
              );
            }

            if (runMove) {
              card.moveToWorldPosition(
                cc.v2(winCard.getCenterWorldPosition()),
                () => {
                  this.moveCardToWinInStack(node, i);
                  this.blockTouch.active = false;
                }
              );
            }
            return true;
          }
        }
      }
    }

    for (let i = 0; i < this.tableauCardStacks.length; i++) {
      if (!this.tableauCardStacks[i]) {
        if (card.point === 13) {
          if (
            card.getParentCount(true) > 0 ||
            (this.drawedCardStacks.includes(node) &&
              card.node.active &&
              card.isFront)
          ) {
            if (playAnim) {
              this.highlightNode(node, this.tableauStacks.children[i]);
            }

            if (runMove) {
              card.moveToWorldPosition(
                cc.v2(this.getTableauStackPosition(i)),
                () => {
                  this.moveCardToEmptyStack(node, i);
                  this.blockTouch.active = false;
                }
              );
            }
            return true;
          }
        }
      } else {
        let tableauCard = this.tableauCardStacks[i].getComponent(Card);
        if (tableauCard && tableauCard.isBottomChild(node)) {
          if (
            (this.drawedCardStacks.includes(card.node) &&
              card.node.active &&
              card.isFront) ||
            card.getParentCount() <= tableauCard.getParentCount()
          ) {
            if (playAnim) {
              this.highlightNode(node, this.tableauCardStacks[i]);
            }

            if (runMove) {
              card.moveToWorldPosition(
                cc.v2(tableauCard.getBottomWorldPosition()),
                () => {
                  this.moveCard(node, i);
                  this.blockTouch.active = false;
                }
              );
            }
            return true;
          }
        }
      }
    }

    return false;
  }

  highlightNode(from: cc.Node, dest: cc.Node) {
    cc.warn(from?.getComponent(Card)?.point, from?.getComponent(Card)?.suit);

    cc.tween(from)
      .blink(1, 2)
      .call(() => {
        this.isShowHint = false;
      })
      .start();

    if (dest.getComponent(Card)) {
      cc.tween(dest.getComponent(Card).getBottomCard())
        .blink(1, 2)
        .call(() => {
          this.isShowHint = false;
        })
        .start();
    } else if (dest.getChildByName("boder_highlight")) {
      cc.tween(dest.getChildByName("boder_highlight"))
        .set({ active: true })
        .blink(1, 2)
        .call(() => {
          dest.getChildByName("boder_highlight").active = false;
          this.isShowHint = false;
        })
        .start();
    } else {
      cc.tween(dest)
        .blink(1, 2)
        .call(() => {
          this.isShowHint = false;
        })
        .start();
    }
  }

  checkCardWin(card) {
    const findCard = this.listCardWin.find((pocker) => pocker === card);
    if (!findCard) {
      this.listCardWin.push(card);
      this.score++;
      this.listTimeStamp.push(parseFloat(this.timer.toFixed(2)));
      this.boardScore
        .getComponent(BoardScore)
        .setScore(this.score, KEY_HIGH_SCORE_GAME);
      if (this.score === 52) {
        console.log(this.listTimeStamp);
        this.blockTouch.active = false;
        this.isStartGame = false;
        this.isWin = true;
        const data: ResultData = {
          score: this.score,
          highScore: this.score,
          time: Utils.toMMSS(this.timer),
        };
        PopupManager.inst.openResult(true, data, "Klondike_HighScore");
      }
    }
  }

  undo() {
    SoundManager.inst.playEffect("CLICK_BTN");
    const record = this.popUndoStack();
    this.btnUndo.interactable = this.undoStacks.length > 0;
    //cc.warn("lenght", this.undoStacks);
    //cc.warn("undo", record);
    if (record) {
      switch (record.from) {
        case "tableau":
          let stackCard = this.tableauCardStacks[record.idx];
          if (stackCard) {
            {
              this.moveCard(record.node, record.idx, true);
              const parent = record.node.parent.parent;
              if (parent && !record.parentStatus)
                record.node.parent.parent.getComponent(Card).turnToBack();
            }
            //cc.warn("here", record.idx);
          } else {
            // cc.warn("here", record.idx);
            this.removeCardFromOldStack(record.node);
            this.moveCardToEmptyStack(record.node, record.idx, true);
          }
          break;
        case "win":
          this.moveCardToWinInStack(record.node, record.idx);
          break;
        case "deck":
          this.blockTouch.active = this.drawMode === DrawMode.ONE;
          let startPosition = this.convertToCardsNodePosition(
            this.getStartStackPosition()
          );
          for (let i = 0; i < this.drawMode; i++) {
            let cardNo = this.drawedCardStacks.pop();
            if (!cardNo) return;
            if (
              this.drawedCardStacks.length - 1 >= 0 &&
              this.drawMode === DrawMode.ONE
            ) {
              this.drawedCardStacks[
                this.drawedCardStacks.length - 1
              ].getComponent(Card).isUnder = false;
            }
            this.showHidingDrawedCard();
            cardNo.parent = this.container;
            cardNo.zIndex = 1;
            cardNo.position = cc.v3(startPosition.x, startPosition.y);
            this.deckCards.push(cardNo);
            let card = cardNo.getComponent(Card);
            card.isUnder = false;
            card.turnToBack();
          }

          if (this.drawMode === DrawMode.THREE) {
            let count = 0;
            for (let i = this.undoStacks.length - 1; i >= 0; i--) {
              if (this.undoStacks[i].from === "drawedCards") {
                count++;
              } else if (this.undoStacks[i].from === "deck") {
                break;
              }
            }

            const lastCard = this.drawedCardStacks.slice(-1);
            let maxCards = 3 - count;

            if (lastCard.length > 0) {
              maxCards = Math.min(
                lastCard[0].getComponent(Card).getParentCount() + 1,
                3 - count
              );
            }

            cc.warn({ lastCard, maxCards });

            for (
              let i = this.drawedCardStacks.length - maxCards;
              i < this.drawedCardStacks.length;
              i++
            ) {
              const nodeCard = this.drawedCardStacks[i];
              if (!nodeCard) {
                break;
              }
              nodeCard.active = true;
              const card = nodeCard.getComponent(Card);

              card.turnToFront();
            }
          }

          this.removeCardFromOldStack(record.node);
          break;
        case "reset":
          if (this.drawMode === DrawMode.ONE) {
            let toPosition2 = this.convertToCardsNodePosition(
              this.getDrawStackPosition()
            );

            while (this.deckCards.length > 0) {
              let cardNo = this.deckCards.pop();
              cardNo.active = true;
              cardNo.zIndex = 52 + this.drawedCardStacks.length;
              cardNo.getComponent(Card).isUnder = true;
              cardNo.getComponent(Card).turnToFront();
              cardNo.position = cc.v3(
                toPosition2.x - 22 - 27,
                toPosition2.y,
                0
              );
              this.drawedCardStacks.push(cardNo);
            }

            const lastThreeCard = this.drawedCardStacks.slice(-3);
            for (let i = 0; i < lastThreeCard.length; i++) {
              cc.tween(lastThreeCard[i])
                .set({ zIndex: 52 + this.drawedCardStacks.length })
                .to(0.15, {
                  position: cc.v3(
                    toPosition2.x - 22 + (i - 1) * 27,
                    toPosition2.y,
                    0
                  ),
                })
                .call(() => {
                  lastThreeCard[i].getComponent(Card).turnToFront();
                })
                .start();

              if (i === lastThreeCard.length - 1) {
                lastThreeCard[i].getComponent(Card).isUnder = false;
              }
            }
          } else {
            cc.log(record.drawedCardStacks);

            let lastResetIdx = this.undoStacks
              .map((step) => step.from)
              .lastIndexOf("reset");
            cc.log(lastResetIdx);

            const steps = this.undoStacks.slice(lastResetIdx + 1);
            cc.log(steps);

            let toPosition2 = this.convertToCardsNodePosition(
              this.getDrawStackPosition()
            );

            let counter = 0;
            let firstNode = null;
            // this.deckCards.length = 0;
            // this.drawedCardStacks = record.drawedCardStacks;

            let deckStartIdx =
              steps.findIndex((step) => step.from === "deck") + 1;
            cc.warn({ firstDeck: deckStartIdx });
            while (this.deckCards.length > 0) {
              let count = 0;
              for (let i = deckStartIdx; i < steps.length; i++) {
                if (steps[i].from === "drawedCards") {
                  count++;
                } else if (steps[i].from === "deck") {
                  deckStartIdx = i + 1;
                  break;
                }
              }

              cc.warn({ count });
              // deckStartIdx += count || 1;

              for (let j = 0; j < 3 - count; j++) {
                let cardNode = this.deckCards.pop();
                if (cardNode) {
                  cardNode.active = false;

                  if (j === 0) {
                    firstNode = cardNode;
                    firstNode.position = cc.v3(
                      toPosition2.x - 22,
                      toPosition2.y,
                      0
                    );
                  } else {
                    firstNode.getComponent(Card).setRightChild(cardNode);
                    cardNode.position = cc.v3(0);
                  }

                  cardNode.getComponent(Card).turnToFront();
                  this.drawedCardStacks.push(cardNode);
                }
              }
            }

            if (deckStartIdx - 1 < steps.length) {
              for (let i = deckStartIdx - 1; i < steps.length; i++) {
                if (steps[i].from === "deck") {
                  cc.log("empty deck");
                  return;
                }
              }
            }

            cc.log(this.drawedCardStacks);
            const lastThreeCard = this.drawedCardStacks.slice(-1);
            if (lastThreeCard[0]) {
              lastThreeCard[0].active = true;
              lastThreeCard[0].getComponent(Card).turnToFront();
              let parent = lastThreeCard[0].parent.parent.getComponent(Card);
              if (parent) {
                parent.node.active = true;
                if (parent.node.parent.parent.getComponent(Card)) {
                  parent.node.parent.parent.active = true;
                  parent.node.parent.parent.getComponent(Card).turnToFront();
                  parent.turnToFront();
                  lastThreeCard[0].getComponent(Card).turnToFront();
                } else {
                  parent.turnToFront();
                  lastThreeCard[0].getComponent(Card).turnToFront();
                }
              }
            }
          }
          break;
        default:
          let toPosition = this.convertToCardsNodePosition(
            this.getDrawStackPosition()
          );

          if (this.drawMode === DrawMode.THREE) {
            const shownCardNo = this.drawedCardStacks.filter(
              (card) => card.active && card.getComponent(Card).isFront
            );
            //cc.warn(shownCardNo.length);
            let firstNode = shownCardNo[0];

            cc.tween(record.node)
              .set({
                parent: this.container,
                position: this.convertToCardsNodePosition(
                  record.node.parent.convertToWorldSpaceAR(record.node)
                ),
                zIndex: 52 + shownCardNo.length,
              })
              .to(0.15, {
                position: cc.v3(
                  toPosition.x - 22 + shownCardNo.length * 27,
                  toPosition.y
                ),
              })
              .call(() => {
                this.blockTouch.active = false;
                if (firstNode)
                  firstNode.getComponent(Card).setRightChild(record.node);
              })
              .start();
          } else {
            let toPosition = this.convertToCardsNodePosition(
              this.getDrawStackPosition()
            );

            const idx =
              this.drawedCardStacks.length >= 3
                ? 2
                : this.drawedCardStacks.length;
            const pos =
              this.drawMode === DrawMode.ONE
                ? cc.v3(toPosition.x - 22 + (idx - 1) * 27, toPosition.y)
                : toPosition;
            cc.tween(record.node)
              .set({
                parent: this.container,
                position: this.convertToCardsNodePosition(
                  record.node.parent.convertToWorldSpaceAR(record.node)
                ),
              })
              .to(0.15, {
                position: pos,
              })
              .call(() => {
                this.blockTouch.active = false;
              })
              .start();
            this.hideFirstCard();
          }

          this.drawedCardStacks.push(record.node);
          if (this.drawMode === DrawMode.ONE) {
            this.drawedCardStacks[
              this.drawedCardStacks.length - 1
            ].getComponent(Card).isUnder = false;
          }
          this.removeCardFromOldStack(record.node);
          break;
      }
      this.checkSolve();
    }
  }

  removeCardFromOldStack(node: cc.Node) {
    this.tableauCardStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.tableauCardStacks[index] = null;
      }
    });

    this.winStacks.forEach((stackNode, index) => {
      if (stackNode === node) {
        this.winStacks[index] = null;
      }
    });
  }

  async onSolved2() {
    this.isSolved = true;
    this.currentCardMove++;
    const remainingCards = this.container
      .getComponentsInChildren(Card)
      .filter(
        (card) =>
          card.isFront &&
          !card.isInWin &&
          !this.drawedCardStacks.includes(card.node) &&
          card.bottomContainer.childrenCount === 0
      );
    if (remainingCards.length === 0) return;
    this.blockTouch.active = true;
    if (this.currentCardMove > remainingCards.length - 1) {
      this.currentCardMove = -1;
      this.onSolved();
    } else {
      await this.checkResultInWinStack(
        remainingCards[this.currentCardMove].node
      );
    }
    //cc.log(remainingCards);
  }

  onSolved() {
    this.isWin = true;
    this.schedule(() => {
      if (this.tableauCardStacks.every((card) => carlistTimeStampupdated == null)) {
        this.unscheduleAllCallbacks();
        return;
      }

      this.tableauCardStacks.forEach((col) => {
        if (col) {
          let card = col.getComponent(Card);
          if (card) {
            const bottomCard = card.getBottomCard();
            if (bottomCard) {
              this.detectPosibleMove(bottomCard, false, true);
            } else {
              this.detectPosibleMove(col, false, true);
            }
          }
        }
      });
    }, 0.15);

    // while (this.tableauCardStacks.some((card) => card !== null)) {
    //   this.tableauCardStacks.forEach((col) => {
    //     let card = col.getComponent(Card);
    //     if (card) {
    //       const bottomCard = card.getBottomCard();
    //       if (bottomCard) {
    //         this.detectPosibleMove(bottomCard, false, true);
    //       } else {
    //         this.detectPosibleMove(col, false, true);
    //       }
    //     }
    //   });
    // }
  }

  popUndoStack() {
    cc.warn("pop", this.undoStacks.slice(), this.undoStacks);
    return this.undoStacks.pop();
  }

  async checkResultInWinStack(node: cc.Node) {
    let card = node.getComponent(Card);
    if (card.bottomContainer.childrenCount === 0) {
      this.detectPosibleMove(node, false, true);
      this.scheduleOnce(() => {
        this.onSolved();
      }, 0.15);
    }
  }

  checkSolve() {
    const cardIsBottom = this.container
      .getComponentsInChildren(Card)
      .filter((card) => !card.isFront);
    this.btnSolved.active =
      this.drawedCardStacks.length === 0 &&
      cardIsBottom.length === 0 &&
      this.deckCards.length === 0;
  }
}
