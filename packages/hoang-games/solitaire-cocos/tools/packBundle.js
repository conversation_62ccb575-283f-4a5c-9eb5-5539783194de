const path = require('path');
const { pathExists, ensureDir, move, readFile, writeFile } = require('fs-extra');
const readline = require('readline');
const rimraf = require('rimraf');

const PRJ_DIR = path.dirname(__dirname, '..');
const BUILD_DIR = path.join(PRJ_DIR, 'build');
const GAME_CONFIG = [
    {
        name: 'Solitaire Klondike',
        folder: 'klondike',
    },
    {
        name: 'Solitaire Spider',
        folder: 'spider',
    }
]

process.chdir(PRJ_DIR);

async function main() {
    const platformDir = path.join(BUILD_DIR, 'web-mobile');
    const wasBuild = await pathExists(platformDir);
    if(!wasBuild) {
        console.log('Please build game');
        return;
    }

    let chooseOpt = 1;
    await new Promise((resolve, reject) => {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
        });
        rl.question(`> Choose Bundle Name:\n   1. ${GAME_CONFIG[0].name}\n   2. ${GAME_CONFIG[1].name}\nInput: `, (answer) => {
            rl.close();
            const numberId = Number.parseInt(answer);
            if(!Number.isNaN(numberId)) {
                if(numberId === 2) {
                    chooseOpt = 2;
                }
                resolve();
            } else {
                console.log('Wrong input!');
                process.exit(0);
            }
        });
    });

    // 1. remote trash file (style-mobile.css, style-desktop.css)
    await new Promise((resolve) => {
        rimraf(path.join(platformDir, 'style-*.css'), {}, (err) => {
            if(err) throw err;
            resolve();
        })
    });

    // 2. create bundle folder
    await ensureDir(path.join(platformDir, '../tmp'));
    await new Promise((resolve) => {
        rimraf(path.join(platformDir, '../tmp/*'), {}, (err) => {
            if(err) throw err;
            resolve();
        })
    });

    // move all to child folder
    const tmpDirName = 'tmp';
    const bundleDirName = GAME_CONFIG[chooseOpt - 1].folder;
    const tmpPath = `../${tmpDirName}`;
    await move(platformDir, path.join(platformDir, `${tmpPath}/${bundleDirName}`));
    await move(path.join(platformDir, `${tmpPath}/${bundleDirName}/index.html`), path.join(platformDir, `${tmpPath}/index.html`));
    await move(path.join(platformDir, `${tmpPath}/${bundleDirName}/favicon.ico`), path.join(platformDir, `${tmpPath}/favicon.ico`));
    await move(path.join(platformDir, `${tmpPath}`), platformDir);

    // replace file
    let indexContent = await readFile(path.join(platformDir, 'index.html'), { encoding: 'utf8' });
    indexContent = indexContent.replace(`<% project.title %>`, `${GAME_CONFIG[chooseOpt - 1].name}`)
    indexContent = indexContent.replace(`"entry.js"`, `"${bundleDirName}/entry.js"`)
    await writeFile(path.join(platformDir, 'index.html'), indexContent, { encoding: 'utf8' });

    let entryContent = await readFile(path.join(platformDir, `${bundleDirName}/entry.js`), { encoding: 'utf8' });
    entryContent = entryContent.replace(`STATIC_ROOT_PATH: ''`, `STATIC_ROOT_PATH: '${bundleDirName}/'`)
    await writeFile(path.join(platformDir, `${bundleDirName}/entry.js`), entryContent, { encoding: 'utf8' });

    console.log('  DONE.');
}

main();