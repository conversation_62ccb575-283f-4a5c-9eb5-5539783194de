/**
 * https://tinypng.com/developers
 */
const path = require('path');
const tinify = require('tinify');
const isImage = require('is-image');
const chalk = require('chalk');
const { getFiles, hashFile, sortObject, readChecksum, saveChecksum, existAsync, delay, CS_TINIFY_PROPERTY: SAVE_PROPERTY } = require('./utils');
const packageConfig = require('../package.json');

// load tinify key
tinify.key = packageConfig.tinifyKey;

/* const lastArg = process.argv[process.argv.length - 1];
const targetFileFilter = process.argv.length > 2 ? lastArg : null; */
let targetPath = '../build/assets/';

const MAX_RUNNER_AT_TIME = 100;
const SAVE_POINT = MAX_RUNNER_AT_TIME / 2;
let lockerPromise = null;
let runningTasks = 0;
let total = 0;
let finish = 0;
let requestCounter = 0;
let responseCounter = 0;
let lastSavePoint = 0;
let needPrintNewLine = false;
let commonChecksum = null;


async function verifyChecksum(checksum) {
    const savedPath = Object.keys(checksum);
    for (let i = 0; i < savedPath.length; i++) {
        const filePath = savedPath[i];
        const isAssetExists = await existAsync(filePath);
        if (!isAssetExists) {
            // console.log(` ${chalk.green('CLEAR:')} ${filePath.replace(targetPath, '/')}`);
            delete checksum[filePath];
        }
    }
    return checksum;
}

async function tinifyTexture(filePath, checksumJson) {
    const autoSaveChecksum = async (isVerify = false) => {
        if (isVerify) {
            // verify checksum
            console.log(chalk.magenta('\n **** Verify Checksum ... ****'));
            checksumJson = await verifyChecksum(checksumJson);
        }
        commonChecksum[SAVE_PROPERTY] = sortObject(checksumJson);
        await saveChecksum(commonChecksum, true);
        if (isVerify) {
            console.log(` >>> ${chalk.green('VERIFIED CHECKSUM')} <<<`);
            lockerPromise?.resolve();
        } else {
            // console.log(` >>> ${chalk.yellow('SAVED PROCESS')} <<<`);
            process.stdout.write(' --\n');
        }
    };
    process.stdout.write(chalk.cyan('.'));
    needPrintNewLine = true;
    ++requestCounter;
    await tinify.fromFile(filePath).toFile(filePath).then(async () => {
        --requestCounter;
        /* Generate hash and store to file hash */
        await hashFile(filePath).then(async (hash) => {
            checksumJson[filePath] = hash;
            ++responseCounter;
            ++finish;
            --runningTasks;
            if (needPrintNewLine) {
                needPrintNewLine = false;
                console.log();
            }
            console.log(` ${chalk.green('DONE(')}${chalk.white(responseCounter)}${chalk.green('):')} ${filePath.replace(targetPath, '/')} ${chalk.gray(`[${finish}/${total}]`)}`);
            if (/* !targetFileFilter &&  */finish >= total) {
                // check done for all
                await autoSaveChecksum(true);
            } else {
                if (responseCounter >= lastSavePoint + SAVE_POINT) {
                    lastSavePoint = responseCounter;
                    await autoSaveChecksum();
                }
            }
        }).catch((err) => {
            --requestCounter;
            console.log(`${chalk.red('ERR')}: Hash ${filePath.replace(targetPath, '/')}`, err);
        });
    }).catch((err) => {
        console.log(`\n${chalk.red('ERR')}: Tinify ${filePath}: ${err}`);
        autoSaveChecksum();
        throw err;
    });
}

async function compareWithPreviousHash(filePath, checksumJson) {
    await hashFile(filePath).then((hash) => {
        const savedHash = checksumJson[filePath];
        const isIgnoreByJson = savedHash.includes('ignore') || savedHash.includes('skip') || savedHash === 'ignore' || savedHash === 'skip';
        if (isIgnoreByJson || hash === savedHash) {
            /* There is no changes in texture just skip */
            // const forceSkipMsg = isIgnoreByJson ? ' <= [Skip by JSON]' : '';
            // console.log(` SKIP: ${filePath.replace(targetPath, '/')}${forceSkipMsg}`);
            // process.stdout.write(chalk.gray('.'));
            // needPrintNewLine = true;
            ++finish;
            --runningTasks;
            if (finish >= total) {
                // check done for all
                console.log('\n > DONE');
                lockerPromise?.resolve();
            }
        } else {
            /* Last hash is different so texture are updated, so let tinify it */
            tinifyTexture(filePath, checksumJson);
        }
    });
}

async function run(path) {
    commonChecksum = await readChecksum();
    const tinifyChecksum = commonChecksum[SAVE_PROPERTY] || {};

    console.log(chalk.magenta('\n **** Tinify textures ... ****'));
    const files = await getFiles(path);
    total = files.length;

    // add favicon png in the parent folder of asset
    // if (path.indexOf('/assets/') > -1) {
    //     files.push({ path: path + '../favicon.png', name: 'favicon' });
    // }

    for (let i = 0; i < total; i++) {
        const filePath = files[i].path;
        if (!isImage(filePath)/*  || targetFileFilter && !(filePath.includes(targetFileFilter) || files[i].name.includes(targetFileFilter)) */) {
            // console.log(` SKIP: ${filePath} <= [By Filter: ${targetFileFilter}]`);
            // process.stdout.write(chalk.gray('.'));
            ++finish;
            continue;
        }
        while (runningTasks >= MAX_RUNNER_AT_TIME) {
            process.stdout.write(chalk.yellow('.'));
            needPrintNewLine = true;
            await delay(requestCounter >= MAX_RUNNER_AT_TIME ? 7000 : 20);
        }
        // console.log(` BUILD: ${filePath.replace(targetPath, '/')}`);
        ++runningTasks;
        if (tinifyChecksum[filePath]) {
            /* If there is a hash file for that file then we will compare it with the latest hash */
            compareWithPreviousHash(filePath, tinifyChecksum);
        } else {
            /* If no hash file generated yet just start tinify texture */
            tinifyTexture(filePath, tinifyChecksum);
        }
    }
    await new Promise((resolve, reject) => {
        lockerPromise = { resolve, reject };
    });
}

// switch to script path
process.chdir(path.join(__dirname));

(async () => {
    if (process.argv.length >= 3) {
        const targetPools = process.argv[2].split(',');
        for (let i = 0; i < targetPools.length; i++) {
            targetPath = targetPools[i];
            console.log(` ${chalk.yellow(`** Build ${targetPath} ... **`)}\n`);
            await run(targetPath);
        }
    }
})();