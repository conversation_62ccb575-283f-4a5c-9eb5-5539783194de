{"paths": {"0": ["gui/Menu/clock_33", 1, 1], "1": ["gui/Menu/highscore_30x44", 1, 1], "2": ["Fonts/Lexend-Medium", 2], "3": ["gui/Menu/bullet_64px", 1, 1], "4": ["audio/spcfg", 4], "5": ["gui/Popup/exit", 1, 1], "6": ["gui/Popup/rule_title", 1, 1], "7": ["gui/Klondike/Solitaire_BG", 6], "8": ["gui/Popup/done-btn", 1, 1], "9": ["Prefabs/Klondike/Card", 3], "10": ["Cards/clubs_09", 1, 1], "12": ["gui/Menu/undo_active_46x42", 1, 1], "13": ["gui/Menu/icon_sound_off_54x44", 1, 1], "14": ["Cards/hearts_03", 1, 1], "15": ["Prefabs/Spider/Popup/SelectSuitGamePopup", 3], "17": ["Cards/hearts_king", 1, 1], "18": ["gui/Menu/banner_bot", 1, 1], "19": ["Cards/diamonds_king", 1, 1], "20": ["gui/Klondike/empty_card", 1, 1], "22": ["Prefabs/Klondike/Popup/GameRule", 3], "23": ["Prefabs/Klondike/Popup/ResultPopup", 3], "24": ["gui/Menu/back_lock", 1, 1], "25": ["Cards/spades_06", 1, 1], "26": ["Cards/hearts_06", 1, 1], "28": ["gui/<PERSON>/boder_hightlight", 1, 1], "29": ["gui/Klondike/home_bg", 1, 1], "30": ["gui/Spider/foundation_spot", 1, 1], "31": ["Cards/clubs_04", 1, 1], "32": ["Cards/clubs_08", 1, 1], "33": ["Cards/clubs_03", 1, 1], "34": ["Cards/diamonds_05", 1, 1], "35": ["gui/Menu/fllrect_top", 1, 1], "36": ["Cards/hearts_jack", 1, 1], "37": ["Cards/clubs_10", 1, 1], "38": ["Cards/diamonds_ace", 1, 1], "39": ["gui/Klondike/stack", 1, 1], "40": ["sprite/square", 1, 1], "41": ["Cards/spades_05", 1, 1], "42": ["Cards/spades_king", 1, 1], "43": ["Fonts/Lexend-Regular", 2], "44": ["Cards/hearts_queen", 1, 1], "45": ["gui/Menu/power-ups_blue", 1, 1], "47": ["audio/spk0", 5], "48": ["gui/Menu/bg_arrow", 1, 1], "49": ["Cards/diamonds_07", 1, 1], "51": ["gui/Popup/Rules_popup_2", 1, 1], "52": ["Cards/spades_07", 1, 1], "53": ["gui/Menu/icon_sound_on_54x44", 1, 1], "54": ["gui/Menu/hint_30x44", 1, 1], "55": ["Cards/diamonds_03", 1, 1], "56": ["sprite/pixil-layer-Background", 1, 1], "57": ["Cards/spades_03", 1, 1], "59": ["Cards/spades_04", 1, 1], "60": ["Cards/hearts_02", 1, 1], "61": ["Cards/spades_10", 1, 1], "62": ["gui/Klondike/Untitled-2", 1, 1], "63": ["Cards/spades_09", 1, 1], "64": ["Cards/diamonds_10", 1, 1], "65": ["gui/Menu/new_56x56.png", 1, 1], "66": ["Cards/clubs_jack", 1, 1], "68": ["gui/Klondike/backside", 1, 1], "69": ["gui/Popup/btn_newgame", 1, 1], "70": ["gui/<PERSON>/spider_red", 1, 1], "71": ["Fonts/Lexend-Bold", 2], "72": ["Prefabs/sounds/SoundManager", 3], "73": ["Prefabs/Spider/CardSpider", 3], "74": ["gui/Menu/star_46x46", 1, 1], "75": ["gui/Popup/X", 1, 1], "76": ["Cards/diamonds_04", 1, 1], "77": ["AutoAtlas", 0], "78": ["gui/Menu/power-ups_gray", 1, 1], "79": ["Cards/spades_ace", 1, 1], "81": ["gui/Popup/pop-up-title", 1, 1], "82": ["Cards/hearts_05", 1, 1], "83": ["gui/Menu/icon_tick", 1, 1], "85": ["Cards/diamonds_09", 1, 1], "86": ["Prefabs/Klondike/Popup/SelectModeGamePopup", 3], "87": ["Prefabs/Spider/Popup/ResultPopup", 3], "88": ["Cards/hearts_04", 1, 1], "90": ["Cards/clubs_05", 1, 1], "91": ["sprite/SE 56R", 1, 1], "92": ["gui/Menu/icon_arrow", 1, 1], "93": ["Cards/diamonds_08", 1, 1], "94": ["gui/Menu/icon_game_rule_36x50", 1, 1], "95": ["Cards/spades_02", 1, 1], "96": ["gui/Menu/load", 1, 1], "97": ["gui/Spider/tableau_spot", 1, 1], "98": ["Cards/spades_queen", 1, 1], "99": ["Cards/hearts_07", 1, 1], "100": ["Cards/diamonds_02", 1, 1], "101": ["Cards/clubs_queen", 1, 1], "102": ["Prefabs/ConfirmPopup", 3], "103": ["sprite/pixil-frame-0", 1, 1], "104": ["Cards/diamonds_06", 1, 1], "106": ["Cards/clubs_king", 1, 1], "108": ["gui/Klondike/under_cards", 1, 1], "109": ["Cards/diamonds_jack", 1, 1], "110": ["Cards/hearts_08", 1, 1], "111": ["Cards/hearts_10", 1, 1], "112": ["gui/Menu/power-ups_green", 1, 1], "113": ["Cards/hearts_ace", 1, 1], "114": ["Prefabs/Spider/Popup/NotificationPopup", 3], "115": ["Cards/diamonds_queen", 1, 1], "116": ["Cards/clubs_07", 1, 1], "117": ["gui/<PERSON>londike/boder", 1, 1], "118": ["Cards/clubs_ace", 1, 1], "119": ["gui/Klondike/Solitaire_BG", 1, 1], "124": ["Cards/spades_08", 1, 1], "125": ["Cards/hearts_09", 1, 1], "127": ["Prefabs/Klondike/Popup/NotificationPopup", 3], "128": ["Cards/spades_jack", 1, 1], "129": ["gui/<PERSON>londike/boder_hightlight", 1, 1], "130": ["Prefabs/Spider/Popup/GameRule", 3], "131": ["Cards/clubs_02", 1, 1], "132": ["sprite/default_panel", 1, 1], "133": ["Cards/clubs_06", 1, 1]}, "types": ["cc.SpriteAtlas", "cc.SpriteFrame", "cc.TTFFont", "cc.Prefab", "cc.Json<PERSON>set", "cc.AudioClip", "cc.Texture2D"], "uuids": ["0bzOF3fK1Pfrm7cUI8g0K6", "11FgQmHEhIraSEX1uPOmgN", "12Bmab5o5Pt5pD1nGzInXB", "17oBh5fSJPYpasgk4cL/Kr", "1a9+rJkyFNlrCUwQYYg+lq", "25a7PPAZxBCraEfIFDXzB2", "5fEa2MzSlHb59KtCV9tXG8", "61v1MkCGhCl6Seo7BgQyDE", "6a8cYYLflLjYykEBlvh1/x", "77FkEoKWJM/JADLChwSlXR", "01y0clsCRBooWHF72JJ21R", "02delMVqdBD70a/HSD99FK", "0bRU22FgVNGr1RNi2KFxtU", "0dJSxVQIVEz4xfCsFoZnth", "0eJlHZQrxD/JPFOIerBZk9", "0fS+qb1qZJupbeDfiMxJ6j", "161ab9828", "17dKuN04ZOX51lZZQFEp8G", "1aZuym7lJFsbpujfI/nva/", "1d/39U+HRDP6eij8wKxqQG", "1eXyulmYxJLbjWK54WE5PN", "1ed22c2ad", "1f7D3hautMkZtphVruZAeG", "22tfx67L1ArLgTbnif+wAL", "24Jd6I1Q1Kh57Y0VP5q4PL", "26iWQd/25McYPfmbYcpky+", "28ldwVpqlLOqU5y4/oFXMP", "29FYIk+N1GYaeWH/q1NxQO", "29J3kA8jtCDYlt67ND1/bj", "2cDBJw7uxL9arUBkD/Gk2X", "2cqlAs8RdH2JOPtCs8VlHZ", "2fHBUdeSxNI7ow2oRIQF6D", "3ablWuug1BNL6N6bEpNCxD", "3f3BN1BS5B470kRH7iSo7f", "45Jyigo8FC2q8/XSdiW+eO", "469dBq5UVLvZLzIjJubSpn", "47xOGA6VpNQYKxWkMo2/dM", "48D/tFUeJLFpFOeQJpQ246", "483OK+rqdIL4EAT137+<PERSON>u", "4bYzCFVCtLoYVz6hw5G/fs", "4bn2lsqHlKYragVNfZBShy", "4eetrFHAVM54V8ZdeDct1Z", "51RB3pR2VJ0Ia8NP56uJu+", "52tl/i5lpON45XINPzyH6h", "53Hxoz4wJLyqjDm0B84sgC", "54HSuTLmNHgLvGhCxKnFTJ", "56fc2Ai/RFNYpaMT8crweK", "57nhVEGHBDAoS9cOMShUXI", "589VumnAhHeqwWwRHCqz93", "5ao5z113xHq5wS1jXvDaY6", "5cO7kybDxGj4ipyMYdRYZB", "5ctdTDq8NEopiwExXi7wmw", "61c90Y5zpLrYbRDjgaP5eq", "62MiBhiG1IqJYp/tRueSYD", "66WDwBFaxHe6ONTTxg2EQv", "675EKfBkdAIJmWxbD83NZ2", "69LnyY4g5AI6IhbcGOcfe6", "71DI3CO7tC+qGPLfLTmiQ0", "71VhFCTINJM6/Ky3oX9nBT", "74k/BVKApDN4Lsmhekutr8", "75CjitdkpJtKYKzWVBQFaj", "7c4472qwROQowdXtCkXajO", "7emkzWjVpBYoN2mx8xiwbK", "842w6oTi1Keq7u6syTK7md", "86R3ArhUNPGYZpbvWhGRqL", "8dXoZExapPv7XLTD0HAsKy", "90YjWpmENMdqNr91ZJUQYq", "90/BnAyuFNf6joGoGJm/2d", "acTj92Br5BfYv+yiZyBsVS", "aff92mttJEkpP2rDVeoefb", "d2AAm2L0hGUJWeGCd2vP9E", "e10un9MTFDsbnT7kxeSHbB", "ec0BMkHlFE+av69K9tE7Zz", "ee8l5KpqhBrIg45/HeWis/", "f2ptjRktJPzKQsRXVh/Tom", "fb7FiNEONNgpQ3H/rUPCQ/", "96r/6ifx1HhrmdcfkyZP+C", "97Z+Myjs1JzrPgKN8RUbEr", "98R8YBbltJbrkjAekledYd", "9aQsDPMPpOComdReOuOvE4", "9bvaMerUlDyary99mJa6xp", "9cY6JWd8NNOaOQ5dCYmurq", "9dgsg8+cZAlbPk8i6cqQKt", "a1Hh/Xf29OUb3DB1BC0BYD", "a2MjXRFdtLlYQ5ouAFv/+R", "a3qt2cIDxFX4BVS23Kv43b", "a5UZZJU/lKpqnwIdkT092E", "a88yn5viRNI7NJPm+Xgz2o", "b2CeZHssZAFJLfIeZa4xXR", "b4P/PCArtIdIH38t6mlw8Y", "b7y/Ei5ctADqWlUqLmz8oA", "b8qOJRWRhOK7zPnEqvkgVP", "b9sfmzNhRBDLi0U1GPD83S", "bcCbdRbUtFwqIxI/gUoNDa", "bdTM5S1URMDY77pBPPwBMx", "bfo17vyY5MI6EnaW1r0UHU", "c0toa3vT1HHZ7K5xyLwbqr", "c1E+0bvftJxYX7weIIcIr2", "c2LDD1C3dA/bJ4A1sQrEgF", "c526hU/5VG7rZiNCe6lr5o", "c6reRe2utFfoAjT/P/FbY5", "cakSQSOR1LKbIymA/WkZCw", "cau+o9IIRD0aGheFHGZ02p", "cbLkf//etAQYWSU9sX5ZV+", "ce0l0pveVJlIwg6IknLJXO", "d608qFRoFHwbXd0Dap056i", "d7c9vuCLJHy6IFW2qOuELd", "d8HsitJHxOYqo801xBk8ev", "d8VVhMTkVBD7Tw5Bp2m14e", "d8o4AOfyNHgI6+9qree4R9", "d9nMqmvJ9DsoHA2dc5D37V", "da59ckV6BBl7Sjr49QZMnn", "dcYK6V2hhE4aTHf6RquAWG", "dczHQBcq1HAZMlLe/fGmm0", "e0czFbOuRBDalzRf5QKme1", "e39zZMWKlHFKPN5QXbWnZn", "e4A+F7EzNKM5abVhZEz5Wn", "e44+RRcOdGPYM4oEoJpHF4", "e6MYyGN8NIhL0m/UX10A/Y", "e6xYuLjlNDJbi1RFh/4qnz", "e7q6FL+VZEgLJUjVeDLic/", "e8Ueib+qJEhL6mXAHdnwbi", "e97GVMl6JHh5Ml5qEDdSGa", "ecpdLyjvZBwrvm+cedCcQy", "edKTEF/LtKdbYo0myMcy9n", "edjmZv6dNBiqXAKo6lvF81", "f0BIwQ8D5Ml7nTNQbh1YlS", "f2L5Oo/L9OhawqcTD1rvL1", "f2bwHzfFJK6bqRl9uwmQXY", "f23ZPsoB9BC4K2+WLjogZ2", "f6g40siXVL1YzYh5dR+SCI", "f6xb1z5eJIy7fLKuB8JzoM", "f7+97s+ZJITqnvjR3utars", "ff8An7JWFA5a8c6qYwpMAp"], "scenes": {}, "redirect": [11, 0, 123, 0], "deps": ["internal"], "packs": {"02004102c": [50, 6, 84, 75], "0224018f0": [27, 8, 69, 122, 126], "025059118": [16, 21, 46, 7, 58, 89, 105, 107, 121], "040e045e6": [3, 5, 120], "050f95543": [2, 71], "076e3eb1a": [0, 1, 74], "092d7addf": [9, 68], "0bf4da0a9": [70, 73], "0c45741d1": [4, 72]}, "name": "resources", "importBase": "import", "nativeBase": "native", "debug": false, "isZip": false, "encrypted": false}