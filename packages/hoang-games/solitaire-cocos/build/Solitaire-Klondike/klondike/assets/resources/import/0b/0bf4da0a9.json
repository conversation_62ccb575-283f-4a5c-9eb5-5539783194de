[1, ["ecpdLyjvZBwrvm+cedCcQy", "d2AAm2L0hGUJWeGCd2vP9E", "1ed22c2ad"], ["node", "_spriteFrame", "_textureSetter", "root", "bottomContainer", "right<PERSON><PERSON><PERSON>", "centerContainer", "fgNode", "bgNode", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs"], 1, 4, 1, 9, 5, 2, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["9d57alANGVJNYoLs+tGedfG", ["node", "bgNode", "fgNode", "centerContainer", "right<PERSON><PERSON><PERSON>", "bottomContainer"], 3, 1, 1, 1, 1, 1, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1]], [[1, 0, 1, 2, 2], [5, 0, 1, 2, 3, 2], [6, 0, 1, 2, 3, 4], [0, 0, 3, 4, 2, 5, 2], [0, 0, 3, 2, 7, 2], [3, 0, 2], [0, 0, 6, 4, 2, 5, 2], [0, 0, 1, 3, 4, 2, 5, 3], [0, 0, 3, 2, 2], [4, 0, 1, 2, 3, 4, 5, 1], [1, 1, 2, 1]], [[[{"name": "spider_red", "rect": [441, 679, 103, 139], "offset": [0, 0], "originalSize": [103, 139], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[[5, "<PERSON><PERSON><PERSON><PERSON>"], [6, "Card", [-8, -9, -10, -11, -12, -13], [[9, -7, -6, -5, -4, -3, -2]], [10, -1, 0], [5, 100, 140]], [3, "Fg", 1, [[1, 0, -14, [2], 3], [2, 45, 100, 130, -15]], [0, "b4aJbJn7VPcqzI+CkX9D0L", 1, 0], [5, 100, 140]], [3, "Bg", 1, [[1, 0, -16, [4], 5], [2, 45, 100, 130, -17]], [0, "23iZvnZsBEo4/sv6PB5wB9", 1, 0], [5, 100, 140]], [7, "Block", 0, 1, [[1, 0, -18, [0], 1], [2, 45, 100, 100, -19]], [0, "3baTumZ2RCKIlo/hOvD+EJ", 1, 0], [5, 100, 140]], [4, "RightContainer", 1, [0, "d5DXAQJxZJ0L6qBM5N2+f4", 1, 0], [27, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "BottomContainer", 1, [0, "a6xk683z9IyrSTYPJ2DIOv", 1, 0], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "CenterContainer", 1, [0, "das696UfhFqoZr+WyVC8H9", 1, 0]]], 0, [0, 3, 1, 0, 4, 6, 0, 5, 5, 0, 6, 7, 0, 7, 2, 0, 8, 3, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 3, 0, -4, 5, 0, -5, 6, 0, -6, 7, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 9, 1, 19], [0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1], [0, 1, 0, 1, 0, 1]]]]