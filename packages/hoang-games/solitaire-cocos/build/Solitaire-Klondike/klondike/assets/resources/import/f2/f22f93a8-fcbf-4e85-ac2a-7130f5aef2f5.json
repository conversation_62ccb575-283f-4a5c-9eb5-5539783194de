[1, ["ecpdLyjvZBwrvm+cedCcQy", "e10un9MTFDsbnT7kxeSHbB", "aff92mttJEkpP2rDVeoefb", "6a8cYYLflLjYykEBlvh1/x", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "9cY6JWd8NNOaOQ5dCYmurq", "9bvaMerUlDyary99mJa6xp", "5ctdTDq8NEopiwExXi7wmw", "4bn2lsqHlKYragVNfZBShy", "12Bmab5o5Pt5pD1nGzInXB"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "content", "titleLbl", "blurBg", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_color"], 1, 9, 4, 5, 2, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_verticalCenter", "alignMode", "node"], -3, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_lineHeight", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs"], 1, 1, 12, 4, 5, 5, 7], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$affectedByScale", "node", "_layoutSize"], -1, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["71d9dLT6N9JbII81eOinzsU", ["node", "blurBg", "titleLbl", "content"], 3, 1, 1, 1, 1]], [[3, 0, 1, 2], [3, 0, 1, 2, 2], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 5, 2, 3, 4, 7, 2], [0, 0, 6, 5, 2, 3, 4, 7, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [2, 5, 0, 1, 2, 6, 5], [4, 0, 1, 2, 4, 3, 8, 9, 10, 6], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [9, 0, 1, 2, 3], [6, 0, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 6, 5, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 6, 5, 3], [5, 0, 2, 3, 4, 5, 7, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 0, 3, 4, 2], [1, 2, 1, 0, 3, 4, 5, 4], [2, 0, 1, 2, 6, 4], [2, 0, 3, 4, 6, 4], [2, 0, 6, 2], [3, 1, 2, 1], [4, 0, 1, 6, 2, 4, 3, 5, 8, 9, 8], [4, 0, 1, 7, 2, 3, 5, 8, 9, 7], [10, 0, 1, 2, 3, 4, 5, 5], [11, 0, 1], [12, 0, 1, 2, 3, 1]], [[12, "NotificationPopup"], [3, "layout", [-4, -5], [[19, false, 1, 0, -2, [21], 22], [26, 1, 1, 70, true, -3, [5, 524, 73]]], [0, "80uo4lOyVAPIdHK5h/FmrU", -1], [5, 524, 73], [4, -172.966, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "NotificationPopup", [-11], [[28, -10, -9, -8, -7]], [23, -6, 0], [5, 1560, 720]], [3, "result-popup", [-13, -14, -15, 1], [[6, 0, -12, [23], 24]], [1, "e07P8e+5ZPNKy/hFHnYlct", 2, 0], [5, 718, 471], [0, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Panel", 2, [-18, 3], [[22, 45, -16], [27, -17]], [1, "40Jrqg/R1K5Zv7WD5bnq0l", 2, 0], [5, 1560, 720]], [5, "Background", 512, [-21], [[7, 1, 0, -19, [7], 8], [8, 0, 45, 100, 40, -20]], [0, "ddx1Pn5ytAI7m+DYfE70Wy", 1], [4, 4293322470], [5, 227, 64]], [5, "Background", 512, [-24], [[7, 1, 0, -22, [15], 16], [8, 0, 45, 100, 40, -23]], [0, "4dfMIC/FRKZZQDkJnYEE67", 1], [4, 4293322470], [5, 227, 64]], [15, "BG", 150, 4, [[-25, [20, 45, 40, 36, -26]], 1, 4], [1, "6dTyxLBO9NWoli7WHtillt", 2, 0], [4, 4278190080], [5, 1560, 720]], [16, "Label Header", 3, [[-27, [21, 17, 11.5, 74.054, -28]], 1, 4], [1, "38ekN1x59DCLGIwS2jIy04", 2, 0], [5, 496, 76], [0, 186, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "content", 3, [-30], [0, "67ARB+hQtNx5gpv5f6KzlB", -29], [5, 605, 244], [0, -10.833, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "ButtonNewPlay", 1, [5], [[10, 1, -31, [[11, "71d9dLT6N9JbII81eOinzsU", "onClickOkButton", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 5, 9, 10, 11, 12]], [0, "f6nQwLs0ZL7rgFlgdoG3JH", 1], [5, 227, 64], [-148.5, 4.672, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "ButtonCountinue", 1, [6], [[10, 1, -32, [[11, "71d9dLT6N9JbII81eOinzsU", "onClickCountinueButton", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 6, 17, 18, 19, 20]], [0, "98IkueY5hAbqTj9hQ0sXEu", 1], [5, 227, 64], [148.5, 4.672, 0, 0, 0, 0, 1, 1, 1, 1]], [18, 0, 7, [0]], [2, "pop-up-title", 3, [[6, 0, -33, [1], 2]], [1, "38W98q6z9My6MjBwH5s5CX", 2, 0], [5, 717.6, 108], [-0.3, 182, 0, 0, 0, 0, 1, 1, 1, 1]], [24, " No Moves Remaining", 50, 60, false, 1, 1, 2, 8, [3]], [25, "You can undo some steps to win the game.", 35, false, false, 1, 2, 9, [4]], [2, "label", 5, [[9, "New game", 30, false, 1, 1, -34, [5], 6]], [0, "79i01tYLVLvLNLBBwWWss0", 1], [5, 162.39, 50.4], [0, 2.561, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "label", 6, [[9, "Continue", 30, false, 1, 1, -35, [13], 14]], [0, "03ZIFhbsxA/Lb5yZspuwjq", 1], [5, 136.5, 50.4], [0, 2.561, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, 4, 2, 0, 9, 15, 0, 10, 14, 0, 11, 12, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, -1, 13, 0, -2, 8, 0, -3, 9, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, -1, 16, 0, 0, 6, 0, 0, 6, 0, -1, 17, 0, -1, 12, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 4, 9, 0, -1, 15, 0, 0, 10, 0, 0, 11, 0, 0, 13, 0, 0, 16, 0, 0, 17, 0, 12, 2, 1, 3, 3, 3, 3, 4, 5, 3, 10, 6, 3, 11, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 14, 15], [-1, -1, 1, -1, -1, -1, 2, -1, 1, 5, 6, 7, 8, -1, 2, -1, 1, 5, 6, 7, 8, -1, 1, -1, 1, 1, 2, 2], [0, 0, 7, 0, 0, 0, 1, 0, 2, 3, 4, 5, 6, 0, 1, 0, 2, 3, 4, 5, 6, 0, 8, 0, 9, 10, 1, 11]]