[1, ["4bn2lsqHlKYragVNfZBShy", "69LnyY4g5AI6IhbcGOcfe6", "cbLkf//etAQYWSU9sX5ZV+", "f7+97s+ZJITqnvjR3utars", "b9sfmzNhRBDLi0U1GPD83S", "11FgQmHEhIraSEX1uPOmgN", "66WDwBFaxHe6ONTTxg2EQv", "0bzOF3fK1Pfrm7cUI8g0K6", "6a8cYYLflLjYykEBlvh1/x", "25a7PPAZxBCraEfIFDXzB2", "bdTM5S1URMDY77pBPPwBMx", "fb7FiNEONNgpQ3H/rUPCQ/", "17oBh5fSJPYpasgk4cL/Kr", "24Jd6I1Q1Kh57Y0VP5q4PL", "0bRU22FgVNGr1RNi2KFxtU", "0dJSxVQIVEz4xfCsFoZnth", "62MiBhiG1IqJYp/tRueSYD", "54HSuTLmNHgLvGhCxKnFTJ", "98R8YBbltJbrkjAekledYd", "dcYK6V2hhE4aTHf6RquAWG", "f2ptjRktJPzKQsRXVh/Tom", "8dXoZExapPv7XLTD0HAsKy", "a1Hh/Xf29OUb3DB1BC0BYD", "469dBq5UVLvZLzIjJubSpn", "aff92mttJEkpP2rDVeoefb", "2cDBJw7uxL9arUBkD/Gk2X", "589VumnAhHeqwWwRHCqz93", "1aZuym7lJFsbpujfI/nva/", "d8VVhMTkVBD7Tw5Bp2m14e", "c0toa3vT1HHZ7K5xyLwbqr", "01y0clsCRBooWHF72JJ21R", "0eJlHZQrxD/JPFOIerBZk9", "17dKuN04ZOX51lZZQFEp8G", "1d/39U+HRDP6eij8wKxqQG", "26iWQd/25McYPfmbYcpky+", "28ldwVpqlLOqU5y4/oFXMP", "2fHBUdeSxNI7ow2oRIQF6D", "3ablWuug1BNL6N6bEpNCxD", "3f3BN1BS5B470kRH7iSo7f", "45Jyigo8FC2q8/XSdiW+eO", "47xOGA6VpNQYKxWkMo2/dM", "48D/tFUeJLFpFOeQJpQ246", "483OK+rqdIL4EAT137+<PERSON>u", "4eetrFHAVM54V8ZdeDct1Z", "51RB3pR2VJ0Ia8NP56uJu+", "53Hxoz4wJLyqjDm0B84sgC", "5ao5z113xHq5wS1jXvDaY6", "61c90Y5zpLrYbRDjgaP5eq", "675EKfBkdAIJmWxbD83NZ2", "71DI3CO7tC+qGPLfLTmiQ0", "74k/BVKApDN4Lsmhekutr8", "75CjitdkpJtKYKzWVBQFaj", "7c4472qwROQowdXtCkXajO", "842w6oTi1Keq7u6syTK7md", "86R3ArhUNPGYZpbvWhGRqL", "90YjWpmENMdqNr91ZJUQYq", "96r/6ifx1HhrmdcfkyZP+C", "9aQsDPMPpOComdReOuOvE4", "9dgsg8+cZAlbPk8i6cqQKt", "a3qt2cIDxFX4BVS23Kv43b", "acTj92Br5BfYv+yiZyBsVS", "b2CeZHssZAFJLfIeZa4xXR", "b7y/Ei5ctADqWlUqLmz8oA", "bcCbdRbUtFwqIxI/gUoNDa", "bfo17vyY5MI6EnaW1r0UHU", "c2LDD1C3dA/bJ4A1sQrEgF", "c526hU/5VG7rZiNCe6lr5o", "d2AAm2L0hGUJWeGCd2vP9E", "c6reRe2utFfoAjT/P/FbY5", "cakSQSOR1LKbIymA/WkZCw", "ce0l0pveVJlIwg6IknLJXO", "d7c9vuCLJHy6IFW2qOuELd", "d8o4AOfyNHgI6+9qree4R9", "d9nMqmvJ9DsoHA2dc5D37V", "da59ckV6BBl7Sjr49QZMnn", "dczHQBcq1HAZMlLe/fGmm0", "e39zZMWKlHFKPN5QXbWnZn", "e4A+F7EzNKM5abVhZEz5Wn", "e6MYyGN8NIhL0m/UX10A/Y", "edKTEF/LtKdbYo0myMcy9n", "edjmZv6dNBiqXAKo6lvF81", "f2bwHzfFJK6bqRl9uwmQXY", "f6xb1z5eJIy7fLKuB8JzoM", "ff8An7JWFA5a8c6qYwpMAp", "5fEa2MzSlHb59KtCV9tXG8", "f23ZPsoB9BC4K2+WLjogZ2", "5ctdTDq8NEopiwExXi7wmw", "e44+RRcOdGPYM4oEoJpHF4", "7emkzWjVpBYoN2mx8xiwbK", "4bYzCFVCtLoYVz6hw5G/fs", "2cqlAs8RdH2JOPtCs8VlHZ", "c1E+0bvftJxYX7weIIcIr2", "b8qOJRWRhOK7zPnEqvkgVP", "1eXyulmYxJLbjWK54WE5PN", "9cY6JWd8NNOaOQ5dCYmurq"], ["square", "pixil-layer-Background", "pixil-frame-0", "default_panel", "icon_arrow", "highscore_30x44", "hint_30x44", "clock_33", "done-btn", "exit", "icon_game_rule_36x50", "X", "bullet_64px", "back_lock", "undo_active_46x42", "icon_sound_off_54x44", "icon_sound_on_54x44", "power-ups_blue", "power-ups_gray", "power-ups_green", "star_46x46", "new_56x56", "icon_tick", "fllrect_top", "btn_newgame", "home_bg", "bg_arrow", "banner_bot", "under_cards", "load", "clubs_09", "hearts_03", "hearts_king", "diamonds_king", "spades_06", "hearts_06", "clubs_04", "clubs_08", "clubs_03", "diamonds_05", "hearts_jack", "clubs_10", "diamonds_ace", "spades_05", "spades_king", "hearts_queen", "diamonds_07", "spades_07", "diamonds_03", "spades_03", "spades_04", "hearts_02", "spades_10", "spades_09", "diamonds_10", "clubs_jack", "diamonds_04", "spades_ace", "hearts_05", "diamonds_09", "backside", "hearts_04", "clubs_05", "diamonds_08", "spades_02", "spades_queen", "hearts_07", "spider_red", "diamonds_02", "clubs_queen", "diamonds_06", "clubs_king", "diamonds_jack", "hearts_08", "hearts_10", "hearts_ace", "diamonds_queen", "clubs_07", "clubs_ace", "spades_08", "hearts_09", "spades_jack", "clubs_02", "clubs_06", "rule_title", "boder_hightlight", "Rules_popup_2", "boder", "Untitled-2", "stack", "foundation_spot", "tableau_spot", "SE 56R", "empty_card", "pop-up-title"], [["cc.SpriteAtlas", ["_spriteFrames"], 3, 11]], [[0, 0, 1]], [[0, [{}, "square", 6, 0, "pixil-layer-Background", 6, 1, "pixil-frame-0", 6, 2, "default_panel", 6, 3, "icon_arrow", 6, 4, "highscore_30x44", 6, 5, "hint_30x44", 6, 6, "clock_33", 6, 7, "done-btn", 6, 8, "exit", 6, 9, "icon_game_rule_36x50", 6, 10, "X", 6, 11, "bullet_64px", 6, 12, "back_lock", 6, 13, "undo_active_46x42", 6, 14, "icon_sound_off_54x44", 6, 15, "icon_sound_on_54x44", 6, 16, "power-ups_blue", 6, 17, "power-ups_gray", 6, 18, "power-ups_green", 6, 19, "star_46x46", 6, 20, "new_56x56", 6, 21, "icon_tick", 6, 22, "fllrect_top", 6, 23, "btn_newgame", 6, 24, "home_bg", 6, 25, "bg_arrow", 6, 26, "banner_bot", 6, 27, "under_cards", 6, 28, "load", 6, 29, "clubs_09", 6, 30, "hearts_03", 6, 31, "hearts_king", 6, 32, "diamonds_king", 6, 33, "spades_06", 6, 34, "hearts_06", 6, 35, "clubs_04", 6, 36, "clubs_08", 6, 37, "clubs_03", 6, 38, "diamonds_05", 6, 39, "hearts_jack", 6, 40, "clubs_10", 6, 41, "diamonds_ace", 6, 42, "spades_05", 6, 43, "spades_king", 6, 44, "hearts_queen", 6, 45, "diamonds_07", 6, 46, "spades_07", 6, 47, "diamonds_03", 6, 48, "spades_03", 6, 49, "spades_04", 6, 50, "hearts_02", 6, 51, "spades_10", 6, 52, "spades_09", 6, 53, "diamonds_10", 6, 54, "clubs_jack", 6, 55, "diamonds_04", 6, 56, "spades_ace", 6, 57, "hearts_05", 6, 58, "diamonds_09", 6, 59, "backside", 6, 60, "hearts_04", 6, 61, "clubs_05", 6, 62, "diamonds_08", 6, 63, "spades_02", 6, 64, "spades_queen", 6, 65, "hearts_07", 6, 66, "spider_red", 6, 67, "diamonds_02", 6, 68, "clubs_queen", 6, 69, "diamonds_06", 6, 70, "clubs_king", 6, 71, "diamonds_jack", 6, 72, "hearts_08", 6, 73, "hearts_10", 6, 74, "hearts_ace", 6, 75, "diamonds_queen", 6, 76, "clubs_07", 6, 77, "clubs_ace", 6, 78, "spades_08", 6, 79, "hearts_09", 6, 80, "spades_jack", 6, 81, "clubs_02", 6, 82, "clubs_06", 6, 83, "rule_title", 6, 84, "boder_hightlight", 6, 85, "Rules_popup_2", 6, 86, "boder", 6, 87, "Untitled-2", 6, 88, "stack", 6, 89, "foundation_spot", 6, 90, "tableau_spot", 6, 91, "SE 56R", 6, 92, "empty_card", 6, 93, "pop-up-title", 6, 94]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94]]