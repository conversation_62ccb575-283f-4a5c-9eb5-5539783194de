[1, ["ecpdLyjvZBwrvm+cedCcQy", "e7q6FL+VZEgLJUjVeDLic/", "12Bmab5o5Pt5pD1nGzInXB", "17oBh5fSJPYpasgk4cL/Kr", "e10un9MTFDsbnT7kxeSHbB", "6a8cYYLflLjYykEBlvh1/x", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "9cY6JWd8NNOaOQ5dCYmurq", "aff92mttJEkpP2rDVeoefb", "25a7PPAZxBCraEfIFDXzB2", "5ctdTDq8NEopiwExXi7wmw", "4bn2lsqHlKYragVNfZBShy"], ["node", "_spriteFrame", "_parent", "_N$file", "checkMark", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "btnClose", "confirmBtn", "blurBg", "data"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_color"], 0, 9, 4, 5, 2, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_top", "_verticalCenter", "node"], -3, 1], ["cc.Label", ["_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_fontSize", "_name", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 1, 1, 5, 1, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_color", "_contentSize"], 1, 1, 12, 4, 5, 5], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["7fd79t6CNlMla6/29mS6CfN", ["node", "blurBg", "confirmBtn", "btnClose"], 3, 1, 1, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 7, 3, 4, 5, 8, 2], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 7, 3, 4, 9, 5, 8, 2], [1, 0, 1, 3, 4, 5, 3], [1, 0, 1, 3, 4, 3], [3, 5, 3, 4, 0, 1, 2, 8, 9, 10, 7], [7, 0, 1, 2, 3, 4], [0, 0, 2, 6, 3, 4, 9, 5, 3], [0, 0, 7, 6, 3, 4, 5, 8, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 3, 4, 5, 2], [1, 2, 0, 3, 4, 5, 3], [2, 0, 1, 2, 6, 4], [2, 3, 0, 1, 2, 6, 5], [7, 0, 1, 3, 3], [8, 0, 1, 2, 3, 4, 5, 6, 3], [9, 0, 2], [0, 0, 1, 6, 3, 4, 5, 3], [0, 0, 7, 6, 3, 4, 5, 2], [10, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 2, 7, 3, 4, 5, 6, 2], [4, 0, 2, 3, 4, 5, 6, 2], [1, 0, 3, 4, 2], [2, 0, 4, 5, 6, 4], [2, 0, 6, 2], [5, 1, 2, 1], [3, 3, 6, 0, 1, 2, 7, 8, 9, 10, 7], [3, 3, 4, 6, 0, 1, 2, 8, 9, 10, 7], [3, 5, 4, 0, 1, 2, 8, 9, 6], [6, 0, 1, 2, 3, 4, 5, 6, 2], [6, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [8, 0, 2, 3, 4, 5, 6, 2], [11, 0, 1], [12, 0, 1], [13, 0, 1, 2, 3, 1]], [[17, "SelectSuitGamePopup"], [18, "SelecSuitGamePopup", false, [-7], [[35, -5, -4, -3, -2], [13, 45, 1560, 720, -6]], [26, -1, 0], [5, 1560, 720]], [2, "result-popup", [-9, -10, -11, -12, -13], [[11, 0, -8, [31], 32]], [0, "b7fFWd7wlAiYVlMN72tOTQ", 1, 0], [5, 718, 399], [0, -14.454, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "toggle1", [-17, -18, -19], [[32, 3, -16, [4, 4292269782], -15, -14, [[7, "7fd79t6CNlMla6/29mS6CfN", "onSelectDraw", "1", 1]]]], [0, "bd0iPY18FFRaSuT7M+m2Pl", 1, 0], [5, 308, 80], [-203.632, -4.367, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "toggle2", [-23, -24, -25], [[16, 3, false, -22, [4, 4292269782], -21, -20, [[7, "7fd79t6CNlMla6/29mS6CfN", "onSelectDraw", "2", 1]]]], [0, "7fR4UUuYZP4oYZKB02v769", 1, 0], [5, 288, 80], [34.399, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "toggle3", [-29, -30, -31], [[16, 3, false, -28, [4, 4292269782], -27, -26, [[7, "7fd79t6CNlMla6/29mS6CfN", "onSelectDraw", "4", 1]]]], [0, "ef0SK20xJB2oKICOFQ/zk7", 1, 0], [5, 288, 80], [269.231, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "Panel", 1, [-34, 2], [[25, 45, -32], [34, -33]], [0, "3fscoIS21CI7xnhBXnNj56", 1, 0], [5, 1560, 720]], [8, "Background", 512, [-37], [[12, 1, 0, -35, [5], 6], [14, 0, 45, 100, 40, -36]], [0, "1738eGblBGyYXB44i6R+Oe", 1, 0], [4, 4293322470], [5, 227, 64]], [9, "toggleC<PERSON><PERSON>", 2, [3, 4, 5], [[33, -38]], [0, "961nC7s5pBA5XLOmgdFTWt", 1, 0], [5, 221, 61], [-8.933, -14.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 512, [-41], [[12, 1, 0, -39, [25], 26], [14, 0, 45, 100, 40, -40]], [0, "91soySiHhI9I8azjOHQNFN", 1, 0], [4, 4293322470], [5, 50, 50]], [9, "buttonClose", 2, [9], [[31, -42, [[15, "7fd79t6CNlMla6/29mS6CfN", "onClose", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 9, 27, 28, 29, 30]], [0, "18k4GmM0JEGIIoBzzrzUy2", 1, 0], [5, 50, 50], [307.561, 146.047, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "BG", 150, 6, [[-43, [13, 45, 40, 36, -44]], 1, 4], [0, "ebxHe/LClC+ofSuaqEwHve", 1, 0], [4, 4278190080], [5, 1560, 720]], [21, "ButtonNewPlay", 2, [7], [-45], [0, "d56jVLCzhMwIfqW3thPbxk", 1, 0], [5, 227, 64], [0, -137.876, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label Header", 2, [[27, "CHOOSE A GAME", 50, false, 1, 1, 3, -46, [7], 8], [24, 17, 22.278, 74.054, -47]], [0, "84Tk1Tl6dMdaVDrffZlgd/", 1, 0], [5, 412.5, 63], [0, 145.722, 0, 0, 0, 0, 1, 1, 1, 1]], [23, 0, 11, [0]], [1, "pop-up-title", 2, [[11, 0, -48, [1], 2]], [0, "1fxrUrciBCn4046ZBGnCYB", 1, 0], [5, 717.6, 108], [-0.2, 145.932, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 7, [[6, "Label<Label>", "New Game", 30, false, 1, 1, -49, [3], 4]], [0, "9amt4KFGVOtY+5HT0UgY0z", 1, 0], [5, 165.78, 50.4], [0, -0.209, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 1, 12, [[15, "7fd79t6CNlMla6/29mS6CfN", "onClickNewPlay", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 7], [3, "Background", 3, [[4, 2, false, -50, [9], 10]], [0, "50aVkLSwJPpbuMwbbo4Z7V", 1, 0], [4, 4289369983], [5, 32, 32], [-85.796, 2.341, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [22, "checkmark", 3, [-51], [0, "8cS0XEWupDgKUBHAeJHDiX", 1, 0], [5, 64, 64], [-85.436, 2.341, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 2, false, 19, [11]], [1, "label", 3, [[28, "1 SUIT", 30, 35, false, 1, 1, -52, [12], 13]], [0, "38TAOBWLRON6wj5ibZpwS3", 1, 0], [5, 99.03, 44.1], [8.184, 6.616, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Background", 4, [[4, 2, false, -53, [14], 15]], [0, "4bn4ANr0BFR4J+9WlQcKZZ", 1, 0], [4, 4289369983], [5, 32, 32], [-92.392, 0, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [10, "checkmark", false, 4, [-54], [0, "efmrEck+1H77JG7PqZO7rW", 1, 0], [5, 64, 64], [-91.935, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 2, false, 23, [16]], [1, "label", 4, [[6, "Label<Label>", "2 SUITS", 30, false, 1, 1, -55, [17], 18]], [0, "09yk14TDNLHrlTk6CNKJ3w", 1, 0], [5, 118.62, 50.4], [3.493, 1.386, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Background", 5, [[4, 2, false, -56, [19], 20]], [0, "83KFhDTwpELISt8obeIjiI", 1, 0], [4, 4289369983], [5, 32, 32], [-92.154, 0, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [10, "checkmark", false, 5, [-57], [0, "2cnPr5CT9Ino+wKeulvc94", 1, 0], [5, 64, 64], [-91.697, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 2, false, 27, [21]], [1, "label", 5, [[6, "Label<Label>", "4 SUITS", 30, false, 1, 1, -58, [22], 23]], [0, "0eQUKBM0xOYoBQG28nTdAh", 1, 0], [5, 121.23, 50.4], [3.493, 1.719, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 9, [[29, "Label<Label>", 30, false, 1, 1, -59, [24]]], [0, "76xbKISlNO1buq3mj0MVU0", 1, 0], [5, 0, 50.4], [0, 0.363, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 10, 1, 0, 11, 10, 0, 12, 17, 0, 13, 14, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, 0, 2, 0, -1, 15, 0, -2, 12, 0, -3, 13, 0, -4, 8, 0, -5, 10, 0, 4, 20, 0, 5, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, -3, 21, 0, 4, 24, 0, 5, 4, 0, 0, 4, 0, -1, 22, 0, -2, 23, 0, -3, 25, 0, 4, 28, 0, 5, 5, 0, 0, 5, 0, -1, 26, 0, -2, 27, 0, -3, 29, 0, 0, 6, 0, 0, 6, 0, -1, 11, 0, 0, 7, 0, 0, 7, 0, -1, 16, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -1, 30, 0, 0, 10, 0, -1, 14, 0, 0, 11, 0, -1, 17, 0, 0, 13, 0, 0, 13, 0, 0, 15, 0, 0, 16, 0, 0, 18, 0, -1, 20, 0, 0, 21, 0, 0, 22, 0, -1, 24, 0, 0, 25, 0, 0, 26, 0, -1, 28, 0, 0, 29, 0, 0, 30, 0, 14, 1, 2, 2, 6, 3, 2, 8, 4, 2, 8, 5, 2, 8, 7, 2, 12, 9, 2, 10, 59], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 17, 17, 17, 17, 20, 24, 28], [-1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, -1, 3, -1, 1, -1, -1, 3, -1, 1, -1, -1, 3, -1, -1, 1, 6, 7, 8, 9, -1, 1, 1, 6, 7, 8, 9, 1, 1, 1], [0, 0, 9, 0, 4, 0, 10, 0, 4, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 0, 11, 5, 6, 7, 8, 0, 12, 13, 5, 6, 7, 8, 3, 3, 3]]