[1, ["ecpdLyjvZBwrvm+cedCcQy", "12Bmab5o5Pt5pD1nGzInXB", "e10un9MTFDsbnT7kxeSHbB", "4bn2lsqHlKYragVNfZBShy", "a2MjXRFdtLlYQ5ouAFv/+R", "5ctdTDq8NEopiwExXi7wmw", "fb7FiNEONNgpQ3H/rUPCQ/", "5fEa2MzSlHb59KtCV9tXG8", "5cO7kybDxGj4ipyMYdRYZB"], ["node", "root", "_N$file", "_spriteFrame", "_N$target", "data", "_parent", "_scrollView"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_bottom", "_left", "_right", "_enabled", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 12, 4, 5, 7, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint"], 1, 1, 2, 4, 5, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$target"], 2, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["8c902IZnG1CPaHsnEjErPue", ["node"], 3, 1]], [[4, 0, 1, 2], [0, 0, 6, 3, 4, 5, 7, 2], [4, 0, 1, 2, 2], [3, 0, 2, 3, 1, 4, 6, 8, 9, 10, 7], [0, 0, 6, 3, 4, 9, 5, 7, 2], [3, 0, 2, 3, 1, 5, 4, 8, 9, 10, 7], [2, 0, 3, 4, 5, 2], [2, 1, 0, 3, 4, 3], [2, 1, 0, 3, 4, 5, 3], [1, 0, 2, 3, 8, 4], [8, 0, 1], [6, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 2, 6, 3, 4, 9, 5, 3], [0, 0, 6, 8, 3, 4, 9, 5, 7, 2], [0, 0, 8, 3, 4, 9, 5, 10, 7, 2], [0, 0, 6, 8, 3, 4, 5, 7, 2], [5, 0, 2, 3, 4, 5, 6, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 7, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [2, 2, 0, 3, 4, 5, 3], [1, 0, 1, 4, 2, 3, 8, 6], [1, 0, 5, 1, 4, 3, 8, 6], [1, 7, 0, 5, 6, 1, 4, 2, 3, 8, 9], [1, 0, 6, 1, 8, 4], [1, 7, 0, 5, 6, 1, 2, 8, 7], [4, 1, 2, 1], [3, 0, 3, 7, 1, 5, 8, 9, 10, 6], [3, 0, 2, 1, 5, 4, 8, 9, 10, 6], [9, 0, 1, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 5], [12, 0, 1, 2, 3, 2], [13, 0, 1, 2, 3], [14, 0, 1]], [[11, "GameRule"], [12, "GameRule", false, [-5, -6, -7], [[10, -2], [34, -3], [9, 45, 1560, 720, -4]], [26, -1, 0], [5, 1560, 720]], [15, "content", [-9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [[20, false, 0, -8, [24], 25]], [2, "f71VAQHuBGg4tKPvsSX0uH", 1, 0], [4, 4294938437], [5, 1280, 2158], [0, 0.5, 1], [0, 249, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "New ScrollView", 1, [-23, -24], [[[8, 1, 0, -20, [29], 30], -21, [23, false, 13, 92.5, 92.5, 61.79699999999997, 52.20300000000003, 1280, 720, -22]], 4, 1, 4], [2, "42FqASZSFHG4EJyRHSDG97", 1, 0], [5, 1280, 606], [0, -4.796999999999969, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollBar", 512, 3, [-28], [[-25, [22, 37, 350.07654921020657, 108.79999999999998, 3.3306690738754696e-14, 237, -26], [7, 1, 0, -27, [28]]], 1, 4, 4], [2, "11L8s+sFBAcJG4+Sv7klc8", 1, 0], [5, 12, 497.19999999999993], [0, 0, 0.5], [535.5, -54.39999999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Title", 1, [-31, -32], [[8, 1, 0, -29, [35], 36], [25, false, 41, 232.5, 232.5, 61.40999999999997, 1560, -30]], [2, "ebUGdW9iRPzpJRo26w20RJ", 1, 0], [5, 1095, 108], [0, 244.59000000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "closeBtn", 5, [[6, 0, -33, [31], 32], [32, 1, -35, [[33, "8c902IZnG1CPaHsnEjErPue", "close", 1]], -34], [24, 33, 31.732999999999947, 28.048000000000002, -36]], [2, "ccMeYgypRIJpmtyJWYew38", 1, 0], [5, 41, 37], [491.16700000000003, 3.751999999999999, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [13, "BG", 150, 1, [[6, 0, -37, [0], 1], [9, 45, 40, 36, -38], [10, -39]], [2, "06lJux9wlO7Yk1pDgaTIfG", 1, 0], [4, 4278190080], [5, 1560, 720]], [14, "view", 3, [2], [[29, -40, [26]], [21, 45, 113.5, -0.5, 1280, 720, -41]], [2, "3fS/UQu3ZPA74EkY1y6D94", 1, 0], [4, 4294638330], [5, 1095, 493], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content1", 2, [[3, "Spider is a Solitaire game made popular by Microsoft Windows. It is played by 1 person only and uses 2 decks of cards. To fully understand how to play Spider Solitaire, we will first take a look at the playing field. The field is made up of 3 sections:\n\nThe Tableau is the section in which the game is played. Here, around half (54 cards) of the 104 cards in play are arranged in 10 columns. The top cards are always dealt face up.\n\nThe Stock contains the 50 cards that have not yet been dealt. Each column in the tableau gets a new card whenever you tap on this stock.\n\nThe Foundation will ultimately contain all 104 cards, sorted by colour and in order from King to Ace, arranged in 8 stacks.", 24, 35, false, 1, 2, -43, [2], 3]], [0, "dbfkP/mKhJ0bgCj+8MjTqE", -42], [5, 980, 450], [0, -324, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 2, [[27, "SPIDER SOLITAIRE", 45, false, false, 1, -45, [4], 5]], [0, "58IcA44+5D4LKAIf4Jc11i", -44], [4, **********], [5, 404.04, 56.7], [0, -70.027, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content", 2, [[3, "The aim of Spider Solitaire is to move all cards from the tableau to the foundation. For this purpose, you must arrange all cards in the tableau in descending order in the same suit, from King to Ace. Once you have completed a sequence, it will automatically be moved to the foundation and you can start on the next sequence and so on, until you have cleared the whole tableau.\n\nOur Spider Solitaire game has 3 levels: 1 colour (easy), 2 colours (more challenging), and 4 colours (extremely challenging, only for the real expert). Level 1 is played differently from the other two levels:", 24, 35, false, 1, 2, -47, [6], 7]], [0, "aalK35lQVCDohyBYqC5bcA", -46], [5, 980, 291], [0, -702, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "suit", 2, [[5, "1 suit", 25, 30, false, 1, 1, -49, [8], 9]], [0, "4e2+lS1DhO0ZbmJBOfGPbu", -48], [4, **********], [5, 65.02, 37.8], [0, -883.764, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content1", 2, [[3, "This is our beginner's level. It is played with just one suit: Spades! In this game, you do not need to take account of the colours when moving the cards. The most important rule of the Spider Solitaire game is that you can only place a card on top of another card that is 1 point higher in value.\n\nFor example, you can only place the 2 of Spades on the 3 of Spades (see illustration). It is also possible to move several cards at once, if they are all in ascending order with a one point difference. For example if the 7, 6, and 5 of Spades are on top of each other, then you can move all three at the same time to an open 8. Whenever you move a card that was face down, the previously hidden card will be turned up. Furthermore, it is important to know that you can place any random card in an empty column and that you go back one step using the undo button.", 24, 35, false, 1, 2, -51, [10], 11]], [0, "1af0MdsaVHirZqWXKft8R5", -50], [5, 980, 388], [0, -1125.562, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content1", 2, [[3, "This functions largely in the same way as playing with a single suit, except that you need to take the colours into account. When playing with 2 or 4 suits, you can simply move cards to other cards that are one point higher in value, irrespective of the colour. For example, a Jack of Clubs can be placed on a Queen of Hearts. However, complete sequences can only be cleared to the foundation if they belong to the same suit. So, it is a good idea to arrange the cards by colour as much as possible when starting the game.", 24, 35, false, 1, 2, -53, [12], 13]], [0, "2fTzAX5ShJvYBdikveDYq0", -52], [5, 980, 223], [0, -1538.725, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "suit", 2, [[5, "2 and 4 suits Spider Solitaire", 25, 30, false, 1, 1, -55, [14], 15]], [0, "09wPHXqF1ADb1R12W3E3F4", -54], [4, **********], [5, 354.05, 37.8], [0, -1373.277, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "suit", 2, [[5, "Power-up feature", 25, 30, false, 1, 1, -57, [16], 17]], [0, "efKp8cfnBDO4fVGpP9JvC1", -56], [4, **********], [5, 215.02, 37.8], [0, -1885.969, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content1", 2, [[3, "You must deal new cards when you can no longer move any cards. You can only do this if there is at least 1 card in each column. So, fill all empty spots before dealing.\n\n", 24, 35, false, 1, 2, -59, [18], 19]], [0, "8f81aVNPJBO5/eiBf0KCIk", -58], [5, 980, 128], [0, -1813, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content1", 2, [[3, "A power-up that could move any specific card to the bottom of its column, so that it won't be blocked. Once activated, you can utilize it for a duration of 15 seconds. Following the conclusion of the 15-second interval, a cooldown period of 60 seconds will ensue before the power-up becomes accessible for use once more.", 24, 35, false, 1, 2, -61, [20], 21]], [0, "a8L/yFKPBN4KabhWQAO1AT", -60], [5, 980, 131.2], [0, -2004.301, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "suit", 2, [[5, "Dealing new cards from the stock", 25, 30, false, 1, 1, -63, [22], 23]], [0, "e3AiLow9lEQrF82TFe9D4r", -62], [4, **********], [5, 413.57, 37.8], [0, -1696.766, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 5, [[28, "HOW TO PLAY", 50, false, 1, 1, -65, [33], 34]], [0, "92mRz+++pA4ooEI1G+SrcP", -64], [5, 361.5, 50.4], [0, 2.903, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bar", 512, 4, [-66], [2, "6dBEEV2e1KVYT2mFzr25zF", 1, 0], [5, 10, 64.9379932356257], [0, 0, 0]], [7, 1, 0, 21, [27]], [30, 1, 4, 22], [31, false, 0.75, 0.23, null, 3, 2, 23]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 3, 0, -3, 5, 0, 0, 2, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 14, 0, -7, 15, 0, -8, 16, 0, -9, 17, 0, -10, 18, 0, -11, 19, 0, 0, 3, 0, -2, 24, 0, 0, 3, 0, -1, 8, 0, -2, 4, 0, -1, 23, 0, 0, 4, 0, 0, 4, 0, -1, 21, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, -2, 20, 0, 0, 6, 0, 4, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 1, 9, 0, 0, 9, 0, 1, 10, 0, 0, 10, 0, 1, 11, 0, 0, 11, 0, 1, 12, 0, 0, 12, 0, 1, 13, 0, 0, 13, 0, 1, 14, 0, 0, 14, 0, 1, 15, 0, 0, 15, 0, 1, 16, 0, 0, 16, 0, 1, 17, 0, 0, 17, 0, 1, 18, 0, 0, 18, 0, 1, 19, 0, 0, 19, 0, 1, 20, 0, 0, 20, 0, -1, 22, 0, 5, 1, 2, 6, 8, 23, 7, 24, 66], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22], [-1, 3, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 3, -1, -1, -1, -1, 3, -1, 3, -1, 2, -1, 3, 3], [0, 3, 0, 1, 0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 4, 0, 0, 0, 0, 5, 0, 6, 0, 2, 0, 7, 8]]