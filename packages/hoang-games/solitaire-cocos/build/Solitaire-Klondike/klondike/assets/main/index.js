window.__require=function t(e,n,o){function i(a,s){if(!n[a]){if(!e[a]){var c=a.split("/");if(c=c[c.length-1],!e[c]){var u="function"==typeof __require&&__require;if(!s&&u)return u(c,!0);if(r)return r(c,!0);throw new Error("Cannot find module '"+a+"'")}a=c}var l=n[a]={exports:{}};e[a][0].call(l.exports,function(t){return i(e[a][1][t]||t)},l,l.exports,t,e,n,o)}return n[a].exports}for(var r="function"==typeof __require&&__require,a=0;a<o.length;a++)i(o[a]);return i}({ArrangeRenderOrder:[function(t,e,n){"use strict";cc._RF.push(e,"6c9c6zBLjtPqo3gOjcJ3anm","ArrangeRenderOrder"),Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function t(){this._nodes=new Map}return t.prototype.addNode=function(t,e,n){void 0===n&&(n=0);var o={node:t,groupId:e,orderId:n};if(this._nodes.has(e)){var i=this._nodes.get(e);i.push(o),i.sort(function(t,e){return t.orderId-e.orderId})}else this._nodes.set(e,[o])},t.prototype.sort=function(){for(var t=Array.from(this._nodes.keys()).sort(function(t,e){return t-e}),e=0,n=0;n<t.length;n++){var o=t[n];this._nodes.get(o).forEach(function(t){t.node.setSiblingIndex(e++)})}},t}();n.default=o,cc._RF.pop()},{}],AttachedNode:[function(t,e,n){"use strict";cc._RF.push(e,"4220a1/3gpA5YB+xKXmf29T","AttachedNode");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("./Utils"),s=cc._decorator,c=s.ccclass,u=s.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.attachTo=null,e._originalParent=null,e._originalPosition=null,e._originalPolarVector=null,e._isAttaching=!1,e}return i(e,t),e.prototype.lateUpdate=function(){if(this._isAttaching){var t=this._originalParent.getPosition(),e=a.default.polarToCartesian(this._originalPolarVector);this.node.setPosition(t.add(cc.v2(e.x,e.y)))}},e.prototype.attach=function(){var t=this;this._isAttaching=!0,this._originalPosition=this.node.getPosition(),this._originalParent=this.node.parent,this._originalPolarVector=a.default.cartesianToPolar(cc.v2(this.node.position.x,this.node.position.y)),this.node.parent=this.attachTo,this._originalParent.on("detach",function(){t._detach()})},e.prototype._detach=function(){this._isAttaching=!1,this.node.parent=this._originalParent,this.node.setPosition(this._originalPosition)},r([u(cc.Node)],e.prototype,"attachTo",void 0),r([c],e)}(cc.Component);n.default=l,cc._RF.pop()},{"./Utils":"Utils"}],AudioPlayerData:[function(t,e,n){"use strict";cc._RF.push(e,"18f9eo9rmlAeK5N+RcJl33l","AudioPlayerData"),Object.defineProperty(n,"__esModule",{value:!0}),n.AudioPlayerType=void 0,function(t){t[t.None=0]="None",t[t.Native=1]="Native",t[t.Web=2]="Web"}(n.AudioPlayerType||(n.AudioPlayerType={})),cc._RF.pop()},{}],AudioResources:[function(t,e,n){"use strict";cc._RF.push(e,"efcf9jSqOtBppuh4AV358UT","AudioResources"),Object.defineProperty(n,"__esModule",{value:!0}),n.RawConfigs=n.MusicIds=n.SoundNames=n.AudioPacks=n.InGamePacks=n.PreLoadPacks=n.AudioFormat=void 0,n.AudioFormat=["mp3"],n.PreLoadPacks=[],n.InGamePacks=["spk0"],n.AudioPacks=["spk0"],n.SoundNames=["CLICK_BTN","CARD","WIN","CARD_CLICK","CARD_FLIP","NO_HINT","MOVE_WIN","DEAL_CARD","NONE"],n.MusicIds=[],n.RawConfigs=[{p:"spk0",s:"click-btn"},{p:"spk0",s:"card"},{p:"spk0",s:"win"},{p:"spk0",s:"card_click"},{p:"spk0",s:"card_flip"},{p:"spk0",s:"no_hint"},{p:"spk0",s:"move_win"},{p:"spk0",s:"deal_card",v:.6}],cc._RF.pop()},{}],AudioSource:[function(t,e,n){"use strict";cc._RF.push(e,"1d527T5UFlBqbl5PGjW4NEk","AudioSource"),Object.defineProperty(n,"__esModule",{value:!0}),n.AudioSource=void 0;var o=t("./AudioPlayerData"),i=t("howler"),r=function(){function t(t,e){this.audioId="NONE",this.playId=-1,this.info=null,this.clip=null,this.fadingHandler=null,this.endCallback=null,this.isMusic=!1,this.isFading=!1,this.isAutoStop=!1,this.isLockVolume=!1,this.player=null,this._type=t,this.clip=e.clip,this.clip.addRef(),this.player=e.howl}return t.prototype.reset=function(){-1!==this.playId&&(--this.info.numberOfPlaying,this.playId=-1,this.audioId="NONE",this.isMusic=!1,this.isAutoStop=!1,this.info=null)},t.prototype.howl=function(){return this.player},t.prototype.isWebAudio=function(){return this._type===o.AudioPlayerType.Web},t.prototype.cleanUp=function(){this.clip.decRef()},t.prototype.clearFadeInfo=function(){this.fadingHandler=null,this.isFading=!1},t.prototype.initHowl=function(t){this.player=new i.Howl(t)},t}();n.AudioSource=r,cc._RF.pop()},{"./AudioPlayerData":"AudioPlayerData",howler:1}],BaseAudioPlayer:[function(t,e,n){"use strict";cc._RF.push(e,"38c07HXNJVMva/DX83NeK0Y","BaseAudioPlayer");var o=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},i=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var r=t("./AudioPlayerData"),a=cc._decorator,s=(a.ccclass,a.property,function(){function t(t,e){this._wasCleanUp=!1,this._ctx=t,this._shareVars=e}return t.prototype.cleanUp=function(){this._wasCleanUp=!0},t.prototype.onVerifyResources=function(){return o(this,void 0,Promise,function(){return i(this,function(){return[2,!0]})})},t.prototype.onLoadAudioComplete=function(){return o(this,void 0,Promise,function(){return i(this,function(){return[2,!0]})})},t.prototype.onPause=function(t){t?this._onInterrupt():this._onGameResume()},t.prototype.isWebPlayer=function(){return this._type===r.AudioPlayerType.Web},t.prototype.getType=function(){return this._type},t.prototype._isVolumeValid=function(t){return!!(t&&"AUTO"===t||t>=0&&t<=1)},t}());n.default=s,cc._RF.pop()},{"./AudioPlayerData":"AudioPlayerData"}],BoardScore:[function(t,e,n){"use strict";cc._RF.push(e,"57ac4SQLsZIrIQBu5aHJob8","BoardScore");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../events/Events"),s=t("../../utils/SaveGame"),c=cc._decorator,u=c.ccclass,l=c.property,d=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.highScore=null,e.score=null,e.timer=null,e._isshowAnimPopup=!1,e._highScore=0,e}return i(e,t),e.prototype.onLoad=function(){s.default.init(),a.Events.On(a.EventType.NEW_GAME,this.reset.bind(this))},e.prototype.setTimer=function(t){this.timer.string=""+t},e.prototype.setScore=function(t,e){var n=s.default.getInstance().getData(e);(!n||n<t)&&(this.highScore.string=""+t,s.default.getInstance().setData(e,t)),this.score.string=""+t},e.prototype.reset=function(t){var e=s.default.getInstance().getData(t);e?(this._highScore=e,this.highScore.string=""+this._highScore):(this._highScore=0,this.highScore.string="0")},r([l(cc.Label)],e.prototype,"highScore",void 0),r([l(cc.Label)],e.prototype,"score",void 0),r([l(cc.Label)],e.prototype,"timer",void 0),r([u],e)}(cc.Component);n.default=d,cc._RF.pop()},{"../../events/Events":"Events","../../utils/SaveGame":"SaveGame"}],CardSpider:[function(t,e,n){"use strict";cc._RF.push(e,"9d57alANGVJNYoLs+tGedfG","CardSpider");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("../events/Events"),u=t("../sounds/SoundManager"),l=cc._decorator,d=l.ccclass,p=l.property,h=["spades","hearts","clubs","diamonds"],f=["jack","queen","king"],_=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.bgNode=null,e.fgNode=null,e.centerContainer=null,e.rightContainer=null,e.bottomContainer=null,e.point=0,e.suit=0,e.cardMoveContainer=null,e.isFront=!1,e.isInWin=!1,e.positionTemp=cc.Vec3.ZERO,e.zIndexTemp=0,e.touchStartCallback=null,e.touchEndCallback=null,e.drawCallback=null,e.doubleClickCallback=null,e.popUndo=null,e.isPower=null,e.lastClickTime=0,e.currentClickTime=0,e.isMoving=!1,e.isDisable=!1,e.cardId="",e.isUnder=!1,e.hasMoved=!1,e.tmpCardzIndex=1,e}var n;return i(e,t),n=e,e.prototype.start=function(){this.openTouch()},e.prototype.onEnable=function(){this.turnToBack()},e.prototype.onDestroy=function(){this.closeTouch()},e.prototype.setData=function(t,e){this.cardMoveContainer=e;var n=t.split("_"),o=n[0],i=n[1];this.suit=h.indexOf(o),this.point=+i,this.updateUI()},e.prototype.updateUI=function(){var t,e=this,n=h[this.suit];t=this.point>10?f[this.point-10-1]:1==this.point?"ace":this.point.toString().padStart(2,"0"),cc.resources.load("Cards/"+n+"_"+t,cc.SpriteFrame,function(t,n){t?cc.error("url error "+t):e.fgNode.getComponent(cc.Sprite).spriteFrame=n})},e.prototype.openTouch=function(){this.node.on(cc.Node.EventType.TOUCH_START,this.touchStart.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_MOVE,this.touchsMove.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_END,this.touchEnd.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_CANCEL,this.touchEnd.bind(this),this)},e.prototype.closeTouch=function(){this.node.off(cc.Node.EventType.TOUCH_START,this.touchStart.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_MOVE,this.touchsMove.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_END,this.touchEnd.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_CANCEL,this.touchEnd.bind(this),this)},Object.defineProperty(e.prototype,"canTouch",{get:function(){return!(!this.isFront||this.rightContainer.childrenCount>0||this.isUnder)},enumerable:!1,configurable:!0}),e.prototype.touchStart=function(t){this.isInWin||(t.stopPropagation(),this.lastClickTime=this.currentClickTime,this.currentClickTime=Date.now(),this.checkSequences()&&(this.isMoving||this.canTouch&&(this.positionTemp=this.node.position,this.zIndexTemp=this.zIndex,this.zIndex=100,this.touchStartCallback&&this.touchStartCallback(this.node))))},e.prototype.checkSequences=function(){if(this.bottomContainer.childrenCount>0){var t=this.bottomContainer.children[0].getComponent(n);return this.suit===t.suit&&this.point-1===t.point&&t.checkSequences()}return!0},e.prototype.touchsMove=function(t){if(!this.isInWin){t.stopPropagation();var e=t.getDelta();if(this.isPower()&&this.isDisable&&this.canTouch)this.zIndex=100,this.tmpCardzIndex=this.node.getChildByName("Fg").zIndex,this.node.getChildByName("Fg").position=cc.v3(e.add(cc.v2(this.node.getChildByName("Fg").position)));else{if(!this.checkSequences())return;if(!this.canTouch)return;this.hasMoved||u.default.inst.playEffect("CARD"),this.hasMoved=!0,this.node.position=cc.v3(e.add(cc.v2(this.node.position)))}}},e.prototype.touchEnd=function(t){if(!this.isInWin)if(t.stopPropagation(),this.isPower()&&this.isDisable&&this.canTouch){this.zIndex=this.zIndexTemp,this.node.getChildByName("Fg").position=cc.v3(0);var e=this.bottomContainer.children[0],o=this.node.parent.parent;o.getComponent(n)?(this.setEnable(),this.node.parent=cc.Canvas.instance.node,o.getComponent(n).setBottomChild(e,!0),e.getComponent(n).setBottomChild(this.node,!0),c.Events.Emit(c.EventType.POWER_ACTION,{node:this.node,parent:o,child:e,from:"power"})):(this.setEnable(),this.node.parent=cc.Canvas.instance.node.getChildByName("Gameplay").getChildByName("Container"),e.parent=cc.Canvas.instance.node.getChildByName("Gameplay").getChildByName("Container"),e.position=this.node.position,e.getComponent(n).setBottomChild(this.node,!0),c.Events.Emit(c.EventType.POWER_ACTION,{node:this.node,parent:null,child:e,from:"power"}))}else if(this.checkSequences())return this.hasMoved?(this.zIndex=this.zIndexTemp,this.touchEndCallback&&(this.touchEndCallback(this.node),u.default.inst.playing("CARD")&&u.default.inst.fade("CARD","AUTO",0,.1,!0),u.default.inst.playEffect("CARD_FLIP")),void(this.hasMoved=!1)):void(this.isMoving||(this.canTouch?(u.default.inst.playEffect("CARD_CLICK"),this.currentClickTime-this.lastClickTime<=600&&(this.zIndex=this.zIndexTemp,this.doubleClickCallback)?this.doubleClickCallback(this.node):(this.zIndex=this.zIndexTemp,this.popUndo())):this.drawCallback&&this.bottomContainer.childrenCount<=0&&this.rightContainer.childrenCount<=0&&!this.isUnder&&(this.zIndex=this.zIndexTemp,this.drawCallback(this.node))))},e.prototype.setEnable=function(){this.isDisable=!1,this.node.getChildByName("Fg").color=cc.Color.WHITE},e.prototype.setDisable=function(){this.isDisable=!0,this.node.getChildByName("Fg").color=cc.Color.fromHEX(this.node.getChildByName("Fg").color,"#535353")},e.prototype.turnToFront=function(){this.isFront||(this.bottomContainer.y=-25,this.isFront=!0,this.bgNode.opacity=0)},e.prototype.turnToBack=function(){this.bottomContainer.y=-8,this.isFront=!1,this.bgNode.opacity=255},e.prototype.reset=function(){var t=this;cc.tween(this.node).to(0,{position:this.positionTemp}).call(function(){t.zIndex=t.zIndexTemp}).start()},e.prototype.resetPowerUp=function(){cc.warn("reset",this.point),this.zIndex=this.zIndexTemp,this.node.getChildByName("Fg").position=cc.v3(0)},e.prototype.setTouchCallback=function(t,e,n,o,i,r){this.touchStartCallback=t,this.drawCallback=o,this.doubleClickCallback=n,this.touchEndCallback=e,this.popUndo=i,this.isPower=r},e.prototype.getBottomCard=function(){return this.bottomContainer.childrenCount>0?this.bottomContainer.children[0].getComponent(n).getBottomCard():this.node},e.prototype.getRightCard=function(){return this.rightContainer.childrenCount>0?this.rightContainer.children[0].getComponent(n).getRightCard():this.node},e.prototype.getParentCount=function(t,e){void 0===t&&(t=!1),void 0===e&&(e=0);var o=this.node.parent.parent.getComponent(n);return o&&o.isFront===!t&&o.suit==this.suit?(e++,o.getParentCount(t,e)):e},e.prototype.getParentEnableCount=function(t,e){void 0===t&&(t=!1),void 0===e&&(e=0);var o=this.node.parent.parent.getComponent(n);return o&&o.isFront===!t&&o.suit==this.suit&&!o.isDisable?(e++,o.getParentCount(t,e)):e},e.prototype.getBottomCount=function(t){var e;void 0===t&&(t=0);var o=null===(e=this.bottomContainer.children[0])||void 0===e?void 0:e.getComponent(n);return o?(t++,o.getBottomCount(t)):t},e.prototype.setBottomChild=function(t,e){if(void 0===e&&(e=!0),this.bottomContainer.childrenCount>0)return this.bottomContainer.children[0].getComponent(n).setBottomChild(t,e);if(t==this.node)return this.reset(),!1;if(e)return t.parent=this.bottomContainer,t.position=cc.v3(0,0),this.calcHeight(),!0;var o=t.getComponent(n);return o.point==this.point-1?(t.parent=this.bottomContainer,t.position=cc.v3(0,0),this.calcHeight(),!0):(o.reset(),!1)},e.prototype.calcHeight=function(){var t=this.getBottomCount();t>32?this.setHeight(-13):t>29?this.setHeight(-16):t>26?this.setHeight(-18):t>23?this.setHeight(-20):t>20?this.setHeight(-22):t>18?this.setHeight(-23):t>15?this.setHeight(-24):this.setHeight(-25)},e.prototype.setHeight=function(t){if(this.isFront&&(this.bottomContainer.y=t),this.bottomContainer.childrenCount>0)return this.bottomContainer.children[0].getComponent(n).setHeight(t)},e.prototype.isBottomChild=function(t){return this.bottomContainer.childrenCount>0?this.bottomContainer.children[0].getComponent(n).isBottomChild(t):t!==this.node&&t.getComponent(n).point==this.point-1},e.prototype.turnLastBottomChildToFront=function(){this.bottomContainer.childrenCount>0?this.bottomContainer.children[0].getComponent(n).turnLastBottomChildToFront():this.turnToFront()},e.prototype.setRightChild=function(t){if(!(this.rightContainer.childrenCount>0))return t.parent=this.rightContainer,t.position=cc.v3(0,0),!0;this.rightContainer.children[0].getComponent(n).setRightChild(t)},e.prototype.setCenterChild=function(t,e){return a(this,void 0,Promise,function(){var o,i=this;return s(this,function(r){switch(r.label){case 0:return this.centerContainer.childrenCount>0?[2,this.centerContainer.children[0].getComponent(n).setCenterChild(t)]:[3,1];case 1:return t!=this.node?[3,2]:(this.reset(),[2,!1]);case 2:return(o=t.getComponent(n)).point!=this.point+1||o.suit!=this.suit?[3,6]:e?[3,3]:(t.parent=this.centerContainer,t.position=cc.v3(0,0),[3,5]);case 3:return[4,new Promise(function(e){cc.tween(t).to(.1,{position:t.parent.convertToNodeSpaceAR(i.centerContainer.parent.convertToWorldSpaceAR(i.centerContainer.position))}).call(function(){t.parent=i.centerContainer,t.position=cc.v3(0,0),e(!0)})})];case 4:r.sent(),r.label=5;case 5:return[2,!0];case 6:return o.reset(),[2,!1]}})})},e.prototype.isCenterChild=function(t){if(this.centerContainer.childrenCount>0)return this.centerContainer.children[0].getComponent(n).isCenterChild(t);if(t==this.node)return!1;var e=t.getComponent(n);return e.point==this.point+1&&e.suit==this.suit},Object.defineProperty(e.prototype,"zIndex",{get:function(){var t=this.node.parent.parent.getComponent(n);return t?t.zIndex:this.node.zIndex},set:function(t){var e=this.node.parent.parent.getComponent(n);e?e.zIndex=t:this.node.zIndex=t},enumerable:!1,configurable:!0}),e.prototype.getBottomWorldPosition=function(){var t=cc.Vec3.ZERO;return this.bottomContainer.childrenCount>0?t=this.bottomContainer.children[0].getComponent(n).getBottomWorldPosition():this.bottomContainer.parent.convertToWorldSpaceAR(this.bottomContainer.position,t),t},e.prototype.getCenterWorldPosition=function(){var t=cc.Vec3.ZERO;return this.centerContainer.childrenCount>0?t=this.centerContainer.children[0].getComponent(n).getCenterWorldPosition():this.centerContainer.parent.convertToWorldSpaceAR(this.centerContainer.position,t),t},e.prototype.moveToWorldPosition=function(t,e){var n=cc.Vec2.ZERO;this.node.parent.convertToNodeSpaceAR(t,n),cc.tween(this.node).to(.1,{position:cc.v3(n)}).call(function(){e&&e()}).start()},e.prototype.shake=function(){var t=this;this.isMoving=!0;var e=this.node.position.x,o=this.node.position.y;cc.tween(this.node).to(.018,{position:cc.v3(e+5,o+7)}).to(.018,{position:cc.v3(e-6,o+7)}).to(.018,{position:cc.v3(e-13,o+3)}).to(.018,{position:cc.v3(e+3,o-6)}).to(.018,{position:cc.v3(e-5,o+5)}).to(.018,{position:cc.v3(e+2,o-8)}).to(.018,{position:cc.v3(e-8,o-10)}).to(.018,{position:cc.v3(e+3,o+10)}).to(.018,{position:cc.v3(e+0,o+0)}).call(function(){t.node.parent.parent.getComponent(n)&&(t.node.position=cc.v3(0,0)),t.isMoving=!1}).start()},e.prototype.shrink=function(){if(this.bottomContainer.childrenCount>0)return this.bottomContainer.y=0,this.bottomContainer.children[0].getComponent(n).shrink()},r([p(cc.Node)],e.prototype,"bgNode",void 0),r([p(cc.Node)],e.prototype,"fgNode",void 0),r([p(cc.Node)],e.prototype,"centerContainer",void 0),r([p(cc.Node)],e.prototype,"rightContainer",void 0),r([p(cc.Node)],e.prototype,"bottomContainer",void 0),n=r([d],e)}(cc.Component);n.default=_,cc._RF.pop()},{"../events/Events":"Events","../sounds/SoundManager":"SoundManager"}],Card:[function(t,e,n){"use strict";cc._RF.push(e,"b1db8u/phhEVp+V0VG0hax1","Card");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("../sounds/SoundManager"),u=cc._decorator,l=u.ccclass,d=u.property,p=["diamonds","clubs","hearts","spades"],h=["jack","queen","king"],f=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.bgNode=null,e.fgNode=null,e.centerContainer=null,e.rightContainer=null,e.bottomContainer=null,e.point=0,e.suit=0,e.cardMoveContainer=null,e.isFront=!1,e.isInWin=!1,e.positionTemp=cc.Vec3.ZERO,e.zIndexTemp=0,e.touchStartCallback=null,e.touchEndCallback=null,e.drawCallback=null,e.doubleClickCallback=null,e.popUndo=null,e.lastClickTime=0,e.currentClickTime=0,e.isMoving=!1,e.hasMoved=!1,e.isUnder=!1,e}var n;return i(e,t),n=e,e.prototype.start=function(){this.openTouch()},e.prototype.onEnable=function(){this.turnToBack()},e.prototype.onDestroy=function(){this.closeTouch()},e.prototype.setData=function(t,e){this.cardMoveContainer=e,this.suit=Math.floor((t-1)/13),this.point=(t-13*this.suit)%13,0===this.point&&(this.point=13),this.updateUI()},e.prototype.updateUI=function(){var t,e=this,n=p[this.suit];t=this.point>10?h[this.point-10-1]:1==this.point?"ace":this.point.toString().padStart(2,"0"),cc.resources.load("Cards/"+n+"_"+t,cc.SpriteFrame,function(t,n){t?cc.error("url error "+t):e.fgNode.getComponent(cc.Sprite).spriteFrame=n})},e.prototype.openTouch=function(){this.node.on(cc.Node.EventType.TOUCH_START,this.touchStart.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_MOVE,this.touchsMove.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_END,this.touchEnd.bind(this),this),this.node.on(cc.Node.EventType.TOUCH_CANCEL,this.touchEnd.bind(this),this)},e.prototype.closeTouch=function(){this.node.off(cc.Node.EventType.TOUCH_START,this.touchStart.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_MOVE,this.touchsMove.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_END,this.touchEnd.bind(this),this),this.node.off(cc.Node.EventType.TOUCH_CANCEL,this.touchEnd.bind(this),this)},Object.defineProperty(e.prototype,"canTouch",{get:function(){return!(!this.isFront||this.rightContainer.childrenCount>0||this.isUnder)},enumerable:!1,configurable:!0}),e.prototype.touchStart=function(t){t.stopPropagation(),this.lastClickTime=this.currentClickTime,this.currentClickTime=Date.now(),this.isMoving||this.canTouch&&(this.positionTemp=this.node.position,this.zIndexTemp=this.zIndex,this.zIndex=100,this.touchStartCallback&&this.touchStartCallback(this.node))},e.prototype.touchsMove=function(t){if(t.stopPropagation(),this.canTouch){this.hasMoved||c.default.inst.playEffect("CARD"),this.hasMoved=!0;var e=t.getDelta();this.node.position=cc.v3(e.add(cc.v2(this.node.position)))}},e.prototype.touchEnd=function(t){if(t.stopPropagation(),this.hasMoved)return this.zIndex=this.zIndexTemp,this.touchEndCallback&&(c.default.inst.playing("CARD")&&c.default.inst.fade("CARD","AUTO",0,.1,!0),c.default.inst.playEffect("CARD_FLIP"),this.touchEndCallback(this.node)),void(this.hasMoved=!1);this.isMoving||(this.canTouch?(c.default.inst.playEffect("CARD_CLICK"),this.currentClickTime-this.lastClickTime<=600&&(this.zIndex=this.zIndexTemp,this.doubleClickCallback)?this.doubleClickCallback(this.node):(this.zIndex=this.zIndexTemp,this.popUndo())):this.drawCallback&&this.bottomContainer.childrenCount<=0&&this.rightContainer.childrenCount<=0&&!this.isUnder&&(this.zIndex=this.zIndexTemp,cc.log("single click"),this.drawCallback(this.node)))},e.prototype.turnToFront=function(){this.isFront||(this.isFront=!0,this.bgNode.opacity=0)},e.prototype.turnToBack=function(){this.isFront=!1,this.bgNode.opacity=255},e.prototype.reset=function(){var t=this;cc.warn("reset",this.point),cc.tween(this.node).to(0,{position:this.positionTemp}).call(function(){t.zIndex=t.zIndexTemp}).start()},e.prototype.setTouchCallback=function(t,e,n,o,i){this.touchStartCallback=t,this.drawCallback=n,this.touchEndCallback=e,this.doubleClickCallback=o,this.popUndo=i},e.prototype.getBottomCard=function(){return this.bottomContainer.childrenCount>0?this.bottomContainer.children[0].getComponent(n).getBottomCard():this.node},e.prototype.getRightCard=function(){return this.rightContainer.childrenCount>0?this.rightContainer.children[0].getComponent(n).getRightCard():this.node},e.prototype.getParentCount=function(t,e){void 0===t&&(t=!1),void 0===e&&(e=0);var o=this.node.parent.parent.getComponent(n);return o&&o.isFront===!t?(e++,o.getParentCount(t,e)):e},e.prototype.setBottomChild=function(t,e){if(void 0===e&&(e=!0),this.bottomContainer.childrenCount>0)return this.bottomContainer.children[0].getComponent(n).setBottomChild(t,e);if(t==this.node)return this.reset(),!1;if(e)return t.parent=this.bottomContainer,t.position=cc.v3(0,0),!0;var o=t.getComponent(n),i=Math.abs(o.suit-this.suit);return o.point!=this.point-1||1!=i&&3!=i?(o.reset(),!1):(t.parent=this.bottomContainer,t.position=cc.v3(0,0),!0)},e.prototype.isBottomChild=function(t){if(this.bottomContainer.childrenCount>0)return this.bottomContainer.children[0].getComponent(n).isBottomChild(t);if(t===this.node)return!1;var e=t.getComponent(n),o=Math.abs(e.suit-this.suit);return e.point==this.point-1&&(1==o||3==o)},e.prototype.turnLastBottomChildToFront=function(){this.bottomContainer.childrenCount>0?this.bottomContainer.children[0].getComponent(n).turnLastBottomChildToFront():this.turnToFront()},e.prototype.setRightChild=function(t){if(!(this.rightContainer.childrenCount>0))return t.parent=this.rightContainer,t.position=cc.v3(0,0),!0;this.rightContainer.children[0].getComponent(n).setRightChild(t)},e.prototype.setCenterChild=function(t,e){return a(this,void 0,Promise,function(){var o,i=this;return s(this,function(r){switch(r.label){case 0:return this.centerContainer.childrenCount>0?[2,this.centerContainer.children[0].getComponent(n).setCenterChild(t)]:[3,1];case 1:return t!=this.node?[3,2]:(this.reset(),[2,!1]);case 2:return(o=t.getComponent(n)).point!=this.point+1||o.suit!=this.suit?[3,6]:e?[3,3]:(t.parent=this.centerContainer,t.position=cc.v3(0,0),[3,5]);case 3:return[4,new Promise(function(e){cc.tween(t).to(.1,{position:t.parent.convertToNodeSpaceAR(i.centerContainer.parent.convertToWorldSpaceAR(i.centerContainer.position))}).call(function(){t.parent=i.centerContainer,t.position=cc.v3(0,0),e(!0)})})];case 4:r.sent(),r.label=5;case 5:return[2,!0];case 6:return o.reset(),[2,!1]}})})},e.prototype.isCenterChild=function(t){if(this.centerContainer.childrenCount>0)return this.centerContainer.children[0].getComponent(n).isCenterChild(t);if(t==this.node)return!1;var e=t.getComponent(n);return e.point==this.point+1&&e.suit==this.suit},Object.defineProperty(e.prototype,"zIndex",{get:function(){var t=this.node.parent.parent.getComponent(n);return t?t.zIndex:this.node.zIndex},set:function(t){var e=this.node.parent.parent.getComponent(n);e?e.zIndex=t:this.node.zIndex=t},enumerable:!1,configurable:!0}),e.prototype.getBottomWorldPosition=function(){var t=cc.Vec3.ZERO;return this.bottomContainer.childrenCount>0?t=this.bottomContainer.children[0].getComponent(n).getBottomWorldPosition():this.bottomContainer.parent.convertToWorldSpaceAR(this.bottomContainer.position,t),t},e.prototype.getCenterWorldPosition=function(){var t=cc.Vec3.ZERO;return this.centerContainer.childrenCount>0?t=this.centerContainer.children[0].getComponent(n).getCenterWorldPosition():this.centerContainer.parent.convertToWorldSpaceAR(this.centerContainer.position,t),t},e.prototype.moveToWorldPosition=function(t,e){cc.warn("moveToWorld::Point::",this.node.getComponent(n).point);var o=cc.Vec2.ZERO;this.node.parent.convertToNodeSpaceAR(t,o),cc.tween(this.node).to(.1,{position:cc.v3(o)}).call(function(){e&&e()}).start()},e.prototype.shake=function(){var t=this;this.isMoving=!0;var e=this.node.position.x,o=this.node.position.y;cc.tween(this.node).to(.018,{position:cc.v3(e+5,o+7)}).to(.018,{position:cc.v3(e-6,o+7)}).to(.018,{position:cc.v3(e-13,o+3)}).to(.018,{position:cc.v3(e+3,o-6)}).to(.018,{position:cc.v3(e-5,o+5)}).to(.018,{position:cc.v3(e+2,o-8)}).to(.018,{position:cc.v3(e-8,o-10)}).to(.018,{position:cc.v3(e+3,o+10)}).to(.018,{position:cc.v3(e+0,o+0)}).call(function(){t.node.parent.parent.getComponent(n)&&(t.node.position=cc.v3(0,0)),t.isMoving=!1}).start()},r([d(cc.Node)],e.prototype,"bgNode",void 0),r([d(cc.Node)],e.prototype,"fgNode",void 0),r([d(cc.Node)],e.prototype,"centerContainer",void 0),r([d(cc.Node)],e.prototype,"rightContainer",void 0),r([d(cc.Node)],e.prototype,"bottomContainer",void 0),n=r([l],e)}(cc.Component);n.default=f,cc._RF.pop()},{"../sounds/SoundManager":"SoundManager"}],1:[function(t,e,n){(function(t){(function(){"use strict";var e=function(){this.init()};e.prototype={init:function(){var t=this||o;return t._counter=1e3,t._html5AudioPool=[],t.html5PoolSize=10,t._codecs={},t._howls=[],t._muted=!1,t._volume=1,t._canPlayEvent="canplaythrough",t._navigator="undefined"!=typeof window&&window.navigator?window.navigator:null,t.masterGain=null,t.noAudio=!1,t.usingWebAudio=!0,t.autoSuspend=!0,t.ctx=null,t.autoUnlock=!0,t._setup(),t},volume:function(t){var e=this||o;if(t=parseFloat(t),e.ctx||d(),void 0!==t&&t>=0&&t<=1){if(e._volume=t,e._muted)return e;e.usingWebAudio&&e.masterGain.gain.setValueAtTime(t,o.ctx.currentTime);for(var n=0;n<e._howls.length;n++)if(!e._howls[n]._webAudio)for(var i=e._howls[n]._getSoundIds(),r=0;r<i.length;r++){var a=e._howls[n]._soundById(i[r]);a&&a._node&&(a._node.volume=a._volume*t)}return e}return e._volume},mute:function(t){var e=this||o;e.ctx||d(),e._muted=t,e.usingWebAudio&&e.masterGain.gain.setValueAtTime(t?0:e._volume,o.ctx.currentTime);for(var n=0;n<e._howls.length;n++)if(!e._howls[n]._webAudio)for(var i=e._howls[n]._getSoundIds(),r=0;r<i.length;r++){var a=e._howls[n]._soundById(i[r]);a&&a._node&&(a._node.muted=!!t||a._muted)}return e},stop:function(){for(var t=this||o,e=0;e<t._howls.length;e++)t._howls[e].stop();return t},unload:function(){for(var t=this||o,e=t._howls.length-1;e>=0;e--)t._howls[e].unload();return t.usingWebAudio&&t.ctx&&void 0!==t.ctx.close&&(t.ctx.close(),t.ctx=null,d()),t},codecs:function(t){return(this||o)._codecs[t.replace(/^x-/,"")]},_setup:function(){var t=this||o;if(t.state=t.ctx&&t.ctx.state||"suspended",t._autoSuspend(),!t.usingWebAudio)if("undefined"!=typeof Audio)try{void 0===(new Audio).oncanplaythrough&&(t._canPlayEvent="canplay")}catch(e){t.noAudio=!0}else t.noAudio=!0;try{(new Audio).muted&&(t.noAudio=!0)}catch(e){}return t.noAudio||t._setupCodecs(),t},_setupCodecs:function(){var t=this||o,e=null;try{e="undefined"!=typeof Audio?new Audio:null}catch(l){return t}if(!e||"function"!=typeof e.canPlayType)return t;var n=e.canPlayType("audio/mpeg;").replace(/^no$/,""),i=t._navigator?t._navigator.userAgent:"",r=i.match(/OPR\/([0-6].)/g),a=r&&parseInt(r[0].split("/")[1],10)<33,s=-1!==i.indexOf("Safari")&&-1===i.indexOf("Chrome"),c=i.match(/Version\/(.*?) /),u=s&&c&&parseInt(c[1],10)<15;return t._codecs={mp3:!(a||!n&&!e.canPlayType("audio/mp3;").replace(/^no$/,"")),mpeg:!!n,opus:!!e.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),wav:!!(e.canPlayType('audio/wav; codecs="1"')||e.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!e.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!e.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(e.canPlayType("audio/x-m4b;")||e.canPlayType("audio/m4b;")||e.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(e.canPlayType("audio/x-mp4;")||e.canPlayType("audio/mp4;")||e.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!(u||!e.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!(u||!e.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!e.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(e.canPlayType("audio/x-flac;")||e.canPlayType("audio/flac;")).replace(/^no$/,"")},t},_unlockAudio:function(){var t=this||o;if(!t._audioUnlocked&&t.ctx){t._audioUnlocked=!1,t.autoUnlock=!1,t._mobileUnloaded||44100===t.ctx.sampleRate||(t._mobileUnloaded=!0,t.unload()),t._scratchBuffer=t.ctx.createBuffer(1,1,22050);var e=function(){for(;t._html5AudioPool.length<t.html5PoolSize;)try{var n=new Audio;n._unlocked=!0,t._releaseHtml5Audio(n)}catch(c){t.noAudio=!0;break}for(var o=0;o<t._howls.length;o++)if(!t._howls[o]._webAudio)for(var i=t._howls[o]._getSoundIds(),r=0;r<i.length;r++){var a=t._howls[o]._soundById(i[r]);a&&a._node&&!a._node._unlocked&&(a._node._unlocked=!0,a._node.load())}t._autoResume();var s=t.ctx.createBufferSource();s.buffer=t._scratchBuffer,s.connect(t.ctx.destination),void 0===s.start?s.noteOn(0):s.start(0),"function"==typeof t.ctx.resume&&t.ctx.resume(),s.onended=function(){s.disconnect(0),t._audioUnlocked=!0,document.removeEventListener("touchstart",e,!0),document.removeEventListener("touchend",e,!0),document.removeEventListener("click",e,!0),document.removeEventListener("keydown",e,!0);for(var n=0;n<t._howls.length;n++)t._howls[n]._emit("unlock")}};return document.addEventListener("touchstart",e,!0),document.addEventListener("touchend",e,!0),document.addEventListener("click",e,!0),document.addEventListener("keydown",e,!0),t}},_obtainHtml5Audio:function(){var t=this||o;if(t._html5AudioPool.length)return t._html5AudioPool.pop();var e=(new Audio).play();return e&&"undefined"!=typeof Promise&&(e instanceof Promise||"function"==typeof e.then)&&e.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")}),new Audio},_releaseHtml5Audio:function(t){var e=this||o;return t._unlocked&&e._html5AudioPool.push(t),e},_autoSuspend:function(){var t=this;if(t.autoSuspend&&t.ctx&&void 0!==t.ctx.suspend&&o.usingWebAudio){for(var e=0;e<t._howls.length;e++)if(t._howls[e]._webAudio)for(var n=0;n<t._howls[e]._sounds.length;n++)if(!t._howls[e]._sounds[n]._paused)return t;return t._suspendTimer&&clearTimeout(t._suspendTimer),t._suspendTimer=setTimeout(function(){if(t.autoSuspend){t._suspendTimer=null,t.state="suspending";var e=function(){t.state="suspended",t._resumeAfterSuspend&&(delete t._resumeAfterSuspend,t._autoResume())};t.ctx.suspend().then(e,e)}},3e4),t}},_autoResume:function(){var t=this;if(t.ctx&&void 0!==t.ctx.resume&&o.usingWebAudio)return"running"===t.state&&"interrupted"!==t.ctx.state&&t._suspendTimer?(clearTimeout(t._suspendTimer),t._suspendTimer=null):"suspended"===t.state||"running"===t.state&&"interrupted"===t.ctx.state?(t.ctx.resume().then(function(){t.state="running";for(var e=0;e<t._howls.length;e++)t._howls[e]._emit("resume")}),t._suspendTimer&&(clearTimeout(t._suspendTimer),t._suspendTimer=null)):"suspending"===t.state&&(t._resumeAfterSuspend=!0),t}};var o=new e,i=function(t){t.src&&0!==t.src.length?this.init(t):console.error("An array of source files must be passed with any new Howl.")};i.prototype={init:function(t){var e=this;return o.ctx||d(),e._autoplay=t.autoplay||!1,e._format="string"!=typeof t.format?t.format:[t.format],e._html5=t.html5||!1,e._muted=t.mute||!1,e._loop=t.loop||!1,e._pool=t.pool||5,e._preload="boolean"!=typeof t.preload&&"metadata"!==t.preload||t.preload,e._rate=t.rate||1,e._sprite=t.sprite||{},e._src="string"!=typeof t.src?t.src:[t.src],e._volume=void 0!==t.volume?t.volume:1,e._xhr={method:t.xhr&&t.xhr.method?t.xhr.method:"GET",headers:t.xhr&&t.xhr.headers?t.xhr.headers:null,withCredentials:!(!t.xhr||!t.xhr.withCredentials)&&t.xhr.withCredentials},e._duration=0,e._state="unloaded",e._sounds=[],e._endTimers={},e._queue=[],e._playLock=!1,e._onend=t.onend?[{fn:t.onend}]:[],e._onfade=t.onfade?[{fn:t.onfade}]:[],e._onload=t.onload?[{fn:t.onload}]:[],e._onloaderror=t.onloaderror?[{fn:t.onloaderror}]:[],e._onplayerror=t.onplayerror?[{fn:t.onplayerror}]:[],e._onpause=t.onpause?[{fn:t.onpause}]:[],e._onplay=t.onplay?[{fn:t.onplay}]:[],e._onstop=t.onstop?[{fn:t.onstop}]:[],e._onmute=t.onmute?[{fn:t.onmute}]:[],e._onvolume=t.onvolume?[{fn:t.onvolume}]:[],e._onrate=t.onrate?[{fn:t.onrate}]:[],e._onseek=t.onseek?[{fn:t.onseek}]:[],e._onunlock=t.onunlock?[{fn:t.onunlock}]:[],e._onresume=[],e._webAudio=o.usingWebAudio&&!e._html5,void 0!==o.ctx&&o.ctx&&o.autoUnlock&&o._unlockAudio(),o._howls.push(e),e._autoplay&&e._queue.push({event:"play",action:function(){e.play()}}),e._preload&&"none"!==e._preload&&e.load(),e},load:function(){var t=null;if(o.noAudio)this._emit("loaderror",null,"No audio support.");else{"string"==typeof this._src&&(this._src=[this._src]);for(var e=0;e<this._src.length;e++){var n,i;if(this._format&&this._format[e])n=this._format[e];else{if("string"!=typeof(i=this._src[e])){this._emit("loaderror",null,"Non-string found in selected audio sources - ignoring.");continue}(n=/^data:audio\/([^;,]+);/i.exec(i))||(n=/\.([^.]+)$/.exec(i.split("?",1)[0])),n&&(n=n[1].toLowerCase())}if(n||console.warn('No file extension was found. Consider using the "format" property or specify an extension.'),n&&o.codecs(n)){t=this._src[e];break}}if(t)return this._src=t,this._state="loading","https:"===window.location.protocol&&"http:"===t.slice(0,5)&&(this._html5=!0,this._webAudio=!1),new r(this),this._webAudio&&s(this),this;this._emit("loaderror",null,"No codec support for selected audio sources.")}},play:function(t,e){var n=this,i=null;if("number"==typeof t)i=t,t=null;else{if("string"==typeof t&&"loaded"===n._state&&!n._sprite[t])return null;if(void 0===t&&(t="__default",!n._playLock)){for(var r=0,a=0;a<n._sounds.length;a++)n._sounds[a]._paused&&!n._sounds[a]._ended&&(r++,i=n._sounds[a]._id);1===r?t=null:i=null}}var s=i?n._soundById(i):n._inactiveSound();if(!s)return null;if(i&&!t&&(t=s._sprite||"__default"),"loaded"!==n._state){s._sprite=t,s._ended=!1;var c=s._id;return n._queue.push({event:"play",action:function(){n.play(c)}}),c}if(i&&!s._paused)return e||n._loadQueue("play"),s._id;n._webAudio&&o._autoResume();var u=Math.max(0,s._seek>0?s._seek:n._sprite[t][0]/1e3),l=Math.max(0,(n._sprite[t][0]+n._sprite[t][1])/1e3-u),d=1e3*l/Math.abs(s._rate),p=n._sprite[t][0]/1e3,h=(n._sprite[t][0]+n._sprite[t][1])/1e3;s._sprite=t,s._ended=!1;var f=function(){s._paused=!1,s._seek=u,s._start=p,s._stop=h,s._loop=!(!s._loop&&!n._sprite[t][2])};if(!(u>=h)){var _=s._node;if(n._webAudio){var v=function(){n._playLock=!1,f(),n._refreshBuffer(s);var t=s._muted||n._muted?0:s._volume;_.gain.setValueAtTime(t,o.ctx.currentTime),s._playStart=o.ctx.currentTime,void 0===_.bufferSource.start?s._loop?_.bufferSource.noteGrainOn(0,u,86400):_.bufferSource.noteGrainOn(0,u,l):s._loop?_.bufferSource.start(0,u,86400):_.bufferSource.start(0,u,l),d!==1/0&&(n._endTimers[s._id]=setTimeout(n._ended.bind(n,s),d)),e||setTimeout(function(){n._emit("play",s._id),n._loadQueue()},0)};"running"===o.state&&"interrupted"!==o.ctx.state?v():(n._playLock=!0,n.once("resume",v),n._clearTimer(s._id))}else{var y=function(){_.currentTime=u,_.muted=s._muted||n._muted||o._muted||_.muted,_.volume=s._volume*o.volume(),_.playbackRate=s._rate;try{var i=_.play();if(i&&"undefined"!=typeof Promise&&(i instanceof Promise||"function"==typeof i.then)?(n._playLock=!0,f(),i.then(function(){n._playLock=!1,_._unlocked=!0,e?n._loadQueue():n._emit("play",s._id)}).catch(function(){n._playLock=!1,n._emit("playerror",s._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."),s._ended=!0,s._paused=!0})):e||(n._playLock=!1,f(),n._emit("play",s._id)),_.playbackRate=s._rate,_.paused)return void n._emit("playerror",s._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");"__default"!==t||s._loop?n._endTimers[s._id]=setTimeout(n._ended.bind(n,s),d):(n._endTimers[s._id]=function(){n._ended(s),_.removeEventListener("ended",n._endTimers[s._id],!1)},_.addEventListener("ended",n._endTimers[s._id],!1))}catch(r){n._emit("playerror",s._id,r)}};"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"===_.src&&(_.src=n._src,_.load());var g=window&&window.ejecta||!_.readyState&&o._navigator.isCocoonJS;if(_.readyState>=3||g)y();else{n._playLock=!0,n._state="loading";var m=function(){n._state="loaded",y(),_.removeEventListener(o._canPlayEvent,m,!1)};_.addEventListener(o._canPlayEvent,m,!1),n._clearTimer(s._id)}}return s._id}n._ended(s)},pause:function(t){var e=this;if("loaded"!==e._state||e._playLock)return e._queue.push({event:"pause",action:function(){e.pause(t)}}),e;for(var n=e._getSoundIds(t),o=0;o<n.length;o++){e._clearTimer(n[o]);var i=e._soundById(n[o]);if(i&&!i._paused&&(i._seek=e.seek(n[o]),i._rateSeek=0,i._paused=!0,e._stopFade(n[o]),i._node))if(e._webAudio){if(!i._node.bufferSource)continue;void 0===i._node.bufferSource.stop?i._node.bufferSource.noteOff(0):i._node.bufferSource.stop(0),e._cleanBuffer(i._node)}else isNaN(i._node.duration)&&i._node.duration!==1/0||i._node.pause();arguments[1]||e._emit("pause",i?i._id:null)}return e},stop:function(t,e){var n=this;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"stop",action:function(){n.stop(t)}}),n;for(var o=n._getSoundIds(t),i=0;i<o.length;i++){n._clearTimer(o[i]);var r=n._soundById(o[i]);r&&(r._seek=r._start||0,r._rateSeek=0,r._paused=!0,r._ended=!0,n._stopFade(o[i]),r._node&&(n._webAudio?r._node.bufferSource&&(void 0===r._node.bufferSource.stop?r._node.bufferSource.noteOff(0):r._node.bufferSource.stop(0),n._cleanBuffer(r._node)):isNaN(r._node.duration)&&r._node.duration!==1/0||(r._node.currentTime=r._start||0,r._node.pause(),r._node.duration===1/0&&n._clearSound(r._node))),e||n._emit("stop",r._id))}return n},mute:function(t,e){var n=this;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"mute",action:function(){n.mute(t,e)}}),n;if(void 0===e){if("boolean"!=typeof t)return n._muted;n._muted=t}for(var i=n._getSoundIds(e),r=0;r<i.length;r++){var a=n._soundById(i[r]);a&&(a._muted=t,a._interval&&n._stopFade(a._id),n._webAudio&&a._node?a._node.gain.setValueAtTime(t?0:a._volume,o.ctx.currentTime):a._node&&(a._node.muted=!!o._muted||t),n._emit("mute",a._id))}return n},volume:function(){var t,e,n,i=this,r=arguments;if(0===r.length)return i._volume;if(1===r.length||2===r.length&&void 0===r[1]){var a=i._getSoundIds(),s=a.indexOf(r[0]);s>=0?e=parseInt(r[0],10):t=parseFloat(r[0])}else r.length>=2&&(t=parseFloat(r[0]),e=parseInt(r[1],10));if(!(void 0!==t&&t>=0&&t<=1))return(n=e?i._soundById(e):i._sounds[0])?n._volume:0;if("loaded"!==i._state||i._playLock)return i._queue.push({event:"volume",action:function(){i.volume.apply(i,r)}}),i;void 0===e&&(i._volume=t),e=i._getSoundIds(e);for(var c=0;c<e.length;c++)(n=i._soundById(e[c]))&&(n._volume=t,r[2]||i._stopFade(e[c]),i._webAudio&&n._node&&!n._muted?n._node.gain.setValueAtTime(t,o.ctx.currentTime):n._node&&!n._muted&&(n._node.volume=t*o.volume()),i._emit("volume",n._id));return i},fade:function(t,e,n,i){var r=this;if("loaded"!==r._state||r._playLock)return r._queue.push({event:"fade",action:function(){r.fade(t,e,n,i)}}),r;t=Math.min(Math.max(0,parseFloat(t)),1),e=Math.min(Math.max(0,parseFloat(e)),1),n=parseFloat(n),r.volume(t,i);for(var a=r._getSoundIds(i),s=0;s<a.length;s++){var c=r._soundById(a[s]);if(c){if(i||r._stopFade(a[s]),r._webAudio&&!c._muted){var u=o.ctx.currentTime,l=u+n/1e3;c._volume=t,c._node.gain.setValueAtTime(t,u),c._node.gain.linearRampToValueAtTime(e,l)}r._startFadeInterval(c,t,e,n,a[s],void 0===i)}}return r},_startFadeInterval:function(t,e,n,o,i,r){var a=this,s=e,c=n-e,u=Math.abs(c/.01),l=Math.max(4,u>0?o/u:o),d=Date.now();t._fadeTo=n,t._interval=setInterval(function(){var i=(Date.now()-d)/o;d=Date.now(),s+=c*i,s=Math.round(100*s)/100,s=c<0?Math.max(n,s):Math.min(n,s),a._webAudio?t._volume=s:a.volume(s,t._id,!0),r&&(a._volume=s),(n<e&&s<=n||n>e&&s>=n)&&(clearInterval(t._interval),t._interval=null,t._fadeTo=null,a.volume(n,t._id),a._emit("fade",t._id))},l)},_stopFade:function(t){var e=this._soundById(t);return e&&e._interval&&(this._webAudio&&e._node.gain.cancelScheduledValues(o.ctx.currentTime),clearInterval(e._interval),e._interval=null,this.volume(e._fadeTo,t),e._fadeTo=null,this._emit("fade",t)),this},loop:function(){var t,e,n,o=this,i=arguments;if(0===i.length)return o._loop;if(1===i.length){if("boolean"!=typeof i[0])return!!(n=o._soundById(parseInt(i[0],10)))&&n._loop;t=i[0],o._loop=t}else 2===i.length&&(t=i[0],e=parseInt(i[1],10));for(var r=o._getSoundIds(e),a=0;a<r.length;a++)(n=o._soundById(r[a]))&&(n._loop=t,o._webAudio&&n._node&&n._node.bufferSource&&(n._node.bufferSource.loop=t,t&&(n._node.bufferSource.loopStart=n._start||0,n._node.bufferSource.loopEnd=n._stop,o.playing(r[a])&&(o.pause(r[a],!0),o.play(r[a],!0)))));return o},rate:function(){var t,e,n,i=this,r=arguments;if(0===r.length)e=i._sounds[0]._id;else if(1===r.length){var a=i._getSoundIds(),s=a.indexOf(r[0]);s>=0?e=parseInt(r[0],10):t=parseFloat(r[0])}else 2===r.length&&(t=parseFloat(r[0]),e=parseInt(r[1],10));if("number"!=typeof t)return(n=i._soundById(e))?n._rate:i._rate;if("loaded"!==i._state||i._playLock)return i._queue.push({event:"rate",action:function(){i.rate.apply(i,r)}}),i;void 0===e&&(i._rate=t),e=i._getSoundIds(e);for(var c=0;c<e.length;c++)if(n=i._soundById(e[c])){i.playing(e[c])&&(n._rateSeek=i.seek(e[c]),n._playStart=i._webAudio?o.ctx.currentTime:n._playStart),n._rate=t,i._webAudio&&n._node&&n._node.bufferSource?n._node.bufferSource.playbackRate.setValueAtTime(t,o.ctx.currentTime):n._node&&(n._node.playbackRate=t);var u=i.seek(e[c]),l=(i._sprite[n._sprite][0]+i._sprite[n._sprite][1])/1e3-u,d=1e3*l/Math.abs(n._rate);!i._endTimers[e[c]]&&n._paused||(i._clearTimer(e[c]),i._endTimers[e[c]]=setTimeout(i._ended.bind(i,n),d)),i._emit("rate",n._id)}return i},seek:function(){var t,e,n=this,i=arguments;if(0===i.length)n._sounds.length&&(e=n._sounds[0]._id);else if(1===i.length){var r=n._getSoundIds(),a=r.indexOf(i[0]);a>=0?e=parseInt(i[0],10):n._sounds.length&&(e=n._sounds[0]._id,t=parseFloat(i[0]))}else 2===i.length&&(t=parseFloat(i[0]),e=parseInt(i[1],10));if(void 0===e)return 0;if("number"==typeof t&&("loaded"!==n._state||n._playLock))return n._queue.push({event:"seek",action:function(){n.seek.apply(n,i)}}),n;var s=n._soundById(e);if(s){if(!("number"==typeof t&&t>=0)){if(n._webAudio){var c=n.playing(e)?o.ctx.currentTime-s._playStart:0,u=s._rateSeek?s._rateSeek-s._seek:0;return s._seek+(u+c*Math.abs(s._rate))}return s._node.currentTime}var l=n.playing(e);l&&n.pause(e,!0),s._seek=t,s._ended=!1,n._clearTimer(e),n._webAudio||!s._node||isNaN(s._node.duration)||(s._node.currentTime=t);var d=function(){l&&n.play(e,!0),n._emit("seek",e)};if(l&&!n._webAudio){var p=function(){n._playLock?setTimeout(p,0):d()};setTimeout(p,0)}else d()}return n},playing:function(t){if("number"==typeof t){var e=this._soundById(t);return!!e&&!e._paused}for(var n=0;n<this._sounds.length;n++)if(!this._sounds[n]._paused)return!0;return!1},duration:function(t){var e=this._duration,n=this._soundById(t);return n&&(e=this._sprite[n._sprite][1]/1e3),e},state:function(){return this._state},unload:function(){for(var t=this,e=t._sounds,n=0;n<e.length;n++)e[n]._paused||t.stop(e[n]._id),t._webAudio||(t._clearSound(e[n]._node),e[n]._node.removeEventListener("error",e[n]._errorFn,!1),e[n]._node.removeEventListener(o._canPlayEvent,e[n]._loadFn,!1),e[n]._node.removeEventListener("ended",e[n]._endFn,!1),o._releaseHtml5Audio(e[n]._node)),delete e[n]._node,t._clearTimer(e[n]._id);var i=o._howls.indexOf(t);i>=0&&o._howls.splice(i,1);var r=!0;for(n=0;n<o._howls.length;n++)if(o._howls[n]._src===t._src||t._src.indexOf(o._howls[n]._src)>=0){r=!1;break}return a&&r&&delete a[t._src],o.noAudio=!1,t._state="unloaded",t._sounds=[],t=null,null},on:function(t,e,n,o){var i=this["_on"+t];return"function"==typeof e&&i.push(o?{id:n,fn:e,once:o}:{id:n,fn:e}),this},off:function(t,e,n){var o=this["_on"+t],i=0;if("number"==typeof e&&(n=e,e=null),e||n)for(i=0;i<o.length;i++){var r=n===o[i].id;if(e===o[i].fn&&r||!e&&r){o.splice(i,1);break}}else if(t)this["_on"+t]=[];else{var a=Object.keys(this);for(i=0;i<a.length;i++)0===a[i].indexOf("_on")&&Array.isArray(this[a[i]])&&(this[a[i]]=[])}return this},once:function(t,e,n){return this.on(t,e,n,1),this},_emit:function(t,e,n){for(var o=this["_on"+t],i=o.length-1;i>=0;i--)o[i].id&&o[i].id!==e&&"load"!==t||(setTimeout(function(t){t.call(this,e,n)}.bind(this,o[i].fn),0),o[i].once&&this.off(t,o[i].fn,o[i].id));return this._loadQueue(t),this},_loadQueue:function(t){if(this._queue.length>0){var e=this._queue[0];e.event===t&&(this._queue.shift(),this._loadQueue()),t||e.action()}return this},_ended:function(t){var e=t._sprite;if(!this._webAudio&&t._node&&!t._node.paused&&!t._node.ended&&t._node.currentTime<t._stop)return setTimeout(this._ended.bind(this,t),100),this;var n=!(!t._loop&&!this._sprite[e][2]);if(this._emit("end",t._id),!this._webAudio&&n&&this.stop(t._id,!0).play(t._id),this._webAudio&&n){this._emit("play",t._id),t._seek=t._start||0,t._rateSeek=0,t._playStart=o.ctx.currentTime;var i=1e3*(t._stop-t._start)/Math.abs(t._rate);this._endTimers[t._id]=setTimeout(this._ended.bind(this,t),i)}return this._webAudio&&!n&&(t._paused=!0,t._ended=!0,t._seek=t._start||0,t._rateSeek=0,this._clearTimer(t._id),this._cleanBuffer(t._node),o._autoSuspend()),this._webAudio||n||this.stop(t._id,!0),this},_clearTimer:function(t){if(this._endTimers[t]){if("function"!=typeof this._endTimers[t])clearTimeout(this._endTimers[t]);else{var e=this._soundById(t);e&&e._node&&e._node.removeEventListener("ended",this._endTimers[t],!1)}delete this._endTimers[t]}return this},_soundById:function(t){for(var e=0;e<this._sounds.length;e++)if(t===this._sounds[e]._id)return this._sounds[e];return null},_inactiveSound:function(){this._drain();for(var t=0;t<this._sounds.length;t++)if(this._sounds[t]._ended)return this._sounds[t].reset();return new r(this)},_drain:function(){var t=this._pool,e=0,n=0;if(!(this._sounds.length<t)){for(n=0;n<this._sounds.length;n++)this._sounds[n]._ended&&e++;for(n=this._sounds.length-1;n>=0;n--){if(e<=t)return;this._sounds[n]._ended&&(this._webAudio&&this._sounds[n]._node&&this._sounds[n]._node.disconnect(0),this._sounds.splice(n,1),e--)}}},_getSoundIds:function(t){if(void 0===t){for(var e=[],n=0;n<this._sounds.length;n++)e.push(this._sounds[n]._id);return e}return[t]},_refreshBuffer:function(t){return t._node.bufferSource=o.ctx.createBufferSource(),t._node.bufferSource.buffer=a[this._src],t._panner?t._node.bufferSource.connect(t._panner):t._node.bufferSource.connect(t._node),t._node.bufferSource.loop=t._loop,t._loop&&(t._node.bufferSource.loopStart=t._start||0,t._node.bufferSource.loopEnd=t._stop||0),t._node.bufferSource.playbackRate.setValueAtTime(t._rate,o.ctx.currentTime),this},_cleanBuffer:function(t){var e=o._navigator&&o._navigator.vendor.indexOf("Apple")>=0;if(o._scratchBuffer&&t.bufferSource&&(t.bufferSource.onended=null,t.bufferSource.disconnect(0),e))try{t.bufferSource.buffer=o._scratchBuffer}catch(n){}return t.bufferSource=null,this},_clearSound:function(t){/MSIE |Trident\//.test(o._navigator&&o._navigator.userAgent)||(t.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}};var r=function(t){this._parent=t,this.init()};r.prototype={init:function(){var t=this._parent;return this._muted=t._muted,this._loop=t._loop,this._volume=t._volume,this._rate=t._rate,this._seek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++o._counter,t._sounds.push(this),this.create(),this},create:function(){var t=this._parent,e=o._muted||this._muted||this._parent._muted?0:this._volume;return t._webAudio?(this._node=void 0===o.ctx.createGain?o.ctx.createGainNode():o.ctx.createGain(),this._node.gain.setValueAtTime(e,o.ctx.currentTime),this._node.paused=!0,this._node.connect(o.masterGain)):o.noAudio||(this._node=o._obtainHtml5Audio(),this._errorFn=this._errorListener.bind(this),this._node.addEventListener("error",this._errorFn,!1),this._loadFn=this._loadListener.bind(this),this._node.addEventListener(o._canPlayEvent,this._loadFn,!1),this._endFn=this._endListener.bind(this),this._node.addEventListener("ended",this._endFn,!1),this._node.src=t._src,this._node.preload=!0===t._preload?"auto":t._preload,this._node.volume=e*o.volume(),this._node.load()),this},reset:function(){var t=this._parent;return this._muted=t._muted,this._loop=t._loop,this._volume=t._volume,this._rate=t._rate,this._seek=0,this._rateSeek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++o._counter,this},_errorListener:function(){this._parent._emit("loaderror",this._id,this._node.error?this._node.error.code:0),this._node.removeEventListener("error",this._errorFn,!1)},_loadListener:function(){var t=this._parent;t._duration=Math.ceil(10*this._node.duration)/10,0===Object.keys(t._sprite).length&&(t._sprite={__default:[0,1e3*t._duration]}),"loaded"!==t._state&&(t._state="loaded",t._emit("load"),t._loadQueue()),this._node.removeEventListener(o._canPlayEvent,this._loadFn,!1)},_endListener:function(){var t=this._parent;t._duration===1/0&&(t._duration=Math.ceil(10*this._node.duration)/10,t._sprite.__default[1]===1/0&&(t._sprite.__default[1]=1e3*t._duration),t._ended(this)),this._node.removeEventListener("ended",this._endFn,!1)}};var a={},s=function(t){var e=t._src;if(a[e])return t._duration=a[e].duration,void l(t);if(/^data:[^;]+;base64,/.test(e)){for(var n=atob(e.split(",")[1]),o=new Uint8Array(n.length),i=0;i<n.length;++i)o[i]=n.charCodeAt(i);u(o.buffer,t)}else{var r=new XMLHttpRequest;r.open(t._xhr.method,e,!0),r.withCredentials=t._xhr.withCredentials,r.responseType="arraybuffer",t._xhr.headers&&Object.keys(t._xhr.headers).forEach(function(e){r.setRequestHeader(e,t._xhr.headers[e])}),r.onload=function(){var e=(r.status+"")[0];"0"===e||"2"===e||"3"===e?u(r.response,t):t._emit("loaderror",null,"Failed loading audio file with status: "+r.status+".")},r.onerror=function(){t._webAudio&&(t._html5=!0,t._webAudio=!1,t._sounds=[],delete a[e],t.load())},c(r)}},c=function(t){try{t.send()}catch(e){t.onerror()}},u=function(t,e){var n=function(){e._emit("loaderror",null,"Decoding audio data failed.")},i=function(t){t&&e._sounds.length>0?(a[e._src]=t,l(e,t)):n()};"undefined"!=typeof Promise&&1===o.ctx.decodeAudioData.length?o.ctx.decodeAudioData(t).then(i).catch(n):o.ctx.decodeAudioData(t,i,n)},l=function(t,e){e&&!t._duration&&(t._duration=e.duration),0===Object.keys(t._sprite).length&&(t._sprite={__default:[0,1e3*t._duration]}),"loaded"!==t._state&&(t._state="loaded",t._emit("load"),t._loadQueue())},d=function(){if(o.usingWebAudio){try{"undefined"!=typeof AudioContext?o.ctx=new AudioContext:"undefined"!=typeof webkitAudioContext?o.ctx=new webkitAudioContext:o.usingWebAudio=!1}catch(r){o.usingWebAudio=!1}o.ctx||(o.usingWebAudio=!1);var t=/iP(hone|od|ad)/.test(o._navigator&&o._navigator.platform),e=o._navigator&&o._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),n=e?parseInt(e[1],10):null;if(t&&n&&n<9){var i=/safari/.test(o._navigator&&o._navigator.userAgent.toLowerCase());o._navigator&&!i&&(o.usingWebAudio=!1)}o.usingWebAudio&&(o.masterGain=void 0===o.ctx.createGain?o.ctx.createGainNode():o.ctx.createGain(),o.masterGain.gain.setValueAtTime(o._muted?0:o._volume,o.ctx.currentTime),o.masterGain.connect(o.ctx.destination)),o._setup()}};"function"==typeof define&&define.amd&&define([],function(){return{Howler:o,Howl:i}}),void 0!==n&&(n.Howler=o,n.Howl=i),void 0!==t?(t.HowlerGlobal=e,t.Howler=o,t.Howl=i,t.Sound=r):"undefined"!=typeof window&&(window.HowlerGlobal=e,window.Howler=o,window.Howl=i,window.Sound=r)})(),function(){"use strict";var t;HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(t){if(!this.ctx||!this.ctx.listener)return this;for(var e=this._howls.length-1;e>=0;e--)this._howls[e].stereo(t);return this},HowlerGlobal.prototype.pos=function(t,e,n){return this.ctx&&this.ctx.listener?(e="number"!=typeof e?this._pos[1]:e,n="number"!=typeof n?this._pos[2]:n,"number"!=typeof t?this._pos:(this._pos=[t,e,n],void 0!==this.ctx.listener.positionX?(this.ctx.listener.positionX.setTargetAtTime(this._pos[0],Howler.ctx.currentTime,.1),this.ctx.listener.positionY.setTargetAtTime(this._pos[1],Howler.ctx.currentTime,.1),this.ctx.listener.positionZ.setTargetAtTime(this._pos[2],Howler.ctx.currentTime,.1)):this.ctx.listener.setPosition(this._pos[0],this._pos[1],this._pos[2]),this)):this},HowlerGlobal.prototype.orientation=function(t,e,n,o,i,r){if(!this.ctx||!this.ctx.listener)return this;var a=this._orientation;return e="number"!=typeof e?a[1]:e,n="number"!=typeof n?a[2]:n,o="number"!=typeof o?a[3]:o,i="number"!=typeof i?a[4]:i,r="number"!=typeof r?a[5]:r,"number"!=typeof t?a:(this._orientation=[t,e,n,o,i,r],void 0!==this.ctx.listener.forwardX?(this.ctx.listener.forwardX.setTargetAtTime(t,Howler.ctx.currentTime,.1),this.ctx.listener.forwardY.setTargetAtTime(e,Howler.ctx.currentTime,.1),this.ctx.listener.forwardZ.setTargetAtTime(n,Howler.ctx.currentTime,.1),this.ctx.listener.upX.setTargetAtTime(o,Howler.ctx.currentTime,.1),this.ctx.listener.upY.setTargetAtTime(i,Howler.ctx.currentTime,.1),this.ctx.listener.upZ.setTargetAtTime(r,Howler.ctx.currentTime,.1)):this.ctx.listener.setOrientation(t,e,n,o,i,r),this)},Howl.prototype.init=(t=Howl.prototype.init,function(e){return this._orientation=e.orientation||[1,0,0],this._stereo=e.stereo||null,this._pos=e.pos||null,this._pannerAttr={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:360,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:360,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:0,distanceModel:void 0!==e.distanceModel?e.distanceModel:"inverse",maxDistance:void 0!==e.maxDistance?e.maxDistance:1e4,panningModel:void 0!==e.panningModel?e.panningModel:"HRTF",refDistance:void 0!==e.refDistance?e.refDistance:1,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:1},this._onstereo=e.onstereo?[{fn:e.onstereo}]:[],this._onpos=e.onpos?[{fn:e.onpos}]:[],this._onorientation=e.onorientation?[{fn:e.onorientation}]:[],t.call(this,e)}),Howl.prototype.stereo=function(t,n){var o=this;if(!o._webAudio)return o;if("loaded"!==o._state)return o._queue.push({event:"stereo",action:function(){o.stereo(t,n)}}),o;var i=void 0===Howler.ctx.createStereoPanner?"spatial":"stereo";if(void 0===n){if("number"!=typeof t)return o._stereo;o._stereo=t,o._pos=[t,0,0]}for(var r=o._getSoundIds(n),a=0;a<r.length;a++){var s=o._soundById(r[a]);if(s){if("number"!=typeof t)return s._stereo;s._stereo=t,s._pos=[t,0,0],s._node&&(s._pannerAttr.panningModel="equalpower",s._panner&&s._panner.pan||e(s,i),"spatial"===i?void 0!==s._panner.positionX?(s._panner.positionX.setValueAtTime(t,Howler.ctx.currentTime),s._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),s._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):s._panner.setPosition(t,0,0):s._panner.pan.setValueAtTime(t,Howler.ctx.currentTime)),o._emit("stereo",s._id)}}return o},Howl.prototype.pos=function(t,n,o,i){var r=this;if(!r._webAudio)return r;if("loaded"!==r._state)return r._queue.push({event:"pos",action:function(){r.pos(t,n,o,i)}}),r;if(n="number"!=typeof n?0:n,o="number"!=typeof o?-.5:o,void 0===i){if("number"!=typeof t)return r._pos;r._pos=[t,n,o]}for(var a=r._getSoundIds(i),s=0;s<a.length;s++){var c=r._soundById(a[s]);if(c){if("number"!=typeof t)return c._pos;c._pos=[t,n,o],c._node&&(c._panner&&!c._panner.pan||e(c,"spatial"),void 0!==c._panner.positionX?(c._panner.positionX.setValueAtTime(t,Howler.ctx.currentTime),c._panner.positionY.setValueAtTime(n,Howler.ctx.currentTime),c._panner.positionZ.setValueAtTime(o,Howler.ctx.currentTime)):c._panner.setPosition(t,n,o)),r._emit("pos",c._id)}}return r},Howl.prototype.orientation=function(t,n,o,i){var r=this;if(!r._webAudio)return r;if("loaded"!==r._state)return r._queue.push({event:"orientation",action:function(){r.orientation(t,n,o,i)}}),r;if(n="number"!=typeof n?r._orientation[1]:n,o="number"!=typeof o?r._orientation[2]:o,void 0===i){if("number"!=typeof t)return r._orientation;r._orientation=[t,n,o]}for(var a=r._getSoundIds(i),s=0;s<a.length;s++){var c=r._soundById(a[s]);if(c){if("number"!=typeof t)return c._orientation;c._orientation=[t,n,o],c._node&&(c._panner||(c._pos||(c._pos=r._pos||[0,0,-.5]),e(c,"spatial")),void 0!==c._panner.orientationX?(c._panner.orientationX.setValueAtTime(t,Howler.ctx.currentTime),c._panner.orientationY.setValueAtTime(n,Howler.ctx.currentTime),c._panner.orientationZ.setValueAtTime(o,Howler.ctx.currentTime)):c._panner.setOrientation(t,n,o)),r._emit("orientation",c._id)}}return r},Howl.prototype.pannerAttr=function(){var t,n,o,i=this,r=arguments;if(!i._webAudio)return i;if(0===r.length)return i._pannerAttr;if(1===r.length){if("object"!=typeof r[0])return(o=i._soundById(parseInt(r[0],10)))?o._pannerAttr:i._pannerAttr;t=r[0],void 0===n&&(t.pannerAttr||(t.pannerAttr={coneInnerAngle:t.coneInnerAngle,coneOuterAngle:t.coneOuterAngle,coneOuterGain:t.coneOuterGain,distanceModel:t.distanceModel,maxDistance:t.maxDistance,refDistance:t.refDistance,rolloffFactor:t.rolloffFactor,panningModel:t.panningModel}),i._pannerAttr={coneInnerAngle:void 0!==t.pannerAttr.coneInnerAngle?t.pannerAttr.coneInnerAngle:i._coneInnerAngle,coneOuterAngle:void 0!==t.pannerAttr.coneOuterAngle?t.pannerAttr.coneOuterAngle:i._coneOuterAngle,coneOuterGain:void 0!==t.pannerAttr.coneOuterGain?t.pannerAttr.coneOuterGain:i._coneOuterGain,distanceModel:void 0!==t.pannerAttr.distanceModel?t.pannerAttr.distanceModel:i._distanceModel,maxDistance:void 0!==t.pannerAttr.maxDistance?t.pannerAttr.maxDistance:i._maxDistance,refDistance:void 0!==t.pannerAttr.refDistance?t.pannerAttr.refDistance:i._refDistance,rolloffFactor:void 0!==t.pannerAttr.rolloffFactor?t.pannerAttr.rolloffFactor:i._rolloffFactor,panningModel:void 0!==t.pannerAttr.panningModel?t.pannerAttr.panningModel:i._panningModel})}else 2===r.length&&(t=r[0],n=parseInt(r[1],10));for(var a=i._getSoundIds(n),s=0;s<a.length;s++)if(o=i._soundById(a[s])){var c=o._pannerAttr;c={coneInnerAngle:void 0!==t.coneInnerAngle?t.coneInnerAngle:c.coneInnerAngle,coneOuterAngle:void 0!==t.coneOuterAngle?t.coneOuterAngle:c.coneOuterAngle,coneOuterGain:void 0!==t.coneOuterGain?t.coneOuterGain:c.coneOuterGain,distanceModel:void 0!==t.distanceModel?t.distanceModel:c.distanceModel,maxDistance:void 0!==t.maxDistance?t.maxDistance:c.maxDistance,refDistance:void 0!==t.refDistance?t.refDistance:c.refDistance,rolloffFactor:void 0!==t.rolloffFactor?t.rolloffFactor:c.rolloffFactor,panningModel:void 0!==t.panningModel?t.panningModel:c.panningModel};var u=o._panner;u?(u.coneInnerAngle=c.coneInnerAngle,u.coneOuterAngle=c.coneOuterAngle,u.coneOuterGain=c.coneOuterGain,u.distanceModel=c.distanceModel,u.maxDistance=c.maxDistance,u.refDistance=c.refDistance,u.rolloffFactor=c.rolloffFactor,u.panningModel=c.panningModel):(o._pos||(o._pos=i._pos||[0,0,-.5]),e(o,"spatial"))}return i},Sound.prototype.init=function(t){return function(){var e=this._parent;this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,t.call(this),this._stereo?e.stereo(this._stereo):this._pos&&e.pos(this._pos[0],this._pos[1],this._pos[2],this._id)}}(Sound.prototype.init),Sound.prototype.reset=function(t){return function(){var e=this._parent;return this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,this._stereo?e.stereo(this._stereo):this._pos?e.pos(this._pos[0],this._pos[1],this._pos[2],this._id):this._panner&&(this._panner.disconnect(0),this._panner=void 0,e._refreshBuffer(this)),t.call(this)}}(Sound.prototype.reset);var e=function(t,e){"spatial"===(e=e||"spatial")?(t._panner=Howler.ctx.createPanner(),t._panner.coneInnerAngle=t._pannerAttr.coneInnerAngle,t._panner.coneOuterAngle=t._pannerAttr.coneOuterAngle,t._panner.coneOuterGain=t._pannerAttr.coneOuterGain,t._panner.distanceModel=t._pannerAttr.distanceModel,t._panner.maxDistance=t._pannerAttr.maxDistance,t._panner.refDistance=t._pannerAttr.refDistance,t._panner.rolloffFactor=t._pannerAttr.rolloffFactor,t._panner.panningModel=t._pannerAttr.panningModel,void 0!==t._panner.positionX?(t._panner.positionX.setValueAtTime(t._pos[0],Howler.ctx.currentTime),t._panner.positionY.setValueAtTime(t._pos[1],Howler.ctx.currentTime),t._panner.positionZ.setValueAtTime(t._pos[2],Howler.ctx.currentTime)):t._panner.setPosition(t._pos[0],t._pos[1],t._pos[2]),void 0!==t._panner.orientationX?(t._panner.orientationX.setValueAtTime(t._orientation[0],Howler.ctx.currentTime),t._panner.orientationY.setValueAtTime(t._orientation[1],Howler.ctx.currentTime),t._panner.orientationZ.setValueAtTime(t._orientation[2],Howler.ctx.currentTime)):t._panner.setOrientation(t._orientation[0],t._orientation[1],t._orientation[2])):(t._panner=Howler.ctx.createStereoPanner(),t._panner.pan.setValueAtTime(t._stereo,Howler.ctx.currentTime)),t._panner.connect(t._node),t._paused||t._parent.pause(t._id,!0).play(t._id,!0)}}()}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],Easing:[function(t,e,n){"use strict";cc._RF.push(e,"c2a16rk1dVO1IFkKKpu0vW7","Easing"),Object.defineProperty(n,"__esModule",{value:!0}),n.Easing=void 0,n.Easing={Linear:{None:function(t){return t}},Quadratic:{In:function(t){return t*t},Out:function(t){return t*(2-t)},InOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}},Cubic:{In:function(t){return t*t*t},Out:function(t){return--t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}},Quartic:{In:function(t){return t*t*t*t},Out:function(t){return 1- --t*t*t*t},InOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}},Quintic:{In:function(t){return t*t*t*t*t},Out:function(t){return--t*t*t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},Sinusoidal:{In:function(t){return 1-Math.cos(t*Math.PI/2)},Out:function(t){return Math.sin(t*Math.PI/2)},InOut:function(t){return.5*(1-Math.cos(Math.PI*t))}},Exponential:{In:function(t){return 0===t?0:Math.pow(1024,t-1)},Out:function(t){return 1===t?1:1-Math.pow(2,-10*t)},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))}},Circular:{In:function(t){return 1-Math.sqrt(1-t*t)},Out:function(t){return Math.sqrt(1- --t*t)},InOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},Elastic:{In:function(t){var e=0,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/.4))},Out:function(t){var e=0,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/.4)+1)},InOut:function(t){var e=0,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/.4)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/.4)*.5+1)}},Back:{In:function(t){var e=1.70158;return t*t*((e+1)*t-e)},Out:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},Out33:function(t){var e=.94431;return--t*t*((e+1)*t+e)+1},Out66:function(t){var e=1.34996;return--t*t*((e+1)*t+e)+1},InOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)}},Bounce:{In:function(t){return 1-n.Easing.Bounce.Out(1-t)},Out:function(t){var e=7.5625,n=2.75;return t<1/n?e*t*t:t<2/n?e*(t-=1.5/n)*t+.75:t<2.5/n?e*(t-=2.25/n)*t+.9375:e*(t-=2.625/n)*t+.984375},InOut:function(t){return t<.5?.5*n.Easing.Bounce.In(2*t):.5*n.Easing.Bounce.Out(2*t-1)+.5},SymbolOut1:function(t){return t<.3146?10*t*t:1.5*(t-.65)*(t-.65)+.83},SymbolOut2:function(t){return t<.2581?15*t*t:t<.6988?1.2*(t-.48)*(t-.48)+.94:1.2*(t-.85)*(t-.85)+.97},SymbolOut3:function(t){return t<.2581?15*t*t:t<.5758?1.7*(t-.42)*(t-.42)+.955:t<.8294?1.7*(t-.7)*(t-.7)+.97:1.7*(t-.9)*(t-.9)+.99}}},cc._RF.pop()},{}],Events:[function(t,e,n){"use strict";cc._RF.push(e,"fe06fsUCkFMPo66rSpdfVP5","Events"),Object.defineProperty(n,"__esModule",{value:!0}),n.Events=n.EventType=void 0;var o=t("./Event");(function(t){t.NEW_GAME="NEW_GAME",t.DRAW_MODE="DRAW_MODE",t.SELECT_SUIT="SELECT_SUIT",t.RESET_GAMEOVER="RESET_GAMEOVER",t.POWER_ACTION="POWER_ACTION"})(n.EventType||(n.EventType={}));var i=function(){function t(){}return t.On=function(e,n,i){void 0===i&&(i=Number.POSITIVE_INFINITY);var r=t.nextId++;return e in t.events||(t.events[e]={}),t.events[e][r]=new o.Event(n,i),r},t.Once=function(e,n){return t.On(e,n,1)},t.Off=function(e,n){return!!(e in t.events&&t.events[e][n])&&(delete t.events[e][n],!0)},t.Emit=function(e,n,o,i,r,a){if(!(e in t.events))return cc.log("[ES] Event without subscribers: "+e),!1;for(var s=Object.keys(t.events[e]).map(Number),c=0,u=s;c<u.length;c++){var l=u[c];t.events[e][l].Emit(n,o,i,r,a)||delete t.events[e][l]}return s.length>0},t.generateEventsTree=function(){return{}},t.events=t.generateEventsTree(),t.nextId=0,t}();n.Events=i,cc._RF.pop()},{"./Event":"Event"}],Event:[function(t,e,n){"use strict";cc._RF.push(e,"5073enmvFpBJpCeXqll8HDP","Event"),Object.defineProperty(n,"__esModule",{value:!0}),n.Event=void 0;var o=function(){function t(t,e){this.count=0,this.callback=t,this.count=Math.max(e,0)}return t.prototype.Emit=function(t,e,n,o,i){if(this.count>0)try{this.callback(t,e,n,o,i),--this.count}catch(r){console.error(r)}return this.count>0},t}();n.Event=o,cc._RF.pop()},{}],FPSInfo:[function(t,e,n){"use strict";cc._RF.push(e,"a8978CO+WRANKvKD9h+im+D","FPSInfo");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=cc._decorator,s=a.ccclass,c=a.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.fpsLabel=null,e._curCounter=0,e._curTimer=0,e}return i(e,t),e.prototype.update=function(t){this._curCounter++,this._curTimer+=t,this._curTimer>1&&(this.fpsLabel.string="FPS: "+this._curCounter,this._curCounter=0,this._curTimer%=1)},r([c(cc.Label)],e.prototype,"fpsLabel",void 0),r([s],e)}(cc.Component);n.default=u,cc._RF.pop()},{}],GameRule:[function(t,e,n){"use strict";cc._RF.push(e,"8c902IZnG1CPaHsnEjErPue","GameRule");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../sounds/SoundManager"),s=cc._decorator,c=s.ccclass,u=(s.property,function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._scrollView=null,e._mainNode=null,e}return i(e,t),e.prototype.onLoad=function(){var t=this;this._scrollView=this.getComponentInChildren(cc.ScrollView),this._mainNode=cc.find("Canvas/Gameplay"),this._mainNode&&(this._mainNode.on(cc.Node.EventType.SIZE_CHANGED,function(){t._onCanvasSizeChanged()}),this._onCanvasSizeChanged())},e.prototype._onCanvasSizeChanged=function(){this.node.width=this._mainNode.width},e.prototype.start=function(){},e.prototype.open=function(){this.node.active=!0,this.node.setPosition(0,-760),cc.tween(this.node).to(.2,{position:cc.v3(this.node.position.x,this.node.position.y+760)}).start()},e.prototype.close=function(){var t=this;a.default.inst.playEffect("CLICK_BTN"),this.stopScroll(),cc.tween(this.node).to(.2,{position:cc.v3(this.node.position.x,this.node.position.y-760)}).call(function(){t.node.active=!1}).start()},e.prototype.stopScroll=function(){var t,e;null===(t=this._scrollView)||void 0===t||t.stopAutoScroll(),null===(e=this._scrollView)||void 0===e||e.scrollToTop(0)},r([c],e)}(cc.Component));n.default=u,cc._RF.pop()},{"../../sounds/SoundManager":"SoundManager"}],Gameinterface:[function(t,e,n){"use strict";cc._RF.push(e,"f21dfn0pElIm5+xJQQhb0O3","Gameinterface"),Object.defineProperty(n,"__esModule",{value:!0}),n.SuitMode=n.DrawMode=void 0,function(t){t[t.ONE=1]="ONE",t[t.THREE=3]="THREE"}(n.DrawMode||(n.DrawMode={})),function(t){t[t.ONE=1]="ONE",t[t.TWO=2]="TWO",t[t.FOUR=4]="FOUR"}(n.SuitMode||(n.SuitMode={})),cc._RF.pop()},{}],InformationPopup:[function(t,e,n){"use strict";cc._RF.push(e,"ef45ebjKn5Kt5a8B1N9lOzX","InformationPopup");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../sounds/SoundManager"),s=cc._decorator,c=s.ccclass,u=s.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.textInformation=null,e._mainNode=null,e.isShowing=!1,e}return i(e,t),e.prototype.onLoad=function(){var t=this;this._mainNode=cc.find("Canvas/Gameplay"),this._mainNode&&(this._mainNode.on(cc.Node.EventType.SIZE_CHANGED,function(){t._onCanvasSizeChanged()}),this._onCanvasSizeChanged())},e.prototype._onCanvasSizeChanged=function(){this.node.width=this._mainNode.width},e.prototype.start=function(){},e.prototype.open=function(t){this.textInformation.string=t,this.node.active=!0,this.isShowing=!0,this.node.setPosition(0,-760),cc.tween(this.node).to(.2,{position:cc.v3(this.node.position.x,this.node.position.y+760)}).start()},e.prototype.close=function(){var t=this;this.isShowing=!1,a.default.inst.playEffect("CLICK_BTN"),cc.tween(this.node).to(.2,{position:cc.v3(this.node.position.x,this.node.position.y-760)}).call(function(){t.node.active=!1}).start()},r([u(cc.Label)],e.prototype,"textInformation",void 0),r([c],e)}(cc.Component);n.default=l,cc._RF.pop()},{"../../sounds/SoundManager":"SoundManager"}],LoadingKlondikeScene:[function(t,e,n){"use strict";cc._RF.push(e,"da1aaeVc/hIvoFKzvMQ6duP","LoadingKlondikeScene");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("../sounds/SoundManager"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.loading=null,e.text=null,e}return i(e,t),e.prototype.start=function(){this.loading.active=!0,this.text.string="Loading",cc.tween(this.loading).repeatForever(cc.tween().by(.01,{angle:-5})).start(),this.preLoadGameScene()},e.prototype.preLoadGameScene=function(){return a(this,void 0,void 0,function(){var t=this;return s(this,function(e){switch(e.label){case 0:return cc.resources.preloadDir("Cards"),[4,c.default.inst.loadAudioResources(null,function(){return a(t,void 0,void 0,function(){return s(this,function(){return cc.director.loadScene("SolitaireGame"),[2]})})})];case 1:return e.sent(),[2]}})})},r([d(cc.Node)],e.prototype,"loading",void 0),r([d(cc.Label)],e.prototype,"text",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../sounds/SoundManager":"SoundManager"}],LoadingSpiderScene:[function(t,e,n){"use strict";cc._RF.push(e,"74cc5EdSVBAnKo5Ie7M1cMQ","LoadingSpiderScene");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("../sounds/SoundManager"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.loading=null,e.text=null,e}return i(e,t),e.prototype.start=function(){this.loading.active=!0,this.text.string="Loading",cc.tween(this.loading).repeatForever(cc.tween().by(.01,{angle:-5})).start(),this.preLoadGameScene()},e.prototype.preLoadGameScene=function(){return a(this,void 0,void 0,function(){var t=this;return s(this,function(e){switch(e.label){case 0:return cc.resources.preloadDir("Cards"),[4,c.default.inst.loadAudioResources(null,function(){return a(t,void 0,void 0,function(){return s(this,function(){return cc.director.loadScene("SpiderGame"),[2]})})})];case 1:return e.sent(),[2]}})})},r([d(cc.Node)],e.prototype,"loading",void 0),r([d(cc.Label)],e.prototype,"text",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../sounds/SoundManager":"SoundManager"}],MenuSetting:[function(t,e,n){"use strict";cc._RF.push(e,"147b6fFzCdNhL4rC3VJZ1og","MenuSetting");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("../../sounds/SoundManager"),u=t("../Popup/PopupManager"),l=cc._decorator,d=l.ccclass,p=l.property,h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.btnMore=null,e.btnHint=null,e.btnUndo=null,e.layoutButton=null,e.layoutLeft=null,e.soundToggle=null,e._isshowAnimPopup=!1,e._mainNode=null,e._timePowerUp=60,e._timeActive=10,e}return i(e,t),e.prototype.onLoad=function(){return a(this,void 0,void 0,function(){var t=this;return s(this,function(){return this._iconButtonMore=this.btnMore.getChildByName("iconMore"),this._mainNode=cc.find("Canvas/Gameplay"),this._mainNode&&(this._mainNode.on(cc.Node.EventType.SIZE_CHANGED,function(){t._onCanvasSizeChanged()}),this._onCanvasSizeChanged()),this._loadSoundSetting(),[2]})})},e.prototype._loadSoundSetting=function(){c.default.inst.isMusicOn()&&(this.soundToggle.check(),this.soundToggle.node.getChildByName("Background").active=!1)},e.prototype._onCanvasSizeChanged=function(){this.node.width=this._mainNode.width,this.layoutButton.x=this._mainNode.width/2-this.layoutButton.width/2,this.btnMore.x=this._mainNode.width/2-this.btnMore.width/2,this.layoutLeft?(this.layoutLeft.x=-this._mainNode.width/2+this.layoutLeft.width/1.5,this.btnUndo.x=this._mainNode.width/2-this.btnUndo.width):(this.btnHint.x=-this._mainNode.width/2+this.btnHint.width/2,this.btnUndo.x=this._mainNode.width/2-this.btnUndo.width/2)},e.prototype.openMenu=function(){var t=this;c.default.inst.playEffect("CLICK_BTN"),this._isshowAnimPopup||(this._isshowAnimPopup=!0,cc.tween(this.layoutButton).by(.3,{x:this.layoutButton.width*this.btnMore.scaleX}).call(function(){t._isshowAnimPopup=!1,t.btnMore.scaleX*=-1}).start())},e.prototype.openNewPlayKlondike=function(){c.default.inst.playEffect("CLICK_BTN"),u.default.inst.openSelectMode()},e.prototype.openNewPlaySpider=function(){c.default.inst.playEffect("CLICK_BTN"),u.default.inst.openSuitMode()},e.prototype.onSoundToggleCheck=function(t){this.soundToggle.node.getChildByName("Background").active=!t.isChecked,c.default.inst.setSoundOption(t.isChecked,"music"),c.default.inst.setSoundOption(t.isChecked,"sfx"),c.default.inst.playEffect("CLICK_BTN")},e.prototype.openGameRule=function(){c.default.inst.playEffect("CLICK_BTN"),u.default.inst.openGameRule()},r([p(cc.Node)],e.prototype,"btnMore",void 0),r([p(cc.Node)],e.prototype,"btnHint",void 0),r([p(cc.Node)],e.prototype,"btnUndo",void 0),r([p(cc.Node)],e.prototype,"layoutButton",void 0),r([p(cc.Node)],e.prototype,"layoutLeft",void 0),r([p(cc.Toggle)],e.prototype,"soundToggle",void 0),r([d],e)}(cc.Component);n.default=h,cc._RF.pop()},{"../../sounds/SoundManager":"SoundManager","../Popup/PopupManager":"PopupManager"}],NotificationPopup:[function(t,e,n){"use strict";cc._RF.push(e,"71d9dLT6N9JbII81eOinzsU","NotificationPopup");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../events/Events"),s=t("../../sounds/SoundManager"),c=t("./PopupManager"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.blurBg=null,e.titleLbl=null,e.content=null,e._waitPromise=null,e}return i(e,t),e.prototype.onLoad=function(){},e.prototype.start=function(){},e.prototype.show=function(t){var e=this;return this.titleLbl.string="No Moves Remaining",this.content.string=t,this.node.active=!0,new Promise(function(t){e._waitPromise=t})},e.prototype.onClickOkButton=function(){s.default.inst.playEffect("CLICK_BTN"),this.node.active=!1,this._waitPromise&&this._waitPromise(),c.default.inst.openSelectMode()},e.prototype.onClickCountinueButton=function(){s.default.inst.playEffect("CLICK_BTN"),a.Events.Emit(a.EventType.RESET_GAMEOVER),this.node.active=!1},r([d(cc.Sprite)],e.prototype,"blurBg",void 0),r([d(cc.Label)],e.prototype,"titleLbl",void 0),r([d(cc.Label)],e.prototype,"content",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../../events/Events":"Events","../../sounds/SoundManager":"SoundManager","./PopupManager":"PopupManager"}],ObjectPool:[function(t,e,n){"use strict";cc._RF.push(e,"f812aKYfy1FHpXm8Z+Q7FgK","ObjectPool"),Object.defineProperty(n,"__esModule",{value:!0}),n.ObjectPool=void 0;var o=65536,i=function(){function t(t,e,n,i,r){if(this._maxSize=t,this._generator=e,this._recycler=n,this._name=i,this._isDebug=r,isNaN(t)||t<1)throw new Error("Must be at valid number least 1");if(t>o)throw new Error("Must be less than or equal to "+o);this._localAbsMaxSize=Math.min(2*t,o),this._pool=[]}return Object.defineProperty(t.prototype,"maxSize",{get:function(){return this._maxSize},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"count",{get:function(){var t=this._pool;return t?t.length:0},enumerable:!1,configurable:!0}),t.prototype.clear=function(){this._pool.length=0},t.prototype.toArrayAndClear=function(){var t=this._pool;return this._pool=[],t},t.prototype.add=function(t){this._pool.length>=this._localAbsMaxSize?cc.warn("[POOL] Overhead of pool '"+this._name+"', status: "+this._pool.length+"/"+this._maxSize):(this._recycler&&this._recycler(t),this._pool.push(t),this._isDebug&&cc.warn("[POOL] "+this._name+" add, new size: "+this.count))},t.prototype.take=function(t){if(!this._generator&&!t)throw new Error("Must provide a factory if on was not provided at construction time");return this._isDebug&&cc.warn("[POOL] "+this._name+" take, current size: "+this.count),this._pool.pop()||t&&t()||this._generator()},t.prototype.prepare=function(t){if(this._pool.length<t){for(var e=t-this._pool.length,n=0;n<e;n++)this._pool.push(this._generator());this._isDebug&&cc.warn("[POOL] "+this._name+" create "+e+" item to prepare, size: "+this.count)}},t}();n.ObjectPool=i,cc._RF.pop()},{}],PopupManager:[function(t,e,n){"use strict";cc._RF.push(e,"6f25d/atJdJnKdZPn1S43YK","PopupManager");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=t("./GameRule"),u=t("./InformationPopup"),l=t("./NotificationPopup"),d=t("./ResultPopup"),p=t("./SelectGamePopup"),h=t("./SelectSuitPopup"),f=cc._decorator,_=f.ccclass,v=f.property,y=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.resultPopup=null,e.gameRule=null,e.selectMode=null,e.notificationPopup=null,e.informationPopup=null,e}var n;return i(e,t),n=e,e.prototype.onLoad=function(){n.inst&&cc.log("There are several PopupManager construction!!!"),n.inst=this,globalThis.PopupManager=this},e.prototype.openGameRule=function(){this.gameRule.getComponent(c.default).open()},e.prototype.openSelectMode=function(t){void 0===t&&(t=!1),this.selectMode.getComponent(p.default).open(t)},e.prototype.openSuitMode=function(t){void 0===t&&(t=!1),this.selectMode.getComponent(h.default).open(t)},e.prototype.openResult=function(t,e,n){return a(this,void 0,void 0,function(){return s(this,function(o){switch(o.label){case 0:return[4,this.resultPopup.getComponent(d.default).show(t,e,n)];case 1:return o.sent(),[2]}})})},e.prototype.openNotification=function(){return a(this,void 0,void 0,function(){return s(this,function(t){switch(t.label){case 0:return[4,this.notificationPopup.getComponent(l.default).show("You can undo some steps to win the game.")];case 1:return t.sent(),[2]}})})},e.prototype.openInformation=function(t){this.informationPopup.getComponent(u.default).open(t)},e.prototype.closeInformation=function(){this.informationPopup.getComponent(u.default).close()},e.prototype.isPopupShowing=function(){var t;switch(!0){case null===(t=this.informationPopup)||void 0===t?void 0:t.getComponent(u.default).isShowing:return!0;default:return!1}},e.inst=null,r([v(cc.Node)],e.prototype,"resultPopup",void 0),r([v(cc.Node)],e.prototype,"gameRule",void 0),r([v(cc.Node)],e.prototype,"selectMode",void 0),r([v(cc.Node)],e.prototype,"notificationPopup",void 0),r([v(cc.Node)],e.prototype,"informationPopup",void 0),n=r([_],e)}(cc.Component);n.default=y,cc._RF.pop()},{"./GameRule":"GameRule","./InformationPopup":"InformationPopup","./NotificationPopup":"NotificationPopup","./ResultPopup":"ResultPopup","./SelectGamePopup":"SelectGamePopup","./SelectSuitPopup":"SelectSuitPopup"}],ResizeCanvas:[function(t,e,n){"use strict";cc._RF.push(e,"e465cQRkatGQYPqV+2UHdun","ResizeCanvas");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=cc._decorator,s=a.ccclass,c=a.property,u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.followCanvasNodes=[],e.gameOrientationConfig=null,e}return i(e,t),e.prototype.onLoad=function(){var t=this;this.gameOrientationConfig=window.gameOrientationConfig,this.gameOrientationConfig||this.gameOrientationConfig||(this.gameOrientationConfig={isPortraitOnly:!1,isLandscapeOnly:!0,isFixedScreen:!1,isSupportBlurBg:!0,designScreenWidth:1280,designScreenHeight:720,supportScreenWidth:1560,supportScreenHeight:1560}),cc.view.setResizeCallback(function(){t.resizeCanvas()}),this.resizeCanvas()},e.prototype.resizeCanvas=function(){if(!this.gameOrientationConfig.isFixedScreen){var t,e,n=0,o=cc.find("Canvas"),i=window.innerWidth/window.innerHeight,r=this.gameOrientationConfig.designScreenWidth/this.gameOrientationConfig.designScreenHeight;if(this.gameOrientationConfig.isLandscapeOnly){var a=this.gameOrientationConfig.designScreenHeight/window.innerHeight;r=cc.misc.clampf(a*window.innerWidth,this.gameOrientationConfig.designScreenWidth,this.gameOrientationConfig.supportScreenWidth)/this.gameOrientationConfig.designScreenHeight}else{var s=this.gameOrientationConfig.designScreenWidth/window.innerWidth,c=cc.misc.clampf(s*window.innerHeight,this.gameOrientationConfig.supportScreenHeight,this.gameOrientationConfig.designScreenHeight);r=this.gameOrientationConfig.designScreenWidth/c}if(i>r?(t=window.innerHeight*r,n=window.innerHeight):(t=window.innerWidth,n=window.innerWidth/r),e=this.gameOrientationConfig.isLandscapeOnly?this.gameOrientationConfig.designScreenHeight/n:this.gameOrientationConfig.designScreenWidth/t,t*=e,n*=e,o.width=t,o.height=n,!this.gameOrientationConfig.isLandscapeOnly){var u=t;t=n,n=u}this.followCanvasNodes.forEach(function(e){e&&(e.width=t,e.height=n)})}},r([c(cc.Node)],e.prototype,"followCanvasNodes",void 0),r([s],e)}(cc.Component);n.default=u,cc._RF.pop()},{}],ResultPopup:[function(t,e,n){"use strict";cc._RF.push(e,"f19464Eh2dAnZnvHwz5tHZI","ResultPopup");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../sounds/SoundManager"),s=t("../../utils/SaveGame"),c=t("./PopupManager"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.blurBg=null,e.titleLbl=null,e.score=null,e.highScore=null,e.timer=null,e._waitPromise=null,e}return i(e,t),e.prototype.onLoad=function(){s.default.init()},e.prototype.start=function(){},e.prototype.show=function(t,e,n){var o=this,i=t?"Congratulations!":"Game Over";t&&a.default.inst.playEffect("WIN"),this.titleLbl.string=i.toUpperCase(),this.node.active=!0,this.score.string="Score: "+e.score,this.timer.string="Time: "+e.time;var r=s.default.getInstance().getData(n);return r||(r=e.score),this.highScore.string="High score: "+r,new Promise(function(t){o._waitPromise=t})},e.prototype.onClickPlayKlondike=function(){a.default.inst.playEffect("CLICK_BTN"),this.node.active=!1,this._waitPromise&&this._waitPromise(),c.default.inst.openSelectMode()},e.prototype.onClickPlaySpider=function(){a.default.inst.playEffect("CLICK_BTN"),this.node.active=!1,this._waitPromise&&this._waitPromise(),c.default.inst.openSuitMode()},r([d(cc.Sprite)],e.prototype,"blurBg",void 0),r([d(cc.Label)],e.prototype,"titleLbl",void 0),r([d(cc.Label)],e.prototype,"score",void 0),r([d(cc.Label)],e.prototype,"highScore",void 0),r([d(cc.Label)],e.prototype,"timer",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../../sounds/SoundManager":"SoundManager","../../utils/SaveGame":"SaveGame","./PopupManager":"PopupManager"}],SaveGame:[function(t,e,n){"use strict";cc._RF.push(e,"f22aameFZBNsbE3r6MH9DAS","SaveGame");var o=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},i=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function t(){t._instance=this}return t.getInstance=function(){return t._instance},t.init=function(){null==t.getInstance()&&new t},t.prototype.setData=function(t,e){return o(this,void 0,void 0,function(){return i(this,function(){return cc.sys.localStorage.setItem(t,e),[2]})})},t.prototype.getData=function(t){var e={};return e[t]=cc.sys.localStorage.getItem(t),e[t]},t.prototype.setDataAsync=function(t){return o(this,void 0,void 0,function(){var e,n;return i(this,function(){for(e in t)t.hasOwnProperty(e)&&(n=t[e],cc.sys.localStorage.setItem(e,n));return[2,!0]})})},t.prototype.getDataAsync=function(t){return o(this,void 0,void 0,function(){var e;return i(this,function(){return e={},t.forEach(function(t){var n=cc.sys.localStorage.getItem(t);e[t]=n}),[2,e]})})},t.prototype.removeData=function(t){return o(this,void 0,void 0,function(){return i(this,function(){return t.forEach(function(t){cc.sys.localStorage.removeItem(t)}),[2]})})},t._instance=null,t}();n.default=r,cc._RF.pop()},{}],Scheduler:[function(t,e,n){"use strict";cc._RF.push(e,"3e48cyRfF9Ctb0sBEBSX1nR","Scheduler"),Object.defineProperty(n,"__esModule",{value:!0}),n.Scheduler=void 0;var o=function(){function t(){}return t.setTimeout=function(t,e,n){return n.scheduleOnce(e,t/1e3),e},t.clearTimeout=function(t,e){e.unschedule(t)},t}();n.Scheduler=o,cc._RF.pop()},{}],SelectGamePopup:[function(t,e,n){"use strict";cc._RF.push(e,"ea50ba/RZVBhI2vikUDm4mX","SelectGamePopup");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../events/Events"),s=t("../../sounds/SoundManager"),c=t("../../utils/Gameinterface"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.blurBg=null,e.confirmBtn=null,e.btnClose=null,e._waitPromise=null,e._clickBtnCallback=null,e.modeGame=c.DrawMode.ONE,e}return i(e,t),e.prototype.open=function(t){var e=this;return this.btnClose.active=!t,this.node.active=!0,new Promise(function(t){e._waitPromise=t})},e.prototype.onSelectDraw=function(t,e){switch(parseInt(e)){case 1:this.modeGame=c.DrawMode.ONE;break;case 3:this.modeGame=c.DrawMode.THREE}},e.prototype.onClickNewPlay=function(){s.default.inst.playEffect("CLICK_BTN"),this._clickBtnCallback&&this._clickBtnCallback(),this.node.active=!1,this._waitPromise&&this._waitPromise(),a.Events.Emit(a.EventType.DRAW_MODE,this.modeGame),a.Events.Emit(a.EventType.NEW_GAME,"Klondike_HighScore")},e.prototype.onClose=function(){s.default.inst.playEffect("CLICK_BTN"),this.node.active=!1},r([d(cc.Sprite)],e.prototype,"blurBg",void 0),r([d(cc.Button)],e.prototype,"confirmBtn",void 0),r([d(cc.Node)],e.prototype,"btnClose",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../../events/Events":"Events","../../sounds/SoundManager":"SoundManager","../../utils/Gameinterface":"Gameinterface"}],SelectSuitPopup:[function(t,e,n){"use strict";cc._RF.push(e,"7fd79t6CNlMla6/29mS6CfN","SelectSuitPopup");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../events/Events"),s=t("../../sounds/SoundManager"),c=t("../../utils/Gameinterface"),u=cc._decorator,l=u.ccclass,d=u.property,p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.blurBg=null,e.confirmBtn=null,e.btnClose=null,e._waitPromise=null,e._clickBtnCallback=null,e.modeGame=c.SuitMode.ONE,e}return i(e,t),e.prototype.open=function(t){var e=this;return this.btnClose.active=!t,this.node.active=!0,new Promise(function(t){e._waitPromise=t})},e.prototype.onSelectDraw=function(t,e){switch(parseInt(e)){case 1:this.modeGame=c.SuitMode.ONE;break;case 2:this.modeGame=c.SuitMode.TWO;break;case 4:this.modeGame=c.SuitMode.FOUR}},e.prototype.onClickNewPlay=function(){s.default.inst.playEffect("CLICK_BTN"),this._clickBtnCallback&&this._clickBtnCallback(),this.node.active=!1,this._waitPromise&&this._waitPromise(),a.Events.Emit(a.EventType.SELECT_SUIT,this.modeGame),a.Events.Emit(a.EventType.NEW_GAME,"Spider_HighScore")},e.prototype.onClose=function(){s.default.inst.playEffect("CLICK_BTN"),this.node.active=!1},r([d(cc.Sprite)],e.prototype,"blurBg",void 0),r([d(cc.Button)],e.prototype,"confirmBtn",void 0),r([d(cc.Node)],e.prototype,"btnClose",void 0),r([l],e)}(cc.Component);n.default=p,cc._RF.pop()},{"../../events/Events":"Events","../../sounds/SoundManager":"SoundManager","../../utils/Gameinterface":"Gameinterface"}],ShakeAction:[function(t,e,n){"use strict";cc._RF.push(e,"807cdQChDlMtK0iqOTpMvPP","ShakeAction");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=t("../../utils/Utils"),a=cc._decorator,s=(a.ccclass,a.property,function(t){function e(e,n,o,i){var r=t.call(this)||this;return r._interval=1/60,r._duration=n,r._speed=o,r._magnitude=i,r}return i(e,t),e.create=function(t,n,o,i){void 0===n&&(n=.8),void 0===o&&(o=6),void 0===i&&(i=4);var r=new e(1/60,n,o,i);return r.setTarget(t),r},e.prototype.interpolate=function(t,e,n){var o=3.1415927*n,i=.5*(1-Math.cos(o));return t*(1-i)+e*i},e.prototype.findnoise=function(t,e){var n=Number.parseInt(t+"")+57*Number.parseInt(e+""),o=(n^=n<<13)*(n*n*60493+19990303)+1376312589&2147483647;return 1-Number.parseFloat(o+"")/1073741824},e.prototype.lerp=function(t,e,n){return t*(1-n)+e*n},e.prototype.noise=function(t,e){var n,o,i,r,a=Number.parseInt(t+""),s=Number.parseInt(e+"");n=this.findnoise(a,s),o=this.findnoise(a+1,s),i=this.findnoise(a,s+1),r=this.findnoise(a+1,s+1);var c=this.lerp(n,o,t-a),u=this.lerp(i,r,t-a);return this.lerp(c,u,e-s)},e.prototype.clamp=function(t,e,n){return t<e?t=e:t>n&&(t=n),t},e.prototype.update=function(t){var n=r.default.randomRange(-1e3,1e3);if(this.getTarget()){var o=this.getTarget(),i=o.getPosition();e._elapsed+=t;var a=e._elapsed/this._duration,s=1-this.clamp(2*a-1,0,1),c=n+this._speed*a,u=2*this.noise(c,0)-1,l=2*this.noise(0,c)-1;u*=this._magnitude*s,l*=this._magnitude*s,o.setPosition(u,l),e._elapsed>=this._duration&&(e._elapsed=0,o.setPosition(i))}},Object.defineProperty(e.prototype,"duration",{set:function(t){this._duration=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"magnitude",{set:function(t){this._magnitude=t},enumerable:!1,configurable:!0}),e._elapsed=0,e}(cc.ActionInterval));n.default=s,cc._RF.pop()},{"../../utils/Utils":"Utils"}],ShakeCamera:[function(t,e,n){"use strict";cc._RF.push(e,"5e0740n6tRBDo1Dg9KYej0X","ShakeCamera");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=t("../../UI/actions/ShakeAction"),s=cc._decorator,c=s.ccclass,u=s.property,l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.eventName="shake-camera",e}return i(e,t),e.prototype.onLoad=function(){cc.systemEvent.on(this.eventName,this._onEventTriggered.bind(this))},e.prototype.start=function(){},e.prototype._onEventTriggered=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=a.default.create(this.node),o=Number.parseFloat(t[0]);Number.isNaN(o)||(n.duration=o);var i=Number.parseFloat(t[1]);Number.isNaN(i)||(n.magnitude=i),this.node.runAction(n)},r([u(cc.String)],e.prototype,"eventName",void 0),r([c],e)}(cc.Component);n.default=l,cc._RF.pop()},{"../../UI/actions/ShakeAction":"ShakeAction"}],Singleton:[function(t,e,n){"use strict";cc._RF.push(e,"1d28bD6Eg5IRpMyXRBR5wpi","Singleton");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.SingletonComponent=n.Singleton=void 0;var r=function(){function t(){}return t.inst=function(t){return this._instance||new t},t._instance=null,t}();n.Singleton=r;var a=function(t){function e(){return t.call(this)||this}return i(e,t),e._setupInstance=function(t){return this._instance||new t},e.prototype.onLoad=function(){e._instance?cc.warn("There are several "+this.constructor.name+" construction!"):e._instance=this},e._instance=null,e}(cc.Component);n.SingletonComponent=a,cc._RF.pop()},{}],Solitaire:[function(t,e,n){"use strict";cc._RF.push(e,"7d50ezs4TpNbqWv2ZmzvaOX","Solitaire");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c,u=t("../UI/Popup/PopupManager"),l=t("../UI/menu/BoardScore"),d=t("../events/Events"),p=t("../sounds/SoundManager"),h=t("../utils/Gameinterface"),f=t("../utils/Utils"),_=t("./Card"),v=cc._decorator,y=v.ccclass,g=v.property;(function(t){t[t.A1=0]="A1",t[t.A2=1]="A2",t[t.A3=2]="A3",t[t.A4=3]="A4",t[t.DECK=4]="DECK",t[t.SHOW=5]="SHOW"})(c||(c={}));var m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.finalStacks=null,e.tableauStacks=null,e.container=null,e.cardPrefab=null,e.boardScore=null,e.blockTouch=null,e.btnSolved=null,e.btnUndo=null,e.undoStacks=[],e.deckCards=[],e.drawedCardStacks=[],e.tableauCardStacks=[],e.winStacks=[null,null,null,null],e.drawMode=h.DrawMode.ONE,e.timer=0,e.score=0,e.listCardWin=[],e.isWin=!1,e.listCardPlayer=[],e.isShowHint=!1,e.resetTime=0,e.noMovePredicts=!1,e.currentClickCard=0,e.lockTimerClickCard=1,e.isMovedInWin=!1,e.isSolved=!1,e.isStartGame=!1,e.currentCardMove=-1,e.originArr=null,e.hintCount=0,e.listTimeStamp=[],e}return i(e,t),e.prototype.onLoad=function(){d.Events.On(d.EventType.DRAW_MODE,this.setUpGamePlay.bind(this)),d.Events.On(d.EventType.RESET_GAMEOVER,this.resetCheckGameOver.bind(this))},e.prototype.start=function(){u.default.inst.openSelectMode(!0)},e.prototype.getRandomArray=function(){for(var t=[],e=0;e<52;e++)t[e]=e+1;return t.sort(function(){return.5-Math.random()}),this.originArr=t,t},e.prototype.setUpGamePlay=function(t){void 0===t&&(t=h.DrawMode.ONE),this.isStartGame=!0,this.isWin=!1,this.noMovePredicts=!1,this.btnSolved.active=!1,this.isSolved=!1,this.deckCards.length=0,this.listCardWin.length=0,this.drawedCardStacks.length=0,this.tableauCardStacks.length=0,this.listTimeStamp.length=0,this.undoStacks.length=0,this.winStacks=[null,null,null,null],this.timer=0,this.score=0,this.boardScore.getComponent(l.default).setScore(this.score,"Klondike_HighScore"),this.blockTouch.active=!1,this.container.removeAllChildren(),this.drawMode=t,this.isShowHint=!1,this.btnUndo.interactable=!1,this.generateMap()},e.prototype.generateMap=function(){return a(this,void 0,void 0,function(){var t,e,n,o,i,r=this;return s(this,function(){for(t=this.getRandomArray(),e=this.convertToCardsNodePosition(this.getStartStackPosition()),n=[],o=0;o<52;o++)i=cc.instantiate(this.cardPrefab),n.push(i);return n.forEach(function(n,o){n.parent=r.container,n.position=cc.v3(e.x,e.y+.5*o);var i=n.getComponent(_.default);n.zIndex=o,i.setData(t[o],r.container),i.setTouchCallback(r.cardTouchStart.bind(r),r.cardTouchEnd.bind(r),r.drawCard.bind(r),r.cardDoubleClick.bind(r),r.popUndoStack.bind(r))}),this.deckCards=n,this.dealCard(),[2]})})},e.prototype.dealCard=function(){for(var t=0;t<7;t++)for(var e=t;e<7;e++){var n=this.convertToCardsNodePosition(this.getTableauStackPosition(e)),o=this.deckCards.pop();o.position=cc.v3(n.x,n.y+-30*t),o.zIndex=t+e,o.zIndex=1,0===t?this.tableauCardStacks.push(o):this.tableauCardStacks[e].getComponent(_.default).setBottomChild(o),t===e&&o.getComponent(_.default).turnToFront()}},e.prototype.cardTouchStart=function(t){var e,n,o,i,r=cc.Vec3.ZERO;t.parent.convertToWorldSpaceAR(t.position,r),r.y-100>this.getTableauStackPosition(0).y?r.x-150>this.getWinStackPosition(c.SHOW).x?(o=this.getTableauStackIndex(r.x),i="win",cc.warn("from Win",o)):(i="drawedCards",cc.warn("from drawedCards",o,t.getComponent(_.default).point)):(o=this.getTableauStackIndex(r.x),cc.warn("from tableau",o,t.getComponent(_.default).point),i="tableau");var a=null!==(e=t.parent.parent)&&void 0!==e?e:null;this.undoStacks.push({node:t,idx:o,from:i,parentStatus:!("tableau"!==i||!a)&&(null===(n=a.getComponent(_.default))||void 0===n?void 0:n.isFront)})},e.prototype.cardTouchMove=function(){},e.prototype.cardTouchEnd=function(t){return a(this,void 0,void 0,function(){var e,n;return s(this,function(){return this.undoStacks[this.undoStacks.length-1]&&this.undoStacks[this.undoStacks.length-1].node===t?(this.isMovedInWin=!1,e=cc.Vec3.ZERO,t.parent.convertToWorldSpaceAR(t.position,e),n=this.getTableauStackIndex(e.x),e.y-100>this.getTableauStackPosition(0).y?(cc.log(e.x-150,this.getWinStackPosition(c.DECK).x),e.x-150>this.getWinStackPosition(c.SHOW).x?(n=this.getWinStackIndex(e.x),cc.log(n),this.moveCardToWinInStack(t,n),t.getComponent(_.default).isMoving=!1):(this.popUndoStack(),t.getComponent(_.default).reset())):this.tableauCardStacks[n]?t===this.tableauCardStacks[n]?(this.popUndoStack(),t.getComponent(_.default).reset()):(this.moveCard(t,n),t.getComponent(_.default).isMoving=!1):this.moveCardToEmptyStack(t,n),[2]):[2]})})},e.prototype.cardDoubleClick=function(t){cc.warn("double"),this.blockTouch.active=!0,this.lockTimerClickCard=.6,this.detectPosibleMove(t,!1,!0)||(t.getComponent(_.default).shake(),this.popUndoStack(),cc.warn("pop",this.undoStacks),this.blockTouch.active=!1),cc.warn(this.undoStacks)},e.prototype.drawCard=function(t){this.drawMode===h.DrawMode.ONE?this.drawOneCard():this.drawThreeCards(),this.undoStacks.push({node:t,from:"deck"}),this.showHint(!1)&&this.hintCount++,this.btnUndo.interactable=this.undoStacks.length>0},e.prototype.drawOneCard=function(){var t=this;this.blockTouch.active=!0;var e=this.convertToCardsNodePosition(this.getDrawStackPosition()),n=this.deckCards.pop();if(!n)return this.blockTouch.active=!1,void this.popUndoStack();var o=this.drawedCardStacks.length+1>=3?2:this.drawedCardStacks.length;cc.tween(n).set({zIndex:this.drawedCardStacks.slice(-1).length>0?this.drawedCardStacks.slice(-1)[0].zIndex+1:52}).to(.15,{position:cc.v3(e.x-22+27*(o-1),e.y,0)}).call(function(){cc.log("zi",n.zIndex),t.blockTouch.active=!1,n.getComponent(_.default).turnToFront()}).start(),this.hideFirstCard(),this.drawedCardStacks.forEach(function(t){t.getComponent(_.default).isUnder=!0}),this.drawedCardStacks.push(n)},e.prototype.hideFirstCard=function(){var t=this;if(this.drawMode===h.DrawMode.ONE){var e=this.convertToCardsNodePosition(this.getDrawStackPosition());if(this.drawedCardStacks.length>=3){var n=0;this.blockTouch.active=!0;for(var o=function(o){cc.tween(i.drawedCardStacks[o]).to(.15,{position:cc.v3(e.x-22+27*(n-1),e.y,0)}).call(function(){t.blockTouch.active=!1,t.drawedCardStacks[o].getComponent(_.default).isUnder=!0}).start(),n++},i=this,r=this.drawedCardStacks.length-2;r<this.drawedCardStacks.length;r++)o(r)}else this.blockTouch.active=!1}},e.prototype.showHidingDrawedCard=function(){var t=this;if(this.drawMode===h.DrawMode.ONE){var e=this.convertToCardsNodePosition(this.getDrawStackPosition()),n=this.drawedCardStacks.slice(-3);0===n.length&&(this.blockTouch.active=!1);for(var o=function(o){cc.tween(n[o]).set({zIndex:52+i.drawedCardStacks.length-n.length+o}).to(.15,{position:cc.v3(e.x-22+27*(o-1),e.y,0)}).call(function(){cc.log("zi",n[o].zIndex,52+t.drawedCardStacks.length-n.length+o),o===n.length-1&&(n[o].getComponent(_.default).isUnder=!1,t.blockTouch.active=!1)}).start()},i=this,r=0;r<n.length;r++)o(r)}},e.prototype.drawThreeCards=function(){var t=this;this.drawedCardStacks.forEach(function(t){t.active=!1});for(var e=null,n=function(n){o.blockTouch.active=!0;var i=o.convertToCardsNodePosition(o.getDrawStackPosition()),r=o.deckCards.pop();if(!r)return o.blockTouch.active=!1,"break";cc.tween(r).delay(.1*n).parallel(cc.tween().to(.15,{position:cc.v2(i.x-22+27*n,i.y)}),cc.tween().delay(.1).set({zIndex:52+n})).call(function(){0==n?e=r:e.getComponent(_.default).setRightChild(r),r.getComponent(_.default).turnToFront(),2===n&&(t.blockTouch.active=!1)}).start(),o.drawedCardStacks.push(r)},o=this,i=0;i<3&&"break"!==n(i);i++);},e.prototype.resetCards=function(){cc.log(this.drawedCardStacks.map(function(t){return Object.assign({},t)})),this.undoStacks.push({from:"reset",drawedCardStacks:this.drawedCardStacks.map(function(t){return Object.assign({},t)})});for(var t=this.convertToCardsNodePosition(this.getStartStackPosition()),e=0;this.drawedCardStacks.length>0;){var n=this.drawedCardStacks.pop();n.parent=this.container,n.active=!0,e++,n.zIndex=1,n.position=cc.v3(t.x,t.y+.5*e),this.deckCards.push(n);var o=n.getComponent(_.default);o.isUnder=!1,o.turnToBack()}this.showHint(!1)||this.resetTime++,this.resetTime>1&&0===this.hintCount&&(this.noMovePredicts=!0),this.hintCount=0},e.prototype.getStartStackPosition=function(){var t=cc.Vec3.ZERO;return this.finalStacks.convertToWorldSpaceAR(this.finalStacks.children[c.DECK].position,t),t},e.prototype.getDrawStackPosition=function(){var t=cc.Vec3.ZERO;return this.finalStacks.convertToWorldSpaceAR(this.finalStacks.children[c.SHOW].position,t),t},e.prototype.getWinStackPosition=function(t){var e=cc.Vec3.ZERO;return this.finalStacks.convertToWorldSpaceAR(this.finalStacks.children[t].position,e),e},e.prototype.getWinStackIndex=function(t){for(var e=0,n=0,o=0,i=0,r=0;r<4;r++)i=this.getWinStackPosition(r).x,n=Math.abs(i-t),(0===r||o>n)&&(o=n,e=r);return e},e.prototype.getTableauStackPosition=function(t){var e=cc.Vec3.ZERO;return this.tableauStacks.convertToWorldSpaceAR(this.tableauStacks.children[t].position,e),e},e.prototype.getTableauStackIndex=function(t){for(var e=0,n=0,o=0,i=0,r=0;r<7;r++)i=this.getTableauStackPosition(r).x,n=Math.abs(i-t),(0===r||o>n)&&(o=n,e=r);return e},e.prototype.convertToCardsNodePosition=function(t){var e=cc.Vec3.ZERO;return this.container.convertToNodeSpaceAR(t,e),e},e.prototype.moveCardToWinInStack=function(t,e,n){return void 0===n&&(n=!1),a(this,void 0,void 0,function(){var o,i,r,a=this;return s(this,function(s){switch(s.label){case 0:return o=t.getComponent(_.default),this.winStacks[e]?[3,6]:1!==o.point?[3,4]:(this.isMovedInWin=!0,this.winStacks.forEach(function(e,n){e===t&&(a.winStacks[n]=null)}),this.tableauCardStacks.forEach(function(e,n){e===t&&(a.tableauCardStacks[n]=null)}),t.parent=this.container,this.winStacks[e]=t,i=this.convertToCardsNodePosition(this.getWinStackPosition(e)),this.isSolved?[4,new Promise(function(e){cc.tween(t).to(.1,{position:i}).call(function(){o.isInWin=!0,e(!0)}).start()})]:[3,2]);case 1:return s.sent(),[3,3];case 2:t.position=cc.v3(i.x,i.y),o.isInWin=!0,s.label=3;case 3:return this.removeFromDrawedStack(t),this.showHidingDrawedCard(),this.drawedCardStacks.length-1>=0&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),this.checkCardWin(t),this.btnUndo.interactable=this.undoStacks.length>0,[3,5];case 4:n||(cc.warn("pop"),this.popUndoStack(),t.getComponent(_.default).reset()),s.label=5;case 5:return[3,9];case 6:return this.winStacks[e]!==t?[3,7]:(cc.warn("pop"),this.popUndoStack(),t.getComponent(_.default).reset(),[3,9]);case 7:return(r=this.winStacks[e].getComponent(_.default))&&0===t.getChildByName("BottomContainer").childrenCount?[4,r.setCenterChild(t,this.isSolved)]:[3,9];case 8:s.sent()&&(this.isMovedInWin=!0,this.removeFromDrawedStack(t),this.showHidingDrawedCard(),this.drawedCardStacks.length-1>=0&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),this.checkCardWin(t),this.tableauCardStacks.forEach(function(e,n){e===t&&(a.tableauCardStacks[n]=null)}),o.isInWin=!0),s.label=9;case 9:return[2]}})})},e.prototype.removeFromDrawedStack=function(t){this.resetTime=0,this.drawedCardStacks=this.drawedCardStacks.filter(function(e){return e!==t}),this.openLastCardInStack(),this.checkSolve()},e.prototype.moveCardToEmptyStack=function(t,e,n){var o=this;void 0===n&&(n=!1);var i=t.getComponent(_.default);if(13===i.point||n){t.parent=this.container,this.tableauCardStacks[e]=t;var r=this.convertToCardsNodePosition(this.getTableauStackPosition(e));t.position=r,t.zIndex=i.point,this.tableauCardStacks.forEach(function(n,i){n===t&&i!==e&&(o.tableauCardStacks[i]=null)}),this.removeFromDrawedStack(t),this.showHidingDrawedCard(),this.drawedCardStacks.length-1>=0&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),this.btnUndo.interactable=this.undoStacks.length>0}else cc.warn("pop"),this.popUndoStack(),t.getComponent(_.default).reset()},e.prototype.moveCard=function(t,e,n){var o=this;void 0===n&&(n=!1);var i=this.tableauCardStacks[e].getComponent(_.default);i&&(i.setBottomChild(t,n)?(cc.warn("moveCard::Point::",t.getComponent(_.default).point),this.drawedCardStacks=this.drawedCardStacks.filter(function(e){return e!==t}),this.showHidingDrawedCard(),this.drawedCardStacks.length-1>=0&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),i.isInWin=!1,this.btnUndo.interactable=this.undoStacks.length>0):this.popUndoStack()),this.tableauCardStacks.forEach(function(e,n){e===t&&(o.tableauCardStacks[n]=null)}),this.winStacks.forEach(function(e,n){e===t&&(o.winStacks[n]=null)}),this.openLastCardInStack()},e.prototype.openLastCardInStack=function(t){var e=this;this.tableauCardStacks.forEach(function(n,o){if(n&&o!==t){var i=n.getComponent(_.default);i&&(i.turnLastBottomChildToFront(),e.checkSolve())}})},e.prototype.update=function(t){!this.isWin&&this.isStartGame&&(this.timer+=t,this.boardScore.getComponent(l.default).setTimer(f.default.toMMSS(this.timer)),this.currentClickCard>0&&(this.lockTimerClickCard-=t,this.lockTimerClickCard<=0&&(this.currentClickCard=0,this.lockTimerClickCard=.6)))},e.prototype.resetCheckGameOver=function(){this.noMovePredicts=!1,this.resetTime=0,this.isStartGame=!0},e.prototype.showHint=function(t){var e=this;if(void 0===t&&(t=!0),p.default.inst.playEffect("CLICK_BTN"),!this.isShowHint){if(this.isShowHint=t,this.noMovePredicts)return this.isStartGame=!1,void u.default.inst.openNotification();for(var n=0,o=this.container.getComponentsInChildren(_.default).filter(function(t){return t.isFront&&!t.isInWin&&!e.drawedCardStacks.includes(t.node)});n<o.length;n++){var i=o[n];if(i&&this.detectPosibleMove(i.node,t))return!0}if(this.drawedCardStacks[this.drawedCardStacks.length-1]){var r=this.drawMode===h.DrawMode.ONE?this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).getBottomCard():this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).getRightCard();if(cc.warn({drawableCard:r}),this.detectPosibleMove(r,t))return!0}return t&&cc.tween(this.finalStacks.children[c.DECK].getChildByName("boder_highlight")).set({active:!0}).blink(1,2).call(function(){e.finalStacks.children[c.DECK].getChildByName("boder_highlight").active=!1,e.isShowHint=!1}).start(),!1}},e.prototype.detectPosibleMove=function(t,e,n){var o,i=this;void 0===n&&(n=!1);var r=t.getComponent(_.default);if(0===r.bottomContainer.childrenCount)for(var a=function(a){if(s.winStacks[a]){var c=s.winStacks[a].getComponent(_.default);if(c&&c.isCenterChild(t))return e&&s.highlightNode(t,null!==(o=s.winStacks[a])&&void 0!==o?o:s.finalStacks.children[a]),n&&r.moveToWorldPosition(cc.v2(c.getCenterWorldPosition()),function(){i.moveCardToWinInStack(t,a),i.blockTouch.active=!1}),{value:!0}}else if(1===r.point)return e&&s.highlightNode(t,s.finalStacks.children[a]),n&&r.moveToWorldPosition(cc.v2(s.getWinStackPosition(a)),function(){i.moveCardToWinInStack(t,a),i.blockTouch.active=!1}),{value:!0}},s=this,c=0;c<this.winStacks.length;c++){var u=a(c);if("object"==typeof u)return u.value}var l=function(o){if(d.tableauCardStacks[o]){var a=d.tableauCardStacks[o].getComponent(_.default);if(a&&a.isBottomChild(t)&&(d.drawedCardStacks.includes(r.node)&&r.node.active&&r.isFront||r.getParentCount()<=a.getParentCount()))return e&&d.highlightNode(t,d.tableauCardStacks[o]),n&&r.moveToWorldPosition(cc.v2(a.getBottomWorldPosition()),function(){i.moveCard(t,o),i.blockTouch.active=!1}),{value:!0}}else if(13===r.point&&(r.getParentCount(!0)>0||d.drawedCardStacks.includes(t)&&r.node.active&&r.isFront))return e&&d.highlightNode(t,d.tableauStacks.children[o]),n&&r.moveToWorldPosition(cc.v2(d.getTableauStackPosition(o)),function(){i.moveCardToEmptyStack(t,o),i.blockTouch.active=!1}),{value:!0}},d=this;for(c=0;c<this.tableauCardStacks.length;c++){var p=l(c);if("object"==typeof p)return p.value}return!1},e.prototype.highlightNode=function(t,e){var n,o,i=this;cc.warn(null===(n=null==t?void 0:t.getComponent(_.default))||void 0===n?void 0:n.point,null===(o=null==t?void 0:t.getComponent(_.default))||void 0===o?void 0:o.suit),cc.tween(t).blink(1,2).call(function(){i.isShowHint=!1}).start(),e.getComponent(_.default)?cc.tween(e.getComponent(_.default).getBottomCard()).blink(1,2).call(function(){i.isShowHint=!1}).start():e.getChildByName("boder_highlight")?cc.tween(e.getChildByName("boder_highlight")).set({active:!0}).blink(1,2).call(function(){e.getChildByName("boder_highlight").active=!1,i.isShowHint=!1}).start():cc.tween(e).blink(1,2).call(function(){i.isShowHint=!1}).start()},e.prototype.checkCardWin=function(t){if(!this.listCardWin.find(function(e){return e===t})&&(this.listCardWin.push(t),this.score++,this.listTimeStamp.push(parseFloat(this.timer.toFixed(2))),this.boardScore.getComponent(l.default).setScore(this.score,"Klondike_HighScore"),52===this.score)){console.log(this.listTimeStamp),this.blockTouch.active=!1,this.isStartGame=!1,this.isWin=!0;var e={score:this.score,highScore:this.score,time:f.default.toMMSS(this.timer)};u.default.inst.openResult(!0,e,"Klondike_HighScore")}},e.prototype.undo=function(){var t=this;p.default.inst.playEffect("CLICK_BTN");var e=this.popUndoStack();if(this.btnUndo.interactable=this.undoStacks.length>0,e){switch(e.from){case"tableau":this.tableauCardStacks[e.idx]?(this.moveCard(e.node,e.idx,!0),(w=e.node.parent.parent)&&!e.parentStatus&&e.node.parent.parent.getComponent(_.default).turnToBack()):(this.removeCardFromOldStack(e.node),this.moveCardToEmptyStack(e.node,e.idx,!0));break;case"win":this.moveCardToWinInStack(e.node,e.idx);break;case"deck":this.blockTouch.active=this.drawMode===h.DrawMode.ONE;for(var n=this.convertToCardsNodePosition(this.getStartStackPosition()),o=0;o<this.drawMode;o++){if(!(l=this.drawedCardStacks.pop()))return;this.drawedCardStacks.length-1>=0&&this.drawMode===h.DrawMode.ONE&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),this.showHidingDrawedCard(),l.parent=this.container,l.zIndex=1,l.position=cc.v3(n.x,n.y),this.deckCards.push(l),(s=l.getComponent(_.default)).isUnder=!1,s.turnToBack()}if(this.drawMode===h.DrawMode.THREE){var i=0;for(o=this.undoStacks.length-1;o>=0;o--)if("drawedCards"===this.undoStacks[o].from)i++;else if("deck"===this.undoStacks[o].from)break;var r=this.drawedCardStacks.slice(-1),a=3-i;for(r.length>0&&(a=Math.min(r[0].getComponent(_.default).getParentCount()+1,3-i)),cc.warn({lastCard:r,maxCards:a}),o=this.drawedCardStacks.length-a;o<this.drawedCardStacks.length;o++){var s,c=this.drawedCardStacks[o];if(!c)break;c.active=!0,(s=c.getComponent(_.default)).turnToFront()}}this.removeCardFromOldStack(e.node);break;case"reset":if(this.drawMode===h.DrawMode.ONE){for(var u=this.convertToCardsNodePosition(this.getDrawStackPosition());this.deckCards.length>0;){var l;(l=this.deckCards.pop()).active=!0,l.zIndex=52+this.drawedCardStacks.length,l.getComponent(_.default).isUnder=!0,l.getComponent(_.default).turnToFront(),l.position=cc.v3(u.x-22-27,u.y,0),this.drawedCardStacks.push(l)}var d=this.drawedCardStacks.slice(-3),f=function(t){cc.tween(d[t]).set({zIndex:52+v.drawedCardStacks.length}).to(.15,{position:cc.v3(u.x-22+27*(t-1),u.y,0)}).call(function(){d[t].getComponent(_.default).turnToFront()}).start(),t===d.length-1&&(d[t].getComponent(_.default).isUnder=!1)},v=this;for(o=0;o<d.length;o++)f(o)}else{cc.log(e.drawedCardStacks);var y=this.undoStacks.map(function(t){return t.from}).lastIndexOf("reset");cc.log(y);var g=this.undoStacks.slice(y+1);cc.log(g),u=this.convertToCardsNodePosition(this.getDrawStackPosition());var m=null,C=g.findIndex(function(t){return"deck"===t.from})+1;for(cc.warn({firstDeck:C});this.deckCards.length>0;){for(i=0,o=C;o<g.length;o++)if("drawedCards"===g[o].from)i++;else if("deck"===g[o].from){C=o+1;break}cc.warn({count:i});for(var b=0;b<3-i;b++){var S=this.deckCards.pop();S&&(S.active=!1,0===b?(m=S).position=cc.v3(u.x-22,u.y,0):(m.getComponent(_.default).setRightChild(S),S.position=cc.v3(0)),S.getComponent(_.default).turnToFront(),this.drawedCardStacks.push(S))}}if(C-1<g.length)for(o=C-1;o<g.length;o++)if("deck"===g[o].from)return void cc.log("empty deck");cc.log(this.drawedCardStacks);var w,k=this.drawedCardStacks.slice(-1);k[0]&&(k[0].active=!0,k[0].getComponent(_.default).turnToFront(),(w=k[0].parent.parent.getComponent(_.default))&&(w.node.active=!0,w.node.parent.parent.getComponent(_.default)?(w.node.parent.parent.active=!0,w.node.parent.parent.getComponent(_.default).turnToFront(),w.turnToFront(),k[0].getComponent(_.default).turnToFront()):(w.turnToFront(),k[0].getComponent(_.default).turnToFront())))}break;default:var P=this.convertToCardsNodePosition(this.getDrawStackPosition());if(this.drawMode===h.DrawMode.THREE){var T=this.drawedCardStacks.filter(function(t){return t.active&&t.getComponent(_.default).isFront}),O=T[0];cc.tween(e.node).set({parent:this.container,position:this.convertToCardsNodePosition(e.node.parent.convertToWorldSpaceAR(e.node)),zIndex:52+T.length}).to(.15,{position:cc.v3(P.x-22+27*T.length,P.y)}).call(function(){t.blockTouch.active=!1,O&&O.getComponent(_.default).setRightChild(e.node)}).start()}else{var A=this.convertToCardsNodePosition(this.getDrawStackPosition()),E=this.drawedCardStacks.length>=3?2:this.drawedCardStacks.length,x=this.drawMode===h.DrawMode.ONE?cc.v3(A.x-22+27*(E-1),A.y):A;cc.tween(e.node).set({parent:this.container,position:this.convertToCardsNodePosition(e.node.parent.convertToWorldSpaceAR(e.node))}).to(.15,{position:x}).call(function(){t.blockTouch.active=!1}).start(),this.hideFirstCard()}this.drawedCardStacks.push(e.node),this.drawMode===h.DrawMode.ONE&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1),this.removeCardFromOldStack(e.node)}this.checkSolve()}},e.prototype.removeCardFromOldStack=function(t){var e=this;this.tableauCardStacks.forEach(function(n,o){n===t&&(e.tableauCardStacks[o]=null)}),this.winStacks.forEach(function(n,o){n===t&&(e.winStacks[o]=null)})},e.prototype.onSolved2=function(){return a(this,void 0,void 0,function(){var t,e=this;return s(this,function(n){switch(n.label){case 0:return this.isSolved=!0,this.currentCardMove++,0===(t=this.container.getComponentsInChildren(_.default).filter(function(t){return t.isFront&&!t.isInWin&&!e.drawedCardStacks.includes(t.node)&&0===t.bottomContainer.childrenCount})).length?[2]:(this.blockTouch.active=!0,this.currentCardMove>t.length-1?(this.currentCardMove=-1,this.onSolved(),[3,3]):[3,1]);case 1:return[4,this.checkResultInWinStack(t[this.currentCardMove].node)];case 2:n.sent(),n.label=3;case 3:return[2]}})})},e.prototype.onSolved=function(){var t=this;this.isWin=!0,this.schedule(function(){t.tableauCardStacks.every(function(t){return null==t})?t.unscheduleAllCallbacks():t.tableauCardStacks.forEach(function(e){if(e){var n=e.getComponent(_.default);if(n){var o=n.getBottomCard();o?t.detectPosibleMove(o,!1,!0):t.detectPosibleMove(e,!1,!0)}}})},.15)},e.prototype.popUndoStack=function(){return cc.warn("pop",this.undoStacks.slice(),this.undoStacks),this.undoStacks.pop()},e.prototype.checkResultInWinStack=function(t){return a(this,void 0,void 0,function(){var e=this;return s(this,function(){return 0===t.getComponent(_.default).bottomContainer.childrenCount&&(this.detectPosibleMove(t,!1,!0),this.scheduleOnce(function(){e.onSolved()},.15)),[2]})})},e.prototype.checkSolve=function(){var t=this.container.getComponentsInChildren(_.default).filter(function(t){return!t.isFront});this.btnSolved.active=0===this.drawedCardStacks.length&&0===t.length&&0===this.deckCards.length},r([g(cc.Node)],e.prototype,"finalStacks",void 0),r([g(cc.Node)],e.prototype,"tableauStacks",void 0),r([g(cc.Node)],e.prototype,"container",void 0),r([g(cc.Prefab)],e.prototype,"cardPrefab",void 0),r([g(cc.Node)],e.prototype,"boardScore",void 0),r([g(cc.Node)],e.prototype,"blockTouch",void 0),r([g(cc.Node)],e.prototype,"btnSolved",void 0),r([g(cc.Button)],e.prototype,"btnUndo",void 0),r([y],e)}(cc.Component);n.default=m,cc._RF.pop()},{"../UI/Popup/PopupManager":"PopupManager","../UI/menu/BoardScore":"BoardScore","../events/Events":"Events","../sounds/SoundManager":"SoundManager","../utils/Gameinterface":"Gameinterface","../utils/Utils":"Utils","./Card":"Card"}],SoundManager:[function(t,e,n){"use strict";cc._RF.push(e,"ddb02Qeh1ZPS6EtbhIVk3Fn","SoundManager");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},c=this&&this.__spreadArrays||function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var o=Array(t),i=0;for(e=0;e<n;e++)for(var r=arguments[e],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(n,"__esModule",{value:!0}),n.SoundMgrState=void 0;var u,l=t("./audio_players/WebAudioPlayer"),d=t("./AudioResources"),p=t("../utils/Utils"),h=cc._decorator,f=h.ccclass,_=h.property;(function(t){t[t.INIT=0]="INIT",t[t.LOAD_ASSET=1]="LOAD_ASSET",t[t.LOAD_ASSET_DONE=2]="LOAD_ASSET_DONE",t[t.CANCEL_LOAD=3]="CANCEL_LOAD",t[t.NONE=4]="NONE"})(u=n.SoundMgrState||(n.SoundMgrState={}));var v=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._PREF_BGM_SETTING="enableBgm",e._PREF_EFFECT_SETTING="enableSfx",e.isDebug=!1,e.isPersistent=!0,e.rootAudioPath="audio",e.resourceConfig=null,e._shareVars=null,e._audioPlayer=null,e._onVisibilityChange=function(){var t="visible"===document.visibilityState;e.node.active&&(t?e._onResume():e._onPause())},e._onResume=function(){e._shareVars.isGamePaused=!1,e._audioPlayer.onPause(!1)},e._onPause=function(){e._shareVars.isGamePaused=!0,e._audioPlayer.onPause(!0)},e}var n;return i(e,t),n=e,e.prototype.onLoad=function(){n.inst?cc.warn("There are several "+this.name+" construction!"):(n.inst=this,this.isPersistent&&cc.game.addPersistRootNode(this.node),this._registerEvents(),this._init())},e.prototype.onQuitGame=function(){return a(this,void 0,Promise,function(){return s(this,function(){return n._state===u.LOAD_ASSET&&(n._state=u.CANCEL_LOAD),this.isPersistent&&cc.game.removePersistRootNode(this.node),this.node.destroy(),[2]})})},e.prototype.onDestroy=function(){this.cleanUp()},e.prototype.cleanUp=function(){this._audioPlayer.cleanUp(),this._unRegisterEvents(),this.unscheduleAllCallbacks(),this._shareVars.audioConfigs.clear(),this&&this===n.inst&&(n.inst=null,n._state=u.NONE)},e.prototype._loadAudioClip=function(t,e){return a(this,void 0,Promise,function(){var o=this;return s(this,function(){return[2,new Promise(function(i){n._state===u.LOAD_ASSET_DONE&&(t&&t(1,1),e&&e(!0),i(!0));var r=o.resourceConfig.json;r||(cc.error("[SOUND] Can' read config!"),i(!1));var a=c(d.PreLoadPacks,d.InGamePacks);if(o._shareVars.audioPacks=a,o._shareVars.totalOfPacks=a.length,o._shareVars.totalOfPacks>0){n._state=u.LOAD_ASSET;var s=[],l=0,p=0,h=!1;a.forEach(function(a){var c=r[a],d=l++;cc.resources.load(o.rootAudioPath+"/"+a,cc.AudioClip,function(e,n){s[d]=e/n;var i=s.reduce(function(t,e){return t+e});t&&t(i,o._shareVars.totalOfPacks)},function(t,r){!t&&r?o._audioPlayer.onLoadAudio(a,r,c):(h=!0,cc.error("[SOUND] CCLoad: "+a+" <= FAIL!"),i(!1)),o._shareVars.debug&&cc.warn("[SOUND] CCLoad "+a+" <= "+(h?"FAIL":"DONE")+"!"),++p>=o._shareVars.totalOfPacks&&(o._shareVars.debug&&cc.warn("[SOUND] CCLoad Sounds <= "+(h?"FAIL":"DONE")+"!"),n._state=u.LOAD_ASSET_DONE,e&&e(!0),i(!h))})})}else cc.warn("[SOUND] Empty sound config!!!"),i(!1)})]})})},e.prototype._verifyResources=function(t,e){return a(this,void 0,Promise,function(){return s(this,function(n){switch(n.label){case 0:return[4,this._audioPlayer.onVerifyResources(this._shareVars.totalOfPacks,t,e)];case 1:return[2,n.sent()]}})})},e.prototype.loadAudioResources=function(t,e){return a(this,void 0,Promise,function(){var n;return s(this,function(o){switch(o.label){case 0:return n=!1,[4,this._loadAudioClip(t,null)];case 1:return n=o.sent(),[4,this._verifyResources(null,e)];case 2:return n=o.sent(),[4,this._audioPlayer.onLoadAudioComplete(this._shareVars.totalOfPacks)];case 3:return o.sent(),cc.warn("[SOUND] Load Audio Done!"),[2,n]}})})},e.prototype.playBgm=function(t,e,n,o,i){void 0===e&&(e="AUTO");var r=-1,a=this._shareVars.bgmId;if(a&&"NONE"!==a){var s=this._audioPlayer.getPlayingAudio(a);if(s.length>0){var c=s[0];c.isAutoStop||c.fadingHandler&&0===c.fadingHandler.to||(cc.warn("[SOUND] Pause music "+t+", because don't fadeout!"),c.howl().pause(c.playId))}}var u=this._shareVars.audioConfigs.get(t),l=this._audioPlayer.getPlayingMusic(t);if(this._shareVars.bgmId=t,l){var d="FADE"===e?0:u.volume,p=!![!0,!1].includes(i)&&i;r=this._audioPlayer.resumeBySource(l,d,p),n&&n(this,t,u,l.howl().duration(r),r,l)}else d="FADE"===e||this._shareVars.isGamePaused||!this._shareVars.isMusicOn?0:e,r=this._playAudio(t,u,d,"AUTO",n,o,!0);return this._shareVars.debug&&cc.warn("[SOUND] Play bgm: "+t+", SoundOn: "+this._shareVars.isMusicOn),r},e.prototype.playEffect=function(t,e,n,o,i){if(this._shareVars.isGamePaused)return cc.warn("[SOUND] "+t+" play during game pause, skip!"),-1;var r=this._shareVars.audioConfigs.get(t);if(r)if(r.isMusic){if(!this._shareVars.isMusicOn)return-1;cc.warn("[SOUND] "+t+" use effect channel!")}else if(!this._shareVars.isEffectOn)return-1;return this._playAudio(t,r,e,n,o,i,!1)},e.prototype._playAudio=function(t,e,n,o,i,r,a){var s=this;if(!(e=null!=e?e:this._shareVars.audioConfigs.get(t)))return cc.error("[SOUND] "+t+" not found config!"),-1;var c=Number.parseFloat(n+""),u=Number.isNaN(c)?e.volume:c,l=!0===o||!1===o?o:e.loop;return this._audioPlayer.playAudio(t,e,u,l,function(t,e,n,o,r){i&&i(s,t,e,r,o,n)},function(t,e){r&&r(s,t,e)},a)},e.prototype.pause=function(t){return this._audioPlayer.pause(t)},e.prototype.resume=function(t){return this._audioPlayer.resume(t)},e.prototype.stop=function(t){return this._audioPlayer.stop(t)},e.prototype.stopByPlayId=function(t){return this._audioPlayer.stopByPlayId(t)},e.prototype.playing=function(t,e){return this._audioPlayer.playing(t,e)},e.prototype.playingByPlayId=function(t){return this._audioPlayer.playingByPlayId(t)},e.prototype.fade=function(t,e,n,o,i,r,a,s,c){return void 0===o&&(o=5),void 0===i&&(i=!0),void 0===s&&(s=.75),void 0===c&&(c=-1),this._audioPlayer.fade(t,e,n,o,i,r,a,s,c)},e.prototype.transitionToMusic=function(t,e,n,o,i,r){var a=this;void 0===n&&(n=.5),void 0===o&&(o=!1),void 0===i&&(i=.5),void 0===r&&(r=.75);var s=function(){a.playing(e)?a.fade(e,0,"AUTO",i,!1):a.playBgm(e,"FADE",function(t,e,n,o,r){t.fade(e,0,"AUTO",i,!1,void 0,void 0,void 0,r)})};(t="AUTO"!==t?t:this._shareVars.bgmId)===e||this.playing(t)?this.fade(t,"AUTO",0,n,o,null,s.bind(this),r):s()},e.prototype.setSoundOption=function(t,e,n){void 0===n&&(n=!0);var o="music"===e;o?this._shareVars.isMusicOn=t:this._shareVars.isEffectOn=t,this._audioPlayer.onChangeSoundOption(t,o),n&&this._saveSettings()},e.prototype.loadSoundSetting=function(){this._shareVars.isMusicOn=p.default.prefGetBoolean(this._PREF_BGM_SETTING,!0),this._shareVars.isEffectOn=p.default.prefGetBoolean(this._PREF_EFFECT_SETTING,!0)},e.prototype._init=function(){var t=this;this._shareVars={audioConfigs:new Map,audioPacks:null,totalOfPacks:0,bgmId:"BGM_NORMAL",isMusicOn:!0,isEffectOn:!0,maxAudioAtTimes:15,totalOfPlayingAudios:0,isGamePaused:!1,debug:this.isDebug},d.SoundNames.forEach(function(e,n){var o=e;if("NONE"!==o){var i=d.RawConfigs[n],r={pack:i.p,sprite:p.default.safeGetProperty(i,"s",void 0),loop:1===p.default.safeGetProperty(i,"l",0),volume:p.default.safeGetProperty(i,"v",1),isMusic:1===p.default.safeGetProperty(i,"m",0),numberOfPlaying:0};t._shareVars.audioConfigs.set(o,r)}}),this._audioPlayer=this._createAudioPlayer(),this.loadSoundSetting()},e.prototype._registerEvents=function(){cc.sys.isBrowser?document.addEventListener("visibilitychange",this._onVisibilityChange):cc.error("This platform is not support!")},e.prototype._unRegisterEvents=function(){cc.sys.isBrowser?document.removeEventListener("visibilitychange",this._onVisibilityChange):cc.error("This platform is not support!")},e.prototype._createAudioPlayer=function(){cc.sys.isBrowser||cc.warn("[SOUND] Don't support this platform!");var t=new l.default(this,this._shareVars);return t.init(),t},e.prototype.setBgmId=function(t){this._shareVars.bgmId=t},e.prototype.getBgmId=function(){return this._shareVars.bgmId},e.prototype.isMusicOn=function(){return this._shareVars.isMusicOn},e.prototype.isEffectOn=function(){return this._shareVars.isEffectOn},e.prototype._saveSettings=function(){p.default.prefSaveBoolean(this._PREF_BGM_SETTING,this._shareVars.isMusicOn),p.default.prefSaveBoolean(this._PREF_EFFECT_SETTING,this._shareVars.isEffectOn)},e.inst=null,e._state=u.NONE,r([_({displayName:"Debug",tooltip:"Enable debug logs"})],e.prototype,"isDebug",void 0),r([_({displayName:"Is Persistent",tooltip:"Keep instance when switch scene"})],e.prototype,"isPersistent",void 0),r([_(cc.String)],e.prototype,"rootAudioPath",void 0),r([_({type:cc.JsonAsset,displayName:"Resource Config"})],e.prototype,"resourceConfig",void 0),n=r([f],e)}(cc.Component);n.default=v,cc._RF.pop()},{"../utils/Utils":"Utils","./AudioResources":"AudioResources","./audio_players/WebAudioPlayer":"WebAudioPlayer"}],SpiderGame:[function(t,e,n){"use strict";cc._RF.push(e,"3a38eMhRCBIj4pnSZHDuqSm","SpiderGame");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__decorate||function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a},a=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},s=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c,u=t("../UI/Popup/PopupManager"),l=t("../UI/menu/BoardScore"),d=t("../events/Events"),p=t("../sounds/SoundManager"),h=t("../utils/Gameinterface"),f=t("../utils/Utils"),_=t("./CardSpider"),v=cc._decorator,y=v.ccclass,g=v.property;(function(t){t[t.A1=0]="A1",t[t.A2=1]="A2",t[t.A3=2]="A3",t[t.A4=3]="A4",t[t.A5=4]="A5"})(c||(c={}));var m=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.winStacks=null,e.deckStacks=null,e.tableauStacks=null,e.container=null,e.cardPrefab=null,e.boardScore=null,e.blockTouch=null,e.btnPowerUp=null,e.highlightStacks=null,e.powerSprite=[],e.btnUndo=null,e.undoSprite=[],e.undoStacks=[],e.deckCards=[],e.drawedCardStacks=[],e.tableauCardStacks=[],e.gameMode=h.SuitMode.ONE,e.timer=0,e.score=500,e.listCardWin=[],e.isWin=!1,e.isShowHint=!1,e.resetTime=0,e.noMovePredicts=!1,e.currentClickCard=0,e.lockTimerClickCard=1,e.isSolved=!1,e.isStartGame=!1,e.ignoreRule=!1,e.timePowerUp=0,e.isTimePowerUp=!1,e.isTimeActive=!1,e.isHighlightStacks=!1,e.timeActivePower=15,e.listTimeStamp=[],e.drawRemainingTimes=5,e.isTimeOut=!1,e.originArr=null,e.hintCount=0,e.winCardStack=[null,null,null,null,null,null,null,null],e}return i(e,t),e.prototype.onLoad=function(){d.Events.On(d.EventType.SELECT_SUIT,this.setUpGamePlay.bind(this)),d.Events.On(d.EventType.POWER_ACTION,this.powerCardEnd.bind(this))},e.prototype.powerCardEnd=function(t){var e=this;t.parent||this.tableauCardStacks.forEach(function(n,o){n===t.node&&(e.tableauCardStacks[o]=t.child)}),this.highlightCard(),this.undoStacks.push(t)},e.prototype.start=function(){u.default.inst.openSuitMode(!0)},e.prototype.getDeckCards=function(){for(var t=[],e=[],n=["spades","hearts","clubs","diamonds"],o=1;o<=13;o++)t[o]=o.toString().padStart(2,"0");for(o=0;o<this.gameMode;o++)t.forEach(function(t){e.push(n[o]+"_"+t)});for(;104/e.length>1;)e=e.concat(e);for(var i,r,a=e.length;a;)i=Math.floor(a--*Math.random()),r=e[a],e[a]=e[i],e[i]=r;return e},e.prototype.setUpGamePlay=function(t){var e=this;void 0===t&&(t=h.SuitMode.ONE),this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[1],this.isTimePowerUp=!1,this.ignoreRule=!1,this.isStartGame=!0,this.isWin=!1,this.noMovePredicts=!1,this.isSolved=!1,this.isHighlightStacks=!1,this.deckCards.length=0,this.listCardWin.length=0,this.drawedCardStacks.length=0,this.tableauCardStacks.length=0,this.listTimeStamp.length=0,this.undoStacks.length=0,this.drawRemainingTimes=5,this.timer=0,this.score=500,this.boardScore.getComponent(l.default).setScore(this.score,"Spider_HighScore"),this.blockTouch.active=!1,this.container.removeAllChildren(),this.gameMode=t,this.isShowHint=!1,this.isTimeActive=!1,this.btnUndo.spriteFrame=this.undoSprite[0],this.deckStacks.children.forEach(function(t){t.x=e.deckStacks.children[c.A5].x,t.active=!0}),this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[1],this.btnPowerUp.getChildByName("Background").getChildByName("Label").active=!1,this.timePowerUp=0,this.generateMap()},e.prototype.generateMap=function(){return a(this,void 0,void 0,function(){var t,e,n,o,i=this;return s(this,function(){for(this.originArr=this.getDeckCards(),t=this.convertToCardsNodePosition(this.getStartStackPosition(4)),e=[],n=0;n<this.originArr.length;n++)o=cc.instantiate(this.cardPrefab),e.push(o);return e.forEach(function(e,n){e.parent=i.container,e.position=cc.v3(t.x,t.y);var o=e.getComponent(_.default);e.zIndex=n,o.setData(i.originArr[n],i.container),o.setTouchCallback(i.cardTouchStart.bind(i),i.cardTouchEnd.bind(i),i.cardDoubleClick.bind(i),i.drawCard.bind(i),i.popUndoStack.bind(i),function(){return i.isTimeOut&&(o.resetPowerUp(),i.isTimeOut=!1),i.isTimePowerUp}.bind(i))}),this.deckCards=e,this.dealCard(),[2]})})},e.prototype.dealCard=function(){var t=this;this.blockTouch.active=!0;for(var e=function(e){var o=e%10,i=n.convertToCardsNodePosition(n.getTableauStackPosition(o)),r=n.deckCards.pop();cc.tween(r).delay(.1*e).parallel(cc.tween().to(.15,{position:cc.v3(i.x,i.y+-30*Math.floor(e/10))}),cc.tween().call(function(){p.default.inst.playEffect("DEAL_CARD"),r.zIndex=e})).call(function(){r.zIndex=1,e<10?t.tableauCardStacks.push(r):t.tableauCardStacks[o].getComponent(_.default).setBottomChild(r)}).start()},n=this,o=0;o<44;o++)e(o);var i=function(e){var n=r.convertToCardsNodePosition(r.getTableauStackPosition(e)),o=r.deckCards.pop();cc.tween(o).delay(.1*(44+e)).parallel(cc.tween().to(.15,{position:cc.v3(n.x,n.y+-30*Math.floor((44+e)/10))}),cc.tween().call(function(){p.default.inst.playEffect("DEAL_CARD"),o.zIndex=e})).call(function(){o.zIndex=1,9===e&&(t.blockTouch.active=!1),t.tableauCardStacks[e].getComponent(_.default).setBottomChild(o),o.getComponent(_.default).turnToFront()}).start()},r=this;for(o=0;o<10;o++)i(o);this.deckStacks.children.forEach(function(e,n){cc.tween(e).delay(5.5).to(.2,{x:t.deckStacks.children[c.A5].x-15*(5-n-1)}).start()})},e.prototype.cardTouchStart=function(t){var e,n;this.isTimeOut=!1;var o,i=cc.Vec3.ZERO;t.parent.convertToWorldSpaceAR(t.position,i),o=this.getTableauStackIndex(i.x);var r=null!==(e=t.parent.parent)&&void 0!==e?e:null;this.undoStacks.push({node:t,idx:o,from:"tableau",parentStatus:!!r&&(null===(n=r.getComponent(_.default))||void 0===n?void 0:n.isFront)})},e.prototype.cardTouchMove=function(){},e.prototype.cardTouchEnd=function(t){return a(this,void 0,void 0,function(){var e,n;return s(this,function(){return this.undoStacks[this.undoStacks.length-1]&&this.undoStacks[this.undoStacks.length-1].node===t?(e=cc.Vec3.ZERO,t.parent.convertToWorldSpaceAR(t.position,e),n=this.getTableauStackIndex(e.x),this.tableauCardStacks[n]?t===this.tableauCardStacks[n]?(this.popUndoStack(),t.getComponent(_.default).reset()):(this.moveCard(t,n,this.ignoreRule),t.getComponent(_.default).isMoving=!1,this.completeSuit(n)):this.moveCardToEmptyStack(t,n),this.highlightCard(),[2]):[2]})})},e.prototype.highlightCard=function(){this.tableauCardStacks.forEach(function(t){if(t&&t.getComponent(_.default).calcHeight(),t){var e=null,n=t.getComponent(_.default).getBottomCard(),o=!0;n.getComponent(_.default).setEnable();for(var i=0;i<=t.getComponent(_.default).getBottomCount()-1;i++)e=n.parent.parent,!o||e.getComponent(_.default)&&e.getComponent(_.default).suit===n.getComponent(_.default).suit&&e.getComponent(_.default).point===n.getComponent(_.default).point+1||(o=!1),n=e,o?n.getComponent(_.default).setEnable():n.getComponent(_.default).setDisable()}})},e.prototype.completeSuit=function(t){var e=this.tableauCardStacks[t].getComponent(_.default).getBottomCard();if(1!==e.getComponent(_.default).point)return!1;var n=null,o=e,i=[];i.push(o);for(var r=0;r<12;r++){if(!(n=o.parent.parent).getComponent(_.default)||n.getComponent(_.default).suit!==o.getComponent(_.default).suit||n.getComponent(_.default).point!==o.getComponent(_.default).point+1)return!1;o=n,i.push(o)}return this.undoStacks.push({from:"reset",parentStatus:o.getComponent(_.default).isFront,idx:t,completedSuit:i}),this.moveToWin(i),!0},e.prototype.moveToWin=function(t){var e=this,n=Math.max(this.winCardStack.findIndex(function(t){return null===t}),0);t.forEach(function(o,i){o.getComponent(_.default).isInWin=!0,cc.tween(o).delay(.1*i).call(function(){p.default.inst.playEffect("MOVE_WIN"),o.getComponent(_.default).zIndex=54+i,o.getComponent(_.default).moveToWorldPosition(cc.v2(e.getWinStackPosition(n)),function(){if(e.moveCardToWinStack(o,n),i===t.length-1&&(e.openLastCardInStack(),e.highlightCard(),7===n)){e.blockTouch.active=!1,e.isStartGame=!1,e.isWin=!0;var r={score:e.score,highScore:e.score,time:f.default.toMMSS(e.timer)};console.log(e.listTimeStamp),u.default.inst.openResult(!0,r,"Spider_HighScore")}})}).start()}),this.listTimeStamp.push(parseFloat(this.timer.toFixed(2))),this.score+=100,this.boardScore.getComponent(l.default).setScore(this.score,"Spider_HighScore")},e.prototype.cardDoubleClick=function(t){var e=this;this.blockTouch.active=!0,this.lockTimerClickCard=.6;var n=[],o=this.detectPosibleMove(t);if(o.length>0&&n.push.apply(n,o),n.length>0){n.sort(function(){return.5-Math.random()}).sort(function(t,e){return e.priority-t.priority}).sort(function(t,e){return e.countCard-t.countCard});var i=n[0];return"card"===i.to?t.getComponent(_.default).moveToWorldPosition(cc.v2(this.tableauCardStacks[i.idx].getComponent(_.default).getBottomWorldPosition()),function(){e.moveCard(t,i.idx),e.blockTouch.active=!1,e.completeSuit(i.idx),e.highlightCard()}):t.getComponent(_.default).moveToWorldPosition(cc.v2(this.getTableauStackPosition(i.idx)),function(){e.moveCardToEmptyStack(t,i.idx),e.blockTouch.active=!1,e.highlightCard()}),!0}this.popUndoStack(),this.blockTouch.active=!1},e.prototype.drawCard=function(t){var e=this;if(this.tableauCardStacks.some(function(t){return null===t})){if(this.isHighlightStacks)return;if(this.isHighlightStacks=!0,this.tableauCardStacks.forEach(function(t,n){null===t&&cc.tween(e.highlightStacks.children[n]).set({active:!0}).to(.5,{scale:1.5,opacity:0}).call(function(){e.highlightStacks.children[n].active=!1,e.highlightStacks.children[n].scale=1,e.highlightStacks.children[n].opacity=255,e.isHighlightStacks=!1}).start()}),u.default.inst.isPopupShowing())return;u.default.inst.openInformation("You cannot deal a new row while any columns are empty.")}else{var n=[];this.blockTouch.active=!0;for(var o=function(t){var o=i.convertToCardsNodePosition(i.getTableauStackPosition(t)),r=i.deckCards.pop();n.push(r),cc.tween(r).delay(.1*t).parallel(cc.tween().to(.2,{position:cc.v3(o.x,o.y+-30*(i.tableauCardStacks[t].getComponent(_.default).getBottomCount()-1))}),cc.tween().call(function(){p.default.inst.playEffect("DEAL_CARD"),r.zIndex=54+t})).call(function(){r.zIndex=1,e.tableauCardStacks[t].getComponent(_.default).setBottomChild(r),r.getComponent(_.default).turnToFront(),e.completeSuit(t),9===t&&(e.drawRemainingTimes--,e.drawRemainingTimes>=0&&(e.deckStacks.children[e.drawRemainingTimes].active=!1),e.deckCards.forEach(function(t){t.x-=15}),e.blockTouch.active=!1,e.highlightCard())}).start()},i=this,r=0;r<10;r++)o(r);this.undoStacks.push({node:t,from:"deck",listDeal:n});var a=this.undoStacks.length>0?1:0;this.btnUndo.spriteFrame=this.undoSprite[a]}},e.prototype.getStartStackPosition=function(t){var e=cc.Vec3.ZERO;return this.deckStacks.convertToWorldSpaceAR(this.deckStacks.children[t].position,e),e},e.prototype.getWinStackPosition=function(t){var e=cc.Vec3.ZERO;return this.winStacks.convertToWorldSpaceAR(this.winStacks.children[t].position,e),e},e.prototype.getWinStackIndex=function(t){for(var e=0,n=0,o=0,i=0,r=0;r<4;r++)i=this.getWinStackPosition(r).x,n=Math.abs(i-t),(0===r||o>n)&&(o=n,e=r);return e},e.prototype.getTableauStackPosition=function(t){var e=cc.Vec3.ZERO;return this.tableauStacks.convertToWorldSpaceAR(this.tableauStacks.children[t].position,e),e},e.prototype.getTableauStackIndex=function(t){for(var e=0,n=0,o=0,i=0,r=0;r<10;r++)i=this.getTableauStackPosition(r).x,n=Math.abs(i-t),(0===r||o>n)&&(o=n,e=r);return e},e.prototype.convertToCardsNodePosition=function(t){var e=cc.Vec3.ZERO;return this.container.convertToNodeSpaceAR(t,e),e},e.prototype.moveCardToEmptyStack=function(t,e){var n=this,o=t.getComponent(_.default);t.parent=this.container,this.tableauCardStacks[e]=t;var i=this.convertToCardsNodePosition(this.getTableauStackPosition(e));t.position=i,t.zIndex=o.point,this.tableauCardStacks.forEach(function(o,i){o===t&&i!==e&&(n.tableauCardStacks[i]=null)}),this.openLastCardInStack()},e.prototype.moveCardToWinStack=function(t,e){var n=this;t.parent=this.container,this.winCardStack[e]=t;var o=this.convertToCardsNodePosition(this.getWinStackPosition(e));t.position=o,t.getComponent(_.default).zIndex=1,this.tableauCardStacks.forEach(function(e,o){e===t&&(n.tableauCardStacks[o]=null)})},e.prototype.moveCard=function(t,e,n){var o=this;void 0===n&&(n=!1);var i=this.tableauCardStacks[e].getComponent(_.default);if(this.score--,this.boardScore.getComponent(l.default).setScore(this.score,"Spider_HighScore"),i)if(i.setBottomChild(t,n)){this.drawedCardStacks=this.drawedCardStacks.filter(function(e){return e!==t}),this.drawedCardStacks.length-1>=0&&(this.drawedCardStacks[this.drawedCardStacks.length-1].getComponent(_.default).isUnder=!1);var r=this.undoStacks.length>0?1:0;this.btnUndo.spriteFrame=this.undoSprite[r]}else this.popUndoStack();this.tableauCardStacks.forEach(function(e,n){e&&e.getComponent(_.default).calcHeight(),e===t&&(o.tableauCardStacks[n]=null)}),this.openLastCardInStack()},e.prototype.openLastCardInStack=function(t){this.tableauCardStacks.forEach(function(e,n){if(e&&n!==t){var o=e.getComponent(_.default);o&&o.turnLastBottomChildToFront()}})},e.prototype.update=function(t){u.default.inst.isPopupShowing()&&this.tableauCardStacks.findIndex(function(t){return null===t})<0&&u.default.inst.closeInformation(),!this.isWin&&this.isStartGame&&(this.timer+=t,this.boardScore.getComponent(l.default).setTimer(f.default.toMMSS(this.timer)),this.currentClickCard>0&&(this.lockTimerClickCard-=t,this.lockTimerClickCard<=0&&(this.currentClickCard=0,this.lockTimerClickCard=.6)),this.timePowerUp-=t,this.timePowerUp>0?(this.isTimeActive=!0,this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[2],this.btnPowerUp.getChildByName("Background").getChildByName("Label").active=!0,this.btnPowerUp.getComponentInChildren(cc.Label).string=f.default.toMMSS(this.timePowerUp),this.btnPowerUp.getChildByName("Background").getChildByName("Label").color=(new cc.Color).fromHEX("#FF3A5A")):(this.isTimeActive=!1,this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[1],this.btnPowerUp.getChildByName("Background").getChildByName("Label").active=!1),this.isTimePowerUp&&(this.timeActivePower-=t,this.timeActivePower>0?(this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[0],this.btnPowerUp.getChildByName("Background").getChildByName("Label").active=!0,this.btnPowerUp.getComponentInChildren(cc.Label).string=f.default.toMMSS(this.timeActivePower)):(this.isTimeOut=!0,this.isTimePowerUp=!1,this.ignoreRule=!1,this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[1],this.timePowerUp=60)))},e.prototype.showHint=function(t){var e=this;if(void 0===t&&(t=!0),p.default.inst.playEffect("CLICK_BTN"),!this.isShowHint){if(this.isShowHint=t,!this.noMovePredicts){for(var n=[],o=0,i=this.container.getComponentsInChildren(_.default).filter(function(t){return t.isFront&&!t.isDisable&&!t.isInWin});o<i.length;o++){var r=i[o];if(r){var a=this.detectPosibleMove(r.node);a.length>0&&n.push.apply(n,a)}}if(n.length>0){n.sort(function(){return.5-Math.random()}).sort(function(t,e){return e.priority-t.priority});var s=n[0];return"card"===s.to?t&&this.highlightNode(s.card.node,s.parent):t&&this.highlightNode(s.card.node,this.tableauStacks.children[s.idx]),!0}return t&&cc.tween(this.deckCards[this.deckCards.length-1]).set({active:!0}).blink(1,2).call(function(){e.isShowHint=!1}).start(),!1}p.default.inst.playEffect("NO_HINT")}},e.prototype.detectPosibleMove=function(t){for(var e=t.getComponent(_.default),n=[],o=0;o<this.tableauCardStacks.length;o++)if(this.tableauCardStacks[o]){var i=this.tableauCardStacks[o].getComponent(_.default);i&&i.isBottomChild(t)&&e.node.active&&e.isFront&&!e.isDisable&&!e.isInWin&&(r=i.getBottomCard().getComponent(_.default).suit===t.getComponent(_.default).suit?2:0,n.push({to:"card",parent:i.getBottomCard(),card:e,idx:o,priority:r,countCard:i.getBottomCard().getComponent(_.default).getParentEnableCount()}))}else if(e.getParentCount(!0)>0||e.node.active&&e.isFront){var r=1;n.push({to:"empty",idx:o,priority:r,card:e})}return n},e.prototype.highlightNode=function(t,e){var n=this;cc.tween(t).blink(1,2).call(function(){n.isShowHint=!1}).start(),e.getComponent(_.default)?cc.tween(e.getComponent(_.default).getBottomCard()).blink(1,2).call(function(){n.isShowHint=!1}).start():e.getChildByName("boder_highlight")?cc.tween(e.getChildByName("boder_highlight")).set({active:!0}).blink(1,2).call(function(){e.getChildByName("boder_highlight").active=!1,n.isShowHint=!1}).start():cc.tween(e).blink(1,2).call(function(){n.isShowHint=!1}).start()},e.prototype.checkCardWin=function(t){this.listCardWin.find(function(e){return e===t})||(this.listCardWin.push(t),this.score+=100,this.boardScore.getComponent(l.default).setScore(this.score,"Spider_HighScore"))},e.prototype.undo=function(){var t=this;p.default.inst.playEffect("CLICK_BTN");var e=this.popUndoStack(),n=this.undoStacks.length>0?1:0;if(this.btnUndo.spriteFrame=this.undoSprite[n],e){switch(e.from){case"tableau":this.tableauCardStacks[e.idx]?(this.moveCard(e.node,e.idx,!0),e.node.parent.parent&&!e.parentStatus&&e.node.parent.parent.getComponent(_.default).turnToBack()):(this.removeCardFromOldStack(e.node),this.moveCardToEmptyStack(e.node,e.idx));break;case"deck":for(var o=this.convertToCardsNodePosition(this.getStartStackPosition(this.drawRemainingTimes));e.listDeal.length>0;){var i=e.listDeal.pop();if(!i)return;i.parent=this.container,i.position=cc.v3(o.x,o.y),this.deckCards.push(i),i.getComponent(_.default).turnToBack()}this.deckStacks.children[this.drawRemainingTimes].active=!0,this.drawRemainingTimes++,this.deckCards.forEach(function(t){t.position=o}),this.removeCardFromOldStack(e.node);break;case"reset":e.completedSuit.reverse(),e.completedSuit.forEach(function(n,o){n.getComponent(_.default).isInWin=!1,n.getComponent(_.default).turnToFront(),t.tableauCardStacks[e.idx]?(t.moveCard(n,e.idx,!0),0===o&&n.parent.parent&&!e.parentStatus&&n.parent.parent.getComponent(_.default).turnToBack(),t.removeCardFromOldStack(n)):(t.removeCardFromOldStack(n),t.moveCardToEmptyStack(n,e.idx))}),this.undo();break;default:e.node.parent=this.container,e.child.parent=this.container,e.parent?e.parent.getComponent(_.default).setBottomChild(e.node,!0):(e.node.position=e.child.position,this.tableauCardStacks.forEach(function(n,o){n===e.child&&(t.tableauCardStacks[o]=e.node)})),e.node.getComponent(_.default).setBottomChild(e.child,!0),this.highlightCard()}this.highlightCard()}},e.prototype.removeCardFromOldStack=function(t){var e=this;this.winCardStack.forEach(function(n,o){n===t&&(e.winCardStack[o]=null)}),this.tableauCardStacks.forEach(function(n,o){n===t&&(e.tableauCardStacks[o]=null)})},e.prototype.popUndoStack=function(){return this.undoStacks.pop()},e.prototype.onPowerUp=function(){this.isTimePowerUp||this.isTimeActive||(p.default.inst.playEffect("CLICK_BTN"),this.btnPowerUp.getChildByName("Background").getComponent(cc.Sprite).spriteFrame=this.powerSprite[0],this.btnPowerUp.getComponentInChildren(cc.Label).string=f.default.toMMSS(this.timeActivePower),this.btnPowerUp.getChildByName("Background").getChildByName("Label").color=(new cc.Color).fromHEX("#A0FF82"),this.timeActivePower=15,this.isTimePowerUp=!0)},r([g(cc.Node)],e.prototype,"winStacks",void 0),r([g(cc.Node)],e.prototype,"deckStacks",void 0),r([g(cc.Node)],e.prototype,"tableauStacks",void 0),r([g(cc.Node)],e.prototype,"container",void 0),r([g(cc.Prefab)],e.prototype,"cardPrefab",void 0),r([g(cc.Node)],e.prototype,"boardScore",void 0),r([g(cc.Node)],e.prototype,"blockTouch",void 0),r([g(cc.Node)],e.prototype,"btnPowerUp",void 0),r([g(cc.Node)],e.prototype,"highlightStacks",void 0),r([g([cc.SpriteFrame])],e.prototype,"powerSprite",void 0),r([g(cc.Sprite)],e.prototype,"btnUndo",void 0),r([g([cc.SpriteFrame])],e.prototype,"undoSprite",void 0),r([y],e)}(cc.Component);n.default=m,cc._RF.pop()},{"../UI/Popup/PopupManager":"PopupManager","../UI/menu/BoardScore":"BoardScore","../events/Events":"Events","../sounds/SoundManager":"SoundManager","../utils/Gameinterface":"Gameinterface","../utils/Utils":"Utils","./CardSpider":"CardSpider"}],Utils:[function(t,e,n){"use strict";cc._RF.push(e,"53aafBka6RGnZXAOHgpE5ff","Utils"),Object.defineProperty(n,"__esModule",{value:!0});var o=t("../events/Events"),i=window.numeral,r=function(){function t(){}return t.colorIntToHex=function(t){return"#"+t.toString(16).substring(0,6)},t.convertToMoneyFormat=function(t,e){return void 0===e&&(e="0,0.[00]"),i(t).format(e)},t.waitToEventEmit=function(t){return new Promise(function(e){o.Events.Once(t,e)})},t.randomRange=function(t,e,n){void 0===n&&(n=!1);var o=e-t,i=t+Math.random()*o;return n&&(i=Math.round(i)),i},t.waitForSeconds=function(t,e){return new Promise(function(n){e.scheduleOnce(n.bind(e),t)})},t.getAnimDuration=function(t,e){var n=t?t.findAnimation(e):null;return n?n.duration:0},t.resetSkeleton=function(t){t.isAnimationCached()||t.clearTrack(0),t.setToSetupPose()},t.prefSaveBoolean=function(t,e){cc.sys.localStorage.setItem(t,e?"1":"0")},t.prefGetBoolean=function(t,e){void 0===e&&(e=!1);var n=cc.sys.localStorage.getItem(t);return n?"1"===n:e},t.safeGetProperty=function(t,e,n){return e in t?t[e]:n},t.cartesianToPolar=function(t){var e=[t.x,t.y],n=e[0],o=e[1],i=Math.atan2(o,n);return new cc.Vec2(this.getLength(t),i)},t.polarToCartesian=function(t){var e=[t.x,t.y],n=e[0],o=e[1];return new cc.Vec2(n*Math.cos(o),n*Math.sin(o))},t.getLength=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.getDateFromTime=function(t,e){void 0===e&&(e=!1);var n=new Date(t),o=("0"+n.getUTCDate()).substr(-2),i=("0"+(n.getMonth()+1)).substr(-2),r=n.getUTCFullYear();return o+"/"+i+(e?"/"+r:"")},t.getTimeUnixDate=function(t){var e=new Date(t);return e.getHours()+":"+("0"+e.getMinutes()).substr(-2)+":"+("0"+e.getSeconds()).substr(-2)},t.setTintNode=function(e,n){var o=255&n,i=o<<16&16711680|o<<8&65280|255&o;e.color=(new cc.Color).fromHEX(t.colorIntToHex(i))},t.checkReelNearWinActive=function(){return[]},t.getURLParam=function(t){return cc.sys.isNative?"":new URL(window.location.href).searchParams.get(t)},t.toMMSS=function(t,e){void 0===e&&(e=!1);var n,o=Math.floor(t%86400/3600),i=Math.floor(t%3600/60),r=Math.floor(t%60);n=e?i>9?i+":":"0"+i+":":i+":";var a=r>9?r+"":"0"+r;return e?o+":"+n+a:n+a},t}();n.default=r,cc._RF.pop()},{"../events/Events":"Events"}],WebAudioPlayer:[function(t,e,n){"use strict";cc._RF.push(e,"fa7ddY6RSBIQ6OPByD93/zl","WebAudioPlayer");var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),r=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))(function(i,r){function a(t){try{c(o.next(t))}catch(e){r(e)}}function s(t){try{c(o.throw(t))}catch(e){r(e)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(a,s)}c((o=o.apply(t,e||[])).next())})},a=this&&this.__generator||function(t,e){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(t){return function(e){return c([t,e])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=t("./BaseAudioPlayer"),c=t("../AudioResources"),u=t("./AudioPlayerData"),l=t("./AudioSource"),d=t("howler"),p=function(t){function e(e,n){var o=t.call(this,e,n)||this;return o._SUSPEND_CONTEXT_DEVICES=["SM-N970"],o._players=null,o._playingAudios=null,o._pendingEndCallbacks=null,o._savedVolumeCfg=null,o._loadStatus=null,o._isSuspendContext=!1,o._type=u.AudioPlayerType.Web,o}return i(e,t),e.prototype.cleanUp=function(){Howler.mute(!0),this._playingAudios.forEach(function(t){var e=t.source.howl();t.playIds.forEach(function(t){e.playing(t)&&e.stop(t)})}),this._playingAudios.clear(),this._players.clear(),this._pendingEndCallbacks.clear(),this._savedVolumeCfg.clear(),this._SUSPEND_CONTEXT_DEVICES.length=0,t.prototype.cleanUp.call(this)},e.prototype.onLoadAudio=function(t,e,n){n.preload=!1,n.format=c.AudioFormat,n.src=e.nativeUrl;var o=new d.Howl(n);this._players.set(t,{howl:o,clip:e})},e.prototype.onVerifyResources=function(t,e,n){return r(this,void 0,Promise,function(){var o=this;return a(this,function(){return[2,new Promise(function(i,r){o._loadStatus.total=t,o._players.forEach(function(t,a){var s=t.howl;s.on("load",function(){o._shareVars.debug&&cc.warn("[SOUND] Howler Load: "+a);var t=++o._loadStatus.finish;e&&e(t,o._loadStatus.total),t>=o._loadStatus.total&&(o._shareVars.debug&&cc.warn("[SOUND] Howler Load << DONE!"),n&&n(!0),i(!0))}),s.on("loaderror",function(t,e){cc.warn("[SOUND] Howler Load failed: "+a),r(e)}),s.on("playerror",function(){cc.warn("[SOUND] Howler Play error: "+a)}),s.load()})})]})})},e.prototype.init=function(){this._players=new Map,this._playingAudios=new Map,this._pendingEndCallbacks=new Map,this._savedVolumeCfg=new Map,this._loadStatus={finish:0,total:-1};for(var t=!0,e=0;e<this._SUSPEND_CONTEXT_DEVICES.length&&t;e++){var n=this._SUSPEND_CONTEXT_DEVICES[e];-1!==navigator.userAgent.indexOf(n)&&(this._isSuspendContext=!0,t=!1)}Howler.autoSuspend=!1,Howler.volume(1),Howler.mute(!1)},e.prototype.pause=function(t){var e=this._playingAudios.get(t);e&&e.playIds.forEach(function(t){e.source.howl().pause(t)})},e.prototype.resume=function(t){if(this._shareVars.isGamePaused)return cc.warn("[SOUND] "+t+" is resume during game pause, skip!"),!1;var e=!1,n=this._playingAudios.get(t);if(n)for(var o=!0,i=0;i<n.playIds.length&&o;i++){var r=n.playIds[i];(n.source.isMusic&&this._shareVars.isMusicOn||!n.source.isMusic&&this._shareVars.isEffectOn)&&(n.source.howl().play(r),e=!0,o=!1)}return e},e.prototype.resumeBySource=function(t,e,n){if(t.isMusic&&!this._shareVars.isMusicOn||!t.isMusic&&!this._shareVars.isEffectOn)return-1;var o=t.howl(),i=t.playId;return!0===n&&(o.volume(0,i),o.seek(0,i)),o.volume(e,i),o.play(i),i},e.prototype.stop=function(t){var e=this._playingAudios.get(t);e&&e.playIds.forEach(function(t){e.source.howl().stop(t)})},e.prototype.stopByPlayId=function(t){var e=!1,n=this._getPlayAudioByPlayId(t);return n&&n.playIds.forEach(function(t){n.source.howl().stop(t),e=!0}),e},e.prototype.playing=function(t,e){void 0===e&&(e=!1);var n=!1,o=this._playingAudios.get(t);if(o)for(var i=!0,r=0;r<o.playIds.length&&i;r++){var a=o.playIds[r];(o.source.howl().playing(a)||e&&this._howlIsPaused(o.source,a))&&(n=!0,i=!1)}return n},e.prototype.playingByPlayId=function(t){var e=!1,n=this._getPlayAudioByPlayId(t);if(n)for(var o=!0,i=0;i<n.playIds.length&&o;i++){var r=n.playIds[i];n.source.howl().playing(r)&&(e=!0,o=!1)}return e},e.prototype.playAudio=function(t,e,n,o,i,r,a){var s=this,c=e.pack,d=e.sprite,p=this._players.get(c),h=new l.AudioSource(u.AudioPlayerType.Web,p),f=h.howl();if("loaded"!==f.state())return cc.warn("[SOUND] "+c+"("+f.state()+") not load yet!"),-1;var _=f.play(d);h.audioId=t,h.playId=_,h.isMusic=e.isMusic&&a,h.info=e,f.loop(o,_),f.volume(n,_);var v=function(e){if(!s._wasCleanUp)if(s._playingAudios.has(t)){var n=s._playingAudios.get(t);if(n.playIds.length>0){var o=n.playIds.indexOf(e);o>-1?(n.playIds.splice(o,1),n.playIds.includes(e)&&cc.error("[SOUND] Remove "+t+"("+_+") failed!")):cc.warn("[SOUND] "+t+"("+_+") not found in playing audios!"),0===n.playIds.length&&s._playingAudios.delete(t)}}else cc.warn("[SOUND] Stop non-play audio "+t+"("+_+")");f.off("end",null,e),f.off("stop",null,e),r&&(h.isFading?(h.endCallback={owner:h,playId:_,callback:r},s._pendingEndCallbacks.set(t,h)):r(t,e)),--s._shareVars.totalOfPlayingAudios,h.reset()};if(f.once("stop",v.bind(this),_),o||f.once("end",v.bind(this),_),this._playingAudios.has(t)){var y=this._playingAudios.get(t);y.playIds.push(_),y.source&&y.source.howl()!==f&&cc.warn("[SOUND] Diff player of audio "+t+"("+_+")")}else this._playingAudios.set(t,{sId:t,source:h,playIds:[_],interrupt:!1});++e.numberOfPlaying,++this._shareVars.totalOfPlayingAudios;var g=function(){f.playing(_)&&f.seek(_)>=0&&(0===n?(f.seek(0,_),f.pause(_)):0===f.volume(_)&&(h.isMusic&&s._shareVars.isMusicOn||!h.isMusic&&s._shareVars.isEffectOn)&&(f.volume(n,_),cc.warn("[SOUND] "+t+"("+_+"), set vol turn 2: "+f.volume(_)+", queue",f._queue.slice(-1))),i&&i(t,e,h,_,f.duration(_)),s._ctx.unschedule(g))};return this._ctx.schedule(g,.001,cc.macro.REPEAT_FOREVER),_},e.prototype.fade=function(t,e,n,o,i,r,a,s,u){var l,d=this;if(-1!==u)l=this.getPlayingAudioByPlayId(u),!t&&l&&(t=l.audioId);else{var p=this.getPlayingAudio(t);if(!(p.length>0)){var h=this._pendingEndCallbacks.get(t);if(h&&h.endCallback){var f=h.endCallback;f.callback?f.callback(t,f.playId):cc.warn("[SOUND] "+t+" missing callback for pending callback"),h.endCallback=null,this._pendingEndCallbacks.delete(t)}else cc.warn("[SOUND] "+t+" not play, can't fade!");return-1}l=p[0],u=l.playId}if(!l)return cc.error("[SOUND] "+t+" ("+u+") is null, recheck this!"),-1;if(-1===u)return cc.warn("[SOUND] "+t+" not play, cant fade!"),-1;if("loaded"!==l.howl().state())return cc.warn("[SOUND] "+t+" not loaded yet, cant fade!"),-1;var _=this._shareVars.audioConfigs.get(t),v=l.howl(),y=Number.parseFloat(e+""),g=Number.parseFloat(n+"");e=Number.isNaN(y)?v.volume(u):y,n=Number.isNaN(g)?_.volume:g;var m=t+" ("+u+": "+e+" => "+n+") "+o+"s";if(!this._isVolumeValid(e)||!this._isVolumeValid(n))return cc.error("[SOUND] "+m+", error volume range!"),-1;o>=5&&cc.warn("[SOUND] "+m+", time fade too long!");var C=_.isMusic&&!this._shareVars.isMusicOn||!_.isMusic&&!this._shareVars.isEffectOn;if(e===n)return cc.warn("[SOUND] "+m+" fade duration is zero, recheck this!"),0!==n||C||(i?(v.stop(u),this._savedVolumeCfg.delete(u),u=-1):v.pause(u)),a&&a(),r&&r(),u;if(C){_.isMusic&&n>0&&(this._shareVars.bgmId=t),i?v.stop(u):(C&&(l.isLockVolume=!0),v.pause(u));var b=!1;return a&&(b=!0,a()),r&&!b&&r(),u}l.isFading&&(l.isFading=!1,v.off("fade",null,u),l.fadingHandler=null),(n>0&&_&&_.isMusic||c.MusicIds.indexOf(t)>-1)&&(this._shareVars.bgmId=t);var S={onStart:function(){l.isFading=!0,v.playing(u)||v.play(u)},onComplete:function(o){e>n&&(i&&0===n?(v.stop(o),d._shareVars.bgmId===t&&(d._shareVars.bgmId="NONE")):v.pause(o)),r&&r(),l.clearFadeInfo()}};if(a){var w=Number.parseFloat(s+"");(s=Number.isNaN(w)?.75:s)>=0&&s<=1?this._ctx.scheduleOnce(a,s*o):cc.error("[SOUND] "+m+", nearFactor must in range [0, 1]!")}return v.volume(e,u),v.once("fade",S.onComplete.bind(this),u),v.fade(e,n,1e3*o,u),S.onStart(),l.fadingHandler={from:e,to:n,handler:null},u},e.prototype.getPlayingAudio=function(t){var e=this._playingAudios.get(t),n=[];return e&&e.playIds.forEach(function(){n.push(e.source)}),n},e.prototype.getPlayingAudioByPlayId=function(t){if(t<0)return null;var e=this._getPlayAudioByPlayId(t);return e?e.source:null},e.prototype.getPlayingMusic=function(t){var e=null,n=this._playingAudios.get(t);if(n)for(var o=!0,i=0;i<n.playIds.length&&o;i++){var r=n.playIds[i];n.source.howl().playing(r)||(e=n.source,o=!1)}return e},e.prototype.onChangeSoundOption=function(t,e){var n=this;t?(this._playingAudios.forEach(function(t){var o=t.source.howl(),i=t.source.playId,r=t.source.audioId,a=t.source.isMusic;if(!a&&n._savedVolumeCfg.has(i)&&o.playing(i)){var s=n._savedVolumeCfg.get(i);o.volume(s,i),n._savedVolumeCfg.delete(i)}if(e&&a&&r===n._shareVars.bgmId&&(o.play(i),0===o.volume(i))){var c=n._shareVars.audioConfigs.get(r),u=c?c.volume:1;t.source.isLockVolume&&(t.source.isLockVolume=!1,o.seek(0,i)),o.volume(u,i)}}),this._savedVolumeCfg.clear()):this._playingAudios.forEach(function(t){if(t.source.isMusic&&e||!t.source.isMusic&&!e){var o=t.source.howl(),i=t.source.audioId;t.playIds.forEach(function(e){if(o.playing(e)){var r=o.volume(e);n._savedVolumeCfg.set(e,r),t.source.isMusic?o.pause(e):o.volume(0,e)}else cc.error("[SOUND] Cant pause: "+i+"("+e+"), playing: "+o.playing(e))})}})},e.prototype._onInterrupt=function(){this._playingAudios.forEach(function(t){var e=!1;t.playIds.forEach(function(n){var o=t.source.howl();o.playing(n)&&(o.pause(n),e=!0)}),t.interrupt=e}),Howler.mute(!0),this._suspendAudioContext()},e.prototype._onGameResume=function(){var t=this;this._resumeAudioContext().then(function(){t._playingAudios.forEach(function(e){(t._shareVars.isMusicOn||e.sId!==t._shareVars.bgmId&&!e.source.isMusic)&&e.interrupt&&(e.playIds.forEach(function(t){e.source.howl().play(t)}),e.interrupt=!1)}),Howler.mute(!1)}).catch(function(t){Howler.mute(!1),cc.error("[SOUND] Cant Resume Context! "+t)})},e.prototype._resumeAudioContext=function(){var t=this;return new Promise(function(e,n){Howler.ctx?"running"!==Howler.ctx.state?Howler.ctx.resume().then(function(){e()}).catch(function(t){cc.error("[SOUND] ResumeAC: Resume failed! ("+t+")"),n(t)}):e():t._playingAudios.size>0?(cc.error("[SOUND] ResumeAC: AC null"),n("AudioContext is null")):(cc.warn("[SOUND] ResumeAC: AC null on start game, skip"),e())})},e.prototype._suspendAudioContext=function(){var t=this;return new Promise(function(e,n){Howler.ctx?t._isSuspendContext?Howler.ctx.suspend().then(function(){cc.log("[SOUND] InterruptAC: Manual Suspended!"),e()}).catch(function(t){cc.warn("[SOUND] InterruptAC: Manual Suspended, Error "+t),n(t)}):(cc.log("[SOUND] InterruptAC: "+Howler.ctx.state),e()):(cc.warn("[SOUND] InterruptAC: Context is null!"),e())})},e.prototype._howlIsPaused=function(t,e){var n=t.howl()._sounds.filter(function(t){return t._id===e});return n.length>0?n.map(function(t){return t._paused}).reduce(function(t,e){return t||e}):(cc.error("[SOUND] "+t.audioId+" Can't check howl paused!"),!1)},e.prototype._getPlayAudioByPlayId=function(t){for(var e=0,n=Array.from(this._playingAudios.entries());e<n.length;e++){var o=n[e][1];if(o.playIds.includes(t))return o}},e}(s.default);n.default=p,cc._RF.pop()},{"../AudioResources":"AudioResources","./AudioPlayerData":"AudioPlayerData","./AudioSource":"AudioSource","./BaseAudioPlayer":"BaseAudioPlayer",howler:1}],"boolean-fuser":[function(t,e,n){"use strict";cc._RF.push(e,"5aa30QMzARFdIW72QpOS2De","boolean-fuser"),Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function t(t){this._dataMap=new Map,this._currentValue=!0,this._onValueChanged=t}return t.prototype.setValue=function(t,e){return void 0===e&&(e="default"),this._dataMap.set(e,t),this._updateValue(),this._currentValue},t.prototype.getValue=function(){return this._currentValue},t.prototype.clearData=function(){this._dataMap.clear()},t.prototype.setCallback=function(t){this._onValueChanged=t},t.prototype._calculateNewValue=function(){var t=!0;return this._dataMap.forEach(function(e){t&&(t=e)}),t},t.prototype._updateValue=function(){var t=this._calculateNewValue();this._currentValue!==t&&(this._currentValue=t,this.onValueChanged(this._currentValue))},t.prototype.onValueChanged=function(t){"function"==typeof this._onValueChanged&&this._onValueChanged(t)},t}();n.default=o,cc._RF.pop()},{}]},{},["Card","LoadingKlondikeScene","Solitaire","CardSpider","LoadingSpiderScene","SpiderGame","GameRule","InformationPopup","NotificationPopup","PopupManager","ResultPopup","SelectGamePopup","SelectSuitPopup","ShakeAction","BoardScore","MenuSetting","Event","Events","AudioResources","SoundManager","AudioPlayerData","AudioSource","BaseAudioPlayer","WebAudioPlayer","ArrangeRenderOrder","AttachedNode","Easing","FPSInfo","Gameinterface","ObjectPool","ResizeCanvas","SaveGame","Scheduler","Singleton","Utils","boolean-fuser","ShakeCamera"]);