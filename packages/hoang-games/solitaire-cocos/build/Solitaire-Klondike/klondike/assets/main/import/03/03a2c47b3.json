[1, ["ecpdLyjvZBwrvm+cedCcQy", "12Bmab5o5Pt5pD1nGzInXB", "d8VVhMTkVBD7Tw5Bp2m14e", "f0BIwQ8D5Ml7nTNQbh1YlS", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "29J3kA8jtCDYlt67ND1/bj", "7emkzWjVpBYoN2mx8xiwbK", "589VumnAhHeqwWwRHCqz93", "e10un9MTFDsbnT7kxeSHbB", "1aZuym7lJFsbpujfI/nva/", "e6xYuLjlNDJbi1RFh/4qnz", "0dJSxVQIVEz4xfCsFoZnth", "c0toa3vT1HHZ7K5xyLwbqr", "ec0BMkHlFE+av69K9tE7Zz", "52tl/i5lpON45XINPzyH6h", "1eXyulmYxJLbjWK54WE5PN", "a1Hh/Xf29OUb3DB1BC0BYD", "11FgQmHEhIraSEX1uPOmgN", "f2ptjRktJPzKQsRXVh/Tom", "0bzOF3fK1Pfrm7cUI8g0K6", "469dBq5UVLvZLzIjJubSpn", "bdTM5S1URMDY77pBPPwBMx", "8dXoZExapPv7XLTD0HAsKy", "b9sfmzNhRBDLi0U1GPD83S", "66WDwBFaxHe6ONTTxg2EQv", "0bRU22FgVNGr1RNi2KFxtU", "a5UZZJU/lKpqnwIdkT092E", "f2L5Oo/L9OhawqcTD1rvL1", "1f7D3hautMkZtphVruZAeG", "22tfx67L1ArLgTbnif+wAL", "77FkEoKWJM/JADLChwSlXR", "62MiBhiG1IqJYp/tRueSYD", "3ae7efMv1CLq2ilvUY/tQi"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "asset", "_N$normalSprite", "_N$target", "scene", "btnUndo", "text", "loading", "btnSolved", "blockTouch", "boardScore", "container", "tableauStacks", "finalStacks", "soundToggle", "layoutButton", "btnHint", "btnMore", "timer", "score", "highScore", "notificationPopup", "selectMode", "gameRule", "resultPopup", "cardPrefab", "_normalMaterial", "_grayMaterial"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_id", "_components", "_contentSize", "_parent", "_trs", "_children", "_prefab", "_anchorPoint", "_eulerAngles", "_color"], -1, 9, 5, 1, 7, 2, 4, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalHeight", "_bottom", "_originalWidth", "_left", "_right", "alignMode", "node"], -5, 1], ["cc.Label", ["_string", "_N$verticalAlign", "_isSystemFontUsed", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$cacheMode", "_N$overflow", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_N$normalSprite"], 0, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 1, 1, 2, 5, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "_enabled", "node", "_layoutSize"], -3, 1, 5], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_parent", "_children", "_components", "_contentSize", "_trs"], 2, 1, 2, 12, 5, 7], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["e465cQRkatGQYPqV+2UHdun", ["node", "followCanvasNodes"], 3, 1, 2], ["da1aaeVc/hIvoFKzvMQ6duP", ["node", "loading", "text"], 3, 1, 1, 1], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["5e0740n6tRBDo1Dg9KYej0X", ["node"], 3, 1], ["cc.PrefabInfo", ["sync", "root", "asset"], 2, 1, 6], ["7d50ezs4TpNbqWv2ZmzvaOX", ["node", "finalStacks", "tableauStacks", "container", "boardScore", "blockTouch", "btnSolved", "btnUndo", "cardPrefab"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["147b6fFzCdNhL4rC3VJZ1og", ["node", "btnMore", "btnHint", "btnUndo", "layoutButton", "soundToggle"], 3, 1, 1, 1, 1, 1, 1], ["57ac4SQLsZIrIQBu5aHJob8", ["node", "highScore", "score", "timer"], 3, 1, 1, 1, 1], ["6f25d/atJdJnKdZPn1S43YK", ["node", "resultPopup", "gameRule", "selectMode", "notificationPopup"], 3, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9]], [[5, 1, 0, 3, 4, 5, 3], [5, 0, 3, 4, 5, 2], [0, 0, 6, 4, 5, 7, 2], [0, 0, 6, 8, 4, 5, 7, 2], [21, 0, 1, 2, 3], [1, 0, 1, 3, 2, 8, 5], [1, 0, 4, 2, 8, 4], [1, 1, 3, 2, 8, 4], [0, 0, 2, 6, 4, 5, 7, 3], [0, 0, 6, 8, 4, 5, 10, 7, 2], [0, 0, 1, 6, 4, 5, 3], [2, 0, 3, 6, 2, 4, 1, 7, 9, 10, 11, 8], [16, 0, 1, 2, 2], [0, 0, 1, 6, 9, 3], [1, 0, 8, 2], [1, 7, 0, 5, 6, 1, 3, 4, 2, 8, 9], [0, 0, 6, 4, 5, 2], [0, 0, 2, 6, 8, 4, 5, 7, 3], [0, 0, 1, 6, 4, 5, 7, 3], [0, 0, 6, 4, 5, 7, 11, 2], [4, 0, 2, 3, 7, 4, 8, 5, 2], [5, 3, 4, 5, 1], [2, 0, 3, 5, 2, 1, 9, 10, 11, 6], [2, 0, 3, 5, 2, 1, 9, 10, 6], [7, 0, 1, 3], [0, 0, 8, 4, 5, 2], [0, 0, 3, 8, 4, 5, 7, 3], [0, 0, 6, 8, 4, 5, 2], [0, 0, 8, 4, 5, 7, 2], [9, 0, 1, 1], [10, 0, 1, 1], [1, 0, 3, 8, 3], [1, 7, 0, 4, 2, 8, 5], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 3], [15, 0, 1], [3, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 2], [3, 0, 3, 4, 5, 6, 7, 8, 12, 9, 10, 11, 2], [0, 0, 3, 6, 9, 3], [0, 0, 1, 6, 8, 4, 5, 7, 3], [0, 0, 2, 1, 6, 4, 5, 7, 4], [0, 0, 2, 1, 6, 4, 12, 5, 4], [0, 0, 2, 1, 6, 4, 5, 4], [4, 0, 2, 3, 4, 5, 2], [4, 0, 2, 6, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 1], [1, 0, 5, 6, 1, 3, 8, 6], [1, 0, 5, 6, 1, 3, 2, 8, 7], [1, 0, 1, 4, 8, 4], [1, 0, 5, 1, 8, 4], [1, 0, 1, 8, 3], [1, 0, 5, 6, 4, 2, 8, 6], [5, 0, 2, 3, 4, 3], [2, 0, 2, 4, 1, 9, 10, 5], [2, 0, 3, 5, 6, 2, 4, 1, 8, 7, 9, 10, 11, 10], [2, 0, 3, 6, 4, 1, 8, 7, 9, 10, 8], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [6, 0, 1, 3, 4, 2, 6, 7, 6], [6, 5, 0, 1, 3, 4, 2, 6, 7, 7], [6, 5, 0, 1, 2, 6, 7, 5], [18, 0, 1, 2, 3, 4, 5, 1], [19, 0, 1, 2, 3, 1], [20, 0, 1, 2, 3, 4, 1], [3, 3, 4, 5, 6, 7, 8, 12, 9, 10, 11, 1], [3, 1, 0, 3, 4, 3], [3, 2, 0, 3, 4, 5, 6, 7, 8, 3], [22, 0, 1], [23, 0, 1, 2, 3, 4, 5, 2]], [[[[24, "LoadingKlondike", null], [25, "Gameplay", [-8, -9, -10], [[29, -1, [5]], [30, -3, [-2]], [48, -6, -5, -4], [6, 45, 1560, 720, -7]], [5, 1560, 720]], [26, "<PERSON><PERSON>", "01FoM/o9FEpYGrziSXMGnj", [-13, 1], [[33, -11, [5, 1560, 720]], [14, 45, -12]], [5, 1560, 720], [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "New Node", false, [2, -14], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Main Camera", 512, 2, [[35, 0, -1, -15], [36, -16]], [5, 1280, 760], [0, 0, 250.28134169370279, 0, 0, 0, 1, 1, 1, 1]], [16, "Bg", 1, [[1, 0, -17, [0], 1], [6, 45, 100, 100, -18]], [5, 1560, 720]], [2, "load", 1, [[21, -19, [2], 3]], [5, 100, 100], [0, 24.176, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "label", 1, [-20], [5, 159.32, 50.4], [0, -65.746, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "Loading", false, 1, 1, 7, [4]], [39, "SoundManager", "25uudfHodIJJq0GjKa483f", 3, [12, true, -21, 6]]], 0, [0, 0, 1, 0, -1, 1, 0, 0, 1, 0, 13, 8, 0, 14, 6, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 8, 0, 7, 9, 0, 11, 3, 1, 3, 2, 2, 3, 3, 21], [0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 1, -1, -1, 8, 2], [0, 11, 0, 13, 0, 0, 14, 15]], [[[24, "SolitaireGame", null], [25, "Gameplay", [-13, -14, -15, -16, -17, -18, -19, -20], [[59, -8, -7, -6, -5, -4, -3, -2, -1, 117], [29, -9, [118]], [30, -11, [-10]], [6, 45, 1560, 720, -12]], [5, 1560, 720]], [3, "TableauStacks", 1, [-23, -24, -25, -26, -27, -28, -29], [[49, 44, 232.25, 232.25, 141.909, -41.40700000000001, -21], [60, 1, 1, 6, 6, 40, -22, [5, 1095.5, 519.4]]], [5, 1095.5, 519.4], [0, -141.70700000000005, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "MenuGame", 1, [-37, -38, -39, -40], [[63, -35, -34, -33, -32, -31, -30], [6, 45, 1280, 720, -36]], [5, 1560, 720]], [3, "FinalStacks", 1, [-43, -44, -45, -46, -47, -48], [[50, 41, 295.75000000000006, 295.75000000000006, 66.20700000000002, 700, 280, -41], [61, false, 1, 1, 6, 6, 40, -42, [5, 968.4999999999999, 150]]], [5, 968.4999999999999, 150], [0, 218.793, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "BannerScore", 1, [-55, -56, -57], [[1, 0, -49, [65], 66], [64, -53, -52, -51, -50], [51, 41, -4.5, 1560, -54]], [5, 1560, 53], [0, 338, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "layoutSetting", 3, [-61, -62, -63], [[1, 0, -58, [87], 88], [31, 36, -3.1786410173072, -59], [62, false, 1, 1, 180, -60, [5, 973, 92]]], [5, 88.2, 402.1], [0, 0.4828634539268461, 0.5018120890756209], [734.3885566363479, -161.4, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [-70, -71, -72, -73], [[65, -68, -67, -66, -65, -64], [14, 45, -69]], [5, 1560, 720]], [26, "<PERSON><PERSON>", "01FoM/o9FEpYGrziSXMGnj", [-76, 1, -77], [[33, -74, [5, 1560, 720]], [14, 45, -75]], [5, 1560, 720], [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnMore", 3, [-81, -82], [[66, -79, [[4, "147b6fFzCdNhL4rC3VJZ1og", "openMenu", 3]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -78, 94, 95, 96, 97], [31, 36, -0.8000000000000114, -80]], [5, 87.6, 77], [736.2, -322.3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnHint", 3, [-86, -87], [[37, 3, -84, [[4, "7d50ezs4TpNbqWv2ZmzvaOX", "showHint", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -83, 104, 105, 106], [52, 9, 1, 48.99599999999998, -85]], [5, 91, 91], [-733.5, 265.504, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "btnUndo", 3, [-90, -91], [[-88, [53, 33, 48.476999999999975, -89]], 1, 4], [5, 91, 92], [734.5, 265.523, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_6", 4, [-94, -95], [[0, 1, 0, -92, [22], 23], [7, -25, -25, 200, -93]], [5, 100, 139], [-423.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "SolvedBtn", false, 1, [-98, -99], [[38, 3, -97, [[4, "7d50ezs4TpNbqWv2ZmzvaOX", "onSolved", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -96, 46, 47, 48, 49]], [5, 57.3, 54.4], [-312.524, 222.082, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Background", 512, 13, [-102], [[0, 1, 0, -100, [42], 43], [32, 0, 45, 100, 40, -101]], [5, 57.3, 54.4], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [28, "Background", [-105], [[0, 1, 0, -103, [69], 70], [15, 0, 45, 0.5, 0.5, 0.5, 0.5, 100, 40, -104]], [5, 36, 50], [0.2053570297967937, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [45, "btnSound", 6, [-107, -108, -109], [-106], [5, 70, 70], [1.539, 45.289, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "Background", [-112], [[0, 1, 0, -110, [82], 83], [15, 0, 45, -3.6999999999999993, -3.6999999999999993, -2.3370000000000033, 7.537000000000006, 100, 40, -111]], [5, 56, 56], [0, 4.937000000000005, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "Background", 512, 9, [-115], [[0, 1, 0, -113, [90], 91], [32, 0, 45, 100, 40, -114]], [5, 87.6, 77], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "Background", 512, 10, [-118], [[0, 1, 0, -116, [102], 103], [15, 0, 45, 30.5, 30.5, 11.756, 35.244, 100, 40, -117]], [5, 30, 44], [0, 11.744, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "Background", 11, [-121], [[0, 1, 0, -119, [111], 112], [15, 0, 45, 22.874000000000002, 22.125999999999998, 14.21, 35.79, 100, 40, -120]], [5, 46, 42], [0.37400000000000233, 10.79, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "col_1", 4, [-124], [[0, 1, 0, -122, [4], 5], [7, -25, -25, 200, -123]], [5, 100, 139], [-27.80000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_2", 4, [-127], [[0, 1, 0, -125, [8], 9], [7, -25, -25, 200, -126]], [5, 100, 139], [122.49999999999996, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_3", 4, [-130], [[0, 1, 0, -128, [12], 13], [7, -25, -25, 200, -129]], [5, 100, 139], [272.79999999999995, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_4", 4, [-133], [[0, 1, 0, -131, [16], 17], [7, -25, -25, 200, -132]], [5, 100, 139], [423.0999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "HighScore", 5, [-135, -136], [[22, "High Score:", 25, 30, false, 1, -134, [53], 54]], [5, 140.82, 37.8], [0, 0, 0.5], [-337.263, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Score", 5, [-138, -139], [[22, "Score:", 25, 30, false, 1, -137, [58], 59]], [5, 75.32, 37.8], [0, 0, 0.5], [-11.634, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Time", 5, [-141, -142], [[22, "Time:", 25, 30, false, 1, -140, [63], 64]], [5, 66.42, 37.8], [0, 0, 0.5], [270.091, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "BlockInput", false, 8, [[6, 45, 1280, 720, -143], [69, -144]], [5, 1280, 720]], [8, "Main Camera", 512, 8, [[35, 0, -1, -145], [36, -146]], [5, 1280, 760], [0, 0, 250.28134169370279, 0, 0, 0, 1, 1, 1, 1]], [16, "Bg", 1, [[1, 0, -147, [0], 1], [6, 45, 100, 100, -148]], [5, 1560, 720]], [3, "refresh", 12, [-150], [[67, 1.02, 3, -149, [[4, "7d50ezs4TpNbqWv2ZmzvaOX", "resetCards", 1]]]], [5, 81.5, 119.8], [1.666, 0.9, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "col_5", 4, [-152], [[7, -25, -25, 200, -151]], [5, 205, 160.75], [-225.**************, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "bg", false, 32, [[0, 1, 0, -153, [24], 25], [54, 45, 20, 10, 100, 100, -154]], [5, 175, 160.75], [5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_1", 2, [[0, 1, 0, -155, [26], 27], [5, 1, 2.5, -25, 200, -156]], [5, 120.5, 160.75], [-481.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_2", 2, [[0, 1, 0, -157, [28], 29], [5, 1, 2.5, -25, 200, -158]], [5, 120.5, 160.75], [-321, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_3", 2, [[0, 1, 0, -159, [30], 31], [5, 1, 2.5, -25, 200, -160]], [5, 120.5, 160.75], [-160.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_4", 2, [[0, 1, 0, -161, [32], 33], [5, 1, 2.5, -25, 200, -162]], [5, 120.5, 160.75], [0, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_5", 2, [[0, 1, 0, -163, [34], 35], [5, 1, 2.5, -25, 200, -164]], [5, 120.5, 160.75], [160.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_6", 2, [[0, 1, 0, -165, [36], 37], [5, 1, 2.5, -25, 200, -166]], [5, 120.5, 160.75], [321, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_7", 2, [[0, 1, 0, -167, [38], 39], [5, 1, 2.5, -25, 200, -168]], [5, 120.5, 160.75], [481.5, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Container", 1, [[14, 45, -169]], [5, 1560, 720]], [9, "gameRuleBtn", 6, [15], [[38, 3, -170, [[4, "147b6fFzCdNhL4rC3VJZ1og", "openGameRule", 3]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 15, 71, 72, 73, 74]], [5, 37, 51], [0, 0.49444981000549204, 0.5], [0.416, -57.963, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "Background", 512, false, 16, [[21, -171, [75], 76]], [5, 54, 44], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btnNew", 6, [17], [[37, 3, -172, [[4, "147b6fFzCdNhL4rC3VJZ1og", "openNewPlayKlondike", 3]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 17, 84, 85, 86]], [5, 48.6, 61.2], [2.721, 153.414, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "SelectModeGamePopup", false, 7, [12, true, -173, 113]], [13, "NotificationPopup", false, 7, [12, true, -174, 114]], [13, "GameRule", false, 7, [12, true, -175, 115]], [13, "ResultPopup", false, 7, [12, true, -176, 116]], [34, "New Node", false, [8], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "boder_highlight", false, 21, [[1, 0, -177, [2], 3]], [5, 110, 142]], [10, "boder_highlight", false, 22, [[1, 0, -178, [6], 7]], [5, 110, 142]], [10, "boder_highlight", false, 23, [[1, 0, -179, [10], 11]], [5, 110, 142]], [10, "boder_highlight", false, 24, [[1, 0, -180, [14], 15]], [5, 110, 142]], [2, "empty_card", 31, [[21, -181, [18], 19]], [5, 116, 116], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [18, "boder_highlight", false, 12, [[1, 0, -182, [20], 21]], [5, 120, 155], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Label", 512, 14, [[57, "Solved", 20, 30, false, false, 1, 1, 2, 1, -183, [40], 41]], [5, 46.3, 25.8], [-0.285, -12.301, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon_tick", 512, 13, [[1, 0, -184, [44], 45]], [5, 40, 30], [2.279, 9.399, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "highscore_64_update", 25, [[1, 0, -185, [50], 51]], [5, 30, 44], [-20.999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "label", 25, [-186], [4, 4279173100], [5, 15.77, 37.8], [0, 0, 0.5], [147.987, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "0", 25, 30, false, 1, 59, [52]], [19, "star_3", 26, [[1, 0, -187, [55], 56]], [5, 46, 46], [-21.687, -1.098, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 1, 1, 1], [1, 0, 0, -5]], [20, "label", 26, [-188], [4, 4279173100], [5, 15.77, 37.8], [0, 0, 0.5], [81.923, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "0", 25, 30, false, 1, 62, [57]], [19, "clock_33", 27, [[1, 0, -189, [60], 61]], [5, 25, 36], [-17.587, -0.601, 0, 0, 0, -0.06104853953485687, 0.9981347984218669, 1, 1, 1], [1, 0, 0, -7]], [20, "label", 27, [-190], [4, 4279173100], [5, 15.77, 37.8], [0, 0, 0.5], [72.145, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "0", 25, 30, false, 1, 65, [62]], [2, "Label", 15, [[11, "Rules", 20, false, false, 1, 1, 2, -191, [67], 68]], [5, 54.58, 40], [0, -38.962, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "checkmark", 512, 16, [-192], [5, 54, 44]], [55, 0, false, 68, [77]], [2, "Label", 16, [[11, "Sound", 20, false, false, 1, 1, 2, -193, [78], 79]], [5, 64.12, 40], [0.6, -38.455, 0, 0, 0, 0, 1, 1, 1, 1]], [70, 2, 16, [4, 4292269782], 43, 69, [[4, "147b6fFzCdNhL4rC3VJZ1og", "onSoundToggleCheck", 3]]], [2, "Label", 17, [[11, "New", 20, false, false, 1, 1, 1, -194, [80], 81]], [5, 44.34, 50.4], [-0.129, -42.999, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "Label", 512, false, 18, [[58, "button", 20, false, 1, 1, 1, 1, -195, [89]]], [4, 4278190080], [5, 100, 40]], [19, "iconMore", 9, [[1, 0, -196, [92], 93]], [5, 43.12, 21.56], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [43, "banner_bot", 512, false, 10, [[1, 0, -197, [98], 99]], [5, 91.1, 92]], [8, "Label", 512, 19, [[11, "Hint", 20, false, false, 1, 1, 1, -198, [100], 101]], [5, 41.18, 50.4], [0.6, -38.284, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "banner_bot", false, 11, [[1, 0, -199, [107], 108]], [5, 91.1, 92], [0.2, 0.384, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 20, [[11, "Undo", 20, false, false, 1, 1, 1, -200, [109], 110]], [5, 52.46, 50.4], [0.6, -36.489, 0, 0, 0, 0, 1, 1, 1, 1]], [68, true, 3, 11, [[4, "7d50ezs4TpNbqWv2ZmzvaOX", "undo", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 20]], 0, [0, 12, 79, 0, 15, 13, 0, 16, 28, 0, 17, 5, 0, 18, 41, 0, 19, 2, 0, 20, 4, 0, 0, 1, 0, 0, 1, 0, -1, 1, 0, 0, 1, 0, 0, 1, 0, -1, 30, 0, -2, 4, 0, -3, 2, 0, -4, 41, 0, -5, 13, 0, -6, 5, 0, -7, 3, 0, -8, 7, 0, 0, 2, 0, 0, 2, 0, -1, 34, 0, -2, 35, 0, -3, 36, 0, -4, 37, 0, -5, 38, 0, -6, 39, 0, -7, 40, 0, 21, 71, 0, 22, 6, 0, 12, 11, 0, 23, 10, 0, 24, 9, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 4, 0, 0, 4, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, -4, 24, 0, -5, 12, 0, -6, 32, 0, 0, 5, 0, 25, 66, 0, 26, 63, 0, 27, 60, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 42, 0, -2, 16, 0, -3, 44, 0, 28, 46, 0, 29, 45, 0, 30, 47, 0, 31, 48, 0, 0, 7, 0, 0, 7, 0, -1, 45, 0, -2, 46, 0, -3, 47, 0, -4, 48, 0, 0, 8, 0, 0, 8, 0, -1, 29, 0, -3, 28, 0, 10, 18, 0, 0, 9, 0, 0, 9, 0, -1, 18, 0, -2, 74, 0, 10, 19, 0, 0, 10, 0, 0, 10, 0, -1, 75, 0, -2, 19, 0, -1, 79, 0, 0, 11, 0, -1, 77, 0, -2, 20, 0, 0, 12, 0, 0, 12, 0, -1, 31, 0, -2, 55, 0, 10, 14, 0, 0, 13, 0, -1, 14, 0, -2, 57, 0, 0, 14, 0, 0, 14, 0, -1, 56, 0, 0, 15, 0, 0, 15, 0, -1, 67, 0, -1, 71, 0, -1, 43, 0, -2, 68, 0, -3, 70, 0, 0, 17, 0, 0, 17, 0, -1, 72, 0, 0, 18, 0, 0, 18, 0, -1, 73, 0, 0, 19, 0, 0, 19, 0, -1, 76, 0, 0, 20, 0, 0, 20, 0, -1, 78, 0, 0, 21, 0, 0, 21, 0, -1, 50, 0, 0, 22, 0, 0, 22, 0, -1, 51, 0, 0, 23, 0, 0, 23, 0, -1, 52, 0, 0, 24, 0, 0, 24, 0, -1, 53, 0, 0, 25, 0, -1, 58, 0, -2, 59, 0, 0, 26, 0, -1, 61, 0, -2, 62, 0, 0, 27, 0, -1, 64, 0, -2, 65, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -1, 54, 0, 0, 32, 0, -1, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 7, 45, 0, 7, 46, 0, 7, 47, 0, 7, 48, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, -1, 60, 0, 0, 61, 0, -1, 63, 0, 0, 64, 0, -1, 66, 0, 0, 67, 0, -1, 69, 0, 0, 70, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 11, 49, 1, 3, 8, 8, 3, 49, 15, 3, 42, 17, 3, 44, 200], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 63, 66, 69, 71, 79, 79, 79, 79, 79], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, 9, 4, 5, 6, -1, 1, -1, -1, 2, -1, 1, -1, -1, 2, -1, 1, -1, -1, 2, -1, 1, -1, 2, -1, 1, 9, 4, 5, 6, -1, 1, -1, -1, 2, -1, 2, -1, 1, 4, 5, 6, -1, 1, -1, -1, 1, -1, 1, 9, 4, 5, 6, -1, 1, -1, 2, -1, 1, 4, 5, 6, -1, 1, -1, 2, -1, 1, 8, 8, 8, 8, 32, -1, 2, 2, 2, 1, 9, 33, 34, 4, 5, 6], [0, 11, 0, 6, 0, 7, 0, 6, 0, 7, 0, 6, 0, 7, 0, 6, 0, 7, 0, 16, 0, 6, 0, 7, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 1, 0, 8, 0, 17, 8, 4, 3, 5, 0, 18, 0, 0, 1, 0, 19, 0, 0, 1, 0, 20, 0, 0, 1, 0, 21, 0, 9, 0, 22, 3, 4, 3, 5, 0, 12, 0, 0, 9, 0, 9, 0, 23, 4, 3, 5, 0, 10, 0, 0, 8, 0, 24, 3, 4, 3, 5, 0, 10, 0, 1, 0, 25, 4, 3, 5, 0, 10, 0, 1, 0, 26, 27, 28, 29, 30, 31, 0, 1, 1, 1, 32, 12, 0, 33, 4, 3, 5]]]]