!function(e,r){"function"==typeof define&&define.amd?define(r):"object"==typeof module&&module.exports?module.exports=r():e.numeral=r()}(this,function(){function e(e,r){this._input=e,this._value=r}var r,t,n={},i={},o={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},a={currentLocale:o.currentLocale,zeroFormat:o.zeroFormat,nullFormat:o.nullFormat,defaultFormat:o.defaultFormat,scalePercentBy100:o.scalePercentBy100};return(r=function(i){var o,l,u,s;if(r.isNumeral(i))o=i.value();else if(0===i||void 0===i)o=0;else if(null===i||t.isNaN(i))o=null;else if("string"==typeof i)if(a.zeroFormat&&i===a.zeroFormat)o=0;else if(a.nullFormat&&i===a.nullFormat||!i.replace(/[^0-9]+/g,"").length)o=null;else{for(l in n)if((s="function"==typeof n[l].regexps.unformat?n[l].regexps.unformat():n[l].regexps.unformat)&&i.match(s)){u=n[l].unformat;break}o=(u=u||r._.stringToNumber)(i)}else o=Number(i)||null;return new e(i,o)}).version="2.0.6",r.isNumeral=function(r){return r instanceof e},r._=t={numberToFormat:function(e,t,n){var o,a,l,u,s,c,f,m,h=i[r.options.currentLocale],d=!1,b=!1,p="",g=1e12,v="",_=!1;if(e=e||0,a=Math.abs(e),r._.includes(t,"(")?(d=!0,t=t.replace(/[\(|\)]/g,"")):(r._.includes(t,"+")||r._.includes(t,"-"))&&(s=r._.includes(t,"+")?t.indexOf("+"):0>e?t.indexOf("-"):-1,t=t.replace(/[\+|\-]/g,"")),r._.includes(t,"a")&&(o=!!(o=t.match(/a(k|m|b|t)?/))&&o[1],r._.includes(t," a")&&(p=" "),t=t.replace(new RegExp(p+"a[kmbt]?"),""),a>=g&&!o||"t"===o?(p+=h.abbreviations.trillion,e/=g):g>a&&a>=1e9&&!o||"b"===o?(p+=h.abbreviations.billion,e/=1e9):1e9>a&&a>=1e6&&!o||"m"===o?(p+=h.abbreviations.million,e/=1e6):(1e6>a&&a>=1e3&&!o||"k"===o)&&(p+=h.abbreviations.thousand,e/=1e3)),r._.includes(t,"[.]")&&(b=!0,t=t.replace("[.]",".")),l=e.toString().split(".")[0],u=t.split(".")[1],c=t.indexOf(","),m=(t.split(".")[0].split(",")[0].match(/0/g)||[]).length,u?(r._.includes(u,"[")?(u=(u=u.replace("]","")).split("["),v=r._.toFixed(e,u[0].length+u[1].length,n,u[1].length)):v=r._.toFixed(e,u.length,n),l=v.split(".")[0],v=r._.includes(v,".")?h.delimiters.decimal+v.split(".")[1]:"",b&&0===Number(v.slice(1))&&(v="")):l=r._.toFixed(e,0,n),p&&!o&&Number(l)>=1e3&&p!==h.abbreviations.trillion)switch(l=String(Number(l)/1e3),p){case h.abbreviations.thousand:p=h.abbreviations.million;break;case h.abbreviations.million:p=h.abbreviations.billion;break;case h.abbreviations.billion:p=h.abbreviations.trillion}if(r._.includes(l,"-")&&(l=l.slice(1),_=!0),l.length<m)for(var y=m-l.length;y>0;y--)l="0"+l;return c>-1&&(l=l.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+h.delimiters.thousands)),0===t.indexOf(".")&&(l=""),f=l+v+(p||""),d?f=(d&&_?"(":"")+f+(d&&_?")":""):s>=0?f=0===s?(_?"-":"+")+f:f+(_?"-":"+"):_&&(f="-"+f),f},stringToNumber:function(e){var r,t,n,o=i[a.currentLocale],l=e,u={thousand:3,million:6,billion:9,trillion:12};if(a.zeroFormat&&e===a.zeroFormat)t=0;else if(a.nullFormat&&e===a.nullFormat||!e.replace(/[^0-9]+/g,"").length)t=null;else{for(r in t=1,"."!==o.delimiters.decimal&&(e=e.replace(/\./g,"").replace(o.delimiters.decimal,".")),u)if(n=new RegExp("[^a-zA-Z]"+o.abbreviations[r]+"(?:\\)|(\\"+o.currency.symbol+")?(?:\\))?)?$"),l.match(n)){t*=Math.pow(10,u[r]);break}t*=(e.split("-").length+Math.min(e.split("(").length-1,e.split(")").length-1))%2?1:-1,e=e.replace(/[^0-9\.]+/g,""),t*=Number(e)}return t},isNaN:function(e){return"number"==typeof e&&isNaN(e)},includes:function(e,r){return-1!==e.indexOf(r)},insert:function(e,r,t){return e.slice(0,t)+r+e.slice(t)},reduce:function(e,r){if(null===this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof r)throw new TypeError(r+" is not a function");var t,n=Object(e),i=n.length>>>0,o=0;if(3===arguments.length)t=arguments[2];else{for(;i>o&&!(o in n);)o++;if(o>=i)throw new TypeError("Reduce of empty array with no initial value");t=n[o++]}for(;i>o;o++)o in n&&(t=r(t,n[o],o,n));return t},multiplier:function(e){var r=e.toString().split(".");return r.length<2?1:Math.pow(10,r[1].length)},correctionFactor:function(){var e=Array.prototype.slice.call(arguments);return e.reduce(function(e,r){var n=t.multiplier(r);return e>n?e:n},1)},toFixed:function(e,r,t,n){var i,o,a,l,u=e.toString().split("."),s=r-(n||0);return i=2===u.length?Math.min(Math.max(u[1].length,s),r):s,a=Math.pow(10,i),l=(t(e+"e+"+i)/a).toFixed(i),n>r-i&&(o=new RegExp("\\.?0{1,"+(n-(r-i))+"}$"),l=l.replace(o,"")),l}},r.options=a,r.formats=n,r.locales=i,r.locale=function(e){return e&&(a.currentLocale=e.toLowerCase()),a.currentLocale},r.localeData=function(e){if(!e)return i[a.currentLocale];if(e=e.toLowerCase(),!i[e])throw new Error("Unknown locale : "+e);return i[e]},r.reset=function(){for(var e in o)a[e]=o[e]},r.zeroFormat=function(e){a.zeroFormat="string"==typeof e?e:null},r.nullFormat=function(e){a.nullFormat="string"==typeof e?e:null},r.defaultFormat=function(e){a.defaultFormat="string"==typeof e?e:"0.0"},r.register=function(e,r,t){if(r=r.toLowerCase(),this[e+"s"][r])throw new TypeError(r+" "+e+" already registered.");return this[e+"s"][r]=t,t},r.validate=function(e,t){var n,i,o,a,l,u,s,c;if("string"!=typeof e&&(e+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",e)),(e=e.trim()).match(/^\d+$/))return!0;if(""===e)return!1;try{s=r.localeData(t)}catch(f){s=r.localeData(r.locale())}return o=s.currency.symbol,l=s.abbreviations,n=s.delimiters.decimal,i="."===s.delimiters.thousands?"\\.":s.delimiters.thousands,!(null!==(c=e.match(/^[^\d]+/))&&(e=e.substr(1),c[0]!==o)||null!==(c=e.match(/[^\d]+$/))&&(e=e.slice(0,-1),c[0]!==l.thousand&&c[0]!==l.million&&c[0]!==l.billion&&c[0]!==l.trillion)||(u=new RegExp(i+"{2}"),e.match(/[^\d.,]/g)||(a=e.split(n)).length>2||(a.length<2?!a[0].match(/^\d+.*\d$/)||a[0].match(u):1===a[0].length?!a[0].match(/^\d+$/)||a[0].match(u)||!a[1].match(/^\d+$/):!a[0].match(/^\d+.*\d$/)||a[0].match(u)||!a[1].match(/^\d+$/))))},r.fn=e.prototype={clone:function(){return r(this)},format:function(e,t){var i,o,l,u=this._value,s=e||a.defaultFormat;if(t=t||Math.round,0===u&&null!==a.zeroFormat)o=a.zeroFormat;else if(null===u&&null!==a.nullFormat)o=a.nullFormat;else{for(i in n)if(s.match(n[i].regexps.format)){l=n[i].format;break}o=(l=l||r._.numberToFormat)(u,s,t)}return o},value:function(){return this._value},input:function(){return this._input},set:function(e){return this._value=Number(e),this},add:function(e){var r=t.correctionFactor.call(null,this._value,e);return this._value=t.reduce([this._value,e],function(e,t){return e+Math.round(r*t)},0)/r,this},subtract:function(e){var r=t.correctionFactor.call(null,this._value,e);return this._value=t.reduce([e],function(e,t){return e-Math.round(r*t)},Math.round(this._value*r))/r,this},multiply:function(e){return this._value=t.reduce([this._value,e],function(e,r){var n=t.correctionFactor(e,r);return Math.round(e*n)*Math.round(r*n)/Math.round(n*n)},1),this},divide:function(e){return this._value=t.reduce([this._value,e],function(e,r){var n=t.correctionFactor(e,r);return Math.round(e*n)/Math.round(r*n)}),this},difference:function(e){return Math.abs(r(this._value).subtract(e).value())}},r.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var r=e%10;return 1==~~(e%100/10)?"th":1===r?"st":2===r?"nd":3===r?"rd":"th"},currency:{symbol:"$"}}),r.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(e,t,n){var i,o=r._.includes(t," BPS")?" ":"";return e*=1e4,t=t.replace(/\s?BPS/,""),i=r._.numberToFormat(e,t,n),r._.includes(i,")")?((i=i.split("")).splice(-1,0,o+"BPS"),i=i.join("")):i=i+o+"BPS",i},unformat:function(e){return+(1e-4*r._.stringToNumber(e)).toFixed(15)}}),function(){var e={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},t={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},n=e.suffixes.concat(t.suffixes.filter(function(r){return e.suffixes.indexOf(r)<0})).join("|");n="("+n.replace("B","B(?!PS)")+")",r.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(n)},format:function(n,i,o){var a,l,u,s=r._.includes(i,"ib")?t:e,c=r._.includes(i," b")||r._.includes(i," ib")?" ":"";for(i=i.replace(/\s?i?b/,""),a=0;a<=s.suffixes.length;a++)if(l=Math.pow(s.base,a),u=Math.pow(s.base,a+1),null===n||0===n||n>=l&&u>n){c+=s.suffixes[a],l>0&&(n/=l);break}return r._.numberToFormat(n,i,o)+c},unformat:function(n){var i,o,a=r._.stringToNumber(n);if(a){for(i=e.suffixes.length-1;i>=0;i--){if(r._.includes(n,e.suffixes[i])){o=Math.pow(e.base,i);break}if(r._.includes(n,t.suffixes[i])){o=Math.pow(t.base,i);break}}a*=o||1}return a}})}(),r.register("format","currency",{regexps:{format:/(\$)/},format:function(e,t,n){var i,o,a=r.locales[r.options.currentLocale],l={before:t.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:t.match(/([\+|\-|\)|\s|\$]*)$/)[0]};for(t=t.replace(/\s?\$\s?/,""),i=r._.numberToFormat(e,t,n),e>=0?(l.before=l.before.replace(/[\-\(]/,""),l.after=l.after.replace(/[\-\)]/,"")):0>e&&!r._.includes(l.before,"-")&&!r._.includes(l.before,"(")&&(l.before="-"+l.before),o=0;o<l.before.length;o++)switch(l.before[o]){case"$":i=r._.insert(i,a.currency.symbol,o);break;case" ":i=r._.insert(i," ",o+a.currency.symbol.length-1)}for(o=l.after.length-1;o>=0;o--)switch(l.after[o]){case"$":i=o===l.after.length-1?i+a.currency.symbol:r._.insert(i,a.currency.symbol,-(l.after.length-(1+o)));break;case" ":i=o===l.after.length-1?i+" ":r._.insert(i," ",-(l.after.length-(1+o)+a.currency.symbol.length-1))}return i}}),r.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(e,t,n){var i=("number"!=typeof e||r._.isNaN(e)?"0e+0":e.toExponential()).split("e");return t=t.replace(/e[\+|\-]{1}0/,""),r._.numberToFormat(Number(i[0]),t,n)+"e"+i[1]},unformat:function(e){var t=r._.includes(e,"e+")?e.split("e+"):e.split("e-"),n=Number(t[0]),i=Number(t[1]);return i=r._.includes(e,"e-")?i*=-1:i,r._.reduce([n,Math.pow(10,i)],function(e,t){var n=r._.correctionFactor(e,t);return e*n*t*n/(n*n)},1)}}),r.register("format","ordinal",{regexps:{format:/(o)/},format:function(e,t,n){var i=r.locales[r.options.currentLocale],o=r._.includes(t," o")?" ":"";return t=t.replace(/\s?o/,""),o+=i.ordinal(e),r._.numberToFormat(e,t,n)+o}}),r.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(e,t,n){var i,o=r._.includes(t," %")?" ":"";return r.options.scalePercentBy100&&(e*=100),t=t.replace(/\s?\%/,""),i=r._.numberToFormat(e,t,n),r._.includes(i,")")?((i=i.split("")).splice(-1,0,o+"%"),i=i.join("")):i=i+o+"%",i},unformat:function(e){var t=r._.stringToNumber(e);return r.options.scalePercentBy100?.01*t:t}}),r.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(e){var r=Math.floor(e/60/60),t=Math.floor((e-3600*r)/60),n=Math.round(e-3600*r-60*t);return r+":"+(10>t?"0"+t:t)+":"+(10>n?"0"+n:n)},unformat:function(e){var r=e.split(":"),t=0;return 3===r.length?(t+=3600*Number(r[0]),t+=60*Number(r[1]),t+=Number(r[2])):2===r.length&&(t+=60*Number(r[0]),t+=Number(r[1])),Number(t)}}),r});