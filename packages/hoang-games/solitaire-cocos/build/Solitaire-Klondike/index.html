﻿<!DOCTYPE html>
<html>
<head>
  <title>Solitaire Klondike</title>
  <meta charset="utf-8">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <style>
    html {
      -ms-touch-action: none;
    }
    body, canvas, div {
      display: block;
      outline: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    
      user-select: none;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      -khtml-user-select: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }
    body {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 0;
      border: 0;
      margin: 0;

      cursor: default;
      color: #888;
      background-color: #333;

      text-align: center;
      font-family: Helvetica, Verdana, Arial, sans-serif;

      display: flex;
      flex-direction: column;
      
      /* overflow: hidden; */
    }
    #game {
      position: block;
      margin: auto;
      display: flex;
      width: 730px;
      aspect-ratio: 1280/720; /* (1280 -> 1560)/720 */
      background-color: #dcdcdc;
      align-items: center;
    }
    #game canvas {
      display: flex;
      width: 100%;
      height: 100%;
    }
    #btn {
      width: 100px;
      height: 30px;
      top: 5px;
      position: relative;
    }
  </style>
</head>
<body>
  <div id="shell">
    <div id="game">
      <script src="klondike/entry.js" charset="utf-8" onload="document.querySelector('#btn').disabled = false;" defer></script>
    </div>
    <button id="btn" onclick="document.querySelector('#btn').remove(); globalThis.jg.load();" disabled>Start Game</button>
  </div>
</body>
</html>
