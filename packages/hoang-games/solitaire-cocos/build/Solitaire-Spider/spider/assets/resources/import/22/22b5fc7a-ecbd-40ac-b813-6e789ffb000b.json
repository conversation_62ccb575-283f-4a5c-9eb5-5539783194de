[1, ["ecpdLyjvZBwrvm+cedCcQy", "12Bmab5o5Pt5pD1nGzInXB", "e10un9MTFDsbnT7kxeSHbB", "aff92mttJEkpP2rDVeoefb", "6a8cYYLflLjYykEBlvh1/x", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "9cY6JWd8NNOaOQ5dCYmurq", "f2ptjRktJPzKQsRXVh/Tom", "0bzOF3fK1Pfrm7cUI8g0K6", "11FgQmHEhIraSEX1uPOmgN", "5ctdTDq8NEopiwExXi7wmw", "4bn2lsqHlKYragVNfZBShy"], ["node", "_spriteFrame", "_N$file", "root", "_parent", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "timer", "score", "titleLbl", "blurBg", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_color", "_anchorPoint"], 1, 9, 4, 5, 2, 1, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_enableWrapText", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_top", "_verticalCenter", "_horizontalCenter", "node"], -4, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs"], 1, 1, 12, 4, 5, 5, 7], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 2, 4, 5, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["f19464Eh2dAnZnvHwz5tHZI", ["node", "blurBg", "titleLbl", "score", "timer"], 3, 1, 1, 1, 1, 1]], [[4, 0, 1, 2, 2], [4, 0, 1, 2], [0, 0, 6, 2, 3, 4, 7, 2], [3, 0, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4, 5, 6, 7, 2], [6, 0, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 6, 5, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 7, 2], [0, 0, 6, 5, 2, 3, 4, 7, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 6, 5, 2, 3, 4, 9, 7, 2], [5, 0, 1, 2, 3, 4, 6, 5, 3], [5, 0, 2, 3, 4, 5, 7, 2], [3, 0, 2, 3, 2], [3, 1, 0, 2, 3, 4, 3], [2, 0, 1, 2, 7, 4], [2, 3, 0, 1, 2, 7, 5], [2, 0, 4, 5, 6, 7, 5], [2, 0, 7, 2], [4, 1, 2, 1], [1, 0, 1, 2, 4, 3, 8, 9, 10, 6], [1, 0, 1, 6, 2, 4, 3, 8, 9, 7], [1, 0, 1, 5, 2, 3, 8, 9, 6], [1, 0, 1, 2, 8, 9, 10, 4], [1, 0, 1, 5, 2, 3, 7, 8, 9, 7], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [9, 0, 1, 2, 3], [10, 0, 1], [11, 0, 1, 2, 3, 4, 1]], [[5, "ResultPopup"], [6, "ResultPopup", [-7], [[29, -6, -5, -4, -3, -2]], [20, -1, 0], [5, 1560, 720]], [8, "result-popup", [-9, -10, -11, -12, -13, -14], [[3, 0, -8, [22], 23]], [0, "e07P8e+5ZPNKy/hFHnYlct", 1, 0], [5, 718, 471], [0, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Panel", 1, [-17, 2], [[19, 45, -15], [28, -16]], [0, "40Jrqg/R1K5Zv7WD5bnq0l", 1, 0], [5, 1560, 720]], [10, "Background", 512, [-20], [[15, 1, 0, -18, [3], 4], [17, 0, 45, 100, 40, -19]], [0, "f1T4KdJaBFBpKAQ+3sEx3H", 1, 0], [4, 4293322470], [5, 227, 64]], [4, "score", 2, [-23], [-22], [1, "6bvh1gkABLP5FqmI57/9uE", -21], [5, 137.83, 50.4], [0, 0, 0.5], [-94.213, 71.061, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "highScore", 2, [-26], [[24, "High score: 0", 35, false, -25, [17], 18]], [1, "16fbSmNtJOjoqbHoiCCbD7", -24], [5, 224.56, 50.4], [0, 0, 0.5], [-93.378, -63.552, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "time", 2, [-29], [-28], [1, "6fnHiFxyhPkZMrdiXbOuwV", -27], [5, 399, 56], [0, 0, 0.5], [-93.602, 5.448, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "BG", 150, 3, [[-30, [16, 45, 40, 36, -31]], 1, 4], [0, "6dTyxLBO9NWoli7WHtillt", 1, 0], [4, 4278190080], [5, 1560, 720]], [9, "ButtonNewPlay", 2, [4], [[26, 1, -32, [[27, "f19464Eh2dAnZnvHwz5tHZI", "onClickPlayKlondike", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 4, 5, 6, 7, 8]], [0, "96eFjeMzZHL58WLxR4tcZ2", 1, 0], [5, 227, 64], [0, -178.328, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Label Header", 2, [[-33, [18, 17, 19.138999999999967, 74.054, 2, -34]], 1, 4], [0, "38ekN1x59DCLGIwS2jIy04", 1, 0], [5, 276.75, 75.6], [2, 178.56100000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 0, 8, [0]], [2, "label", 4, [[21, "New game", 30, false, 1, 1, -35, [1], 2]], [0, "dbnqF7q5FEnY11HVXmVGdK", 1, 0], [5, 162.39, 50.4], [0, 0.137, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pop-up-title", 2, [[3, 0, -36, [9], 10]], [0, "38W98q6z9My6MjBwH5s5CX", 1, 0], [5, 717.6, 108], [-0.3, 182, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "GAME OVER", 45, 60, false, 1, 1, 10, [11]], [2, "star-512_69516", 5, [[3, 0, -37, [12], 13]], [1, "d1Sm+09PxPR6vYfBslFD0k", 5], [5, 50, 50], [-25.844, -1.034, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "Score: 0", 35, false, false, 1, 5, [14]], [2, "best", 6, [[3, 0, -38, [15], 16]], [1, "92SBVPSmpEVIgobV90OpwG", 6], [5, 30, 46], [-24.615, 4.824, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "clock", 7, [[3, 0, -39, [19], 20]], [1, "68RPA7hyxK17bEWGibPxtT", 7], [5, 30, 44], [-24.989, 0.321, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Play time: 0", 35, false, false, 1, 2, 7, [21]]], 0, [0, 3, 1, 0, 9, 19, 0, 10, 16, 0, 11, 14, 0, 12, 11, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, -1, 9, 0, -2, 13, 0, -3, 10, 0, -4, 5, 0, -5, 6, 0, -6, 7, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, -1, 12, 0, 3, 5, 0, -1, 16, 0, -1, 15, 0, 3, 6, 0, 0, 6, 0, -1, 17, 0, 3, 7, 0, -1, 19, 0, -1, 18, 0, -1, 11, 0, 0, 8, 0, 0, 9, 0, -1, 14, 0, 0, 10, 0, 0, 12, 0, 0, 13, 0, 0, 15, 0, 0, 17, 0, 0, 18, 0, 13, 1, 2, 4, 3, 4, 4, 9, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 14, 16, 19], [-1, -1, 2, -1, 1, 5, 6, 7, 8, -1, 1, -1, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, -1, 1, 1, 2, 2, 2], [0, 0, 2, 0, 3, 4, 5, 6, 7, 0, 8, 0, 0, 9, 0, 0, 10, 0, 1, 0, 11, 0, 0, 12, 13, 2, 1, 1]]