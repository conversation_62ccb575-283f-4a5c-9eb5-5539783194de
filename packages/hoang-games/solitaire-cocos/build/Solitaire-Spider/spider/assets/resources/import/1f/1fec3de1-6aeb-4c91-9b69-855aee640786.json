[1, ["ecpdLyjvZBwrvm+cedCcQy", "12Bmab5o5Pt5pD1nGzInXB", "4bn2lsqHlKYragVNfZBShy", "a2MjXRFdtLlYQ5ouAFv/+R", "5ctdTDq8NEopiwExXi7wmw", "fb7FiNEONNgpQ3H/rUPCQ/", "e10un9MTFDsbnT7kxeSHbB", "5fEa2MzSlHb59KtCV9tXG8", "5cO7kybDxGj4ipyMYdRYZB"], ["node", "root", "_N$file", "_spriteFrame", "_N$target", "data", "_parent", "_scrollView"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_bottom", "_left", "_right", "_enabled", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_lineHeight", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 12, 4, 5, 7, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint"], 1, 1, 2, 4, 5, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$target"], 2, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["8c902IZnG1CPaHsnEjErPue", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [3, 0, 1, 2], [0, 0, 6, 3, 4, 5, 7, 2], [4, 0, 1, 2, 3, 5, 8, 9, 10, 6], [0, 0, 6, 3, 4, 9, 5, 7, 2], [2, 0, 3, 4, 5, 2], [4, 0, 1, 6, 7, 2, 4, 8, 9, 10, 7], [2, 1, 0, 3, 4, 3], [8, 0, 1], [6, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 2, 6, 3, 4, 9, 5, 3], [0, 0, 6, 8, 3, 4, 9, 5, 7, 2], [0, 0, 8, 3, 4, 9, 5, 10, 7, 2], [0, 0, 6, 8, 3, 4, 5, 7, 2], [5, 0, 2, 3, 4, 5, 6, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 7, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [2, 2, 0, 3, 4, 5, 3], [2, 1, 0, 3, 4, 5, 3], [1, 0, 2, 3, 8, 4], [1, 0, 1, 4, 2, 3, 8, 6], [1, 0, 5, 1, 4, 3, 8, 6], [1, 7, 0, 5, 6, 1, 4, 2, 3, 8, 9], [1, 0, 6, 1, 8, 4], [1, 7, 0, 5, 6, 1, 2, 8, 7], [3, 1, 2, 1], [4, 0, 1, 2, 4, 3, 8, 9, 10, 6], [9, 0, 1, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 5], [12, 0, 1, 2, 3, 2], [13, 0, 1, 2, 3], [14, 0, 1]], [[9, "GameRule"], [10, "GameRule", false, [-4, -5, -6], [[8, -2], [33, -3]], [26, -1, 0], [5, 1560, 720]], [13, "content", [-8, -9, -10, -11, -12, -13, -14], [[18, false, 0, -7, [16], 17]], [0, "f71VAQHuBGg4tKPvsSX0uH", 1, 0], [4, 4294938437], [5, 1280, 1724], [0, 0.5, 1], [0, 249, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New ScrollView", 1, [-18, -19], [[[19, 1, 0, -15, [21], 22], -16, [23, false, 45, 92.5, 92.5, 61.79699999999997, 52.20300000000003, 1280, 720, -17]], 4, 1, 4], [0, "42FqASZSFHG4EJyRHSDG97", 1, 0], [5, 1095, 606], [0, -4.796999999999969, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "scrollBar", 512, 3, [-23], [[-20, [22, 37, 350.07654921020657, 108.79999999999998, 3.3306690738754696e-14, 237, -21], [7, 1, 0, -22, [20]]], 1, 4, 4], [0, "11L8s+sFBAcJG4+Sv7klc8", 1, 0], [5, 12, 497.19999999999993], [0, 0, 0.5], [535.5, -54.39999999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Title", 1, [-26, -27], [[5, 0, -24, [27], 28], [25, false, 41, 232.5, 232.5, 61.40999999999997, 1560, -25]], [0, "ebUGdW9iRPzpJRo26w20RJ", 1, 0], [5, 1095, 108], [0, 244.59000000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "closeBtn", 5, [[5, 0, -28, [23], 24], [31, 1, -30, [[32, "8c902IZnG1CPaHsnEjErPue", "close", 1]], -29], [24, 33, 31.732999999999947, 28.048000000000002, -31]], [0, "ccMeYgypRIJpmtyJWYew38", 1, 0], [5, 41, 37], [491.16700000000003, 3.751999999999999, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [11, "BG", 150, 1, [[5, 0, -32, [0], 1], [20, 45, 40, 36, -33], [8, -34]], [0, "dfR3g/6xpMSpIt7tYOQAjo", 1, 0], [4, 4278190080], [5, 1560, 720]], [12, "view", 3, [2], [[28, -35, [18]], [21, 45, 113.5, -0.5, 1280, 720, -36]], [0, "3fS/UQu3ZPA74EkY1y6D94", 1, 0], [4, 4294638330], [5, 1095, 493], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "content1", 2, [[3, "Classic solitaire (also known as Klondike Solitaire) is the game that many simply refer to as \"solitaire\". Klondike is by far the most popular version of Solitaire, though there are many variations. If you did not realize there is more than one type of solitaire game, the game you know as Solitaire is likely to be Klondike.\n\nClassic Solitaire is a game of sorting cards. You move cards between columns in an attempt to put them in order into 4 piles of cards separated by suit.", 25, false, 1, 2, -38, [2], 3]], [1, "dbfkP/mKhJ0bgCj+8MjTqE", -37], [5, 980, 304], [0, -183.927, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 2, [[6, "Classic Solitaire Layout", 30, 35, false, false, 1, -40, [4], 5]], [1, "58IcA44+5D4LKAIf4Jc11i", -39], [4, 4279173100], [5, 345.66, 44.1], [0, -389.85, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "content1", 2, [[3, "The game begins with 28 cards dealt into columns. This is known as the tableau. Seven cards are dealt in a row-one card face up, then six more continuing to the right face down. Next, deal a card face up on the second pile, then one more in each pile facing down. Continue in this fashion, dealing one less card each time, until you have seven piles that start on the left with one card and increase by one card with each column from left to right. The top card on each pile is facing up. Each time an Ace appears face up, place it in a row at the top. These are the foundations. The remaining 24 cards are placed in the top left of the game screen as a stock pile you can draw from when you need additional cards.", 25, false, 1, 2, -42, [6], 7]], [1, "aalK35lQVCDohyBYqC5bcA", -41], [5, 980, 319], [0, -591.59, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 2, [[6, "How to Play Classic Solitaire", 30, 35, false, false, 1, -44, [8], 9]], [1, "cbmYR6ivxIUaPl287G6RTy", -43], [4, 4279173100], [5, 420.09, 44.1], [0, -800.93, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "content1", 2, [[3, "The columns in a game of Classic Solitaire are the tableau. They are the primary play area of the game. You are attempting to move cards from the columns into the foundations on the top right of the game screen. Each foundation must be built up by suit and sequence from Ace to the King.\n\nMove any cards you can to the foundations. On the tableau you can build on the face up cards, building down in alternating colors. In order to move a card in a column, it must be turned face up. To turn a card face up, it must become the bottom card in the column, so all the cards below the face down card must be moved into a different column. Every time you move a face up card, you turn up the card beneath it. When there are on more face down cards in a pile and you move the face up card, you can fill the space with an available King.\n\nAfter dealing, the Stock Pile is what remains of the deck after the cards forming the Tableau. You can deal cards one at a time face up. Once you have dealt out all the cards, you can reset the stock pile. There is not a limit to the number of times you can deal out the cards.", 25, false, 1, 2, -46, [10], 11]], [1, "1af0MdsaVHirZqWXKft8R5", -45], [5, 985, 605], [2, -1148.888, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 2, [[6, "Winning", 30, 35, false, false, 1, -48, [12], 13]], [1, "44QEcQOWZNHopC/RLbga1/", -47], [4, 4279173100], [5, 121.92, 44.1], [0, -1493.881, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "content1", 2, [[3, "The goal of Classic Solitaire is to build all the cards on to the foundations at the top of the game screen. All cards must be face up in the suit piles to win. All four Kings will be at the top of the foundations.", 25, false, 1, 2, -50, [14], 15]], [1, "61VMLnKnxFDISfXLKcGmsf", -49], [5, 980, 105], [0, -1594.315, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "label", 5, [[27, "HOW TO PLAY", 50, false, 1, 1, -52, [25], 26]], [1, "92mRz+++pA4ooEI1G+SrcP", -51], [5, 361.5, 50.4], [0, 1.733, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "bar", 512, 4, [-53], [0, "6dBEEV2e1KVYT2mFzr25zF", 1, 0], [5, 10, 64.9379932356257], [0, 0, 0]], [7, 1, 0, 17, [19]], [29, 1, 4, 18], [30, false, 0.75, 0.23, null, 3, 2, 19]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 3, 0, -3, 5, 0, 0, 2, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 14, 0, -7, 15, 0, 0, 3, 0, -2, 20, 0, 0, 3, 0, -1, 8, 0, -2, 4, 0, -1, 19, 0, 0, 4, 0, 0, 4, 0, -1, 17, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, -2, 16, 0, 0, 6, 0, 4, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 1, 9, 0, 0, 9, 0, 1, 10, 0, 0, 10, 0, 1, 11, 0, 0, 11, 0, 1, 12, 0, 0, 12, 0, 1, 13, 0, 0, 13, 0, 1, 14, 0, 0, 14, 0, 1, 15, 0, 0, 15, 0, 1, 16, 0, 0, 16, 0, -1, 18, 0, 5, 1, 2, 6, 8, 19, 7, 20, 53], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18], [-1, 3, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 3, -1, -1, -1, -1, 3, -1, 3, -1, 2, -1, 3, 3], [0, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 3, 0, 0, 0, 0, 4, 0, 5, 0, 6, 0, 7, 8]]