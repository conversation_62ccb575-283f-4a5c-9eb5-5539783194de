[1, ["ecpdLyjvZBwrvm+cedCcQy", "e10un9MTFDsbnT7kxeSHbB", "e7q6FL+VZEgLJUjVeDLic/", "12Bmab5o5Pt5pD1nGzInXB", "6a8cYYLflLjYykEBlvh1/x", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "17oBh5fSJPYpasgk4cL/Kr", "9cY6JWd8NNOaOQ5dCYmurq", "aff92mttJEkpP2rDVeoefb", "25a7PPAZxBCraEfIFDXzB2", "5ctdTDq8NEopiwExXi7wmw", "4bn2lsqHlKYragVNfZBShy"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "checkMark", "root", "btnClose", "confirmBtn", "blurBg", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_color"], 0, 9, 4, 5, 2, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_top", "_verticalCenter", "node"], -3, 1], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_name", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 1, 1, 5, 1, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_color", "_contentSize"], 1, 1, 12, 4, 5, 5], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["ea50ba/RZVBhI2vikUDm4mX", ["node", "blurBg", "confirmBtn", "btnClose"], 3, 1, 1, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 7, 3, 4, 5, 8, 2], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 7, 6, 3, 4, 5, 8, 2], [0, 0, 7, 3, 4, 9, 5, 8, 2], [1, 0, 3, 4, 5, 2], [1, 2, 0, 3, 4, 5, 3], [1, 0, 1, 3, 4, 5, 3], [1, 0, 1, 3, 4, 3], [2, 0, 1, 2, 6, 4], [2, 3, 0, 1, 2, 6, 5], [4, 6, 0, 4, 1, 2, 3, 8, 9, 10, 7], [7, 0, 1, 3, 3], [7, 0, 1, 2, 3, 4], [9, 0, 2], [0, 0, 2, 6, 3, 4, 5, 3], [0, 0, 7, 6, 3, 4, 5, 2], [0, 0, 1, 6, 3, 4, 9, 5, 3], [0, 0, 1, 7, 3, 4, 9, 5, 3], [10, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 2, 7, 3, 4, 5, 6, 2], [3, 0, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 3, 4, 2], [2, 0, 4, 5, 6, 4], [2, 0, 6, 2], [5, 1, 2, 1], [4, 0, 5, 1, 2, 3, 7, 8, 9, 10, 7], [4, 0, 4, 5, 1, 2, 3, 8, 9, 10, 7], [6, 0, 1, 2, 3, 4, 5, 6, 2], [6, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [8, 0, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 5, 6, 3], [11, 0, 1], [12, 0, 1], [13, 0, 1, 2, 3, 1]], [[14, "SelectModeGamePopup"], [15, "SelectModeGamePopup", false, [-7], [[35, -5, -4, -3, -2], [9, 45, 1560, 720, -6]], [26, -1, 0], [5, 1560, 720]], [2, "result-popup", [-9, -10, -11, -12, -13], [[5, 0, -8, [25], 26]], [0, "b7fFWd7wlAiYVlMN72tOTQ", 1, 0], [5, 718, 399], [0, -14.454, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "toggle1", [-17, -18, -19], [[31, 3, -16, [4, 4292269782], -15, -14, [[13, "ea50ba/RZVBhI2vikUDm4mX", "onSelectDraw", "1", 1]]]], [0, "bd0iPY18FFRaSuT7M+m2Pl", 1, 0], [5, 308, 80], [-137.974, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "toggle2", [-23, -24, -25], [[32, 3, false, -22, [4, 4292269782], -21, -20, [[13, "ea50ba/RZVBhI2vikUDm4mX", "onSelectDraw", "3", 1]]]], [0, "7fR4UUuYZP4oYZKB02v769", 1, 0], [5, 288, 80], [174, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "Panel", 1, [-28, 2], [[25, 45, -26], [34, -27]], [0, "3fscoIS21CI7xnhBXnNj56", 1, 0], [5, 1560, 720]], [17, "Background", 512, [-31], [[6, 1, 0, -29, [5], 6], [10, 0, 45, 100, 40, -30]], [0, "1738eGblBGyYXB44i6R+Oe", 1, 0], [4, 4293322470], [5, 227, 64]], [3, "toggleC<PERSON><PERSON>", 2, [3, 4], [[33, -32]], [0, "961nC7s5pBA5XLOmgdFTWt", 1, 0], [5, 221, 61], [0, -14.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "buttonClose", 2, [-35], [[30, -34, [[12, "ea50ba/RZVBhI2vikUDm4mX", "onClose", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -33, 21, 22, 23, 24]], [0, "ff/ehfoLREq432PRCFYOS/", 1, 0], [5, 50, 50], [307.561, 146.047, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "Background", 512, 8, [[6, 1, 0, -36, [19], 20], [10, 0, 45, 100, 40, -37]], [0, "e4j9OMmfdO05EX1ZhxX/UY", 1, 0], [4, 4293322470], [5, 50, 50]], [19, "BG", 150, 5, [[-38, [9, 45, 40, 36, -39]], 1, 4], [0, "ebxHe/LClC+ofSuaqEwHve", 1, 0], [4, 4278190080], [5, 1560, 720]], [20, "ButtonNewPlay", 2, [6], [-40], [0, "d56jVLCzhMwIfqW3thPbxk", 1, 0], [5, 227, 64], [0, -136.008, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label Header", 2, [[27, "CHOOSE A GAME", 50, false, 1, 1, 3, -41, [7], 8], [24, 17, 22.278, 74.054, -42]], [0, "84Tk1Tl6dMdaVDrffZlgd/", 1, 0], [5, 412.5, 63], [0, 145.722, 0, 0, 0, 0, 1, 1, 1, 1]], [23, 0, 10, [0]], [1, "pop-up-title", 2, [[5, 0, -43, [1], 2]], [0, "1fxrUrciBCn4046ZBGnCYB", 1, 0], [5, 717.6, 108], [-0.2, 145.932, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 6, [[11, "Label<Label>", "New Game", 30, false, 1, 1, -44, [3], 4]], [0, "9amt4KFGVOtY+5HT0UgY0z", 1, 0], [5, 165.78, 50.4], [0, 3.75, 0, 0, 0, 0, 1, 1, 1, 1]], [29, 1, 11, [[12, "ea50ba/RZVBhI2vikUDm4mX", "onClickNewPlay", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 6], [4, "Background", 3, [[7, 0, false, -45, [9], 10]], [0, "50aVkLSwJPpbuMwbbo4Z7V", 1, 0], [4, 4289369983], [5, 51.2, 51.2], [-101.048, 2.341, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "checkmark", 3, [-46], [0, "8cS0XEWupDgKUBHAeJHDiX", 1, 0], [5, 64, 64], [-100.688, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 18, [11]], [1, "label", 3, [[28, "Draw 1", 30, 35, false, 1, 1, -47, [12], 13]], [0, "38TAOBWLRON6wj5ibZpwS3", 1, 0], [5, 102.6, 44.1], [8.184, 4.013, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Background", 4, [[7, 0, false, -48, [14], 15]], [0, "4bn4ANr0BFR4J+9WlQcKZZ", 1, 0], [4, 4289369983], [5, 51.2, 51.2], [-106.061, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "checkmark", false, 4, [-49], [0, "efmrEck+1H77JG7PqZO7rW", 1, 0], [5, 64, 64], [-105.604, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 22, [16]], [1, "label", 4, [[11, "Label<Label>", "Draw 3", 30, false, 1, 1, -50, [17], 18]], [0, "09yk14TDNLHrlTk6CNKJ3w", 1, 0], [5, 103.47, 50.4], [3.493, 1.694, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 10, 1, 0, 11, 8, 0, 12, 16, 0, 13, 13, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, 0, 2, 0, -1, 14, 0, -2, 11, 0, -3, 12, 0, -4, 7, 0, -5, 8, 0, 9, 19, 0, 4, 3, 0, 0, 3, 0, -1, 17, 0, -2, 18, 0, -3, 20, 0, 9, 23, 0, 4, 4, 0, 0, 4, 0, -1, 21, 0, -2, 22, 0, -3, 24, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, 0, 6, 0, -1, 15, 0, 0, 7, 0, 4, 9, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, -1, 13, 0, 0, 10, 0, -1, 16, 0, 0, 12, 0, 0, 12, 0, 0, 14, 0, 0, 15, 0, 0, 17, 0, -1, 19, 0, 0, 20, 0, 0, 21, 0, -1, 23, 0, 0, 24, 0, 14, 1, 2, 3, 5, 3, 3, 7, 4, 3, 7, 6, 3, 11, 50], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 16, 16, 16, 16, 19, 23], [-1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, -1, 2, -1, 1, -1, -1, 2, -1, 1, 5, 6, 7, 8, -1, 1, 1, 5, 6, 7, 8, 1, 1], [0, 0, 9, 0, 1, 0, 10, 0, 1, 0, 2, 0, 0, 3, 0, 2, 0, 0, 3, 0, 11, 4, 5, 6, 7, 0, 12, 13, 4, 5, 6, 7, 8, 8]]