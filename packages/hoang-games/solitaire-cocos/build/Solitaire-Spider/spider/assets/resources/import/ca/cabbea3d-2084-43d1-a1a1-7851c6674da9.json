[1, ["ecpdLyjvZBwrvm+cedCcQy", "e10un9MTFDsbnT7kxeSHbB", "aff92mttJEkpP2rDVeoefb", "6a8cYYLflLjYykEBlvh1/x", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "5ctdTDq8NEopiwExXi7wmw", "12Bmab5o5Pt5pD1nGzInXB"], ["node", "_N$file", "_spriteFrame", "root", "_parent", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "textInformation", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_color"], 1, 9, 4, 5, 2, 1, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$overflow", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "alignMode", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["ef45ebjKn5Kt5a8B1N9lOzX", ["node", "textInformation"], 3, 1, 1]], [[1, 0, 1, 2, 2], [5, 0, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 6, 5, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 7, 2], [0, 0, 6, 5, 2, 3, 4, 7, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 6, 2, 3, 4, 7, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 1, 2, 5, 3, 4, 8, 9, 10, 7], [2, 0, 1, 6, 7, 2, 3, 4, 8, 9, 8], [1, 0, 1, 2], [1, 1, 2, 1], [3, 1, 0, 2, 3, 4, 3], [3, 0, 2, 3, 4, 2], [4, 1, 0, 2, 3, 4, 5], [4, 0, 4, 2], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [8, 0, 1, 2, 3], [9, 0, 1, 1]], [[1, "ConfirmPopup"], [2, "ConfirmPopup", [-4], [[19, -3, -2]], [12, -1, 0], [5, 1560, 720]], [6, "Background", 512, [-7], [[13, 1, 0, -5, [2], 3], [15, 0, 45, 100, 40, -6]], [0, "f1T4KdJaBFBpKAQ+3sEx3H", 1, 0], [4, 4293322470], [5, 152, 51]], [4, "result-popup", [-9, -10], [[14, 0, -8, [9], 10]], [0, "e07P8e+5ZPNKy/hFHnYlct", 1, 0], [5, 691, 194], [-247, -216, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Panel", 1, [3], [[16, 45, -11]], [0, "40Jrqg/R1K5Zv7WD5bnq0l", 1, 0], [5, 1560, 720]], [5, "ButtonClose", 3, [2], [[17, 1, -12, [[18, "ef45ebjKn5Kt5a8B1N9lOzX", "close", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 2, 4, 5, 6, 7]], [0, "96eFjeMzZHL58WLxR4tcZ2", 1, 0], [5, 152, 51], [257.607, -58.714, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [8, "TextInformation", 3, [-14], [11, "6bvh1gkABLP5FqmI57/9uE", -13], [5, 652, 111], [0, 0.5068943892588317, 0.33084696120773466], [9, 12.819, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 2, [[9, "Close", 30, false, 1, 1, 2, -15, [0], 1]], [0, "dbnqF7q5FEnY11HVXmVGdK", 1, 0], [5, 145.2, 39.4], [0, 1.976, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "you cannot deal a new row while any columns are empty", 20, 30, false, false, 1, 2, 6, [8]]], 0, [0, 3, 1, 0, 9, 8, 0, 0, 1, 0, -1, 4, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, 0, 3, 0, -1, 5, 0, -2, 6, 0, 0, 4, 0, 0, 5, 0, 3, 6, 0, -1, 8, 0, 0, 7, 0, 10, 1, 2, 4, 5, 3, 4, 4, 15], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 2, 5, 6, 7, 8, -1, -1, 2, 1], [0, 1, 0, 2, 3, 4, 5, 6, 0, 0, 7, 8]]