[1, ["ecpdLyjvZBwrvm+cedCcQy", "c1E+0bvftJxYX7weIIcIr2", "2cqlAs8RdH2JOPtCs8VlHZ", "12Bmab5o5Pt5pD1nGzInXB", "f0BIwQ8D5Ml7nTNQbh1YlS", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "f23ZPsoB9BC4K2+WLjogZ2", "d2AAm2L0hGUJWeGCd2vP9E", "e6xYuLjlNDJbi1RFh/4qnz", "3ae7efMv1CLq2ilvUY/tQi", "54HSuTLmNHgLvGhCxKnFTJ", "0bRU22FgVNGr1RNi2KFxtU", "11FgQmHEhIraSEX1uPOmgN", "f2ptjRktJPzKQsRXVh/Tom", "0bzOF3fK1Pfrm7cUI8g0K6", "469dBq5UVLvZLzIjJubSpn", "bdTM5S1URMDY77pBPPwBMx", "0dJSxVQIVEz4xfCsFoZnth", "8dXoZExapPv7XLTD0HAsKy", "1aZuym7lJFsbpujfI/nva/", "589VumnAhHeqwWwRHCqz93", "b9sfmzNhRBDLi0U1GPD83S", "66WDwBFaxHe6ONTTxg2EQv", "f6g40siXVL1YzYh5dR+SCI", "0fS+qb1qZJupbeDfiMxJ6j", "a88yn5viRNI7NJPm+Xgz2o", "cau+o9IIRD0aGheFHGZ02p", "ee8l5KpqhBrIg45/HeWis/", "dcYK6V2hhE4aTHf6RquAWG", "98R8YBbltJbrkjAekledYd", "24Jd6I1Q1Kh57Y0VP5q4PL", "62MiBhiG1IqJYp/tRueSYD", "c0toa3vT1HHZ7K5xyLwbqr", "ec0BMkHlFE+av69K9tE7Zz", "52tl/i5lpON45XINPzyH6h"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "asset", "_N$target", "btnUndo", "scene", "_N$normalSprite", "_normalMaterial", "_grayMaterial", "highlightStacks", "btnPowerUp", "blockTouch", "boardScore", "container", "tableauStacks", "deckStacks", "winStacks", "soundToggle", "layoutLeft", "layoutButton", "btnHint", "btnMore", "timer", "score", "highScore", "informationPopup", "selectMode", "gameRule", "resultPopup", "cardPrefab", "text", "loading"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_id", "_components", "_contentSize", "_parent", "_trs", "_children", "_prefab", "_color", "_anchorPoint", "_eulerAngles"], -1, 9, 5, 1, 7, 2, 4, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalHeight", "_bottom", "_left", "_right", "_originalWidth", "alignMode", "_enabled", "node"], -6, 1], ["cc.Label", ["_string", "_N$verticalAlign", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_enableWrapText", "_N$cacheMode", "_N$overflow", "_lineHeight", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_N$normalSprite", "_normalMaterial", "_grayMaterial"], 1, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6, 6, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 1, 1, 2, 5, 7, 2, 5, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "_enabled", "_N$affectedByScale", "node", "_layoutSize"], -4, 1, 5], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_parent", "_children", "_components", "_color", "_contentSize", "_trs"], 2, 1, 2, 12, 5, 5, 7], ["cc.Mask", ["_enabled", "node", "_materials"], 2, 1, 3], ["e465cQRkatGQYPqV+2UHdun", ["node", "followCanvasNodes"], 3, 1, 2], ["3a38eMhRCBIj4pnSZHDuqSm", ["node", "winStacks", "deckStacks", "tableauStacks", "container", "boardScore", "blockTouch", "btnPowerUp", "highlightStacks", "powerSprite", "btnUndo", "undoSprite", "cardPrefab"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 3, 6], ["147b6fFzCdNhL4rC3VJZ1og", ["node", "btnMore", "btnHint", "btnUndo", "layoutButton", "layoutLeft", "soundToggle"], 3, 1, 1, 1, 1, 1, 1, 1], ["57ac4SQLsZIrIQBu5aHJob8", ["node", "highScore", "score", "timer"], 3, 1, 1, 1, 1], ["6f25d/atJdJnKdZPn1S43YK", ["node", "resultPopup", "gameRule", "selectMode", "informationPopup"], 3, 1, 1, 1, 1, 1], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["5e0740n6tRBDo1Dg9KYej0X", ["node"], 3, 1], ["cc.PrefabInfo", ["sync", "root", "asset"], 2, 1, 6], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["74cc5EdSVBAnKo5Ie7M1cMQ", ["node", "loading", "text"], 3, 1, 1, 1]], [[1, 0, 1, 3, 2, 9, 5], [3, 1, 0, 3, 4, 5, 3], [0, 0, 6, 4, 5, 7, 2], [0, 0, 6, 8, 4, 5, 7, 2], [0, 0, 1, 6, 4, 5, 7, 3], [3, 0, 3, 4, 5, 2], [3, 1, 0, 3, 4, 3], [16, 0, 1, 2, 3], [1, 0, 6, 2, 9, 4], [1, 1, 3, 2, 9, 4], [20, 0, 1, 2, 2], [0, 0, 6, 8, 4, 5, 11, 7, 2], [0, 0, 1, 6, 9, 3], [0, 0, 6, 4, 5, 7, 12, 2], [1, 0, 9, 2], [1, 7, 0, 4, 5, 1, 3, 6, 2, 9, 9], [0, 0, 6, 4, 5, 2], [5, 0, 2, 3, 7, 4, 8, 5, 2], [2, 0, 2, 3, 1, 9, 10, 11, 5], [2, 0, 2, 3, 1, 9, 10, 5], [7, 0, 1, 3], [0, 0, 8, 4, 5, 2], [0, 0, 6, 8, 4, 5, 2], [0, 0, 3, 8, 4, 5, 7, 3], [0, 0, 8, 4, 5, 7, 2], [0, 0, 2, 6, 4, 5, 7, 3], [0, 0, 2, 1, 6, 4, 5, 7, 4], [9, 0, 1, 2, 2], [10, 0, 1, 1], [1, 0, 3, 9, 3], [1, 0, 1, 9, 3], [6, 5, 0, 1, 3, 4, 2, 7, 8, 7], [15, 0, 1, 1], [4, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [2, 0, 2, 8, 5, 3, 4, 1, 6, 9, 10, 11, 9], [2, 0, 2, 5, 3, 4, 1, 7, 6, 9, 10, 9], [17, 0, 1], [18, 0, 1, 2, 3], [19, 0, 1], [21, 0, 1, 2, 3, 4, 3], [0, 0, 2, 6, 8, 4, 5, 7, 3], [0, 0, 1, 6, 4, 5, 3], [0, 0, 2, 1, 6, 4, 10, 5, 4], [0, 0, 1, 6, 4, 10, 5, 7, 3], [0, 0, 3, 6, 9, 3], [5, 0, 2, 6, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 3], [5, 0, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 6, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [1, 0, 4, 5, 1, 3, 6, 2, 9, 8], [1, 0, 4, 5, 1, 3, 9, 6], [1, 0, 4, 5, 1, 3, 2, 9, 7], [1, 8, 0, 4, 5, 1, 3, 2, 9, 8], [1, 0, 1, 6, 9, 4], [1, 7, 0, 6, 2, 9, 5], [1, 0, 5, 9, 3], [1, 8, 7, 0, 4, 5, 1, 3, 6, 2, 9, 10], [1, 0, 4, 9, 3], [1, 0, 4, 5, 1, 6, 2, 9, 7], [6, 0, 1, 3, 4, 2, 6, 7, 8, 7], [6, 5, 0, 1, 3, 4, 2, 6, 7, 8, 8], [6, 5, 0, 1, 2, 7, 8, 5], [12, 0, 1, 2, 3, 4, 5, 6, 1], [3, 0, 2, 3, 4, 5, 3], [3, 0, 2, 3, 4, 3], [3, 3, 4, 5, 1], [13, 0, 1, 2, 3, 1], [14, 0, 1, 2, 3, 4, 1], [4, 2, 3, 4, 5, 6, 7, 11, 8, 9, 10, 1], [4, 1, 0, 2, 3, 4, 5, 6, 7, 12, 13, 8, 9, 10, 3], [4, 0, 2, 3, 4, 5, 6, 7, 12, 13, 8, 9, 10, 2], [4, 0, 2, 3, 4, 5, 6, 7, 11, 8, 9, 10, 2], [2, 0, 2, 3, 4, 1, 9, 10, 6], [2, 0, 2, 8, 5, 3, 4, 1, 9, 10, 11, 8], [2, 0, 2, 5, 4, 1, 7, 6, 9, 10, 8], [2, 0, 2, 5, 3, 4, 1, 7, 6, 9, 10, 11, 9], [2, 0, 3, 4, 1, 9, 10, 5], [22, 0, 1, 2, 3, 4, 5, 2], [23, 0, 1, 2, 1]], [[[[20, "<PERSON><PERSON><PERSON>", null], [21, "Gameplay", [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24], [[27, false, -1, [145]], [28, -3, [-2]], [49, -13, -12, -11, -10, -9, -8, -7, -6, -5, [147, 148, 149], -4, [150, 151], 146], [8, 45, 1560, 720, -14]], [5, 1560, 720]], [3, "TableauStacks", 1, [-27, -28, -29, -30, -31, -32, -33, -34, -35, -36], [[50, 45, 229, 229, 190.70800000000003, 9.891999999999982, 1102, 519.4, -25], [60, 1, 1, 6, 6, 10, true, -26, [5, 1102, 519.4]]], [5, 1102, 519.4], [0, -90.40800000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "HighlightStacks", 1, [-39, -40, -41, -42, -43, -44, -45, -46, -47, -48], [[51, 44, 229, 229, 141.909, 9.891999999999982, -37], [61, false, 1, 1, 6, 6, 10, true, -38, [5, 1102, 519.4]]], [5, 1102, 519.4], [0, -90.40800000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "WinStacks", 1, [-51, -52, -53, -54, -55, -56, -57, -58], [[52, 41, 32.550000000000125, 927.45, 58.42699999999999, 700, 280, -49], [31, false, 1, 1, 6, 6, 40, -50, [5, 968.4999999999999, 150]]], [5, 599.9999999999998, 150], [-447.44999999999993, 226.57299999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "MenuGame", 1, [-67, -68, -69, -70], [[63, -65, -64, -63, -62, -61, -60, -59], [8, 45, 1280, 720, -66]], [5, 1560, 720]], [3, "DeckStacks", 1, [-73, -74, -75, -76, -77], [[53, false, 33, 780, 36.06200000000001, 53.609999999999985, 700, 280, -71], [31, false, 1, 1, 6, 6, -85, -72, [5, 172, 150]]], [5, 172, 150], [534.297, 231.39, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "BannerScore", 1, [-84, -85, -86, -87], [[5, 0, -78, [86], 87], [67, -82, -81, -80, -79], [54, 41, -4.5, 1560, -83]], [5, 1560, 53], [0, 338, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layoutSetting", 5, [-91, -92, -93], [[5, 0, -88, [108], 109], [29, 36, -2.1999999999999953, -89], [62, false, 1, 1, 180, -90, [5, 973, 92]]], [5, 80.6, 367], [739.7, -178.7, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [-100, -101, -102, -103], [[68, -98, -97, -96, -95, -94], [14, 45, -99]], [5, 1560, 720]], [23, "<PERSON><PERSON>", "01FoM/o9FEpYGrziSXMGnj", [-106, 1, -107], [[32, -104, [5, 1560, 720]], [14, 45, -105]], [5, 1560, 720], [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnMore", 5, [-111, -112], [[69, -109, [[7, "147b6fFzCdNhL4rC3VJZ1og", "openMenu", 5]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -108, 115, 116, 117, 118], [29, 36, -0.75, -110]], [5, 80.6, 65.7], [739.7, -327.9, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "Background", [-115], [[1, 1, 0, -113, [90], 91], [15, 0, 45, 0.2946429702032063, 0.7053570297967937, 0.5, 0.5, 100, 40, -114]], [5, 36, 50], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [45, "btnSound", 8, [-117, -118, -119], [-116], [5, 70, 70], [0, 41.92, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "Background", [-122], [[1, 1, 0, -120, [103], 104], [15, 0, 45, -3.6999999999999993, -3.6999999999999993, -4.655000000000001, 9.855000000000004, 100, 40, -121]], [5, 56, 56], [0, 7.255000000000003, 0, 0, 0, 0, 1, 1, 1, 0]], [40, "Background", 512, 11, [-125], [[1, 1, 0, -123, [111], 112], [55, 0, 45, 100, 40, -124]], [5, 80.6, 65.7], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btnUndo", 5, [-129], [[70, true, 3, -127, [[7, "3a38eMhRCBIj4pnSZHDuqSm", "undo", 1]], [4, 4293322470], [4, 4291348680], [4, 4286085240], -126, 121, 122, 123, 124, 125], [56, 33, 7.157000000000039, -128]], [5, 53, 48], [746.343, 336, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "Background", 16, [-132], [[-130, [57, false, 0, 45, 18.061000000000007, 34.93899999999999, 4.400000000000002, -4.400000000000002, 100, 40, -131]], 1, 4], [4, 4293322470], [5, 46, 42], [-2.776, 0.365, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "layoutLeft", 5, [-134, -135], [[58, 9, 17.885999999999967, -133]], [5, 163.5, 46], [-680.364, 337, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnHint", 18, [-139], [[33, 3, -137, [[7, "3a38eMhRCBIj4pnSZHDuqSm", "showHint", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -136, 129, 130, 131], [30, 1, 0.6000000000000005, -138]], [5, 55.2, 47.6], [30.208, -1.4000000000000021, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Background", 19, [-142], [[64, 0, false, -140, [127], 128], [15, 0, 45, 10.200000000000001, 15.000000000000002, -0.3750000000000029, 3.9750000000000005, 100, 40, -141]], [5, 30, 44], [-2.3999999999999986, 2.1750000000000007, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btnPowerUp", 18, [-146], [[71, 3, -144, [[7, "3a38eMhRCBIj4pnSZHDuqSm", "onPowerUp", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -143, 136, 137, 138, 139, 140], [30, 1, 1.0460000000000007, -145]], [5, 65, 60], [-54.602, -2.0459999999999994, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]], [3, "Background", 21, [-149], [[5, 0, -147, [134], 135], [15, 0, 45, 9.154999999999994, 6.444999999999993, -5.09999999999998, -0.8999999999999773, 100, 40, -148]], [5, 49.40000000000001, 65.99999999999996], [1.3550000000000004, 2.1000000000000014, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "col_1", 6, [-152], [[1, 1, 0, -150, [12], 13], [9, -25, -25, 200, -151]], [5, 100, 139], [-30, 4.592, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_2", 6, [-155], [[1, 1, 0, -153, [16], 17], [9, -25, -25, 200, -154]], [5, 100, 139], [-15, 4.592, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_3", 6, [-158], [[1, 1, 0, -156, [20], 21], [9, -25, -25, 200, -157]], [5, 100, 139], [0, 4.592, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_4", 6, [-161], [[1, 1, 0, -159, [24], 25], [9, -25, -25, 200, -160]], [5, 100, 139], [15, 4.592, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "col_5", 6, [-164], [[1, 1, 0, -162, [28], 29], [9, -25, -25, 200, -163]], [5, 100, 139], [30, 4.592, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "HighScore", 7, [-166, -167], [[18, "High score:", 23, false, 1, -165, [74], 75]], [5, 126.29, 50.4], [0, 0, 0.5], [-245, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Score", 7, [-169, -170], [[18, "Score:", 23, false, 1, -168, [79], 80]], [5, 69.3, 50.4], [0, 0, 0.5], [6.193, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Time", 7, [-172, -173], [[18, "Time:", 23, false, 1, -171, [84], 85]], [5, 61.11, 50.4], [0, 0, 0.5], [201.195, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "BlockInput", false, 10, [[8, 45, 1280, 720, -174], [36, -175]], [5, 1280, 720]], [25, "Main Camera", 512, 10, [[37, 0, -1, -176], [38, -177]], [5, 1560, 760], [0, 0, 250.28134169370279, 0, 0, 0, 1, 1, 1, 1]], [16, "Bg", 1, [[5, 0, -178, [0], 1], [8, 45, 100, 100, -179]], [5, 1560, 720]], [2, "col_1", 4, [[6, 1, 0, -180, [2]], [0, 1, 5.5, -25, 200, -181]], [5, 100, 139], [-246.542, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_2", 4, [[6, 1, 0, -182, [3]], [0, 1, 5.5, -25, 200, -183]], [5, 100, 139], [-123.092, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_3", 4, [[6, 1, 0, -184, [4]], [0, 1, 5.5, -25, 200, -185]], [5, 100, 139], [0.358, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_4", 4, [[6, 1, 0, -186, [5]], [0, 1, 5.5, -25, 200, -187]], [5, 100, 139], [123.808, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_5", 4, [[6, 1, 0, -188, [6]], [0, 1, 5.5, -25, 200, -189]], [5, 100, 139], [247.258, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_6", 4, [[6, 1, 0, -190, [7]], [0, 1, 5.5, -25, 200, -191]], [5, 100, 139], [370.708, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_7", 4, [[6, 1, 0, -192, [8]], [0, 1, 5.5, -25, 200, -193]], [5, 100, 139], [494.158, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_8", 4, [[6, 1, 0, -194, [9]], [0, 1, 5.5, -25, 200, -195]], [5, 100, 139], [617.608, 6.949999999999996, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "col_1", 2, [[1, 1, 0, -196, [30], 31], [0, 1, 13.375, -25, 200, -197]], [5, 100, 139], [-495, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_2", 2, [[1, 1, 0, -198, [32], 33], [0, 1, 13.375, -25, 200, -199]], [5, 100, 139], [-385, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_3", 2, [[1, 1, 0, -200, [34], 35], [0, 1, 13.375, -25, 200, -201]], [5, 100, 139], [-275, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_4", 2, [[1, 1, 0, -202, [36], 37], [0, 1, 13.375, -25, 200, -203]], [5, 100, 139], [-165, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_5", 2, [[1, 1, 0, -204, [38], 39], [0, 1, 13.375, -25, 200, -205]], [5, 100, 139], [-55, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_6", 2, [[1, 1, 0, -206, [40], 41], [0, 1, 13.375, -25, 200, -207]], [5, 100, 139], [55, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_7", 2, [[1, 1, 0, -208, [42], 43], [0, 1, 13.375, -25, 200, -209]], [5, 100, 139], [165, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_8", 2, [[1, 1, 0, -210, [44], 45], [0, 1, 13.375, -25, 200, -211]], [5, 100, 139], [275, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_9", 2, [[1, 1, 0, -212, [46], 47], [0, 1, 13.375, -25, 200, -213]], [5, 100, 139], [385, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "col_10", 2, [[1, 1, 0, -214, [48], 49], [0, 1, 13.375, -25, 200, -215]], [5, 100, 139], [495, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_1", false, 3, [[1, 1, 0, -216, [50], 51], [0, 1, 13.375, -25, 200, -217]], [5, 100, 139], [-495, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_2", false, 3, [[1, 1, 0, -218, [52], 53], [0, 1, 13.375, -25, 200, -219]], [5, 100, 139], [-385, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_3", false, 3, [[1, 1, 0, -220, [54], 55], [0, 1, 13.375, -25, 200, -221]], [5, 100, 139], [-275, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_4", false, 3, [[1, 1, 0, -222, [56], 57], [0, 1, 13.375, -25, 200, -223]], [5, 100, 139], [-165, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_5", false, 3, [[1, 1, 0, -224, [58], 59], [0, 1, 13.375, -25, 200, -225]], [5, 100, 139], [-55, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_6", false, 3, [[1, 1, 0, -226, [60], 61], [0, 1, 13.375, -25, 200, -227]], [5, 100, 139], [55, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_7", false, 3, [[1, 1, 0, -228, [62], 63], [0, 1, 13.375, -25, 200, -229]], [5, 100, 139], [165, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_8", false, 3, [[1, 1, 0, -230, [64], 65], [0, 1, 13.375, -25, 200, -231]], [5, 100, 139], [275, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_9", false, 3, [[1, 1, 0, -232, [66], 67], [0, 1, 13.375, -25, 200, -233]], [5, 100, 139], [385, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "col_10", false, 3, [[1, 1, 0, -234, [68], 69], [0, 1, 13.375, -25, 200, -235]], [5, 100, 139], [495, 176.825, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Container", 1, [[14, 45, -236]], [5, 1560, 720]], [2, "BlockInput", 1, [[59, 9, -1.0000000000000284, 140, -2.1316282072803006e-14, 1280, 720, -237], [36, -238]], [5, 1040, 200], [-261, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "gameRuleBtn", 8, [12], [[72, 3, -239, [[7, "147b6fFzCdNhL4rC3VJZ1og", "openGameRule", 5]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 12, 92, 93, 94, 95]], [5, 37, 51], [0, 0.49444981000549204, 0.5], [0.416, -56.345, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "Background", 512, false, 13, [[5, 0, -240, [96], 97]], [5, 54, 44], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btnNew", 8, [14], [[33, 3, -241, [[7, "147b6fFzCdNhL4rC3VJZ1og", "openNewPlaySpider", 5]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 14, 105, 106, 107]], [5, 48.6, 61.2], [0, 134.532, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "GameRule", false, 9, [10, true, -242, 141]], [12, "SelecSuitGamePopup", false, 9, [10, true, -243, 142]], [12, "ResultPopup", false, 9, [10, true, -244, 143]], [12, "ConfirmPopup", false, 9, [10, true, -245, 144]], [39, "New Node", false, [10], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "boder_highlight", false, 23, [[5, 0, -246, [10], 11]], [5, 121.8, 157.8], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "boder_highlight", false, 24, [[5, 0, -247, [14], 15]], [5, 121.8, 157.8], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "boder_highlight", false, 25, [[5, 0, -248, [18], 19]], [5, 121.8, 157.8], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "boder_highlight", false, 26, [[5, 0, -249, [22], 23]], [5, 121.8, 157.8], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "boder_highlight", false, 27, [[5, 0, -250, [26], 27]], [5, 121.8, 157.8], [0.1, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "HighScore", false, 7, [[73, "High score: 0", 30, false, 1, 1, -251, [70]]], [5, 200.22, 50.4], [-273.682, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "best", 28, [[5, 0, -252, [71], 72]], [5, 30, 44], [-20.799, -1.794, 0, 0, 0, -0.026176948307873153, 0.9996573249755573, 1, 1, 1], [1, 0, 0, -3]], [17, "label", 28, [-253], [4, 4279173100], [5, 15.14, 50.4], [0, 0, 0.5], [133.094, -0.341, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 24, false, 1, 79, [73]], [13, "star-512_69516", 29, [[5, 0, -254, [76], 77]], [5, 46, 46], [-21.137, -1.728, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 1, 1, 1], [1, 0, 0, -5]], [17, "label", 29, [-255], [4, 4279173100], [5, 15.14, 50.4], [0, 0, 0.5], [74.756, -0.341, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 24, false, 1, 82, [78]], [13, "clock", 30, [[5, 0, -256, [81], 82]], [5, 24.9508, 35.378], [-18.018, -0.502, 0, 0, 0, -0.06104853953485687, 0.9981347984218669, 1, 1, 1], [1, 0, 0, -7]], [17, "label", 30, [-257], [4, 4279173100], [5, 51.14, 50.4], [0, 0, 0.5], [66.716, -0.341, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0:00", 24, false, 1, 85, [83]], [2, "Label", 12, [[74, "Rules", 20, 25, false, false, 1, 1, -258, [88], 89]], [5, 52.72, 31.5], [0, -38.27, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "checkmark", 512, 13, [-259], [5, 54, 44]], [65, 0, false, 88, [98]], [2, "Label", 13, [[34, "Sound", 20, 25, false, false, 1, 1, 1, -260, [99], 100]], [5, 62.38, 31.5], [-1.072, -39.057, 0, 0, 0, 0, 1, 1, 1, 1]], [78, 3, 13, [4, 4292269782], 65, 89, [[7, "147b6fFzCdNhL4rC3VJZ1og", "onSoundToggleCheck", 5]]], [2, "Label", 14, [[34, "New", 20, 25, false, false, 1, 1, 1, -261, [101], 102]], [5, 43.72, 31.5], [-0.129, -41.892, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "Label", 512, false, 15, [[75, "button", 20, false, 1, 1, 1, 1, -262, [110]]], [4, 4278190080], [5, 100, 40]], [13, "iconMore", 11, [[5, 0, -263, [113], 114]], [5, 43.12, 21.56], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 0.9, 0.9, 1], [1, 0, 0, -90]], [4, "Label", false, 17, [[35, "Undo", 20, false, false, 1, 1, 1, 1, -264, [119]]], [5, 74, 28.3], [0.6, -36.489, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 1, 0, 17, [120]], [26, "Label", 512, false, 20, [[35, "Hint", 20, false, false, 1, 1, 1, 1, -265, [126]]], [5, 74, 28.3], [0.6, -41.406, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "Label", false, 22, [[76, "0:00", 20, false, false, 1, 1, 1, 1, -266, [132], 133]], [4, 4284103423], [5, 74, 28.3], [0, -21.666, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 0, 1, 0, -1, 1, 0, 0, 1, 0, 10, 96, 0, 15, 3, 0, 16, 21, 0, 17, 31, 0, 18, 7, 0, 19, 62, 0, 20, 2, 0, 21, 6, 0, 22, 4, 0, 0, 1, 0, 0, 1, 0, -1, 33, 0, -2, 4, 0, -3, 6, 0, -4, 2, 0, -5, 3, 0, -6, 62, 0, -7, 63, 0, -8, 7, 0, -9, 5, 0, -10, 9, 0, 0, 2, 0, 0, 2, 0, -1, 42, 0, -2, 43, 0, -3, 44, 0, -4, 45, 0, -5, 46, 0, -6, 47, 0, -7, 48, 0, -8, 49, 0, -9, 50, 0, -10, 51, 0, 0, 3, 0, 0, 3, 0, -1, 52, 0, -2, 53, 0, -3, 54, 0, -4, 55, 0, -5, 56, 0, -6, 57, 0, -7, 58, 0, -8, 59, 0, -9, 60, 0, -10, 61, 0, 0, 4, 0, 0, 4, 0, -1, 34, 0, -2, 35, 0, -3, 36, 0, -4, 37, 0, -5, 38, 0, -6, 39, 0, -7, 40, 0, -8, 41, 0, 23, 91, 0, 24, 18, 0, 25, 8, 0, 10, 16, 0, 26, 19, 0, 27, 11, 0, 0, 5, 0, 0, 5, 0, -1, 8, 0, -2, 11, 0, -3, 16, 0, -4, 18, 0, 0, 6, 0, 0, 6, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, -4, 26, 0, -5, 27, 0, 0, 7, 0, 28, 86, 0, 29, 83, 0, 30, 80, 0, 0, 7, 0, 0, 7, 0, -1, 77, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 64, 0, -2, 13, 0, -3, 66, 0, 31, 70, 0, 32, 68, 0, 33, 67, 0, 34, 69, 0, 0, 9, 0, 0, 9, 0, -1, 67, 0, -2, 68, 0, -3, 69, 0, -4, 70, 0, 0, 10, 0, 0, 10, 0, -1, 32, 0, -3, 31, 0, 9, 15, 0, 0, 11, 0, 0, 11, 0, -1, 15, 0, -2, 94, 0, 0, 12, 0, 0, 12, 0, -1, 87, 0, -1, 91, 0, -1, 65, 0, -2, 88, 0, -3, 90, 0, 0, 14, 0, 0, 14, 0, -1, 92, 0, 0, 15, 0, 0, 15, 0, -1, 93, 0, 9, 17, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, -1, 96, 0, 0, 17, 0, -1, 95, 0, 0, 18, 0, -1, 19, 0, -2, 21, 0, 9, 20, 0, 0, 19, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, -1, 97, 0, 9, 22, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, -1, 98, 0, 0, 23, 0, 0, 23, 0, -1, 72, 0, 0, 24, 0, 0, 24, 0, -1, 73, 0, 0, 25, 0, 0, 25, 0, -1, 74, 0, 0, 26, 0, 0, 26, 0, -1, 75, 0, 0, 27, 0, 0, 27, 0, -1, 76, 0, 0, 28, 0, -1, 78, 0, -2, 79, 0, 0, 29, 0, -1, 81, 0, -2, 82, 0, 0, 30, 0, -1, 84, 0, -2, 85, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 7, 67, 0, 7, 68, 0, 7, 69, 0, 7, 70, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, -1, 80, 0, 0, 81, 0, -1, 83, 0, 0, 84, 0, -1, 86, 0, 0, 87, 0, -1, 89, 0, 0, 90, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 97, 0, 0, 98, 0, 11, 71, 1, 3, 10, 10, 3, 71, 12, 3, 64, 14, 3, 66, 266], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 83, 86, 89, 96], [-1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 2, -1, 1, -1, -1, 2, -1, 1, -1, -1, 2, -1, 1, -1, 2, -1, 1, 12, 4, 5, 6, -1, 1, -1, -1, 2, -1, 2, -1, 1, 4, 5, 6, -1, 1, -1, -1, 1, -1, 1, 12, 4, 5, 6, -1, -1, 13, 14, 4, 5, 6, -1, -1, 1, 4, 5, 6, -1, 2, -1, 1, 13, 14, 4, 5, 6, 8, 8, 8, 8, -1, 35, -1, -2, -3, -1, -2, 2, 2, 2, 1, 1], [0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 8, 0, 7, 0, 8, 0, 7, 0, 8, 0, 7, 0, 8, 0, 7, 0, 8, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0, 13, 0, 0, 3, 0, 14, 0, 0, 3, 0, 15, 0, 0, 3, 0, 16, 0, 3, 0, 17, 4, 5, 4, 6, 0, 18, 0, 0, 3, 0, 3, 0, 19, 5, 4, 6, 0, 20, 0, 0, 21, 0, 22, 4, 5, 4, 6, 0, 0, 0, 10, 5, 4, 6, 0, 0, 23, 5, 4, 6, 0, 3, 0, 11, 0, 10, 5, 4, 6, 24, 25, 26, 27, 0, 28, 29, 11, 30, 31, 12, 3, 3, 3, 32, 12]], [[[20, "Loading<PERSON><PERSON><PERSON>", null], [21, "Gameplay", [-8, -9, -10], [[27, false, -1, [5]], [28, -3, [-2]], [79, -6, -5, -4], [8, 45, 1560, 720, -7]], [5, 1560, 720]], [23, "<PERSON><PERSON>", "01FoM/o9FEpYGrziSXMGnj", [-13, 1], [[32, -11, [5, 1560, 720]], [14, 45, -12]], [5, 1560, 720], [780, 360, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "New Node", false, [2, -14], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Main Camera", 512, 2, [[37, 0, -1, -15], [38, -16]], [5, 1560, 760], [0, 0, 250.28134169370279, 0, 0, 0, 1, 1, 1, 1]], [16, "Bg", 1, [[5, 0, -17, [0], 1], [8, 45, 100, 100, -18]], [5, 1560, 720]], [2, "load", 1, [[66, -19, [2], 3]], [5, 100, 100], [0, 24.176, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "label", 1, [-20], [5, 159.32, 50.4], [0, -58.994, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "Loading", false, 1, 1, 7, [4]], [44, "SoundManager", "25uudfHodIJJq0GjKa483f", 3, [10, true, -21, 6]]], 0, [0, 0, 1, 0, -1, 1, 0, 0, 1, 0, 36, 8, 0, 37, 6, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 8, 0, 7, 9, 0, 11, 3, 1, 3, 2, 2, 3, 3, 21], [0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 1, -1, -1, 8, 2], [0, 9, 0, 33, 0, 0, 34, 35]]]]