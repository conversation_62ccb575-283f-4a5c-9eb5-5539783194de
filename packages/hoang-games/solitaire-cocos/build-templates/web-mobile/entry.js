'use strict';

globalThis.jg = {};
globalThis.jg.gameConfig = {
    STATIC_ROOT_PATH: '',
    ENABLE_WEBGL_ANTIALIAS: true,
    ALLOW_FULL_SCREEN_MODE: false,
    VIEW_CONFIG: {
        isPortrail: false,
        width: 1280,
        height: 720,
        maxSize: 1560,
    }
}
globalThis.jg.shellNode = document.querySelector('#game') ?? document.body;
globalThis.jg.css_path = `styles.css?v${new Date().getTime()}`
globalThis.jg.js_path = `main.js?v${new Date().getTime()}`

function loadScript(src, parentNode = null) {
    return new Promise((resolve, reject) => {
        if (typeof require !== 'undefined') {
            require(src);
            resolve();
            return;
        }
        // var timer = `load ${src}`;
        // console.time(timer);
        var script = document.createElement('script');
        function done() {
            // console.timeEnd(timer);
            // deallocation immediate whatever
            script.remove();
        }
        script.onload = () => {
            done();
            resolve();
        };
        script.onerror = () => {
            const msg = `Load ${src} failed!`;
            done();
            console.error(msg);
            reject(new Error(msg))
        };
        script.setAttribute('type','text/javascript');
        script.setAttribute('charset', 'utf-8');
        script.setAttribute('src', src);
        script.setAttribute('async', true);
        (parentNode ?? document.head).appendChild(script);
    })
}

function loadScripts(sources, parentNode = null) {
    return new Promise((resolve, reject) => {
        let taskCounter = sources.length;
        sources.forEach((src) => {
            loadScript(src, parentNode).then(() => {
                taskCounter--;
                if (taskCounter === 0) {
                    resolve();
                }
            }).catch((err) => {
                reject(err);
            })
        })
    })
}

function loadCss(source, parentNode = null) {
    return new Promise((resolve, reject) => {
        const link = document.createElement("link");
        link.href = source;
        link.type = 'text/css'
        link.rel = 'stylesheet'
        link.onload = resolve;
        link.onerror = () => {
            const msg = `Load ${source} failed!`;
            console.error(msg);
            reject(new Error(msg))
        }
        (parentNode ?? document.head).appendChild(link);
    })
}

function resolvePath(subPath) {
    return `${globalThis.jg.gameConfig.STATIC_ROOT_PATH}${subPath}`;
}

function getBundleUrl(name) {
    const gamePath = globalThis.jg.gameConfig.STATIC_ROOT_PATH;
    const gameUrl = `${location.origin}${location.pathname}`;
    return gamePath ? `${gameUrl}${gamePath}assets/${name}` : name;
}
// globalThis.jg.util = { loadScript, loadScripts, loadCss, getBundleUrl };

globalThis.jg.bindResizeSensor = () => {
    function _0x3454(){const _0x42a189=['FIXED_HEIGHT','gameConfig','EQUAL_TO_FRAME','setDesignResolutionSize','PROPORTION_TO_FRAME','height','ResolutionPolicy','trim','aspectRatio','FIXED_WIDTH','map','NO_BORDER','ContentStrategy','ContainerStrategy','view','includes','parseFloat','split','observe','resizeSensor','shellNode','VIEW_CONFIG','style','clientWidth'];_0x3454=function(){return _0x42a189;};return _0x3454();}function _0x3863(_0x4cf631,_0x3454d3){const _0x38635e=_0x3454();return _0x3863=function(_0x51eac2,_0x53ddea){_0x51eac2=_0x51eac2-0x1e9;let _0x1cd96c=_0x38635e[_0x51eac2];return _0x1cd96c;},_0x3863(_0x4cf631,_0x3454d3);}const _0xa4571e=_0x3863,getAspectRatio=_0x3deb82=>{const _0x3105b1=_0x3863,_0x593fa8=getComputedStyle(_0x3deb82),_0x447236=_0x593fa8[_0x3105b1(0x1f1)];let _0x305f38=Number[_0x3105b1(0x1f9)](_0x447236);if(_0x447236[_0x3105b1(0x1f8)]('*')){const [_0x1b27bc,_0x157e17]=_0x447236[_0x3105b1(0x1fa)]('*')[_0x3105b1(0x1f3)](_0x444be0=>Number[_0x3105b1(0x1f9)](_0x444be0[_0x3105b1(0x1f0)]()));_0x305f38=_0x1b27bc*_0x157e17;}else{if(_0x447236[_0x3105b1(0x1f8)]('/')){const [_0x480ce6,_0x46e706]=_0x447236[_0x3105b1(0x1fa)]('/')[_0x3105b1(0x1f3)](_0x24bbec=>Number[_0x3105b1(0x1f9)](_0x24bbec[_0x3105b1(0x1f0)]()));_0x305f38=_0x480ce6/_0x46e706;}}return _0x305f38;},autoFitScreenSize=(_0x332263,_0x254f0b)=>{const _0x2ae9d0=_0x3863,_0x2b471e=globalThis['jg'][_0x2ae9d0(0x1ea)][_0x2ae9d0(0x1fe)],{width:_0x3e4f13,height:_0x10a1f5,maxSize:_0x4b523e,isPortrail:_0x1b9b37}=_0x2b471e;let _0x221729=null,_0x4707bd=_0x3e4f13,_0xf0ed12=_0x10a1f5;const _0x401f4c=_0x1b9b37?_0x254f0b/_0x332263:_0x332263/_0x254f0b;if(_0x1b9b37){if(_0x401f4c<=0x10/0x9)_0x221729=new cc[(_0x2ae9d0(0x1ef))](cc[_0x2ae9d0(0x1f6)][_0x2ae9d0(0x1ed)],cc[_0x2ae9d0(0x1f5)][_0x2ae9d0(0x1f4)]);else _0x401f4c<=19.5/0x9?_0x221729=new cc['ResolutionPolicy'](cc[_0x2ae9d0(0x1f6)][_0x2ae9d0(0x1eb)],cc[_0x2ae9d0(0x1f5)][_0x2ae9d0(0x1f2)]):(_0x221729=new cc[(_0x2ae9d0(0x1ef))](cc[_0x2ae9d0(0x1f6)][_0x2ae9d0(0x1ed)],cc[_0x2ae9d0(0x1f5)][_0x2ae9d0(0x1f4)]),_0xf0ed12=_0x4b523e);}else{if(_0x401f4c<=0x10/0x9)_0x221729=new cc[(_0x2ae9d0(0x1ef))](cc[_0x2ae9d0(0x1f6)][_0x2ae9d0(0x1ed)],cc['ContentStrategy']['NO_BORDER']);else _0x401f4c<=19.5/0x9?_0x221729=new cc[(_0x2ae9d0(0x1ef))](cc[_0x2ae9d0(0x1f6)]['EQUAL_TO_FRAME'],cc[_0x2ae9d0(0x1f5)][_0x2ae9d0(0x1e9)]):(_0x221729=new cc[(_0x2ae9d0(0x1ef))](cc[_0x2ae9d0(0x1f6)][_0x2ae9d0(0x1ed)],cc['ContentStrategy'][_0x2ae9d0(0x1f4)]),_0x4707bd=_0x4b523e);}cc[_0x2ae9d0(0x1f7)][_0x2ae9d0(0x1ec)](_0x4707bd,_0xf0ed12,_0x221729);},sensor=new ResizeObserver(_0xd6fba3=>{const _0x217bf6=_0x3863,_0x36fda2=globalThis['jg'][_0x217bf6(0x1fd)],_0x408ad8=getAspectRatio(_0x36fda2);_0x36fda2[_0x217bf6(0x1ff)][_0x217bf6(0x1ee)]=_0x36fda2[_0x217bf6(0x200)]/_0x408ad8+'px';const _0x2ca84f=_0xd6fba3[0x0]['contentRect'],{width:_0x5357db,height:_0x3f2f4a}=_0x2ca84f;globalThis['cc']&&autoFitScreenSize(_0x5357db,_0x3f2f4a);});sensor[_0xa4571e(0x1fb)](globalThis['jg'][_0xa4571e(0x1fd)]),globalThis['jg'][_0xa4571e(0x1fc)]=sensor;
};

globalThis.jg.unbindResizeSensor = () => {
    function _0x5818(_0x1cb0b3,_0x32d932){var _0x581861=_0x32d9();return _0x5818=function(_0x212adf,_0x24cf30){_0x212adf=_0x212adf-0x85;var _0x42e1cd=_0x581861[_0x212adf];return _0x42e1cd;},_0x5818(_0x1cb0b3,_0x32d932);}function _0x32d9(){var _0x3fb143=['shellNode','unobserve'];_0x32d9=function(){return _0x3fb143;};return _0x32d9();}var _0xc7afab=_0x5818;globalThis['jg']['resizeSensor'][_0xc7afab(0x86)](globalThis['jg'][_0xc7afab(0x85)]);
};

globalThis.jg.load = () => {
    const parentNode = globalThis.jg.shellNode;
    const injectSplash = [
        "<div id='splash' style='width: inherit; height: inherit; position: absolute; background-color: #171717;'>",
        "   <div class='progress-bar stripes'>",
        "       <span style='width: 0%'></span>",
        "   </div>",
        "</div>",
    ].join("\n");
    globalThis.jg.shellNode.insertAdjacentHTML("beforeend", injectSplash);
    globalThis.jg.bindResizeSensor();
    loadCss(resolvePath(globalThis.jg.css_path), parentNode).then(() => {
        var splash = document.getElementById('splash');
        splash.style.display = 'block';
    
        loadScript(resolvePath('src/settings.js'), parentNode).then(() => {
            const debug = window._CCSettings.debug;
    
            // open web debugger console
            /* if (typeof VConsole !== 'undefined') {
                window.vConsole = new VConsole();
            } */
            loadScript(resolvePath(globalThis.jg.js_path), parentNode).then(() => {
                loadScript(resolvePath(debug ? 'cocos2d-js.js' : 'cocos2d-js-min.js'), parentNode).then(() => {
                    if (CC_PHYSICS_BUILTIN || CC_PHYSICS_CANNON) {
                        loadScript(resolvePath(debug ? 'physics.js' : 'physics-min.js'), parentNode).then(() => {
                            window.boot();
                        });
                    } else {
                        window.boot();
                    }
                })
            })
        });
    })
}