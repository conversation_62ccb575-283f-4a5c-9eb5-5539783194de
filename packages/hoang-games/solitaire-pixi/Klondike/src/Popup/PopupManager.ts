import AppConstants from '../AppConstants';
import { MultiResolutionHandler } from '../Scenes/MultiResolutionHandler';
import { Point, TextStyle } from '../pixi';
import { BlockInputEvent } from './BlockTouchEvent';
import { ConfirmPopup } from './ConfirmPopup';
import { GameOverPopup } from './GameOverPopup';
import { InputNamePopup } from './InputNamePopup';
import { LoadingResourcePopup } from './MenuPopup/LoadingResourcePopup';
import { RulesPopup } from './MenuPopup/RulesPopup';
import { NewGamePopup } from './NewGamePopup';
import { QuestionPopup } from './NotificationPopup/QuestionPopup';
import { RankPopup } from './RankPopup';
import { WinGamePopup } from './WinPopup';

export class PopupManager {
    public static rulesPopup: RulesPopup = null;
    public static questionPopup: QuestionPopup = null;

    public static loadingResourcePopup: LoadingResourcePopup = null;

    public static newGamePopup: NewGamePopup = null;
    public static inputNamePopup: InputNamePopup = null;
    public static winPopup: WinGamePopup = null;
    public static gameOverPopup: GameOverPopup = null;
    public static rankPopup: RankPopup = null;
    public static blockInputEvent: BlockInputEvent = null;

    public static confirmPopup: ConfirmPopup = null;
    public static getConfirmPopup(): ConfirmPopup {
        if (!PopupManager.confirmPopup) {
            this._initConfirmPopup();
        }
        return PopupManager.confirmPopup;
    }

    public static initializePopups() {
        PopupManager._initMenuPopups();
        PopupManager._initQuestionPopup();
        PopupManager._initLoadingResourcePopup();
        PopupManager._initNewGamePopup();
        PopupManager._initInputNamePopup();
        PopupManager._initWinPopup();
        PopupManager._initGameOverPopup();
        PopupManager._initRankPopup();
        // top of all others popup
        PopupManager._initConfirmPopup();
        PopupManager._initBlockInputEvent();

        // TODO: find other solution to avoid recall resize event, to align all popups
        MultiResolutionHandler.inst.onResize();
    }

    public static onPause() {}

    public static onResume() {}

    public static onVisibilityChange(isVisible: boolean) {
        if (isVisible) {
            this.onResume();
        } else {
            this.onPause();
        }
    }

    private static _initConfirmPopup() {
        if (!this.confirmPopup) {
            this.confirmPopup = new ConfirmPopup();
            this.confirmPopup.container.zIndex = 1; //make confirm on top
            this.confirmPopup.init(
                {
                    text: '',
                    position: new Point(0, -50),
                    style: new TextStyle({
                        fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                        fontSize: AppConstants.POPUP_TXT_FONT_SIZE,
                        fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                        fill: '0xFFFFFF',
                        //stroke: '#4a1850',
                        //strokeThickness: 5,
                        wordWrap: true,
                        wordWrapWidth: 440,
                        align: 'center',
                    }),
                },
                'Ok',
            );
        }
    }

    private static _initMenuPopups() {
        this.rulesPopup = new RulesPopup();
        this.rulesPopup.init();
    }

    private static _initLoadingResourcePopup() {
        this.loadingResourcePopup = new LoadingResourcePopup();
        this.loadingResourcePopup.init(
            {
                color: 0x000000,
                alpha: 0.75,
            },
            {
                text: '',
                style: new TextStyle({
                    fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                    fontSize: AppConstants.POPUP_TXT_FONT_SIZE + 5,
                    fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                    fill: '0xffc133', // gradient
                    //stroke: '#4a1850',
                    //strokeThickness: 5,
                    wordWrap: true,
                    wordWrapWidth: 440,
                    align: 'center',
                }),
                position: new Point(0, 50),
            },
        );
    }

    private static _initNewGamePopup() {
        this.newGamePopup = new NewGamePopup();
        this.newGamePopup.init();
    }

    private static _initInputNamePopup() {
        this.inputNamePopup = new InputNamePopup();
        this.inputNamePopup.init();
    }

    private static _initWinPopup() {
        this.winPopup = new WinGamePopup();
        this.winPopup.init(true, 0, 0);
    }

    private static _initRankPopup() {
        this.rankPopup = new RankPopup();
        this.rankPopup.init();
    }

    private static _initGameOverPopup() {
        this.gameOverPopup = new GameOverPopup();
        this.gameOverPopup.init();
    }

    private static _initBlockInputEvent() {
        this.blockInputEvent = new BlockInputEvent();
        this.blockInputEvent.init();
    }

    private static _initQuestionPopup() {
        this.questionPopup = new QuestionPopup();
        this.questionPopup.init(
            {
                text: '',
                position: new Point(0, -50),
                style: new TextStyle({
                    fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                    fontSize: AppConstants.POPUP_TXT_FONT_SIZE,
                    fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                    fill: '0xFFFFFF',
                    //stroke: '#4a1850',
                    //strokeThickness: 5,
                    wordWrap: true,
                    wordWrapWidth: 440,
                    align: 'center',
                    leading: 10,
                }),
            },
            'YES',
            'NO',
        );
    }
}
