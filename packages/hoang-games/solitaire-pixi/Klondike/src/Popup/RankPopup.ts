import AppConstants, { DrawMode } from '../AppConstants';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import Utils from '../Utils/Utils';
import { Container, Graphics, ISize, ITextStyle, Rectangle, Sprite, Text, NineSlicePlane } from '../pixi';
import { PopupBase } from './PopupBase';

export class RankPopup extends PopupBase {
    private readonly _TEXT_COLOR: number = 0x89efef;
    private readonly _TEXT_COLOR_2: number = 0x76aaaa;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 16,
        align: 'center',
        fill: 0x00000,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 14,
        align: 'center',
        fill: 0x00000,
    };

    private readonly _TIME_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 12,
        align: 'center',
        fill: 0xa1aabf,
    };

    private readonly _BUTTON_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 20,
        align: 'center',
        fill: 0x00000,
    };

    //rank popup
    private _rankBg: NineSlicePlane;
    private _titleRankTxt: Text;
    private _bgRight: Sprite;
    private _bgLeft: Sprite;
    private _listRankScoreDraw1 = [];
    private _listRankScoreDraw3 = [];
    private _listRankTimerDraw1 = [];
    private _listRankTimerDraw3 = [];
    private _isWin: boolean = false;

    //confirm popup
    private _bgConfirm: Sprite;
    private _titleConfirm: Text;
    private _contentConfirm: Text;
    private _backgroundConfirm: Graphics;

    constructor() {
        super();
    }

    public init() {
        this._initUI();
        this._initConfirm();
        this.container.visible = false;
    }

    private _initUI() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._rankBg = new NineSlicePlane(sprite, 20, 20, 20, 20);
        this._rankBg.width = 624;
        this._rankBg.height = 548;
        this._rankBg.pivot.x = 0.5 * this._rankBg.width;
        this._rankBg.pivot.y = 0.5 * this._rankBg.height;
        this.container.addChild(this._rankBg);
        this._rankBg.interactive = false;

        this._titleRankTxt = new Text('Rank', this._MENU_TEXT_STYLE);
        this._titleRankTxt.anchor.set(0.5);
        this._titleRankTxt.position.set(this._rankBg.width / 2, 35);
        this._rankBg.addChild(this._titleRankTxt);

        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        const btnClose = new Button({
            background: closeSprite,
        });
        btnClose.getContainer().anchor.set(0.5);
        btnClose.getContainer().scale.set(0.65);
        btnClose.getContainer().position.set(this._rankBg.width - closeSprite.width / 2 - 18, closeSprite.height / 2 + 18);
        btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        const [tW, tH] = [btnClose.getContainer().width * 1.7, btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._rankBg.addChild(btnClose.getContainer());
        const resetText = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            align: 'center',
            fill: 0x7c7c7c,
            textBaseline: 'bottom',
        });
        resetText.anchor.set(0.5);
        const btnReset = new Button({
            background: null,
        });
        btnReset.getContainer().width = resetText.width;
        btnReset.getContainer().addChild(resetText);
        btnReset.getContainer().position.set(closeSprite.width / 2 + 30, closeSprite.height / 2 + 18);
        btnReset.on('click', () => {
            this._openResetPanel();
            SoundManager.inst.playSfx('BUTTON_CLICK');
        });
        const underline = new Graphics()
            .lineStyle({ color: 0x7c7c7c, width: 1 })
            .moveTo(resetText.x - resetText.width / 2, resetText.y + resetText.height / 2 - 5)
            .lineTo(resetText.x + resetText.width / 2, resetText.y + resetText.height / 2 - 5)
            .endFill();
        btnReset.getContainer().addChild(underline);
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        btnReset.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._rankBg.addChild(btnReset.getContainer());

        const titleGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'klondike_title'));
        titleGame.anchor.set(0.5);
        titleGame.position.set(this._rankBg.width / 2, 100);
        this._rankBg.addChild(titleGame);

        //rank content
        this._bgLeft = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'layout_rank'));
        this._bgLeft.anchor.set(0.5);
        this._bgLeft.position.set(this._rankBg.width / 2 - 144, this._rankBg.height / 2 + 52);
        this._rankBg.addChild(this._bgLeft);

        const title_Draw1 = new Text(Localization.getText('DRAW_1'), this._TITLE_STYLE);
        title_Draw1.anchor.set(0.5);
        title_Draw1.position.set(0, -this._bgLeft.height / 2 + 15);
        this._bgLeft.addChild(title_Draw1);

        const scoreTitle1 = new Text(Localization.getText('SCORE'), this._VALUE_STYLE);
        this._underLine(-this._bgLeft.width / 2 + 12, -scoreTitle1.width / 2 - 8, -this._bgLeft.height / 2 + 44, this._bgLeft);
        this._underLine(this._bgLeft.width / 2 - 12, scoreTitle1.width / 2 + 8, -this._bgLeft.height / 2 + 44, this._bgLeft);
        scoreTitle1.anchor.set(0.5);
        scoreTitle1.position.set(0, -this._bgLeft.height / 2 + 43);
        this._bgLeft.addChild(scoreTitle1);

        const speedTitle1 = new Text(Localization.getText('SPEED_RUN'), this._VALUE_STYLE);
        this._underLine(-this._bgLeft.width / 2 + 12, -speedTitle1.width / 2 - 8, 22, this._bgLeft);
        this._underLine(this._bgLeft.width / 2 - 12, speedTitle1.width / 2 + 8, 22, this._bgLeft);
        speedTitle1.anchor.set(0.5);
        speedTitle1.position.set(0, 20);
        this._bgLeft.addChild(speedTitle1);

        const startY = -this._bgLeft.height / 2 + 62;
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            name.name = 'name';
            //name.anchor.set(0.5);
            name.position.set(-this._bgLeft.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);
            timer.name = 'timer';

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(this._bgLeft.width / 2 - 20, 0);
            score.name = 'score';

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startY + 15 * i);
            this._listRankScoreDraw1.push(lineUser);
            this._bgLeft.addChild(lineUser);
        }

        const startYSpend = 41;
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            //name.anchor.set(0.5);
            name.position.set(-this._bgLeft.width / 2 + 12, -10);
            name.name = 'name';
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);
            timer.name = 'timer';
            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(this._bgLeft.width / 2 - 20, 0);
            score.name = 'score';

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startYSpend + 15 * i);
            this._listRankTimerDraw1.push(lineUser);
            this._bgLeft.addChild(lineUser);
        }

        // layout right
        this._bgRight = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'layout_rank'));
        this._bgRight.anchor.set(0.5);
        this._bgRight.position.set(this._rankBg.width / 2 + 144, this._rankBg.height / 2 + 52);
        this._rankBg.addChild(this._bgRight);

        const title_Draw2 = new Text(Localization.getText('DRAW_3'), this._TITLE_STYLE);
        title_Draw2.anchor.set(0.5);
        title_Draw2.position.set(0, -this._bgRight.height / 2 + 15);
        this._bgRight.addChild(title_Draw2);

        const scoreTitle2 = new Text(Localization.getText('SCORE'), this._VALUE_STYLE);
        this._underLine(-this._bgRight.width / 2 + 12, -scoreTitle2.width / 2 - 8, -this._bgRight.height / 2 + 44, this._bgRight);
        this._underLine(this._bgRight.width / 2 - 12, scoreTitle2.width / 2 + 8, -this._bgRight.height / 2 + 44, this._bgRight);
        scoreTitle2.anchor.set(0.5);
        scoreTitle2.position.set(0, -this._bgRight.height / 2 + 43);
        this._bgRight.addChild(scoreTitle2);

        const speedTitle2 = new Text(Localization.getText('SPEED_RUN'), this._VALUE_STYLE);
        this._underLine(-this._bgRight.width / 2 + 12, -speedTitle2.width / 2 - 10, 22, this._bgRight);
        this._underLine(this._bgRight.width / 2 - 12, speedTitle2.width / 2 + 10, 22, this._bgRight);
        speedTitle2.anchor.set(0.5);
        speedTitle2.position.set(0, 20);
        this._bgRight.addChild(speedTitle2);
        const listRankScoreDraw3 = Utils.getScoreList(DrawMode.THREE);
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            //name.anchor.set(0.5);
            name.position.set(-this._bgRight.width / 2 + 12, -10);
            name.name = 'name';
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);
            timer.name = 'timer';
            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(this._bgRight.width / 2 - 20, 0);
            score.name = 'score';
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                name.text = listRankScoreDraw3[i].name;
                timer.text = listRankScoreDraw3[i].date;
                score.text = listRankScoreDraw3[i].score;
            }

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startY + 15 * i);
            this._listRankScoreDraw3.push(lineUser);
            this._bgRight.addChild(lineUser);
        }

        if (listRankScoreDraw3) {
            listRankScoreDraw3.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            //name.anchor.set(0.5);
            name.position.set(-this._bgRight.width / 2 + 12, -10);
            name.name = 'name';
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);
            timer.name = 'timer';
            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(this._bgRight.width / 2 - 20, 0);
            score.name = 'score';
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                name.text = listRankScoreDraw3[i].name;
                timer.text = listRankScoreDraw3[i].date;
                score.text = Utils.toMMSS(listRankScoreDraw3[i].timer);
            }
            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startYSpend + 15 * i);
            this._listRankTimerDraw3.push(lineUser);
            this._bgRight.addChild(lineUser);
        }
    }

    private _resetRank() {
        const defaultInfo = '???';
        const defaultTimer = '- - - -';
        for (let i = 0; i < 10; i++) {
            this._listRankScoreDraw1[i].children[0].text = defaultInfo;
            this._listRankScoreDraw1[i].children[1].text = defaultTimer;
            this._listRankScoreDraw1[i].children[2].text = defaultInfo;

            this._listRankTimerDraw1[i].children[0].text = defaultInfo;
            this._listRankTimerDraw1[i].children[1].text = defaultTimer;
            this._listRankTimerDraw1[i].children[2].text = defaultInfo;

            this._listRankScoreDraw3[i].children[0].text = defaultInfo;
            this._listRankScoreDraw3[i].children[1].text = defaultTimer;
            this._listRankScoreDraw3[i].children[2].text = defaultInfo;

            this._listRankTimerDraw3[i].children[0].text = defaultInfo;
            this._listRankTimerDraw3[i].children[1].text = defaultTimer;
            this._listRankTimerDraw3[i].children[2].text = defaultInfo;
        }
    }

    public open(isWin: boolean = false) {
        this._isWin = isWin;
        //check rank draw 1
        const listRankScore = Utils.getScoreList(DrawMode.ONE);
        for (let i = 0; i < 10; i++) {
            console.log(this._listRankScoreDraw1[i]);
            if (listRankScore && listRankScore[i]) {
                this._listRankScoreDraw1[i].children[0].text = listRankScore[i].name.slice(0, 8);
                this._listRankScoreDraw1[i].children[1].text = listRankScore[i].date;
                this._listRankScoreDraw1[i].children[2].text = listRankScore[i].score;
            }
        }
        if (listRankScore) {
            listRankScore.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            if (listRankScore && listRankScore[i]) {
                this._listRankTimerDraw1[i].children[0].text = listRankScore[i].name.slice(0, 8);
                this._listRankTimerDraw1[i].children[1].text = listRankScore[i].date;
                this._listRankTimerDraw1[i].children[2].text = Utils.toMMSS(listRankScore[i].timer);
            }
        }
        //check rank draw 3
        const listRankScoreDraw3 = Utils.getScoreList(DrawMode.THREE);
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                this._listRankScoreDraw3[i].children[0].text = listRankScoreDraw3[i].name.slice(0, 8);
                this._listRankScoreDraw3[i].children[1].text = listRankScoreDraw3[i].date;
                this._listRankScoreDraw3[i].children[2].text = listRankScoreDraw3[i].score;
            }
        }

        if (listRankScoreDraw3) {
            listRankScoreDraw3.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                this._listRankTimerDraw3[i].children[0].text = listRankScoreDraw3[i].name.slice(0, 8);
                this._listRankTimerDraw3[i].children[1].text = listRankScoreDraw3[i].date;
                this._listRankTimerDraw3[i].children[2].text = Utils.toMMSS(listRankScoreDraw3[i].timer);
            }
        }
        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(50);
        if (this._isWin) {
            Events.emit(UIEvents.SHOW_CHOOSE_GAME);
        }
        super.close();
    }

    private _initConfirm() {
        this._backgroundConfirm = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._backgroundConfirm.visible = false;
        this._backgroundConfirm.interactive = true;
        this.container.addChild(this._backgroundConfirm);

        this._bgConfirm = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._bgConfirm.anchor.set(0.5);
        this._bgConfirm.interactive = true;
        this.container.addChild(this._bgConfirm);

        const size: ISize = this._bgConfirm;
        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        const btnClose = new Button({
            background: closeSprite,
        });
        btnClose.getContainer().anchor.set(0.5);
        btnClose.getContainer().scale.set(0.65);
        btnClose.getContainer().position.set(size.width / 2 - closeSprite.width / 2 - 18, -size.height / 2 + closeSprite.height / 2 + 18);
        btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        const [tW, tH] = [btnClose.getContainer().width * 1.7, btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + btnClose.getContainer().x, -tH / 2 + btnClose.getContainer().y, tW, tH).endFill());
        btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._bgConfirm.addChild(btnClose.getContainer());

        this._titleConfirm = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 30,
            align: 'center',
            fill: 0x00000,
        });
        this._titleConfirm.anchor.set(0.5);
        this._titleConfirm.position.set(0, -this._bgConfirm.height / 2 + 35);
        this._bgConfirm.addChild(this._titleConfirm);

        this._contentConfirm = new Text('', this._BUTTON_STYLE);
        this._contentConfirm.anchor.set(0.5);
        this._contentConfirm.position.set(0, -15);
        this._bgConfirm.addChild(this._contentConfirm);

        this._bgConfirm.visible = false;

        const buttonCancelText = new Text(Localization.getText('CANCEL'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonCancelText.anchor.set(0.5);
        buttonCancelText.y = -4;

        const cancel_bg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        cancel_bg.anchor.set(0.5);
        const cancelButton = new Button({
            background: cancel_bg,
        });
        cancelButton.getContainer().position.set(-70, 85);
        cancelButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        cancelButton.getContainer().addChild(buttonCancelText);
        this._bgConfirm.addChild(cancelButton.getContainer());

        const resetText = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        resetText.anchor.set(0.5);
        resetText.y = -4;

        const resetBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'btn_red'));
        resetBg.anchor.set(0.5);
        const resetButton = new Button({
            background: resetBg,
        });
        resetButton.getContainer().position.set(70, 85);
        resetButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            localStorage.removeItem(AppConstants.GAME_NAME);
            this._resetRank();
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        resetButton.getContainer().addChild(resetText);
        this._bgConfirm.addChild(resetButton.getContainer());
    }

    private _openResetPanel() {
        this._titleConfirm.text = Localization.getText('RESET');
        this._contentConfirm.text = Localization.getText('RESET_CONTENT');
        this._backgroundConfirm.visible = true;
        Utils._fadeIn(this._bgConfirm, 150);
    }

    private _underLine(posStart, posEnd, y, target) {
        const line = new Graphics().lineStyle({ color: 0xacb6cc, width: 1 }).moveTo(posStart, y).lineTo(posEnd, y).endFill();
        target.addChild(line);
    }
}
