import { Graphics, Text } from '../../pixi';
import { Scheduler } from '../../core/utility/scheduler/Scheduler';
import { BGColor, PopupBase, TextInfo } from '../PopupBase';
import AppConstants from '../../AppConstants';
import ResourceManager from '../../ResManagers/ResourceManager';

export class LoadingResourcePopup extends PopupBase {
    constructor() {
        super();
    }

    private _titleText: Text = null;
    private _isAppeared: boolean = false;

    /**
     * Initialize
     * @param background set background color & alpha
     * @param textInfo set title
     */
    public init(background: BGColor, textInfo: TextInfo) {
        this._background = new Graphics();
        this._background.beginFill(background.color, background.alpha);
        this._background.drawRect(-AppConstants.BASE_SCREEN_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.BASE_SCREEN_WIDTH, AppConstants.MAX_CANVAS_HEIGHT);
        this._background.position.y = -AppConstants.MAX_CANVAS_MARGIN_Y;
        this.container.addChild(this._background);

        this._titleText = new Text(textInfo.text, textInfo.style);
        if (textInfo.position) {
            this._titleText.position.copyFrom(textInfo.position);
        }
        this._titleText.anchor.set(0.5);
        this.container.addChild(this._titleText);

        this.initBase();

        this._background.interactive = true;
    }

    /**
     * Show popup
     * @param text set title text.
     * @param time time to show the popup. Should be display forever with -1.
     * @param callback given function should be called when close Popup.
     */

    private _timeoutCheck: string;
    public async open(text?: string, time?: number, callback?: Function, _class?: any) {
        if (!this._isAppeared) {
            this._isAppeared = true;
            if (text !== undefined) {
                this._titleText.text = text;
            }
            this._appear();
        }

        if (this._waitingIcon && this._waitingIcon.hidden) {
            this._waitingIcon.style.visibility = 'visible';
            this._waitingIcon.style.opacity = '1';
            this._waitingIcon.hidden = false;
            this._waitingIcon.parentElement.hidden = false;
        }

        const delayCheck = Scheduler.setInterval(50, () => {
            if (ResourceManager.isAssetReady(_class)) {
                Scheduler.clearInterval(delayCheck);
                Scheduler.clearTimeout(this._timeoutCheck);
                this.close();
                if (callback) {
                    callback();
                }
                this._isAppeared = false;
            }
        });

        if (time && time > -1) {
            this._timeoutCheck = Scheduler.setTimeout(time, () => {
                //Timeout, failed to loading resource => Exit game
                Scheduler.clearInterval(delayCheck);
                Scheduler.clearTimeout(this._timeoutCheck);
                this.close();
                this._isAppeared = false;
            });
        }
    }

    public close() {
        super.close();
        if (!this.container.visible) {
            return;
        }
        if (this._waitingIcon) {
            this._waitingIcon.style.visibility = 'hidden';
            this._waitingIcon.style.opacity = '0';
            this._waitingIcon.style.transition = `visibility 0s ${this._fadeoutTime / 1000}s, opacity ${this._fadeoutTime / 1000}s linear;`;
            this._waitingIcon.addEventListener('transitionend', () => {
                this._waitingIcon.hidden = true;
                this._waitingIcon.parentElement.hidden = true;
            });
        }
    }
}
