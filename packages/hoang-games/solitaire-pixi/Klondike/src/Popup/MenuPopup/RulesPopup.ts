import { Graphics, Text, Sprite, Rectangle, NineSlicePlane } from '../../pixi';
import * as UI from '../../UI/core/UI';
import AppConstants from '../../AppConstants';
import ResourceManager from '../../ResManagers/ResourceManager';
import { MenuPopupBase } from './MenuPopupBase';
import Localization from '../../Localization/Localization';
import { SoundManager } from '../../SoundManager';
import { MainApp } from '../../main';
import { PopupBase } from '../PopupBase';
import { ScreenMetadata } from '../../Scenes/MultiResolutionHandler';

export class RulesPopup extends PopupBase {
    private static TITLE_HEIGHT = 120;
    private readonly _WIDTH_BG = 924;
    private readonly _HEIGHT_BG = 559;

    private _scrollWidget: UI.ScrollWidget;
    private _overlayBg: Graphics;
    private _panelBg: NineSlicePlane;
    private _titleTxt: Text;

    constructor() {
        super();
    }

    public init() {
        super.initBase();

        const { realWidth, realHeight } = this.container.getScreenData();
        this._overlayBg = new Graphics();
        this._overlayBg.beginFill(0x000000, 0.3)
            .drawRect(-realWidth / 2, -realHeight / 2, realWidth, realHeight)
            .endFill();
        this._overlayBg.y = 0;
        this.container.addChild(this._overlayBg);
        this._overlayBg.interactive = true;

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._panelBg = new NineSlicePlane(sprite, 20, 20, 20, 20);
        this._panelBg.width = this._WIDTH_BG;
        this._panelBg.height = this._HEIGHT_BG;
        this._panelBg.pivot.x = 0.5 * this._panelBg.width;
        this._panelBg.pivot.y = 0.5 * this._panelBg.height;
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = false;

        this._titleTxt = new Text(Localization.getText('HOW_TO_PLAY'), {
            fontFamily: 'intersemibold',
            fontSize: 30,
            align: 'center',
            fill: 0x000000,
        });
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(this._panelBg.width / 2, 50);
        this._panelBg.addChild(this._titleTxt);

        const background = new Graphics();
        background.beginFill(0x000000, 0.4);
        background.drawRect(-this._panelBg / 2, 0, this._panelBg.width, AppConstants.MAX_CANVAS_HEIGHT);

        this._scrollWidget = new UI.ScrollWidget({
            background: background,
            clipSize: { width: this._panelBg.width, height: this._panelBg.height - RulesPopup.TITLE_HEIGHT },
            contentSize: { width: this._panelBg.width, height: 6400 }
        });
        this._scrollWidget.getContainer().y = -210;
        this.container.addChild(this._scrollWidget.getContainer());
        this._scrollWidget.getContainer().visible = false;

        //Loading image
        const items = [
            { title: '', content: Localization.getText('GAME_RULES_1') },
            { title: Localization.getText('TITLE_TIP_2'), content: Localization.getText('GAME_RULES_2') },
            { title: Localization.getText('TITLE_TIP_3'), content: Localization.getText('GAME_RULES_3') },
            { title: Localization.getText('TITLE_TIP_4'), content: Localization.getText('GAME_RULES_4') },
        ];

        this._scrollWidget.getContainer().visible = true;

        let rulePosY = 0; //start pos of 1st sprite
        items.forEach((item) => {
            const title = new Text(item.title, {
                fontFamily: 'intersemibold',
                fontSize: 28,
                fill: 0x000000,
                wordWrap: true,
                wordWrapWidth: 800,
                breakWords: true
            });
            const tip = new Text(item.content, {
                fontFamily: 'intersemibold',
                fontSize: 22,
                fill: 0x000000,
                wordWrap: true,
                wordWrapWidth: 800,
                breakWords: true
            });
            title.anchor.set(0.5, 0);

            title.position.set(0, rulePosY);
            rulePosY += title.height + 50;

            tip.anchor.set(0.5, 0);
            tip.position.set(0, rulePosY);
            rulePosY += tip.height + 50;
            this._scrollWidget.addWidget(title);
            this._scrollWidget.addWidget(tip);
            this._scrollWidget.updateContentLength(rulePosY);
        });
        const close_icon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        const closeButton = new UI.Button({
            background: close_icon
        }).on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        closeButton.getContainer().anchor.set(0.5);
        closeButton.getContainer().position.set(this._panelBg.width / 2 - close_icon.width / 2 - 18, -this._panelBg.height / 2 + close_icon.height / 2 + 18);
        closeButton.getContainer().hitArea = new Rectangle(-50, -50, 100, 100);
        this.container.addChild(closeButton.getContainer());
    }

    public open() {
        super._appear();
        this._scrollWidget.resetScrollView();
        this._scrollWidget.subscribeMouseWheelEvent();
    }

    public close() {
        this._scrollWidget.unsubscribeMouseWheelEvent();
        this._scrollWidget.stopAllAction();
        super.close();
    }

    protected _onResize(screen: ScreenMetadata) {
        super._onResize(screen);
        this._scrollWidget.expandClipArea(AppConstants.BASE_SCREEN_WIDTH, this._panelBg.height - 100);
    }
}