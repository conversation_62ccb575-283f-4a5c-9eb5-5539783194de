import { Graphics, Text, Sprite } from '../../pixi';
import { PopupBase, TextInfo } from '../PopupBase';
import * as UI from '../../UI/core/UI';
import AppConstants from '../../AppConstants';
import ResourceManager from '../../ResManagers/ResourceManager';
import GameConfigs from '../../GameConfigs';
import { SoundManager } from '../../SoundManager';
export interface RoundedRect {
    x: number;
    y: number;
    w: number;
    h: number;
    r: number;
}
export class QuestionPopup extends PopupBase {
    constructor() {
        super();
    }

    private _questionText: Text = null;
    private _rejectButton: UI.Button;
    private _confirmButton: UI.Button;

    // eslint-disable-next-line no-unused-vars
    public init(questionInfo: TextInfo, confirmText: string, rejectText: string) {
        this._background = new Graphics();
        this._background.beginFill(0x000000, 0.6);
        this._background.drawRect(-AppConstants.BASE_SCREEN_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.BASE_SCREEN_WIDTH, AppConstants.MAX_CANVAS_HEIGHT);
        this._background.position.y = -AppConstants.MAX_CANVAS_MARGIN_Y;
        this.container.addChild(this._background);
        this._background.interactive = true;

        const background = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_popup'));
        background.anchor.set(0.5);
        this.container.addChild(background);

        this._questionText = new Text(questionInfo.text, questionInfo.style);
        if (questionInfo.position) {
            this._questionText.position.copyFrom(questionInfo.position);
        }
        this._questionText.anchor.set(0.5);
        this.container.addChild(this._questionText);
        this.initBase();
    }

    /**
     * Show a question select to screen
     * @param question message text
     */
    public open(question: string, confirmText: string, rejectText: string, confirmCallback: Function, rejectCallback?: Function) {
        this._questionText.text = question;
        if (this._confirmButton) {
            this.container.removeChild(this._confirmButton.getContainer());
            this._confirmButton.getContainer().destroy();
        }
        if (this._rejectButton) {
            this.container.removeChild(this._rejectButton.getContainer());
            this._rejectButton.getContainer().destroy();
        }
        this._confirmButton = this.createButton(confirmText, AppConstants.POPUP_WIDTH / 4 - 20, confirmCallback);
        this._rejectButton = this.createButton(rejectText, -AppConstants.POPUP_WIDTH / 4 + 20, rejectCallback);
        this.container.addChild(this._confirmButton.getContainer());
        this.container.addChild(this._rejectButton.getContainer());
        this._appear();
    }

    createButton(text: string, posX: number, callback) {
        const textPixi = new Text(text, {
            fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
            fontSize: AppConstants.POPUP_BUTTON_TXT_FONT_SIZE,
            fill: [GameConfigs.BUTTON_TEXT_COLOR]
        });
        const uiButton = new UI.Button({
            background: new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_button')),
            text : textPixi
        }).on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            if (callback) {
                callback();
            }
            this.close();
        });
        uiButton.getContainer().anchor.set(0.5);
        uiButton.getContainer().x = posX;
        uiButton.getContainer().y = AppConstants.POPUP_HEIGHT * 0.3;
        return uiButton;
    }
}