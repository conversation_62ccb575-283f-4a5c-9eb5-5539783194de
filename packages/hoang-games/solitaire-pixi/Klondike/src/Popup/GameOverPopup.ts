import Localization from '../Localization/Localization';
import AppConstants from '../AppConstants';
import { GameEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import { Graphics, ITextStyle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class GameOverPopup extends PopupBase {
    private readonly _TEXT_COLOR: number = 0x89efef;
    private readonly _TEXT_COLOR_2: number = 0x76aaaa;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
        // fontWeight: 'bold',
        // dropShadow: false,
        // dropShadowColor: 0x00000,
        // dropShadowBlur: 3,
        // dropShadowAngle: Math.PI / 6,
        // dropShadowDistance: 3,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 22,
        align: 'center',
        fill: 0x00000,
    };

    private _panelBg: Sprite;
    private _titleTxt: Text;
    private _score: Text;
    private _timer: Text;

    constructor() {
        super();
    }

    public init() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);
        this._background.interactive = true;

        this._panelBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._panelBg.anchor.set(0.5);
        // this._panelBg.position.set(0, 0);
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = true;

        this._titleTxt = new Text(Localization.getText('NO_MOVES_REMAINING'), this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(0, -103);
        this._panelBg.addChild(this._titleTxt);

        const notifi = new Text(Localization.getText('CONTENT_NO_MOVES_REMAINING'), this._TITLE_STYLE);
        notifi.anchor.set(0.5);
        notifi.position.set(0, -15);
        this._panelBg.addChild(notifi);

        const newGameText = new Text(Localization.getText('NEW_GAME'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            align: 'center',
            fill: 0xffffff,
        });
        newGameText.anchor.set(0.5);
        newGameText.position.set(0, -4);

        const newGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        newGame.anchor.set(0.5);
        const submitButton = new Button({
            background: newGame,
        });
        submitButton.getContainer().position.set(-80, 87.5);
        submitButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            Events.emit(GameEvents.NEW_GAME);
            this.close();
        });
        submitButton.getContainer().addChild(newGameText);
        this._panelBg.addChild(submitButton.getContainer());

        const countinueText = new Text(Localization.getText('CONTINUE'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            align: 'center',
            fill: 0xffffff,
        });
        countinueText.anchor.set(0.5);
        countinueText.position.set(0, -4);

        const continueBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        continueBg.anchor.set(0.5);
        const continueButton = new Button({
            background: continueBg,
        });
        continueButton.getContainer().position.set(80, 87.5);
        continueButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        continueButton.getContainer().addChild(countinueText);
        this._panelBg.addChild(continueButton.getContainer());
        this.container.visible = false;
    }

    public open() {
        this._appear();
    }

    public close(): void {
        super.close();
    }
}
