import { Container, Graphics, TextStyle, Texture, Point, Rectangle, Sprite, IPointData } from '../pixi';
import AppConstants from '../AppConstants';
import { AppState } from '../AppState';
import { Tween } from '../core/utility/tween/Tween';
import GameConfigs from '../GameConfigs';
import UIManager from '../UI/UIManager';
import { ResourceAsyncLoader } from '../ResManagers/ResourceAsyncLoader';
import { AlignPosition, AutoAlignContainer } from '../Scenes/AutoAlignContainer';

export interface TextInfo {
    text: string;
    style: TextStyle;
    position?: Point;
}

export interface SpriteInfo {
    texture: Texture;
    position?: Point;
}

export interface BGColor {
    color: number;
    alpha: number;
}

export enum PopupType {
    DEFAULT = 0,
    NOTIFICATION,
}

export enum PopupAnimationType {
    LEFT_TO_RIGT = 0,
    BOTTON_TO_TOP,
}

export interface PopupListener {
    start?(): void;
    end?(): void;
}

export abstract class PopupBase extends ResourceAsyncLoader {
    protected _waitingIcon: HTMLDivElement;
    protected _wasHideWaitingIcon: boolean = false;

    constructor(alignType: AlignPosition = { vertical: 'CENTER', horizontal: 'CENTER' }, offset: Partial<IPointData> = { x: 0, y:0 }) {
        super();
        this.containerBG = new AutoAlignContainer(alignType, offset);
        this.containerBG.name = this.constructor.name + '_BG';
        this.containerBG.addResizeListener(this._onResize.bind(this));
        this.containerBG.position.set(AppConstants.BASE_SCREEN_WIDTH / 2, AppConstants.BASE_SCREEN_HEIGHT / 2);

        this.container = new AutoAlignContainer(alignType, offset);
        // #!debug
        this.container.name = this.constructor.name;
        this.container.addResizeListener(this._onResize.bind(this));
        this.container.position.set(AppConstants.BASE_SCREEN_WIDTH / 2, AppConstants.BASE_SCREEN_HEIGHT / 2);

        UIManager.inst.addPopup(this);

        this._waitingIcon = <HTMLDivElement>document.getElementById('waiting-icon');
    }

    public containerBG: AutoAlignContainer = null;
    public _background: Graphics = null;
    public container: AutoAlignContainer = null;
    public animation: PopupAnimationType = PopupAnimationType.BOTTON_TO_TOP;
    public onPopupOpen: PopupListener[] = [];
    public onPopupClose: PopupListener[] = [];

    protected _type = PopupType.DEFAULT;
    protected _fadeoutTime = GameConfigs.SHOW_POPUP_TIME;

    public initBase() {
        this.containerBG.visible = false;
        this.container.visible = false;
    }

    protected _appear(type?: PopupType) {
        if (this.container === null) {
            console.error('Call Popup open before contruction!!!');
        }

        if (this._background) {
            this.containerBG.visible = true;
            this._background.alpha = 0;
            Tween.to(Number(GameConfigs.FADED_DIM_TIME), this._background, { alpha: 1 }, 0, null, null);
        }

        // Push/pop Popup state if the popup is a DEFAULT type
        if (type) this._type = type;
        if (this._type === PopupType.DEFAULT) AppState.pushState(AppState.State.Popup);

        if (this._waitingIcon && !this._waitingIcon.hidden) {
            this._wasHideWaitingIcon = true;
            this._waitingIcon.hidden = true;
        }

        this.container.visible = true;
    }

    public close() {
        if (!this.container.visible) {
            return;
        }
        if (this._background) {
            Tween.to(this._fadeoutTime, this._background, { alpha: 0 }, 0, () => {
                this.container.visible = false;
                this.containerBG.visible = false;
                // Push/pop Popup state if the popup is a DEFAULT type
                if (this._type === PopupType.DEFAULT) AppState.popState();
            });
        } else {
            this.container.visible = false;

            // Push/pop Popup state if the popup is a DEFAULT type
            if (this._type === PopupType.DEFAULT) AppState.popState();
        }
        if (this._wasHideWaitingIcon && this._waitingIcon) {
            this._wasHideWaitingIcon = false;
            this._waitingIcon.hidden = false;
        }
    }

    protected _onResize(screen) {
    }

    public setFadeoutTime(time: number) {
        this._fadeoutTime = time;
    }

    public isOpenning(): boolean {
        return this.container.visible;
    }

    public addListener(openListener?: PopupListener, closeListener?: PopupListener) {
        if (openListener && this.onPopupOpen.indexOf(openListener) < 0) {
            this.onPopupOpen.push(openListener);
        } else {
            console.warn('[UI] Duplicate listener, skip!');
        }

        if (closeListener && this.onPopupClose.indexOf(closeListener) < 0) {
            this.onPopupClose.push(closeListener);
        } else {
            console.warn('[UI] Duplicate listener, skip!');
        }
    }
}
