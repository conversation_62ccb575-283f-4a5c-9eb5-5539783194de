
import Localization from '../Localization/Localization';
import AppConstants from '../AppConstants';
import { GameEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import Utils from '../Utils/Utils';
import { Graphics, ITextStyle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class WinGamePopup extends PopupBase {
    private readonly _TEXT_COLOR: number = 0x89efef;
    private readonly _TEXT_COLOR_2: number = 0x76aaaa;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
        // fontWeight: 'bold',
        // dropShadow: false,
        // dropShadowColor: 0x00000,
        // dropShadowBlur: 3,
        // dropShadowAngle: Math.PI / 6,
        // dropShadowDistance: 3,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 25,
        align: 'center',
        fill: 0x00000,
    };
    private _panelBg: Sprite;
    private _titleTxt: Text;
    private _score: Text;
    private _timer: Text;

    constructor() {
        super();
    }

    public init(isWin: boolean, score: number, timer: number) {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);
        this._background.interactive = true;

        this._panelBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._panelBg.anchor.set(0.5);
        // this._panelBg.position.set(0, 0);
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = true;


        this._titleTxt = new Text('', this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(0, -this._panelBg.height / 2 + 40);
        this._panelBg.addChild(this._titleTxt);

        this._score = new Text(score.toString(), this._TITLE_STYLE);
        this._score.anchor.set(0.5);
        this._score.position.set(0, -40);
        this._panelBg.addChild(this._score);

        this._timer = new Text(Utils.toMMSS(timer), this._TITLE_STYLE);
        this._timer.anchor.set(0.5);
        this._timer.position.set(0, 15);
        this._panelBg.addChild(this._timer);

        const buttonText = new Text(Localization.getText('NEW_GAME'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonText.anchor.set(0.5);
        buttonText.position.set(0, -4);
        const newGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        newGame.anchor.set(0.5);
        const submitButton = new Button({
            background: newGame,
        });
        submitButton.getContainer().position.set(0, 87.5);
        submitButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        submitButton.getContainer().addChild(buttonText);
        this._panelBg.addChild(submitButton.getContainer());
        this.container.visible = false;
    }

    public open(isWin: boolean, score: number, timer: number) {
        this._score.text = Localization.getText('SCORE') + ': ' + score.toString();
        this._titleTxt.text = isWin ? Localization.getText('CONGRATULATIONS') : Localization.getText('GAMEOVER');
        this._timer.text = Localization.getText('TIME') + ': ' + Utils.toMMSS(timer);
        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(50);
        Events.emit(GameEvents.NEW_GAME);
        super.close();
    }
}