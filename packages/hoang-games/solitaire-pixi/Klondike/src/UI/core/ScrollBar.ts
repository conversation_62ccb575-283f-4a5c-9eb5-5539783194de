import { Sprite, ISize } from '../../pixi';
import ResourceManager from '../../ResManagers/ResourceManager';
import { InteractiveWidget, IInteractiveWidgetOptions } from './InteractiveWidget';

export interface IScrollBarOptions {
    width: number;
    color: number;
    view: ISize;
    content: ISize;
}

export class ScrollBar extends InteractiveWidget {

    // private _background: Graphics;
    private _bar: Sprite;
    private _color: number;
    private _barWidth: number;
    private _view: ISize;
    private _content: ISize;

    private _hideTimeout: number;

    constructor(options: IScrollBarOptions) {

        // const background = new Graphics();
        // background.alpha = 0;
        // const bar = background.addChild(new Graphics());
        const bar = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'scroll_bar'));
        const interactiveOptions: IInteractiveWidgetOptions = {
            background: bar
        };
        super(interactiveOptions);

        // this._background = background;
        this._bar = bar;
        this._bar.anchor.set(0.5, 0);
        this._color = options.color;
        this._barWidth = options.width;
        this._view = options.view;
        this._content = options.content;
        this._updateSize(this._view, this._content);
        this.insetContainer.buttonMode = false;
        this.insetContainer.interactive = false;

        // this._background.y = 5;

        return this;
    }

    public updateBarPosition(posY: number) {
        this._bar.y = posY / this._content.height * (this._view.height - 10);
    }

    public setRenderable(visible) {
        this.getContainer().renderable = visible;
    }

    public updateContentHeight(contentHeight) {
        this._content.height = contentHeight;
        this._updateSize(this._view, this._content);
    }

    public updateViewHeight(viewHeight) {
        this._view.height = viewHeight;
        this._updateSize(this._view, this._content);
    }

    private _updateSize(view: ISize, content: ISize) {

        const viewH = view.height - 5;
        // this._background.clear();
        // this._background.beginFill(this._color, 0.2);
        // this._background.drawRoundedRect(-this._barWidth/2, 0, this._barWidth, viewH, this._barWidth);

        // this._bar.clear();
        // this._bar.beginFill(this._color, 0.5);
        // this._bar.drawRoundedRect(-this._barWidth/2 + 1, 0, this._barWidth - 2, viewH * (viewH / content.height), this._barWidth);

        this._bar.height = viewH * (viewH / content.height);
    }
}