import ResourceManager from '../../ResManagers/ResourceManager';
import { Scheduler } from '../../core/utility/scheduler/Scheduler';
import { Tween } from '../../core/utility/tween/Tween';
import { Container, Graphics, InteractionEvent, Rectangle, Sprite } from '../../pixi';

export type ISliderOptions = {
    background?: Sprite,
    clipRect: Rectangle,
};

export class SliderView extends Container {
    private readonly _DELAY_CHANGE_PAGE = 10000;

    private _options: ISliderOptions;
    private _clipArea: Graphics;
    private _contents: Container;
    private _indicatorBar: Container;
    private _pages: Container[] = [];
    private _indicators: Sprite[] = [];
    private _showIdx: number = 0;
    private _changePageTaskId: string;
    private _handler: { dragging?: boolean; event?: InteractionEvent; delta?: number; };

    constructor(options?: ISliderOptions) {
        super();
        this._options = options;

        if (options?.background) {
            options.background.name = 'bg';
            options.background.anchor.set(0.5);
            this.addChild(options.background);
        }
        this._contents = new Container();
        this.addChild(this._contents);

        const { x, y, width, height } = options.clipRect;
        this._clipArea = new Graphics().beginFill(0x8bc5ff, 0.8).drawRect(x, y, width, height).endFill();
        this._contents.addChild(this._clipArea);
        this._contents.mask = this._clipArea;

        this._indicatorBar = new Container();
        this._indicatorBar.position.set(0, height / 2 - 25);
        this.addChild(this._indicatorBar);

        this.on('pointerdown', (ev: InteractionEvent) => {
            this._handler.event = ev;
            this._handler.delta = ev.data.getLocalPosition(this).x + this.width / 2;
            this._handler.dragging = true;
        });
        this.on('pointermove', (ev: InteractionEvent) => {
            if (this._handler?.dragging) {
                const moveX = this._handler.event.data.getLocalPosition(this).x + this.width / 2;
                if (Math.abs(moveX - this._handler.delta) > 30) {
                    this.interactive = false;
                    this._clearTimeout();
                    const isSwipeRight = moveX > this._handler.delta;
                    this._turnPage(!isSwipeRight).then(() => {
                        this.interactive = true;
                        this._createTask();
                    });
                    this._onEnd(ev);
                }
            }
        });
        this.on('pointerup', this._onEnd.bind(this));
        this.on('pointercancel', this._onEnd.bind(this));
        this.on('pointerleave', this._onEnd.bind(this));
        this.on('pointerout', this._onEnd.bind(this));
        this._handler = {};
        this.interactive = true;
    }

    private _onEnd(ev: InteractionEvent) {
        if (this._handler?.dragging) {
            this._handler.event = null;
            this._handler.dragging = false;
        }
    }

    public addPage(view: Container) {
        const pageCount = this._pages.length;
        this._pages[pageCount] = view;
        view['_pId'] = pageCount;
        view.x = pageCount * this._options.clipRect.width;
        this._contents.addChild(view);

        const pageDot = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', pageCount === 0 ? 'dot_fill' : 'dot_empty'));
        pageDot['_pId'] = pageCount;
        pageDot.anchor.set(0.5);
        pageDot.x = pageCount * (pageDot.width + 10);
        this._indicatorBar.addChild(pageDot);
        this._indicatorBar.x = -(pageCount * (pageDot.width + 10)) / 2;
        this._indicators[pageCount] = pageDot;
    }

    public start() {
        this._showIdx = 0;
        this._createTask();
    }

    private _createTask() {
        this._changePageTaskId = Scheduler.setTimeout(this._DELAY_CHANGE_PAGE, this._turnPage.bind(this, true, true));
    }

    private _clearTimeout() {
        Scheduler.clearTimeout(this._changePageTaskId);
        this._changePageTaskId = null;
    }

    public reset() {
        this._clearTimeout();
    }

    private _turnPage(isRight: boolean, setTask: boolean = false): Promise<void> {
        return new Promise<void>((resolve) => {
            const moveX = this._options.clipRect.width;
            const curPage = this._showIdx;
            let nextPage = 0;
            if (isRight) {
                nextPage = this._showIdx < this._pages.length - 1 ? this._showIdx + 1 : 0;
            } else {
                nextPage = this._showIdx > 0 ? this._showIdx - 1 : this._pages.length - 1;
            }

            Tween.to(300, this._pages[curPage], { x: moveX * (isRight ? -1 : 1) }, 0, () => {
                this._indicators[curPage].texture = ResourceManager.getAtlasFrame('loading_atlas', 'dot_empty');
            });
            this._pages[nextPage].x = moveX * (isRight ? 1 : -1);
            Tween.to(300, this._pages[nextPage], { x: 0 }, 0, () => {
                this._indicators[nextPage].texture = ResourceManager.getAtlasFrame('loading_atlas', 'dot_fill');
                setTask && this._createTask();
                resolve();
            });
            this._showIdx = nextPage;
        });
    }
}