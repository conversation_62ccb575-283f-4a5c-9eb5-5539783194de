import { Container, utils, InteractionEvent } from '../../pixi';
export interface IWidgetOptions {
    background?: Container;
}

export class Widget extends utils.EventEmitter {

    public readonly insetContainer: Container;
    public readonly contentContainer: Container;

    protected _options: IWidgetOptions;

    constructor(options: IWidgetOptions) {
        super();

        this._options = options;

        /**
         * This container owns the background + content of this widget.
         * @readonly
         */
        if (options.background) {
            this.insetContainer = options.background;
        } else {
            this.insetContainer = new Container();
        }

        /**
         * This container holds the content of this widget. Subclasses should add
         * renderable display-objects to this container.
         * @readonly
         */
        this.contentContainer = this.insetContainer.addChild(new Container());

        this.insetContainer.on('pointerdown', (e: InteractionEvent) => { this.onPointerPress(e); });
        this.insetContainer.on('pointermove', (e: InteractionEvent) => { this.onPointerMove(e); });
        this.insetContainer.on('pointerup', (e: InteractionEvent) => { this.onPointerRelease(e); });
        this.insetContainer.on('pointerupoutside', (e: InteractionEvent) => { this.onPointerExit(e); });
        this.insetContainer.on('pointerout', (e: InteractionEvent) => { this.onPointerExit(e); });
        this.insetContainer.on('pointerover', (e: InteractionEvent) => { this.onPointerEnter(e); });
        this.insetContainer.on('rightclick', (e: InteractionEvent) => { this.onRightClick(e); });
    }

    getContainer(): Container {
        return this.insetContainer;
    }

    onPointerPress(e: InteractionEvent) {
        return;
    }

    onPointerMove(e: InteractionEvent) {
        return;
    }

    onPointerRelease(e: InteractionEvent) {
        this.onClick(e);
        return;
    }

    onPointerExit(e: InteractionEvent) {
        return;
    }

    onPointerEnter(e: InteractionEvent) {
        return;
    }

    onRightClick(e: InteractionEvent) {
        return;
    }

    onClick(e: InteractionEvent) {
        return;
    }
}