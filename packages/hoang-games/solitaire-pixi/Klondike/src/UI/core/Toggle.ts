import { Texture, Text, Sprite, InteractionEvent } from '../../pixi';
import { InteractiveWidget } from './InteractiveWidget';

export interface IToggleOptions {
    textureDefault: Texture;
    textureToggle: Texture;
    text?: Text;
    textColorDefault?: number;
    textColorToggle?: number;
}

export class Toggle extends InteractiveWidget {

    constructor(options: IToggleOptions) {
        super({
            background: new Sprite(options.textureDefault),
            text: options.text
        });

        this._defaultTexture = options.textureDefault;
        this._offTexture = options.textureToggle;
        if (options.text) {
            if (options.textColorDefault) {
                this._colorDefault = options.textColorDefault;
            }
            if (options.textColorToggle) {
                this._colorToggle = options.textColorToggle;
            }
        }

        return this;
    }

    private _isToggled: boolean = false;
    private _colorDefault: number;
    private _colorToggle: number;

    onClick(e: InteractionEvent) {
        super.onClick(e);
        this.toggle();
        this.emit('click', e, this._isToggled);
    }

    public toggle() {
        if (this._isToggled) {
            if (this.insetContainer['texture']) {
                this.insetContainer['texture'] = this._defaultTexture;
            }
            if (this._colorDefault) {
                this.getText().style.fill = [this._colorDefault];
            }
            this._isToggled = false;
        } else {
            if (this.insetContainer['texture']) {
                this.insetContainer['texture'] = this._offTexture;
            }
            if (this._colorToggle) {
                this.getText().style.fill = [this._colorToggle];
            }
            this._isToggled = true;
        }
    }

    public isToggled(): boolean {
        return this._isToggled;
    }

    public setToggle(toggled: boolean) {
        if (this._isToggled !== toggled) {
            this.toggle();
        }
    }

    public setInteractable(enable: boolean) {
        this.insetContainer.interactive = enable;
        this._setTint(enable ? this._tintColor : InteractiveWidget.DISABLE_MUL_COLOR);
    }
}