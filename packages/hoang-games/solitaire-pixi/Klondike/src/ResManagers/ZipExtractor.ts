/**
 * Check resource loader:
 * https://github.com/englercj/resource-loader/tree/v3.0.1
 */
import { ILoaderResource, Loader, LoaderResource } from '@pixi/loaders';
import JSZip from 'jszip';
import { BitmapFont, Spritesheet, Texture, utils } from '../pixi';
import FontFaceObserver from 'fontfaceobserver';
import Globals from '../Globals';
import { autoDetectFormat } from './BMF/formats';

export const LoadTypeMap = LoaderResource['_loadTypeMap'];
export const XHRTypeMap = LoaderResource['_xhrTypeMap'];
export type AssetType = 'default' | 'atlas' | 'spine' | 'audio' | 'bmfont' | 'font';
export type ZipPartInfo = {
    filename: string;
    basename: string;
    dirname: string,
    resname: string;
    ext: string;
    loadType; // LoaderResource.LOAD_TYPE
    xhrType; // LoaderResource.XHR_RESPONSE_TYPE
};
export type ZipAssetFile = { file: JSZip.JSZipObject; info: ZipPartInfo };
export type ZipResource = utils.Dict<ZipAssetFile> | ZipAssetFile;
const ZipFolderMapping: utils.Dict<AssetType> = {
    atlas: 'atlas',
    spine: 'spine',
    bmf: 'bmfont',
    fonts: 'font',
    sound: 'audio',
};

export class ZipExtractor {
    private static _DEBUG: boolean = false;
    private static _ExtractMapping = {
        atlas: this.parseAtlas.bind(this),
        spine: this.parseSpine.bind(this),
        audio: this.parseAudio.bind(this),
        font: this.parseWebFont.bind(this),
        bmfont: this.parseBMFont.bind(this),
    };
    private static _loader: Loader;

    public static async parse(loader: Loader, asset: {id: string, data: ZipResource }): Promise<void> {
        const { id, data } = asset;
        const dirname = id.substring(0, id.indexOf('/'));
        const type = ZipFolderMapping[dirname.toLowerCase()];

        this._loader = loader;
        let func = this._ExtractMapping[type] ?? this.parseFile.bind(this);
        if (id.endsWith('.json')) {
            func = this.parseFile.bind(this);
        }
        this._DEBUG && console.warn(`Extract: ${id}`, data, type);
        return func(data);
    }

    public static async parseAtlas(data: ZipResource): Promise<void> {
        const fileKeys = Object.keys(data);
        const jsonKey = fileKeys.find(x => x.endsWith('.json'));
        const { info, file } = data[jsonKey];
        const { filename, basename, resname, loadType, xhrType } = info;

        const blob = await file.async('blob');
        const url = URL.createObjectURL(blob);

        this._loader.add({
            name: resname,
            url,
            loadType,
            xhrType,
            onComplete: (res: ILoaderResource) => {
                if (res.error) {
                    console.error(res.error);
                }
            },
        });

        // load frames
        const resource = this._loader.resources[resname];
        const imageResourceName = `${resname}_image`;

        /* // Check and add the multi atlas
        // Heavily influenced and based on https://github.com/rocket-ua/pixi-tps-loader/blob/master/src/ResourceLoader.js
        // eslint-disable-next-line camelcase
        const multiPacks = resource.data?.meta?.related_multi_packs;
        if (Array.isArray(multiPacks)) {
            for (const item of multiPacks) {
                if (typeof item !== 'string') {
                    continue;
                }

                const itemName = item.replace('.json', '');
                const itemUrl = url.resolve(resource.url.replace(loader.baseUrl, ''), item);

                // Check if the file wasn't already added as multipacks are redundant
                if (loader.resources[itemName]
                    || Object.values(loader.resources).some((r) => url.format(url.parse(r.url)) === itemUrl)) {
                    continue;
                }

                const options = {
                    crossOrigin: resource.crossOrigin,
                    loadType: LoaderResource.LOAD_TYPE.XHR,
                    xhrType: LoaderResource.XHR_RESPONSE_TYPE.JSON,
                    parentResource: resource,
                };

                loader.add(itemName, itemUrl, options);
            }
        } */

        const jsonContent = await file.async('text');
        const jsonObject = JSON.parse(jsonContent);
        const imagePath = jsonObject.meta.image;
        const imageKey = fileKeys.find(x => x.endsWith(imagePath));
        const imageData = <ZipAssetFile>data[imageKey];
        let imageBlobUrl = imagePath;
        if (imageData.file) {
            const imageBlob = await imageData.file.async('blob');
            imageBlobUrl = URL.createObjectURL(imageBlob);
        }
        // console.warn(imagePath, imageData);

        // load the image for this sheet
        this._loader.add({
            name: imageResourceName,
            url: imageBlobUrl,
            crossOrigin: resource.crossOrigin,
            metadata: resource.metadata.imageMetadata,
            loadType: imageData.info.loadType,
            xhrType: imageData.info.xhrType,
            // parentResource: resource,
            onComplete: (res: ILoaderResource) => {
                if (res.error) {
                    console.error(res.error);
                    return;
                }
                const spritesheet = new Spritesheet(res.texture, jsonObject, resource.url);
                spritesheet.parse(() => {
                    resource.spritesheet = spritesheet;
                    resource.textures = spritesheet.textures;
                    this._DEBUG && console.warn(`LOAD: ${filename} > DONE`);
                });
            }
        });
    }

    public static async parseSpine(data: ZipResource): Promise<void> {
        console.warn('Not support Spine yet');
    }

    public static async parseAudio(data: ZipResource): Promise<void> {
        // console.warn('Not support Audio yet');
        const { file, info } = <ZipAssetFile>data;
        const { filename, resname, loadType, xhrType, ext, dirname } = info;
        const blob = await file.async('blob');
        const url = URL.createObjectURL(blob);

        Globals.ExtraResources[resname] = { info, blob, url };
        this._DEBUG && console.warn(`LOAD: ${filename} > DONE`);
    }

    public static async parseWebFont(data: ZipResource): Promise<void> {
        // console.warn('Not support WebFont yet');
        const assetIds = Object.keys(data);
        const configId = assetIds.find((x) => x.endsWith('.json'));
        const config = data[configId];

        const { file: jsonFile, info } = <ZipAssetFile>config;
        const jsonContent = await jsonFile.async('text');
        const configObject = JSON.parse(jsonContent);
        // console.warn(data, configObject);

        const woffIds = assetIds.filter((x) => x.includes('.woff')).map((y) => data[y]);
        const urls = [], types = [];
        for (let i = 0; i < woffIds.length; i++) {
            const { file: woff, info: woffInfo } = woffIds[i];
            const blob = await woff.async('blob');
            const url = URL.createObjectURL(blob);
            urls.push(url);
            types.push(woffInfo.ext);
        }
        // console.warn(woffIds, urls);

        await this._loadRawFont(configObject.name, urls, configObject.weight, configObject.style, types).then(() => {
            this._DEBUG && console.warn(`LOAD: ${info.resname} > DONE`);
        }).catch((error) => {
            console.error(`Load failed: ${info.resname}`, error);
        });
    }

    private static _loadRawFont(name: string, urls: string[], weight: number = 700, style: string = 'normal', types: string[] = []) {
        const fontTag = document.querySelector(`style#${name}`);
        if (!fontTag) {
            const element = document.createElement('style');
            let innerText = `@font-face{font-family:'${name}';src:`;
            urls.forEach((fontUrl, idx, arr) => {
                if (idx > 0 && idx < arr.length) {
                    innerText += ',';
                }
                const fontFormat = types[idx] ?? ('woff' + fontUrl.endsWith('woff2') ? '2' : '');
                innerText += `url(\'${fontUrl}\') format(\'${fontFormat}\')`;
            });
            innerText += `;font-weight:${weight};font-style:${style};}`;
            element.id = name;
            element.innerText = innerText;
            document.head.append(element);
        }

        const fontObserver = new FontFaceObserver(name);
        return fontObserver.load(null, 20000);
    }

    public static async parseBMFont(resources: ZipResource): Promise<void> {
        const fileKeys = Object.keys(resources);
        const tntKey = fileKeys.find(x => x.endsWith('.fnt'));
        const { info, file } = resources[tntKey];
        const { filename, basename, resname, loadType, xhrType } = info;

        // console.warn(info, file);
        const dataContent = await file.async('text');

        const format = autoDetectFormat(dataContent);
        if (!format) { return; }

        const data = format.parse(dataContent);
        const textures: utils.Dict<Texture> = {};

        const blob = await file.async('blob');
        const url = URL.createObjectURL(blob);

        this._loader.add({
            name: resname,
            url,
            loadType,
            xhrType,
            onComplete: (res: ILoaderResource) => {
                if (res.error) {
                    console.error(res.error);
                }
            },
        });
        // load frames
        const resource = this._loader.resources[resname];
        const baseUrl = this.getBaseUrl(this._loader, resource);

        // Handle completed, when the number of textures
        // load is the same number as references in the fnt file
        const completed = (page: ILoaderResource): void => {
            textures[page.metadata.pageFile] = page.texture;

            if (Object.keys(textures).length === data.page.length) {
                resource.bitmapFont = BitmapFont.install(dataContent, textures, true);
            }
        };

        for (let i = 0; i < data.page.length; ++i) {
            const pageFile = data.page[i].file;

            let url = baseUrl + pageFile;
            let exists = false;
            const imageKey = fileKeys.find(x => x.endsWith(pageFile));
            const imageData = <ZipAssetFile>resources[imageKey];
            if (imageData.file) {
                const imageBlob = await imageData.file.async('blob');
                url = URL.createObjectURL(imageBlob);
            }
            // console.warn(pageFile, url, resources, url);

            // incase the image is loaded outside
            // using the same loader, resource will be available
            for (const name in this._loader.resources) {
                const bitmapResource: ILoaderResource = this._loader.resources[name];
                if (bitmapResource.url === url) {
                    bitmapResource.metadata.pageFile = pageFile;
                    if (bitmapResource.texture) {
                        completed(bitmapResource);
                    } else {
                        bitmapResource.onAfterMiddleware.add(completed);
                    }
                    exists = true;
                    break;
                }
            }

            // texture is not loaded, we'll attempt to add
            // it to the load and add the texture to the list
            if (!exists) {
                // Standard loading options for images
                const options = {
                    crossOrigin: resource.crossOrigin,
                    loadType: LoaderResource.LOAD_TYPE.IMAGE,
                    metadata: Object.assign(
                        { pageFile },
                        resource.metadata.imageMetadata
                    ),
                    parentResource: resource,
                };

                this._loader.add(url, options, completed);
            }
        }
    }

    private static dirname(url: string): string {
        const dir = url
            .replace(/\\/g, '/') // convert windows notation to UNIX notation, URL-safe because it's a forbidden character
            .replace(/\/$/, '') // replace trailing slash
            .replace(/\/[^\/]*$/, ''); // remove everything after the last

        // File request is relative, use current directory
        if (dir === url) {
            return '.';
        }
        // Started with a slash
        else if (dir === '') {
            return '/';
        }

        return dir;
    }

    private static getBaseUrl(loader: Loader, resource: ILoaderResource): string {
        let resUrl = !resource.isDataUrl ? this.dirname(resource.url) : '';

        if (resource.isDataUrl) {
            if (resUrl === '.') {
                resUrl = '';
            }

            if (loader.baseUrl && resUrl) {
                // if baseurl has a trailing slash then add one to resUrl so the replace works below
                if (loader.baseUrl.charAt(loader.baseUrl.length - 1) === '/') {
                    resUrl += '/';
                }
            }
        }

        // remove baseUrl from resUrl
        resUrl = resUrl.replace(loader.baseUrl, '');

        // if there is an resUrl now, it needs a trailing slash. Ensure that it does if the string isn't empty.
        if (resUrl && resUrl.charAt(resUrl.length - 1) !== '/') {
            resUrl += '/';
        }

        return resUrl;
    }

    public static async parseFile(data: ZipResource): Promise<void> {
        const { file, info } = <ZipAssetFile>data;
        const { filename, resname, loadType, xhrType } = info;
        const blob = await file.async('blob');
        const url = URL.createObjectURL(blob);

        this._loader.add({
            name: resname,
            url,
            loadType,
            xhrType,
            onComplete: (res) => {
                if (res.error) {
                    console.error(res.error);
                }
                this._DEBUG && console.warn(`LOAD: ${filename} > DONE`);
            },
        });
        this._DEBUG && console.warn(this._loader.resources);
    }

    public static checkFileInfo(filename: string): ZipPartInfo {
        const dotIdx = filename.lastIndexOf('.');
        const basename = filename.substring(0, dotIdx);
        const dirname = filename.substring(0, filename.indexOf('/'));
        const resname = basename.substring(basename.lastIndexOf('/') + 1);
        const ext = filename.substring(dotIdx + 1);
        const loadType = LoadTypeMap[ext] || LoaderResource.LOAD_TYPE.XHR;
        const xhrType = XHRTypeMap[ext] || LoaderResource.XHR_RESPONSE_TYPE.TEXT;
        return { filename, basename, dirname, resname, ext, loadType, xhrType };
    }
}
