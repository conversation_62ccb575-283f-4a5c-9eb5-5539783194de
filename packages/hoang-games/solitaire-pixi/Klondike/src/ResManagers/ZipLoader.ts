import J<PERSON>Z<PERSON> from 'jszip';
import { ILoaderResource, Loader, utils } from '../pixi';
import { ZipExtractor, ZipResource } from './ZipExtractor';

export class ZipLoader {
    private static _loader: Loader;

    public static async loadFromUrl(loader: Loader, zipUrl: string): Promise<utils.Dict<ILoaderResource>> {
        ZipLoader._loader = loader;
        let result = null;
        try {
            if (loader.baseUrl?.length > 0 && !zipUrl.includes(loader.baseUrl)) { zipUrl = `${loader.baseUrl}${zipUrl}`; }
            const zipData = await ZipLoader._loadZipFile(zipUrl);
            await ZipLoader._loadFilesFromZip(zipData);
            console.warn(`LOAD: ${zipUrl} > DONE`);
            result = loader.resources;
        } catch (error) {
            console.error(`LOAD: ${zipUrl} > FAILED`, error);
        }
        return result;
    }

    public static async _loadZipFile(url: string): Promise<JSZip> {
        const response = await fetch(url);
        const bufferData = await response.arrayBuffer();
        return JSZip.loadAsync(bufferData);
    }

    private static async _loadFilesFromZip(zip: JSZip): Promise<void> {
        const assetFiles = this._groupAssetByTypes(zip.files);
        const assetIds = Object.keys(assetFiles);

        for (let i = 0; i < assetIds.length; i++) {
            const id = assetIds[i];
            const data = assetFiles[id];
            await ZipExtractor.parse(this._loader, { id, data });
        }
        return new Promise<void>((resolve) => {
            this._loader.load((loader, resources) => {
                // console.warn(resources);
                resolve();
            });
        });
    }

    private static _groupAssetByTypes(files: utils.Dict<JSZip.JSZipObject>): utils.Dict<ZipResource> {
        const assetFiles = {};
        Object.keys(files).forEach((filename) => {
            const file = files[filename];
            if (!file.dir) {
                const info = ZipExtractor.checkFileInfo(filename);
                const basename = info.basename;
                if (!assetFiles[basename]) {
                    assetFiles[basename] = {};
                }
                assetFiles[basename][filename] = { file, info };
            }
        });
        Object.keys(assetFiles).forEach((assetId) => {
            const data = assetFiles[assetId];
            const childKeys = Object.keys(data);
            if (childKeys.length === 1) {
                const childKey = childKeys[0];
                const childData = data[childKey];
                assetFiles[childKey] = childData;
                delete assetFiles[assetId];
            }
        });
        return assetFiles;
    }
}