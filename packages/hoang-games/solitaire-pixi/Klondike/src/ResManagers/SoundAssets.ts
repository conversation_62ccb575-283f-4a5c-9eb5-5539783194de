/* auto generate by sound config */
export const SoundFormat = [ 'mp3' ];
export const PreLoadPacks: string[] = [ 'spk0', ];
export const InGamePacks: string[] = [ ];
// eslint-disable-next-line no-use-before-define
export type SoundId = typeof SoundNames[number];
export const MusicIds: SoundId[] = [ ];
export const SoundNames = [ 'BUTTON_CLICK', 'CARD', 'WIN', 'CARD_CLICK', 'CARD_FLIP', 'NO_HINT', 'MOVE_WIN', 'DEAL_CARD', ] as const;
export const RawConfigs = [
    { p: 'spk0', s: 'click-btn' },
    { p: 'spk0', s: 'card' },
    { p: 'spk0', s: 'win' },
    { p: 'spk0', s: 'card_click' },
    { p: 'spk0', s: 'card_flip' },
    { p: 'spk0', s: 'no_hint' },
    { p: 'spk0', s: 'move_win' },
    { p: 'spk0', s: 'deal_card', v: 0.6 },
];