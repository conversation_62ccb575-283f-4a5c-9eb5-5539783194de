export class Launcher {
    private _isStandalone: boolean = true;
    private _parentNode: HTMLElement;
    private _gameNode: HTMLElement | null = null;
    private _lazyBundles: HTMLElement[] = [];
    private _appInst: any;
    private _wasLoadLazyScripts: boolean = false;
    private _lazyHolderId: string = null;
    private _loadLazyCb: () => void;

    constructor(parent: HTMLElement, isPortal: boolean) {
        this._parentNode = parent;
        this._isStandalone = !isPortal;

        console.log('[GAME] Launcher');
        globalThis.jg.load = () => {
            this._lazyLoadScripts();
            this._setup();
            this._loadGame();
        };
    }

    private async _lazyLoadScripts(): Promise<void> {
        const lazyScripts = document.createElement('div');
        this._lazyHolderId = 'lz-scripts';
        lazyScripts.id = this._lazyHolderId;
        this._parentNode.prepend(lazyScripts);

        let lazyModules = null;
        try {
            lazyModules = JSON.parse(`${process.env.LAZY_BUNDLES}`);
        } catch (error) {
            console.log('No lazy scripts!');
            this._wasLoadLazyScripts = true;
            return;
        }
        const loaders: Promise<{ status: boolean, message?: string }>[] = [];
        for (let i = 0; i < lazyModules.length; i++) {
            const { src, async, type, id } = lazyModules[i];
            // console.warn(`Load ${src}, ${async}, ${type}, ${id}`);
            loaders.push(this._loadAsyncJS(lazyScripts, `${globalThis.sharedConfig().RUN_PATH}${src}`.replace(/\/\//g, '/'), id, async, type));
        }
        await Promise.all(loaders).then((status) => {
            console.log('Lazy script done!');
            this._wasLoadLazyScripts = true;
            this._loadLazyCb?.();
        }).catch((status) => {
            console.error(status.message);
        });
    }

    private _loadAsyncJS(parent: HTMLElement, src: string, id: string, async?: boolean, type?: string): Promise<{ status: boolean, message?: string }> {
        return new Promise<{ status: boolean, message?: string }>((resolve, reject) => {
            try {
                const element = document.createElement('script');
                element.setAttribute('src', src);
                id && element.setAttribute('id', id);
                type && type.length > 0 && element.setAttribute('type', type);
                async && element.setAttribute('async', async + '');
                element.addEventListener('load', (ev: Event) => {
                    element.onerror = null;
                    this._lazyBundles.push(element);
                    element.remove();
                    resolve({ status: true });
                });
                element.onerror = (ev) => {
                    element.remove();
                    reject({
                        status: false,
                        message: `Unable to load: \`${src}\``
                    });
                };
                parent.append(element);
            } catch (err) {
                reject(err);
            }
        });
    }

    private _loadGame() {
        const holderElement = document.getElementById(this._lazyHolderId);
        if (!holderElement) {
            console.error('Load game failed!');
            return;
        }

        this._loadAsyncJS(holderElement, `${globalThis.sharedConfig().RUN_PATH}${process.env.CHUNK_GAME}.js`, `g_${process.env.LIB_NAME}`, true).then((status) => {
            const setupAppFunc = () => {
                // console.log('Init app!');
                this._setupRenderFunc();
                const { MainApp } = globalThis[`${process.env.LIB_NAME}`];
                this._appInst = new MainApp(this._gameNode);
                globalThis.sharedConfig()?.event?.on('exit-game', async () => {
                    console.warn('Quit Game!');
                    await this._appInst.quit();
                    this.destroy();
                });
            };
            if (!this._wasLoadLazyScripts) {
                this._loadLazyCb = () => {
                    setupAppFunc();
                };
            } else {
                setupAppFunc();
            }
        });
    }

    private _setupRenderFunc() {
        // #!if ENV === 'production'
        let app;
        globalThis.onInit = (ctx, quitFunc) => {
            if (!ctx) return;
            app = ctx;
            const renderer = app?.renderer;
            const original = renderer['render'].bind(renderer);
            app.renderer.render = (...args) => {
                // console.log(args);
                original.apply(renderer, args);
            };
        };
        // #!endif
    }

    private _setup() {
        const rootDiv = globalThis.jg.getRootDiv();
        const injectSplash = [
            '<div id="game-container">',
            '    <canvas id="game-canvas" tabindex="0" hidden></canvas>',
            '</div>',
        ].join('\n');
        rootDiv.insertAdjacentHTML('beforeend', injectSplash);

        this._gameNode = document.getElementById('game-container');
    }

    /* private _loadCss(source, parentNode = null) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.href = source;
            link.type = 'text/css';
            link.rel = 'stylesheet';
            link.onload = resolve;
            link.onerror = () => {
                const msg = `Load ${source} failed!`;
                console.error(msg);
                reject(new Error(msg));
            };
            (parentNode ?? document.head).appendChild(link);
        });
    } */

    public destroy() {
        console.log('Launcher Destroy...');
        // globalThis.sharedConfig()?.event?.emit('launcher-destroy');
        document.querySelector(`div#${this._lazyHolderId}`)?.remove();
    }
}

export function run(gameShell, isPortal: boolean = false) {
    new Launcher(gameShell, isPortal);
}

globalThis.sharedConfig = () => globalThis[`${process.env.LIB_NAME}_cfg`];

window.onload = () => {
    if (!globalThis.sharedConfig()) {
        const hrefUrl = new URL(window.location.href);
        const accessToken = hrefUrl.searchParams.get('token');
        const lang = hrefUrl.searchParams.get('lang');
        globalThis.sharedConfig = () => ({
            IS_PORTAL: false,
            RUN_PATH: globalThis.jg.gameConfig.STATIC_ROOT_PATH ?? '',
            ASSET_FOLDER: process.env.ASSET_FOLDER,
            TOKEN: accessToken,
            language: lang
        });
        const rootDiv = globalThis.jg.getRootDiv();
        run(rootDiv, false);
    }
};