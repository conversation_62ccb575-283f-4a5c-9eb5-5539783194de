import { AppState } from '../AppState';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import { PopupManager } from '../Popup/PopupManager';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { LoadingBar } from '../UI/LoadingBar';
import UIManager from '../UI/UIManager';
import { Button } from '../UI/core/Button';
import Utils from '../Utils/Utils';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import { Tween } from '../core/utility/tween/Tween';
import { MainApp } from '../main';
import { Loader, Point, Sprite } from '../pixi';
import { MultiResolutionHandler, ScreenMetadata } from './MultiResolutionHandler';
import SceneBase from './SceneBase';
import SceneManager from './SceneManager';

export default class LoadingScene extends SceneBase {
    constructor() {
        super();
        this.container.visible = false;
        MultiResolutionHandler.inst.addResizeListener(this._onResize, true);
    }

    private _onResize = (screen: ScreenMetadata) => {
        this.container.x = -screen.offsetX / screen.scaleX;
    };

    public static inst: LoadingScene = null;

    private readonly _TIME_FADE_IN_WEB_BACKGROUND = 1;
    private readonly _TIME_FADE_IN_WHITE_LAYER = 150;

    private _splashSprite: Sprite;
    private _loadingBar: LoadingBar = null;
    private _startButton: Button = null;

    private _isLoadingAssetsLoaded = false;
    private _isGameAssetsLoaded = false;
    private _isGameReady = false;
    private _errorCount = 0;

    public load(callback?: Function): void {
        if (LoadingScene.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
        }
        LoadingScene.inst = this;

        ResourceManager.loadZip(`${globalThis.sharedConfig().RUN_PATH}${process.env.ASSET_FOLDER}.zip`).then((resources) => {
            Localization.loadLanguage(globalThis.sharedConfig().language);

            if (!this._splashSprite) {
                const { realWidth, realHeight } = MultiResolutionHandler.inst.getScreen();
                this._splashSprite = new Sprite(ResourceManager.getTexture('splash-sprite'));
                this._splashSprite.position.set(realWidth / 2, realHeight / 2);
                this._splashSprite.anchor.set(0.5);
                this.container.addChild(this._splashSprite);
            }

            // const { realWidth, realHeight } = MultiResolutionHandler.inst.getScreen();
            // this._loadingBar = new LoadingBar();
            // this._loadingBar.reset();
            // this._loadingBar.container.position.set(realWidth / 2, realHeight * 0.9);
            // this.container.addChild(this._loadingBar.container);

            // this.container.visible = true;

            callback?.();
        });
        // this._loadLoadingResources().then(() => {
        //     // console.log(` >>> load loading resources finished!!!`);
        // });
    }

    public active(): void {
        if (!this._isLoadingAssetsLoaded) {
            this.load(this._onActive.bind(this));
        } else {
            this._onActive();
        }
        AppState.pushState(AppState.State.Loading);
    }

    private _onActive(): void {
        /**
         * If there is 'history' param in url then we should go skip WebSocket connection
         */
        this._loadGameResources();

        MainApp.inst.app.loader.onProgress.add(this._onLoadingProgress.bind(this));
        MainApp.inst.app.loader.onError.add(this._onLoadingError.bind(this));
        MainApp.inst.app.loader.onComplete.once(this._onLoadingComplete.bind(this));

        UIManager.inst.initUIComponents();
        PopupManager.initializePopups();

        Events.on(UIEvents.LOAD_SOUND, (bundle) => {
            this._onGameStart();
        });
        SoundManager.inst.loadSounds('PreGame');
    }

    public deactive(): void {
        MultiResolutionHandler.inst.removeListener(this._onResize);
        AppState.clearStates();
    }

    onPause() {}

    onResume() {}

    public update(dt: number): void {
        // console.log(`loading: ${this._isGameAssetsLoaded} - ${this._isJoinGameSuccess}`);
        if (this._isGameAssetsLoaded) {
            this._loadingBar.container.visible = false;
            if (!this._startButton.getContainer().visible) {
                this.enterTheGame();
            }
            this._startButton.getContainer().visible = true;
        }

        this._loadingBar?.update(dt);
    }

    public destroy(): void {
        //this._splashSkeleton.destroy();
        //this.container.destroy();
    }

    public getNumberOfErrors(): number {
        return this._errorCount;
    }


    private _loadGameResources(showHistory: boolean = false): void {
        if (this._isGameAssetsLoaded) {
            return;
        }

        this._errorCount = 0;
        // this._loadingBar.progress = 0;
        SoundManager.inst.init();
        // ResourceManager.lazyLoadResource('PreGame');
    }

    private enterTheGame() {
        console.log('[APP] Game Start!');

        Scheduler.setTimeout(100, () => {
            //globalThis.htmlLoader.fadeOut(); // remove loading bar in html
            // SoundManager.inst.playSfx('SPLASH_ENTER');
            this._onGameStart();
        });
        SoundManager.inst.checkResumeContext();
    }

    private _onGameStart(): void {
        // fade in base game background
        // SoundManager.inst.playSfx('BUTTON_CLICK');
        SceneManager.inst.goToScene(SceneManager.inst.gameScene);
    }

    private _onLoadingProgress(loader: Loader) {
        // console.error(`_onLoadingProgress: ${loader.progress} - ${loader.loading}`);
        const roundedProgress = Math.round(loader.progress);
        //globalThis.htmlLoader?.updateProgress(roundedProgress); // remove loading bar in html
        this._loadingBar.progress = roundedProgress;
        SoundManager.inst.onLoadingProgress(roundedProgress);
    }

    // eslint-disable-next-line no-unused-vars
    private _onLoadingError(loader) {
        // console.error('loading error: ', loader);
        this._errorCount++;

        /**
         * If there is any error while load assets, let hide loading bar and show error Popup
         */
        if (PopupManager.loadingResourcePopup && PopupManager.loadingResourcePopup.isOpenning()) {
            PopupManager.loadingResourcePopup.close();
        }

        if (!PopupManager.getConfirmPopup().isOpenning()) {
            this._loadingBar.container.visible = false;
            // GameNetworkStateMgr.inst.disconnect();
            PopupManager.getConfirmPopup().open(Localization.getText('NO_CONNECTION'), Localization.getText('RELOAD'), () => {
                location.reload();
            });
        }
    }

    private _zoomOut(target: Point) {
        Tween.to(500, target, { x: 1.0, y: 1.0 }, 0, () => {
            this._zoomIn(target);
        });
    }

    private _zoomIn(target: Point) {
        Tween.to(500, target, { x: 1.1, y: 1.1 }, 0, () => {
            this._zoomOut(target);
        });
    }

    private async _onLoadingComplete() {
        if (this._errorCount > 0) {
            if (!PopupManager.getConfirmPopup().isOpenning()) {
                this._loadingBar.container.visible = false;
                PopupManager.getConfirmPopup().open(Localization.getText('NO_CONNECTION'), Localization.getText('RELOAD'), () => {
                    location.reload();
                });
            }
            return;
        }
        await Utils.Delay(100); //Delay for waiting reponse from server

        this._isGameAssetsLoaded = true;

        // init ui, popup
        UIManager.inst.initUIComponents();
        PopupManager.initializePopups();
        // BoardAnimation.inst.init();

        // don't load pack InGame for history redirect
        ResourceManager.lazyLoadResource('InGame');
        SoundManager.inst.loadSounds('InGame');

        /* globalThis.htmlLoader.onClick = () => { // remove loading bar in html
            this.enterTheGame();
        }; */

        MainApp.inst.app.loader.onLoad.add(this._onResourceLoadCompleted.bind(this));
        //@phong.tt debug
        // globalThis.loadBundle();
        globalThis.loadBundle2();
    }

    private _onResourceLoadCompleted(loader: any, resource: any) {
        if (ResourceManager._getAssetRequests.length > 0) {
            for (let i = 0; i < ResourceManager._getAssetRequests.length; ++i) {
                const req = ResourceManager._getAssetRequests[i];

                //Check if there is waiting asset has same name as loaded asset
                const assetWaitingFor = req.assets.filter((a) => a.name === resource.name)[0];

                //If found waiting asset
                if (assetWaitingFor !== undefined) {
                    //Set data
                    assetWaitingFor.data = resource;

                    //Check if all waiting assets from request have its data
                    const allResourcesReady = req.assets.filter((a) => a.data === undefined)[0] === undefined;

                    //If all waiting assets have data, then call the callback
                    if (allResourcesReady) {
                        const returnVal = req.assets.map((a) => a.data);
                        req.callback(returnVal);
                        ResourceManager._getAssetRequests.splice(ResourceManager._getAssetRequests.indexOf(req), 1);
                        --i;
                    }
                }
            }
        }
    }
}
