import { Container, settings } from '../pixi';
import { MainApp } from '../main';
import GameScene from './GameScene';
import LoadingScene from './LoadingScene';
import SceneBase from './SceneBase';
import AutoUpdateContainer from './AutoUpdateContainer';
import { Tween } from '../core/utility/tween/Tween';

export default class SceneManager {
    public static inst: SceneManager = null;
    public container: Container = null;
    constructor() {
        if (SceneManager.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
            return SceneManager.inst;
        }
        SceneManager.inst = this;

        this.container = new Container();
        this.container.name = 'Scenes';
        MainApp.inst.app.ticker.add(this.onUpdate, this);

        this.loadingScene = new LoadingScene();
        this.container.addChild(this.loadingScene.container);
        this.gameScene = new GameScene();
        this.container.addChild(this.gameScene.container);
    }

    public onUpdate(dtScalar: number) {
        const dt = dtScalar / settings.TARGET_FPMS / 1000;

        // GameStateMgr.getInstance().update(dt);

        //Scene update
        this.curScene?.update(dt);

        AutoUpdateContainer.activatedContainers.forEach((container) => {
            container.update(dt);
        });
    }

    public loadingScene: SceneBase;
    public gameScene: SceneBase;

    public curScene: SceneBase = null;

    public goToScene(nextScene: SceneBase, skipResumeGame: boolean = false) {
        if (this.curScene) {
            this.curScene.deactive();
            const oldScene = this.curScene;
            Tween.to(150, oldScene.container, { alpha: 0 }, 0, () => {
                oldScene.container.visible = false;
            });

            nextScene.container.visible = true;
            nextScene.container.alpha = 0;
            Tween.to(150, nextScene.container, { alpha: 1 });
        } else {
            nextScene.container.visible = true;
        }

        this.curScene = nextScene;
        this.curScene.active(skipResumeGame);
    }

    public onVisibilityChange(isVisible: boolean) {
        if (this.curScene) {
            if (isVisible) {
                this.curScene.onResume();
            } else {
                this.curScene.onPause();
            }
        }
    }
}
