import { Container } from '../pixi';

export default abstract class SceneBase {
    public static allScenes: SceneBase[] = [];
    public container: Container = null;

    constructor() {
        SceneBase.allScenes.push(this);
        this.container = new Container();
        this.container.name = this.constructor.name;
    }

    public abstract load (callback?: Function): void;

    public abstract active (skipResumeGame: boolean): void;

    public abstract deactive (): void;

    public abstract update (dt: number): void;

    public abstract onPause(): void;

    public abstract onResume(): void;

    public onWSConnected(): void {}

    public onWSError(error: string): void {}

    public onWSReconnect(retryTimes: number): void {}
}