import { Container, IPointData } from '../pixi';
import { ResizeCallback, ScreenMetadata } from './MultiResolutionHandler';

export type AlignPosition = {
    vertical?: 'TOP' | 'CENTER' | 'BOTTOM';
    horizontal?: 'LEFT' | 'CENTER' | 'RIGHT';
};

/* eslint-disable no-unused-vars */
export type ExpandSizeCallback = (realStageY: number, expandFactor: number) => void;
/* eslint-enable no-unused-vars */

export class AutoAlignContainer extends Container {
    public static activatedContainers: AutoAlignContainer[] = [];
    public static screen: ScreenMetadata = null;

    private _alignType: AlignPosition;
    private _offset: IPointData;
    private _resizeCallback: ResizeCallback;
    private _expandSizeCallback: ExpandSizeCallback;

    constructor(alignType: Partial<AlignPosition> = { horizontal: 'CENTER', vertical: 'CENTER' }, offset: Partial<IPointData> = { x: 0, y: 0 }) {
        super();
        this._alignType = Object.assign(<AlignPosition>{ horizontal: 'CENTER', vertical: 'CENTER' }, alignType);
        this._offset = Object.assign(<IPointData> { x: 0, y: 0 }, offset);
        this.dryUpdate();
        AutoAlignContainer.activatedContainers.push(this);
    }

    destroy() {
        AutoAlignContainer.activatedContainers.forEach((el, idx, arr) => {
            if (this === el) {
                arr.splice(idx, 1);
            }
        });
        super.destroy();
    }

    public updatePosition(screen: ScreenMetadata): void {
        const { realWidth, realHeight, baseWidth, baseHeight, maxWidth, maxHeight, offsetX: stageX, offsetY: stageY, scaleX, scaleY, isPortrait } = screen;
        const realStageX = stageX / scaleX;
        const realStageY = stageY / scaleY;
        let alignX = 0, alignY = 0;

        switch (this._alignType.horizontal) {
            case 'LEFT':
                alignX = -realStageX;
                break;
            case 'CENTER':
                alignX = realWidth / 2 - realStageX;
                break;
            case 'RIGHT':
                alignX = realWidth - realStageX;
                break;
        }

        switch (this._alignType.vertical) {
            case 'TOP':
                alignY = -realStageY;
                break;
            case 'CENTER':
                alignY = realHeight / 2 - realStageY;
                break;
            case 'BOTTOM':
                alignY = realHeight - realStageY;
                break;
        }

        this.x = this._offset.x + alignX;
        this.y = this._offset.y + alignY;

        this._resizeCallback?.(screen);
        if (this._expandSizeCallback) {
            // this factor in range [0, 1] same as [base height, max height]
            const expandFactor = Math.max((Math.round(realHeight) - baseHeight) / (maxHeight - baseHeight), 0);
            this._expandSizeCallback(realStageY, expandFactor);
        }
    }

    public dryUpdate(): void {
        if (!AutoAlignContainer.screen) {
            console.warn('Cant resize before init value!');
            return;
        }
        this.updatePosition(AutoAlignContainer.screen);
    }

    public get alignType(): AlignPosition {
        return this._alignType;
    }

    public set alignType(value: AlignPosition) {
        this._alignType = value;
        this.dryUpdate();
    }

    public get offset(): IPointData {
        return this._offset;
    }

    public set offset(value: IPointData) {
        this._offset = value;
        this.dryUpdate();
    }

    public getScreenData(): ScreenMetadata {
        return AutoAlignContainer.screen;
    }

    public addResizeListener(handler: ResizeCallback, startNow: boolean = false): void {
        this._resizeCallback = handler;
        if (startNow) {
            this._resizeCallback?.(AutoAlignContainer.screen);
        }
    }

    public addExpandSizeListener(handler: ExpandSizeCallback): void {
        this._expandSizeCallback = handler;
    }

    public removeResizeListener(): void {
        this._resizeCallback = undefined;
        this._expandSizeCallback = undefined;
    }

    public static onSizeChanged(data: ScreenMetadata): void {
        AutoAlignContainer.screen = data;
        AutoAlignContainer.activatedContainers.forEach((container) => {
            container.updatePosition(data);
        });
    }
}
