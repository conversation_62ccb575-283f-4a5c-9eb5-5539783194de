import { AppState } from '../AppState';
import MainGameBoard from '../Game/MainGameBoard';
import UIManager from '../UI/UIManager';
import { MultiResolutionHandler, ScreenMetadata } from './MultiResolutionHandler';
import SceneBase from './SceneBase';

export default class GameScene extends SceneBase {
    constructor() {
        super();

        MultiResolutionHandler.inst.addResizeListener(this._onResize, true);
    }

    private _onResize = (screen: ScreenMetadata) => {
        const { offsetX, offsetY, scaleX, scaleY, realWidth, realHeight } = screen;
        const realStageX = offsetX / scaleX;
        const realStageY = offsetY / scaleY;
        this.container.position.set(realWidth / 2 - realStageX, realHeight / 2 - realStageY);
    };

    public static inst: GameScene = null;

    private _boardContainer: MainGameBoard = null;
    private _isLoaded = false;
    private _isActive = false;

    public load(callback?: Function): void {
        if (GameScene.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
        }
        GameScene.inst = this;

        this._initializeScene().then(() => {
            // console.log(` >>> load loading resources finished!!!`);
            this._isLoaded = true;
            if (callback) {
                callback();
            }
        });
    }

    public active(skipResumeGame: boolean): void {
        if (!this._isLoaded) {
            this.load(() => {
                this._onActive(skipResumeGame);
            });
            this._loadGameSetting();
        } else {
            this._onActive(skipResumeGame);
        }
        AppState.pushState(AppState.State.MainGame);
    }

    private _onActive(skipResumeGame: boolean): void {
        if (!skipResumeGame) {
            //resume the game
            this._boardContainer.onActive();
        }
        UIManager.inst.popupContainer.sortChildren(); //sort order of popup
        this._isActive = true;

        globalThis.gCanvas.hidden = false;
        const loadingIcon = document.getElementById('loading-icon');
        if (loadingIcon) {
            loadingIcon.hidden = true;
            loadingIcon.parentElement.hidden = true;
        }
    }

    public deactive(): void {
        MultiResolutionHandler.inst.removeListener(this._onResize);
        UIManager.inst.deactiveSpinPanel();
        this._isActive = false;
        AppState.clearStates();
    }

    public isActive(): boolean {
        return this._isActive;
    }

    onPause() {
        this._boardContainer?.onPause();
    }

    onResume() {
        this._boardContainer?.onResume();
    }

    // eslint-disable-next-line no-unused-vars
    public update(dt: number): void {}

    private async _initializeScene(): Promise<void> {
        return new Promise((resolve) => {
            this._boardContainer = new MainGameBoard();
            this._boardContainer.init();
            this.container.addChild(this._boardContainer);
            resolve();
        });
    }

    public onWSReconnect(retryTimes: number): void {
        // Events.emit(NetworkEvents.WS_CONNECTED, false);
    }

    public onWSConnected(): void {
        // Events.emit(NetworkEvents.WS_CONNECTED, true);
    }

    private _loadGameSetting() {}

    public getGameBoard(): MainGameBoard {
        return this._boardContainer;
    }
}
