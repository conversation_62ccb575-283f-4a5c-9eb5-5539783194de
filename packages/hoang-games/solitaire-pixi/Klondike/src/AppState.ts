/**
 * Manage application states
 */
export class AppState {
    private static _stackOfStates: AppState.State[] = [];

    /**
     * Get current state of application
     */
    public static curState(): AppState.State {
        return this._stackOfStates[this._stackOfStates.length - 1];
    }

    /**
     * Get current stack of states
     */
    public static curStack(): AppState.State[] {
        return this._stackOfStates;
    }

    /**
     * Add a new state to stack
     * @param state next state
     */
    public static pushState(state: AppState.State) {
        this._stackOfStates.push(state);
    }

    /**
     * Remove the last state from stack
     */
    public static popState() {
        this._stackOfStates.pop();
    }

    /**
     * Clear all states in stack
     */
    public static clearStates() {
        this._stackOfStates = [];
    }
}

export namespace AppState {
    export enum State {
        Init = 'init',
        Loading = 'loading',
        MainGame = 'game',
        MiniGame = 'mini-game',
        Menu = 'Menu',
        Popup = 'Popup'
    }
}