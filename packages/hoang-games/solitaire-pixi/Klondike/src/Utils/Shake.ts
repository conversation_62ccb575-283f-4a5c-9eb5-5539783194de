
export class Shake {
    private _frequency: number;
    private _duration: number;
    private _samples: number[] = [];
    private _isShaking = false;

    /**
     * Initializes a shakeing pattern
     * @param duration The length of the shake in miliseconds
     * @param frequency The frequency of the shake in Hertz
     */
    constructor(duration: number, frequency: number) {
        // The duration in miliseconds
        this._duration = duration;

        // The frequency in Hz
        this._frequency = frequency;

        // The sample count = number of peaks/valleys in the Shake
        const sampleCount = (duration / 1000) * frequency;

        // Populate the samples array with randomized values between -1.0 and 1.0
        for (let i = 0; i < sampleCount; i++) {
            this._samples.push(Math.random() * 2 - 1);
        }
    }

    public getAmplitude(t: number): number {
        // Get the previous and next sample
        const s = t / 1000 * this._frequency;
        const s0 = Math.floor(s);
        const s1 = s0 + 1;

        // Get the current decay
        const k = this._getDecay(t);

        // Return the current amplitide
        return (this._getNoise(s0) + (s - s0) * (this._getNoise(s1) - this._getNoise(s0))) * k;
    }

    /**
     * Get the decay of the shake as a floating point value from 0.0 to 1.0
     * @param t The time since the start of the shake in miliseconds
     * @returns
     */
    private _getDecay(t: number): number {
        // Linear decay
        if (t >= this._duration) return 0;
        return (this._duration - t) / this._duration;
    }

    /**
     * Retrieve the noise at the specified sample.
     * @param s The randomized sample we are interested in.
     * @returns
     */
    private _getNoise(s: number): number {
        // Retrieve the randomized value from the samples
        if (s >= this._samples.length) return 0;
        return this._samples[s];
    }
}