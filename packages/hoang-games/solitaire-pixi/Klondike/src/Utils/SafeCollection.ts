/**
 * Use key in map to storage value as a array
 * to avoid use filter()/slice() to remove element on javascript
 */

export class SafeCollection<T> {
    private _elements: Map<T, 0>;

    constructor(...elements: T[]) {
        this._elements = new Map<T, 0>();
        if (elements && elements.length > 0) {
            this.push(...elements);
        }
    }

    public has(element: T): boolean {
        return this._elements.has(element);
    }

    public push(...elements: T[]): void {
        elements.forEach((el) => this._elements.set(el, 0));
    }

    public remove(element: T): this {
        this._elements.delete(element);
        return this;
    }

    public length(): number {
        return this._elements.size;
    }

    public forEach(callback: (element: T) => void): void {
        this._elements.forEach((value: 0, key: T) => callback(key));
    }
}
