import AppConstants, { DrawMode } from '../AppConstants';
import { GameEvents, UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import { PopupManager } from '../Popup/PopupManager';
import ResourceManager from '../ResManagers/ResourceManager';
import AutoUpdateContainer from '../Scenes/AutoUpdateContainer';
import { MultiResolutionHandler, ScreenMetadata } from '../Scenes/MultiResolutionHandler';
import { SoundManager } from '../SoundManager';
import UIManager from '../UI/UIManager';
import { Button } from '../UI/core/Button';
import { ObjectPool } from '../Utils/ObjectPool';
import Utils from '../Utils/Utils';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import { Tween } from '../core/utility/tween/Tween';
import { Container, IPointData, InteractionEvent, Sprite, Text } from '../pixi';
import { Card, ECardStatus } from './Card';
import { CardHolder } from './CardHolder';

const COLUMNS = 7;
const RIGH_CARD_OFFSET = 20;
const CARD_MOVE_DURATION = 150;

export default class MainGameBoard extends AutoUpdateContainer {
    private _cardPool: ObjectPool<Card>;
    private _gameBg: Sprite;
    private _cardContainer: Container;

    private _deck: Card[]; // contains all cards for checking
    private _cards: Card[];
    private _drawedCards: Card[] = [];
    private _listCardWin: Card[] = [];

    private _tableauColumns: Container[] = [];
    private _winColumns: Container[] = [];
    private _startColumn: CardHolder;
    private _drawedColumn: CardHolder;

    private _tableauStacks: Card[] = [];
    private _winStacks: Card[] = [null, null, null, null];
    private _undoStacks = [];

    private _mode: DrawMode = DrawMode.ONE;
    private _isCheat: boolean = false;
    private _btnReset: Button;
    private _btnSolved: Button;

    private _scoreGame: number = 0;
    private _listTimeStamp: number[] = [];
    private _timer: number = 0;
    private _isStartCountTimer: boolean = false;
    private _isWin: boolean = false;
    private _loopSolved: string;
    private resetTime: number = 0;
    private hintCount: number = 0;
    private _isBlockDrawCard: boolean = false;

    constructor() {
        super();
        MultiResolutionHandler.inst.addResizeListener(this._onResize);
        Events.on(UIEvents.CLICK_CHANGE_LEVEL, this._changeLevel.bind(this));
        Events.on(UIEvents.CLICK_START, this._onClickStart.bind(this));
        Events.on(UIEvents.UNDO, this._undo.bind(this));
        Events.on(UIEvents.HINT, this.showHint.bind(this));
        Events.on(UIEvents.CHOOSE_DRAW_GAME, this._loadDrawGame.bind(this));
        Events.on(GameEvents.NEW_GAME, this._dealCards.bind(this));
        Events.on(UIEvents.ADD_INFO, () => {
            PopupManager.rankPopup.open(true);
        });
        Events.on(UIEvents.SHOW_CHOOSE_GAME, () => {
            PopupManager.newGamePopup.open(true);
        });
    }

    public init() {
        this._gameBg = new Sprite(ResourceManager.getTexture('splash-sprite'));
        this._gameBg.anchor.set(0.5);
        this.addChild(this._gameBg);

        // SoundManager.inst.playMusic('BGM');
        const { realWidth, realHeight } = MultiResolutionHandler.inst.getScreen();
        // init start stack
        this._startColumn = new CardHolder();
        this._startColumn.name = 'StartColumn';
        this._startColumn.init('boder_card');
        this._startColumn.x = -realWidth / 2 + 210;
        this._startColumn.y = -realHeight / 2 + this._startColumn.height / 2 + 85;
        this.addChild(this._startColumn);

        //btn reset card
        const bg = new Sprite(ResourceManager.getAtlasFrame('gui_klondike_atlas', 'empty_card'));
        this._btnReset = new Button({
            background: bg,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._resetDrawedCards();
        });
        this._startColumn.addChild(this._btnReset.getContainer());
        this._btnReset.getContainer().anchor.set(0.5);

        //btn solved card
        const solved_bg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'bg_arrow'));
        this._btnSolved = new Button({
            background: solved_bg,
        }).on('click', (e: InteractionEvent) => {
            e.stopPropagation();
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._autoSolve();
        });
        const icon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'icon_tick'));
        icon.anchor.set(0.5);
        icon.position.set(0, -8);
        this._btnSolved.getContainer().addChild(icon);

        const textSolved = new Text('Solved', {
            fontFamily: 'intersemibold',
            fontSize: 12,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        textSolved.anchor.set(0.5);
        textSolved.position.set(0, 13);
        this._btnSolved.getContainer().addChild(textSolved);

        this._btnSolved.getContainer().anchor.set(0.5);
        this._btnSolved.getContainer().position.set(-285, -195);
        this.addChild(this._btnSolved.getContainer());
        this._btnSolved.getContainer().visible = false;

        // init draw column
        this._drawedColumn = new CardHolder();
        this._drawedColumn.name = 'DrawedColumn';
        this._drawedColumn.init('boder_card');
        this._drawedColumn.x = this._startColumn.x + 140;
        this._drawedColumn.y = this._startColumn.y;
        this._drawedColumn.visible = false;
        this.addChild(this._drawedColumn);

        // init win stacks
        for (let i = 0; i < 4; i++) {
            const winStack = new CardHolder();
            winStack.init('boder_card');
            winStack.name = 'WinStack#' + i;
            winStack.x = i * (winStack.width + 46) + 20;
            winStack.y = -realHeight / 2 + winStack.height / 2 + 85;

            this.addChild(winStack);
            this._winColumns.push(winStack);
        }

        // init tableau stacks
        for (let i = 0; i < 7; i++) {
            const tableauStack = new CardHolder();
            tableauStack.init('behind_card');
            tableauStack.name = 'TableauStack#' + i;
            tableauStack.x = (i - 2.85) * (tableauStack.width + 33);
            tableauStack.y = -realHeight / 15 + 30;

            this.addChild(tableauStack);
            this._tableauColumns.push(tableauStack);
        }

        this._cardContainer = new Container();
        this._cardContainer.width = realWidth;
        this._cardContainer.height = realHeight;
        this._cardContainer.name = 'CardContainer';
        this._cardContainer.sortableChildren = true;
        this.addChild(this._cardContainer);
        // create pool
        const bufferSize = 52;
        this._cardPool = new ObjectPool<Card>(
            bufferSize,
            () => {
                const card = new Card();
                card.openCallback = this._startCallback.bind(this);
                card.drawCallback = this._drawCallback.bind(this);
                card.moveCallback = this._moveCallback.bind(this);
                card.endCallback = this._endCallback.bind(this);
                card.doubleClickCallback = this._doubleClickCallback.bind(this);
                card.rejectUndoCallback = this._rejectLastUndo.bind(this);
                return card;
            },
            (obj: Card) => {
                obj.stopWaitAnim();
                obj.reset();
                obj.visible = false;
            },
            null,
            'BLP',
            Math.round(bufferSize * 0.85),
        );
        this._createCard();
        UIManager.inst.activeSpinPanel();
    }

    private _onResize = (screen: ScreenMetadata) => {
        const { realWidth, realHeight, maxWidth, maxHeight } = screen;
        this._startColumn.x = -realWidth / 2 + 210;
        this._drawedColumn.x = this._startColumn.x + 140;
        const cardInDesk = this._deck.filter((card) => card.status === ECardStatus.IN_DECK);
        if (cardInDesk.length > 0) {
            cardInDesk.forEach((card) => {
                card.x = this._startColumn.x;
            });
        }
        // const lastThreeCard = this._drawedCards.slice(-3);
        // if (lastThreeCard.length === 0) {
        //     return;
        // }
    };

    private _createCard() {
        // create cards
        this._cards = [];
        this._getRandomArray().forEach((i) => {
            const card = this._cardPool.take();
            card.init(i);
            card.position.set(this._startColumn.x, this._startColumn.y);
            card.parentGroup = UIManager.inst.showingGroup;
            this._cardContainer.addChild(card);
            this._cards.push(card);
        });

        this._deck = this._cards.slice();
    }

    private _startCallback(card: Card) {
        const globalPos = this._cardContainer.toLocal(card.parent.toGlobal(card.position));

        let stackIndex;
        let fromStack;

        if (globalPos.y < this._winColumns[0].y + this._winColumns[0].height / 2) {
            if (globalPos.x > this._winColumns[0].x - this._winColumns[0].width / 2) {
                stackIndex = this._getTableauStackIndex(globalPos.x);
                fromStack = 'win';
                console.log('from Win', stackIndex);
            } else {
                fromStack = 'drawedCards';
                console.log('from drawedCards', stackIndex, card.point);
            }
        } else {
            stackIndex = this._getTableauStackIndex(globalPos.x);
            console.log('from tableau', stackIndex, card.point);
            fromStack = 'tableau';
        }

        const parent = (card.parent.parent as Card) ?? null;
        // console.log('PUSH UNDO', card, stackIndex, fromStack, parent);
        this._undoStacks.push({
            node: card,
            idx: stackIndex,
            from: fromStack,
            parentStatus: fromStack === 'tableau' && parent ? parent.isFront : false,
        });
    }

    private _drawCallback(node: Card) {
        const card = this._cards[this._cards.length - 1];
        if (card && card.scheduler) {
            console.log('clear timeout');
            Scheduler.clearTimeout(card.scheduler);
            card.stopWaitAnim();
            this.isShowHint = false;
        }

        if (this._mode === DrawMode.ONE) {
            this._drawOneCard();
        } else {
            this._drawThreeCards();
        }

        // console.log('PUSH UNDO', 'deck', card);
        this._undoStacks.push({ node: card, from: 'deck' });

        if (this.showHint(false)) {
            this.hintCount++;
        }

        UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
    }

    private _drawOneCard() {
        // this.blockTouch.active = true;
        const toPosition = this._drawedColumn.position;

        const cardNode = this._cards.pop();
        if (!cardNode) {
            // this.blockTouch.active = false;
            this._rejectLastUndo();
            return;
        }

        cardNode.status = ECardStatus.DRAWED_FROM_DECK;
        const index = this._drawedCards.length + 1 >= 3 ? 2 : this._drawedCards.length;

        this._arrangeDrawedCards();

        this._drawedCards.forEach((card) => {
            card.isUnder = true;
            card.parentGroup = UIManager.inst.drawedGroup;
        });
        this._drawedCards.push(cardNode);

        // lock reset button
        this._btnReset.setInteractable(false);

        cardNode.parentGroup = UIManager.inst.onTopGroup;
        cardNode.tweenTo({ x: toPosition.x + index * RIGH_CARD_OFFSET, y: toPosition.y }, CARD_MOVE_DURATION, 0, () => {
            // this.blockTouch.active = false;
            cardNode.turnToFront();
            this._btnReset.setInteractable(true);
            cardNode.parentGroup = UIManager.inst.drawedGroup;
        });
    }

    private _arrangeDrawedCards() {
        const toPosition = this._drawedColumn.position;

        if (this._drawedCards.length >= 3) {
            let idx = 0;
            // this.blockTouch.active = true;
            const hideCard = this._drawedCards[this._drawedCards.length - 3];
            for (let i = this._drawedCards.length - 2; i < this._drawedCards.length; i++) {
                this._drawedCards[i].tweenTo({ x: toPosition.x + idx * RIGH_CARD_OFFSET, y: toPosition.y }, CARD_MOVE_DURATION, 0, () => {
                    hideCard.visible = false;
                    this._drawedCards[i].parentGroup = UIManager.inst.drawedGroup;
                });
                idx++;
            }
        } else {
            // this.blockTouch.active = false;
        }
    }

    private _revealDrawedCard(resetCard: boolean = false) {
        const poped = this._drawedCards.pop();
        if (this._mode !== DrawMode.ONE) return;
        const toPosition = this._drawedColumn.position;

        const lastThreeCard = this._drawedCards.slice(-3);
        if (lastThreeCard.length === 0) {
            // this.blockTouch.active = false;
            return;
        }

        for (let i = 0; i < lastThreeCard.length; i++) {
            lastThreeCard[i].visible = true;
            lastThreeCard[i].parentGroup = UIManager.inst.onTopGroup;
            console.log('tween', lastThreeCard[i].name);
            lastThreeCard[i].tweenTo({ x: toPosition.x + i * RIGH_CARD_OFFSET, y: toPosition.y }, CARD_MOVE_DURATION, 0, () => {
                if (i === lastThreeCard.length - 1) {
                    // this.blockTouch.active = false;
                    if (resetCard) {
                        poped.position = this._startColumn.position;
                    }
                }
                lastThreeCard[i].parentGroup = UIManager.inst.drawedGroup;
            });
        }

        if (this._drawedCards.length - 1 >= 0) {
            this._drawedCards[this._drawedCards.length - 1].isUnder = false;
        }
    }

    private _drawThreeCards() {
        if (this._isBlockDrawCard) return;
        this._drawedCards.forEach((card) => {
            card.visible = false;
        });
        let firstNode: Card = null;
        this._isBlockDrawCard = true;
        // lock reset button
        this._btnReset.setInteractable(false);
        for (let i = 0; i < 3; i++) {
            const toPosition = this._drawedColumn.position;
            const cardNode = this._cards.pop();
            if (!cardNode) {
                //cc.log("break");
                this._isBlockDrawCard = false;
                this._btnReset.setInteractable(true);
                break;
            }
            cardNode.status = ECardStatus.DRAWED_FROM_DECK;
            cardNode.parentGroup = UIManager.inst.drawedGroup;
            cardNode.tweenTo({ x: toPosition.x + i * RIGH_CARD_OFFSET, y: toPosition.y }, CARD_MOVE_DURATION, 50 * i, () => {
                if (i == 0) {
                    firstNode = cardNode;
                } else {
                    firstNode.setRightChild(cardNode);
                }
                cardNode.turnToFront();
                if (i === 2) {
                    this._btnReset.setInteractable(true);
                    this._isBlockDrawCard = false;
                }
            });

            // cc.tween(cardNode)
            //     .delay(0.1 * i)
            //     .parallel(
            //         cc.tween().to(0.15, {
            //             position: cc.v2(toPosition.x - 22 + i * 27, toPosition.y),
            //         }),
            //         cc
            //             .tween()
            //             .delay(0.1)
            //             .set({ zIndex: 52 + i }),
            //     )
            //     .call(() => {
            //         if (i == 0) {
            //             firstNode = cardNode;
            //         } else {
            //             firstNode.setRightChild(cardNode);
            //         }
            //         cardNode.turnToFront();
            //         if (i === 2) {
            //             // this.blockTouch.active = false;
            //         }
            //     })
            //     .start();

            this._drawedCards.push(cardNode);
        }
    }

    private _moveCallback(ev: InteractionEvent, newPos: IPointData) {
        const globalPos = this._cardContainer.toLocal(ev.currentTarget.parent.toGlobal(newPos));

        if (Math.abs(globalPos.x) > Math.abs(AppConstants.BASE_SCREEN_WIDTH / 2) || Math.abs(globalPos.y) > Math.abs(AppConstants.BASE_SCREEN_HEIGHT / 2)) {
            console.log('out of bound');
            return;
        }

        ev.currentTarget.position.x = newPos.x;
        ev.currentTarget.position.y = newPos.y;
    }

    private _endCallback(ev: InteractionEvent) {
        const card = ev.currentTarget as Card;
        const globalPos = this._cardContainer.toLocal(ev.currentTarget.parent.toGlobal(ev.currentTarget.position));

        if (globalPos.y < this._winColumns[0].y + this._winColumns[0].height / 2) {
            if (globalPos.x > this._winColumns[0].x - this._winColumns[0].width / 2) {
                const stackIndex = this._getWinStackIndex(globalPos.x);
                console.log('to Win Col', stackIndex);
                this._moveToWinColumn(card, stackIndex);
            } else {
                this._rejectLastUndo();
                card.reset();
            }
        } else {
            const stackIndex = this._getTableauStackIndex(globalPos.x);

            if (!this._tableauStacks[stackIndex]) {
                console.log('to empty stack', stackIndex);
                this._moveToEmptyStack(card, stackIndex, this._isCheat);
            } else if (card === this._tableauStacks[stackIndex]) {
                console.log('to same stack', stackIndex);
                this._rejectLastUndo();
                card.reset();
            } else {
                console.log('to tableau stack', stackIndex);
                this._moveToTableauColumn(card, stackIndex, this._isCheat);
            }
        }
    }

    private _moveToEmptyStack(card: Card, stackIndex: number, force: boolean = false) {
        // king card
        if (card.point === 13 || force) {
            card.setParent(this._cardContainer);
            card.position = this._tableauColumns[stackIndex].position;
            card.status = ECardStatus.IN_PLAY;
            this._tableauStacks[stackIndex] = card;
            card.setParentGroup(UIManager.inst.showingGroup);

            this._tableauStacks.forEach((stackCard, index) => {
                if (stackCard === card && index !== stackIndex) {
                    this._tableauStacks[index] = null;
                }
            });

            this._openLastCardInStack();
            if (this._drawedCards.includes(card)) {
                this._revealDrawedCard();
            }
            UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
        } else {
            this._rejectLastUndo();
            card.reset();
        }
    }

    private async _moveToWinColumn(card: Card, stackIndex: number) {
        if (!this._winStacks[stackIndex]) {
            // if card is ACE
            if (card.point === 1 || this._isCheat) {
                this._winStacks.forEach((stackNode, index) => {
                    if (stackNode === card) {
                        this._winStacks[index] = null;
                    }
                });

                this._tableauStacks.forEach((stackNode, index) => {
                    if (stackNode === card) {
                        this._tableauStacks[index] = null;
                    }
                });

                card.setParent(this._cardContainer);
                this._winStacks[stackIndex] = card;

                const toPosition = this._winColumns[stackIndex].position;

                // if (this.isSolved) {
                //     await new Promise((res, rej) => {
                //         cc.tween(node)
                //             .to(0.1, { position: toPosition })
                //             .call(() => {
                //                 card.isInWin = true;
                //                 res(true);
                //             })
                //             .start();
                //     });
                // } else {
                card.position = toPosition;
                card.status = ECardStatus.IN_FINISH;
                card.parentGroup = UIManager.inst.showingGroup;
                // }
                this._checkCardWin(card);
                if (this._drawedCards.includes(card)) {
                    this._revealDrawedCard();
                }

                // this.checkCardWin(node);
                UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
            } else {
                this._rejectLastUndo();
                card.reset();
                return;
            }
        } else if (this._winStacks[stackIndex] === card) {
            this._rejectLastUndo();
            card.reset();
            return;
        } else {
            const stackCard = this._winStacks[stackIndex];
            if (stackCard) {
                if (stackCard.setCenterChild(card, this._isCheat)) {
                    // this.isMovedInWin = true;
                    card.status = ECardStatus.IN_FINISH;
                    card.parentGroup = UIManager.inst.showingGroup;

                    if (this._drawedCards.includes(card)) {
                        this._revealDrawedCard();
                    }
                    this._checkCardWin(card);
                    this._tableauStacks.forEach((stackNode, index) => {
                        if (stackNode === card) {
                            this._tableauStacks[index] = null;
                        }
                    });
                }
            } else {
                return;
            }
        }

        this._openLastCardInStack();
        // this.checkCardWin(node);
    }

    private _moveToTableauColumn(card: Card, stackIndex: number, ignoreRule: boolean = false) {
        const stackCard = this._tableauStacks[stackIndex];
        if (stackCard) {
            if (stackCard.setBottomChild(card, ignoreRule)) {
                if (this._drawedCards.includes(card)) {
                    this._revealDrawedCard();
                }

                card.status = ECardStatus.IN_PLAY;
                card.setParentGroup(UIManager.inst.showingGroup);
                UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
            } else {
                // cc.warn("pop");
                this._rejectLastUndo();
            }
        }

        this._tableauStacks.forEach((stack, index) => {
            stack && stack.adjustColumnHeight();
            if (stack === card) {
                this._tableauStacks[index] = null;
            }
        });

        this._winStacks.forEach((stackNode, index) => {
            if (stackNode === card) {
                this._winStacks[index] = null;
            }
        });

        this._openLastCardInStack();
    }

    // TODO: check if the loop is necessary
    private _openLastCardInStack(excludeIdx?: number) {
        // console.log('open last card in stack');
        this._tableauStacks.forEach((card, idx) => {
            if (card && idx !== excludeIdx) {
                if (card) {
                    card.turnLastBottomChildToFront();
                    // TODO: auto-solve
                    this._checkGameSolved();
                }
            }
        });
    }

    private _changeLevel(mode: DrawMode) {
        // TODO: change mode
    }

    private async _onClickStart(mode: DrawMode) {
        this._reset();
        await this._loadMatch();
    }

    private _dealCards() {
        this._cardContainer.removeChildren();
        Events.emit(UIEvents.UPDATE_HIGHSCORE, this._mode);
        this._tableauStacks = [];
        this.noMovePredicts = false;
        this._listCardWin = [];
        this._winStacks = [null, null, null, null];
        this._undoStacks = [];
        this._deck = []; // contains all cards for checking
        this._cards = [];
        this._drawedCards = [];
        this._timer = 0;
        this._listTimeStamp = [];
        this._scoreGame = 0;
        this._btnSolved.getContainer().visible = false;
        this._createCard();
        UIManager.inst.getSpinPanel().setStatusUndoBtn(false);
        let count = 0;
        for (let i = 0; i < COLUMNS; i++) {
            for (let j = i; j < COLUMNS; j++) {
                count++;
                const toPosition = this._tableauColumns[j].position;
                const cardNode = this._cards.pop();
                cardNode.status = ECardStatus.IN_PLAY;
                cardNode.parentGroup = UIManager.inst.showingGroup;

                const delay = count * 100;
                cardNode.tweenTo({ x: toPosition.x, y: toPosition.y + i * 13 }, CARD_MOVE_DURATION, delay, () => {
                    SoundManager.inst.playSfx('DEAL_CARD');
                    if (i === 0) {
                        this._tableauStacks.push(cardNode);
                    } else {
                        const parent = this._tableauStacks[j];
                        parent.setBottomChild(cardNode);
                    }

                    if (i === j) {
                        cardNode.turnToFront();
                    }
                    if (i === 6 && j === 6) {
                        this._isStartCountTimer = true;
                        PopupManager.blockInputEvent.close();
                        Events.emit(UIEvents.START_COUNT_TIME, this._mode);
                    }
                });
            }
        }
    }

    private _rejectLastUndo() {
        // console.trace('POP UNDO', this._undoStacks[this._undoStacks.length - 1]);
        return this._undoStacks.pop();
    }

    removeCardFromOldStack(node: Card) {
        this.resetTime = 0;
        this._tableauStacks.forEach((stackNode, index) => {
            if (stackNode === node) {
                this._tableauStacks[index] = null;
            }
        });

        this._winStacks.forEach((stackNode, index) => {
            if (stackNode === node) {
                this._winStacks[index] = null;
            }
        });
        this._checkGameSolved();
    }

    private _undo() {
        // SoundManager.inst.playEffect('CLICK_BTN');
        const record = this._rejectLastUndo();
        UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
        if (record) {
            switch (record.from) {
                case 'tableau':
                    const stackCard = this._tableauStacks[record.idx];
                    if (stackCard) {
                        {
                            this._moveToTableauColumn(record.node, record.idx, true);
                            const parent = record.node.parent.parent;
                            if (parent && !record.parentStatus) record.node.parent.parent.turnToBack();
                        }
                    } else {
                        this.removeCardFromOldStack(record.node);
                        this._moveToEmptyStack(record.node, record.idx, true);
                    }
                    break;
                case 'win':
                    this._moveToWinColumn(record.node, record.idx);
                    break;
                case 'deck':
                    // this.blockTouch.active = this.drawMode === DrawMode.ONE;
                    const startPosition = this._startColumn.position;
                    for (let i = 0; i < this._mode; i++) {
                        // TODO: attention
                        const card = this._drawedCards[this._drawedCards.length - 1];
                        if (!card || (this._mode === DrawMode.THREE && !card.visible)) break;
                        if (this._mode === DrawMode.ONE && this._drawedCards.length - 1 >= 0) {
                            card.isUnder = false;
                        }
                        this._revealDrawedCard(true);
                        card.setParent(this._cardContainer);
                        console.log('stopTweenFor', card.name);
                        card.position = startPosition;
                        this._cards.push(card);
                        card.isUnder = false;
                        card.turnToBack();
                        Tween.stopTweenFor(card);
                    }

                    if (this._mode === DrawMode.THREE) {
                        let count = 0;
                        for (let i = this._undoStacks.length - 1; i >= 0; i--) {
                            if (this._undoStacks[i].from === 'drawedCards') {
                                count++;
                            } else if (this._undoStacks[i].from === 'deck') {
                                break;
                            }
                        }

                        const lastCard = this._drawedCards.slice(-1);
                        let maxCards = 3 - count;

                        if (lastCard.length > 0) {
                            maxCards = Math.min(lastCard[0].getParentCount() + 1, 3 - count);
                        }

                        // cc.warn({ lastCard, maxCards });

                        for (let i = this._drawedCards.length - maxCards; i < this._drawedCards.length; i++) {
                            const card = this._drawedCards[i];
                            if (!card) {
                                break;
                            }
                            card.visible = true;
                            card.turnToFront();
                        }
                    }

                    this.removeCardFromOldStack(record.node);
                    break;
                case 'reset':
                    if (this._mode === DrawMode.ONE) {
                        while (this._cards.length > 0) {
                            const card = this._cards.pop();
                            card.visible = false;
                            card.isUnder = true;
                            card.turnToFront();
                            card.position = this._drawedColumn.position;
                            this._drawedCards.push(card);
                        }

                        const lastThreeCard = this._drawedCards.slice(-3);
                        console.log(lastThreeCard);
                        for (let i = 0; i < lastThreeCard.length; i++) {
                            lastThreeCard[i].visible = true;
                            lastThreeCard[i].tweenTo({ x: this._drawedColumn.x + i * RIGH_CARD_OFFSET, y: this._drawedColumn.y }, CARD_MOVE_DURATION, 0, () => {
                                lastThreeCard[i].turnToFront();
                            });

                            // cc.tween(lastThreeCard[i])
                            //     .set({ zIndex: 52 + this._drawedCards.length })
                            //     .to(0.15, {
                            //         position: cc.v3(toPosition2.x - 22 + (i - 1) * 27, toPosition2.y, 0),
                            //     })
                            //     .call(() => {
                            //         lastThreeCard[i].turnToFront();
                            //     })
                            //     .start();

                            if (i === lastThreeCard.length - 1) {
                                lastThreeCard[i].isUnder = false;
                            }
                        }
                    } else {
                        // cc.log(record._drawedCards);
                        const lastResetIdx = this._undoStacks.map((step) => step.from).lastIndexOf('reset');
                        // cc.log(lastResetIdx);
                        const steps = this._undoStacks.slice(lastResetIdx + 1);
                        // cc.log(steps);

                        let firstNode = null;
                        let deckStartIdx = steps.findIndex((step) => step.from === 'deck') + 1;
                        // cc.warn({ firstDeck: deckStartIdx });
                        while (this._cards.length > 0) {
                            let count = 0;
                            for (let i = deckStartIdx; i < steps.length; i++) {
                                if (steps[i].from === 'drawedCards') {
                                    count++;
                                } else if (steps[i].from === 'deck') {
                                    deckStartIdx = i + 1;
                                    break;
                                }
                            }

                            for (let j = 0; j < 3 - count; j++) {
                                const card = this._cards.pop();
                                if (card) {
                                    card.visible = false;

                                    if (j === 0) {
                                        firstNode = card;
                                        firstNode.position = this._drawedColumn.position;
                                    } else {
                                        firstNode.setRightChild(card);
                                        card.position.set(0, 0);
                                    }

                                    card.turnToFront();
                                    this._drawedCards.push(card);
                                }
                            }
                        }

                        // if (deckStartIdx - 1 < steps.length) {
                        //     for (let i = deckStartIdx - 1; i < steps.length; i++) {
                        //         if (steps[i].from === 'deck') {
                        //             // cc.log('empty deck');
                        //             return;
                        //         }
                        //     }
                        // }

                        // cc.log(this._drawedCards);
                        const lastThreeCard = this._drawedCards.slice(-1);
                        if (lastThreeCard[0]) {
                            lastThreeCard[0].visible = true;
                            lastThreeCard[0].turnToFront();
                            const parent = lastThreeCard[0].parent.parent;
                            if (parent instanceof Card) {
                                parent.visible = true;
                                if (parent.parent.parent instanceof Card) {
                                    parent.parent.parent.visible = true;
                                    (parent.parent.parent as Card).turnToFront();
                                    (parent as Card).turnToFront();
                                    lastThreeCard[0].turnToFront();
                                } else {
                                    (parent as Card).turnToFront();
                                    lastThreeCard[0].turnToFront();
                                }
                            }
                        }
                    }
                    break;
                default:
                    const toPosition = this._drawedColumn.position;
                    if (this._mode === DrawMode.THREE) {
                        const shownCardNo = this._drawedCards.filter((card) => card.visible && card.isFront);
                        //cc.warn(shownCardNo.length);
                        const firstNode = shownCardNo[0];

                        record.node.position = this._cardContainer.toLocal(record.node.parent.toGlobal(record.node.position));
                        record.node.setParent(this._cardContainer);
                        record.node.parentGroup = UIManager.inst.drawedGroup;
                        record.node.tweenTo({ x: toPosition.x + shownCardNo.length * RIGH_CARD_OFFSET, y: toPosition.y }, CARD_MOVE_DURATION, 0, () => {
                            // this.blockTouch.active = false;
                            if (firstNode) firstNode.setRightChild(record.node);
                        });

                        // cc.tween(record.node)
                        //     .set({
                        //         parent: this.container,
                        //         position: this.convertToCardsNodePosition(record.node.parent.convertToWorldSpaceAR(record.node)),
                        //         zIndex: 52 + shownCardNo.length,
                        //     })
                        //     .to(0.15, {
                        //         position: cc.v3(toPosition.x - 22 + shownCardNo.length * 27, toPosition.y),
                        //     })
                        //     .call(() => {
                        //         // this.blockTouch.active = false;
                        //         if (firstNode) firstNode.setRightChild(record.node);
                        //     })
                        //     .start();
                    } else {
                        const idx = this._drawedCards.length >= 3 ? 2 : this._drawedCards.length;
                        const pos = this._mode === DrawMode.ONE ? { x: toPosition.x + idx * RIGH_CARD_OFFSET, y: toPosition.y } : toPosition;

                        record.node.position = this._cardContainer.toLocal(record.node.parent.toGlobal(record.node.position));
                        record.node.setParent(this._cardContainer);
                        record.node.parentGroup = UIManager.inst.drawedGroup;
                        record.node.tweenTo(pos, CARD_MOVE_DURATION);
                        this._arrangeDrawedCards();
                    }

                    this._drawedCards.push(record.node);
                    record.node.status = ECardStatus.DRAWED_FROM_DECK;
                    if (this._mode === DrawMode.ONE) {
                        this._drawedCards[this._drawedCards.length - 1].isUnder = false;
                    }
                    this.removeCardFromOldStack(record.node);
                    break;
            }
            this._checkGameSolved();
        }
    }

    private _checkCardWin(card: Card) {
        const findCard = this._listCardWin.find((pocker) => pocker === card);
        if (!findCard) {
            this._listCardWin.push(card);
            this._scoreGame++;
            this._listTimeStamp.push(parseFloat(this._timer.toFixed(2)));
            Events.emit(UIEvents.UPDATE_SCORE_GAME, this._scoreGame);
            if (this._scoreGame === 52) {
                console.log(this._listTimeStamp);
                this._isWin = true;
                UIManager.inst.getSpinPanel().setGameWin();
                const listHighScore = Utils.getScoreList(this._mode);
                this._isStartCountTimer = false;
                let item = [];
                if (listHighScore && listHighScore.length > 0) {
                    item = listHighScore.find((item) => this._scoreGame >= item.score);
                }
                if (!listHighScore || item || listHighScore.length < 10) {
                    PopupManager.inputNamePopup.open(this._scoreGame, this._timer, this._mode, Utils.getTimer());
                } else {
                    PopupManager.winPopup.open(true, this._scoreGame, this._timer);
                }
                SoundManager.inst.playSfx('WIN');
            }
        }
    }

    private _reset() {}

    private async _loadMatch() {
        this._dealCards();
    }

    public async onActive() {
        PopupManager.newGamePopup.open(true);
        //await this._loadMatch();
    }

    private async _loadDrawGame(mode: number) {
        PopupManager.blockInputEvent.open();
        this._mode = mode === 1 ? DrawMode.ONE : DrawMode.THREE;
        await this._loadMatch();
    }

    public onPause() {}

    public onResume() {}

    public update(dt: number) {
        if (this._isWin && !this._isStartCountTimer) return;
        this._timer += dt;
    }

    private _getRandomArray(): number[] {
        const result: number[] = [];
        for (let i = 0; i < 52; i++) {
            result[i] = i + 1;
        }

        result.sort(function () {
            return 0.5 - Math.random();
        });

        // this.originArr = result;
        console.log(result);
        return result; //[51, 10, 36, 39, 52, 38, 42, 15, 14, 34, 3, 43, 31, 37, 28, 8, 48, 17, 27, 24, 4, 11, 18, 40, 7, 41, 2, 29, 25, 49, 45, 26, 47, 50, 12, 13, 32, 46, 5, 30, 6, 19, 35, 33, 1, 20, 21, 22, 44, 23, 16, 9];

        // return [
        //   8, 21, 46, 31, 22, 4, 52, 18, 36, 11, 35, 33, 3, 47, 32, 26, 30, 38, 25,
        //   51, 15, 28, 42, 12, 41, 10, 9, 20, 49, 24, 39, 2, 5, 19, 14, 34, 44, 1,
        //   27, 48, 40, 13, 43, 45, 7, 50, 6, 23, 17, 37, 29, 16,
        // ];
    }

    private _getWinStackIndex(worldX: number): number {
        let result = 0;
        let distance = 0;
        let minDistance = 0;
        let stackWorldX = 0;
        for (let i = 0; i < 4; i++) {
            stackWorldX = this._winColumns[i].position.x;
            distance = Math.abs(stackWorldX - worldX);
            if (i === 0 || minDistance > distance) {
                minDistance = distance;
                result = i;
            }
        }
        return result;
    }

    private _getTableauStackIndex(worldX: number): number {
        let result = 0;
        let distance = 0;
        let minDistance = 0;
        let stackWorldX = 0;
        for (let i = 0; i < COLUMNS; i++) {
            stackWorldX = this._tableauColumns[i].x;
            distance = Math.abs(stackWorldX - worldX);
            if (i === 0 || minDistance > distance) {
                minDistance = distance;
                result = i;
            }
        }
        return result;
    }

    //====================================================================================================
    //=============================================== HINT ================================================

    private isShowHint: boolean = false;
    private noMovePredicts: boolean = false;

    private _findPossibleMove(
        card: Card,
        playAnim: boolean,
    ): {
            target: Card;
            dest: IPointData;
            callback: Function;
        } {
        // check could move to win stack
        if (card.isLastBottomChild) {
            for (let i = 0; i < this._winStacks.length; i++) {
                if (!this._winStacks[i]) {
                    if (card.point === 1) {
                        if (playAnim) {
                            this.highlightNode(card, this._winStacks[i]);
                        }

                        return {
                            target: card,
                            dest: { x: this._winColumns[i].x, y: this._winColumns[i].y },
                            callback: this._moveToWinColumn.bind(this, card, i),
                        };
                    }
                } else {
                    const winCard = this._winStacks[i];
                    if (winCard && winCard.isCenterChild(card)) {
                        if (playAnim) {
                            this.highlightNode(card, winCard);
                        }

                        return {
                            target: card,
                            dest: { x: this._winColumns[i].x, y: this._winColumns[i].y },
                            callback: this._moveToWinColumn.bind(this, card, i),
                        };
                    }
                }
            }
        }

        // check could move to tableau stack
        for (let i = 0; i < this._tableauStacks.length; i++) {
            // check could move to empty stack
            if (!this._tableauStacks[i] && card.point === 13) {
                if (card.getParentCount(true) > 0 || (card.status === ECardStatus.DRAWED_FROM_DECK && card.visible && card.isFront)) {
                    if (playAnim) {
                        this.highlightNode(card, this._tableauStacks[i]);
                    }

                    return {
                        target: card,
                        dest: { x: this._tableauColumns[i].x, y: this._tableauColumns[i].y },
                        callback: this._moveToEmptyStack.bind(this, card, i),
                    };
                }
            } else {
                const tableauCard = this._tableauStacks[i];
                if (tableauCard && tableauCard.isBottomChild(card)) {
                    if ((card.status === ECardStatus.DRAWED_FROM_DECK && card.visible && card.isFront) || card.getParentCount() <= tableauCard.getParentCount()) {
                        if (playAnim) {
                            this.highlightNode(card, tableauCard.getBottomCard());
                        }

                        const toPosition = this._cardContainer.toLocal(tableauCard.getBottomCard().toGlobal(tableauCard.getBottomCard().getChildByName('bottomContainer').position));
                        return {
                            target: card,
                            dest: { x: toPosition.x, y: toPosition.y },
                            callback: this._moveToTableauColumn.bind(this, card, i),
                        };
                    }
                }
            }
        }

        return null;
    }

    highlightNode(from: Card, dest: Card) {
        console.log('highlight node', from, dest);

        from.shake();
        dest?.shake();

        this.isShowHint = false;

        // else if (dest.getChildByName('boder_highlight')) {
        //     cc.tween(dest.getChildByName('boder_highlight'))
        //         .set({ active: true })
        //         .blink(1, 2)
        //         .call(() => {
        //             dest.getChildByName('boder_highlight').active = false;
        //             this.isShowHint = false;
        //         })
        //         .start();
        // }
    }

    showHint(playAnim: boolean = true) {
        // SoundManager.inst.playEffect('CLICK_BTN');
        if (this.isShowHint) return;
        // console.log('show hint');
        this.isShowHint = playAnim;
        if (this.noMovePredicts) {
            // console.log('GAME OVER');
            // this.isStartGame = false;
            PopupManager.gameOverPopup.open();
            this.noMovePredicts = false;
            this.resetTime = 0;
            this.isShowHint = false;
            return;
        }

        const remainingCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY && !card.isUnder);

        for (const card of remainingCards) {
            if (!card) continue;
            const result = this._findPossibleMove(card, playAnim);
            if (result) return true;
        }

        const drawableCard = this._mode === DrawMode.ONE ? this._drawedCards[this._drawedCards.length - 1] : this._drawedCards[this._drawedCards.length - 1]?.getRightCard();
        if (drawableCard) {
            // cc.warn({ drawableCard });
            const result = this._findPossibleMove(drawableCard, playAnim);
            if (result) return true;
        }

        const firstCardInDeck = this._cards[this._cards.length - 1];
        if (playAnim && firstCardInDeck) {
            firstCardInDeck.shake();
            this.isShowHint = false;
        } else {
            this.isShowHint = false;
        }

        return false;
    }

    private _resetDrawedCards() {
        console.log('reset drawed cards');

        // console.log('PUSH UNDO', 'reset');
        this._undoStacks.push({
            from: 'reset',
            drawedCardStacks: this._drawedCards.map((item) => Object.assign({}, item)),
        });

        const startPosition = this._startColumn.position;
        while (this._drawedCards.length > 0) {
            const card = this._drawedCards.pop();
            card.status = ECardStatus.IN_DECK;
            card.setParent(this._cardContainer);
            card.visible = true;
            card.position = startPosition;
            this._cards.push(card);
            card.isUnder = false;
            card.turnToBack();
        }
        if (!this.showHint(false)) {
            this.resetTime++;
        }
        if (this.resetTime > 1 && this.hintCount === 0) {
            ////cc.log("noMovePredicts");
            this.noMovePredicts = true;
        }
        this.hintCount = 0;
    }

    _doubleClickCallback(card: Card, rejectFeedback: boolean = true) {
        // console.log('double');
        // this.blockTouch.active = true;
        // this.lockTimerClickCard = DOUBLE_CLICK_TIME;

        const possibleMove = this._findPossibleMove(card, false);

        if (possibleMove) {
            possibleMove.target.tweenTo(possibleMove.target.parent.toLocal(this._cardContainer.toGlobal(possibleMove.dest)), CARD_MOVE_DURATION, 0, () => {
                possibleMove.callback && possibleMove.callback();
                // this.blockTouch.active = false;
            });
        } else if (rejectFeedback) {
            card.shake();
            this._rejectLastUndo();
            // cc.warn('pop', this.undoStacks);
            // this.blockTouch.active = false;
        }
    }
    private _checkGameSolved() {
        const cardIsBottom = this._tableauStacks.filter((card) => card && !card.isFront);
        this._btnSolved.getContainer().visible = this._drawedCards.length === 0 && cardIsBottom.length === 0 && this._deck.filter((card) => card.status === ECardStatus.IN_DECK).length === 0;
    }

    private async _autoSolve() {
        this._isWin = true;
        console.log('on solved');
        if (this._tableauStacks.every((card) => card == null)) {
            console.log('all null');
            return;
        }

        this._tableauStacks.forEach((card) => {
            if (card) {
                const bottomCard = card.getBottomCard();
                if (bottomCard) {
                    this._doubleClickCallback(bottomCard, false);
                } else {
                    this._doubleClickCallback(card, false);
                }
            }
        });

        Scheduler.setTimeout(200, this._autoSolve.bind(this));
    }

    private _onSolved() {
        this._isWin = true;
        this._loopSolved = Scheduler.setInterval(150, () => {
            if (this._tableauStacks.every((card) => card == null)) {
                Scheduler.clearInterval(this._loopSolved);
                return;
            }

            this._tableauStacks.forEach((card) => {
                if (card) {
                    const bottomCard = card.getBottomCard();
                    if (bottomCard) {
                        this._findPossibleMove(bottomCard, false);
                    } else {
                        this._findPossibleMove(card, false);
                    }
                }
            });
        });
    }

    private _checkResultInWinStack(card) {
        console.log('check result in win stack');
        if (card.bottomContainer.childrenCount === 0) {
            this._findPossibleMove(card, false);
            Scheduler.setTimeout(150, () => {
                this._onSolved();
            });
        }
    }
}
