import { Container, Graphics, Text, settings, TextStyle } from '../../../pixi';
import AppConstants from '../../../AppConstants';
import { MainApp } from '../../../main';
import { AutoAlignContainer } from '../../../Scenes/AutoAlignContainer';
import LogHook, { LogType } from './LogHook';
import Utils from '../../../Utils/Utils';

type CheatButtonCallback = () => void;

export default class CheatMenu {
    /* CHEAT FLAGS */
    private readonly _DEBUG_VIEWPORT = true;

    private readonly _BUTTON_WIDTH = 75;
    private readonly _BUTTON_HEIGHT = 35;
    private readonly _SPACE_BETWEEN_BUTTONS = 10;
    private readonly _BUTTON_COLOR = 0x01334A;
    private readonly _BUTTON_ALPHA = 0.5;
    private readonly _BUTTON_RADIUS = 8;
    private readonly _BUTTON_TEXT_SIZE = 14;

    private _cheatButton: Graphics;
    private _cheatText: Text;
    private _addedButtons: Graphics[] = [];
    private _drawCallNumber: Text;
    private _resolutionInfo: Text;

    private _showAll: boolean = false;

    private _fpsShowing: boolean;
    private _FPSNumber: Text;
    // private _enableStepByStepSpin: boolean = false;
    // private _cheatNextStepSpin: boolean = false;
    private _fpsTimer = 0;
    private _fpsCounter = 0;
    private _fpsMin = 60;
    private _fpsMax = 0;
    private _fpsAvg = 60;
    private _fpsSeconds = 0;
    private _fpsSum = 0;

    private _showingDrawcall: boolean = false;
    private _showingResolution: boolean = false;
    // private _recordingLogs: boolean = false;
    private _drawCount = 0;
    private _drawCountMax = 0;

    public cheatContainer: AutoAlignContainer;
    public static inst: CheatMenu = null;

    constructor() {
        if (CheatMenu.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
            return CheatMenu.inst;
        }
        CheatMenu.inst = this;

        this.cheatContainer = new AutoAlignContainer({ vertical: 'TOP', horizontal: 'RIGHT' });
        this.cheatContainer.name = 'CheatMenu';
        this.cheatContainer.zIndex = 9999;
        this._init();
        MainApp.inst.app.ticker.add(this.onUpdate, this);
    }

    private _init() {
        // Create cheat button
        this._cheatButton = this._createRoundedRectangleButton(false);
        this._cheatText = this._createText('CHEAT');
        this._cheatButton.addChild(this._cheatText);
        this._cheatButton.alpha = this._BUTTON_ALPHA;
        this._cheatButton.position.set(-this._BUTTON_WIDTH / 2 - 2, this._BUTTON_HEIGHT / 2 + 80);
        this._cheatText.position.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        this.cheatContainer.addChild(this._cheatButton);
        this._cheatButton.on('pointerdown', this._showAllCheatButtons.bind(this));

        const debugInfoStyle = new TextStyle({ fontFamily: 'Arial', fontSize: 27, fontWeight: 'bold', fill: [0xFF0000] });
        const debugInfoX = -this.cheatContainer.getScreenData().realWidth + 2;

        // Drawcall text
        if (!this._drawCallNumber) {
            this._drawCallNumber = new Text('DrawCall:60', debugInfoStyle);
            this._drawCallNumber.anchor.set(0, 0);
            this._drawCallNumber.position.set(debugInfoX, 30);
            this._drawCallNumber.visible = false;
            this.cheatContainer.addChild(this._drawCallNumber);
        }

        // Resolution text
        this._resolutionInfo = new Text('View: 0', debugInfoStyle);
        this._resolutionInfo.anchor.set(0, 0);
        this._resolutionInfo.position.set(debugInfoX, 60);
        this._resolutionInfo.visible = false;
        this.cheatContainer.addChild(this._resolutionInfo);

        // #!if ENV === 'development' || debugDrawCall
        const renderer = MainApp.inst.app.renderer as any;
        const drawElements = renderer.gl.drawElements;
        renderer.gl.drawElements = (...args: any[]) => {
            drawElements.call(renderer.gl, ...args);
            this._drawCount++;
        }; // rewrite drawElements to count draws
        // #!endif

        // cheat buttons
        this.addCustomToggleCheatButton('FPS', () => {
            this._fpsShowing = !this._fpsShowing;
            if (this._fpsShowing) {
                if (!this._FPSNumber) {
                    this._FPSNumber = new Text('FPS:60', debugInfoStyle);
                    this._FPSNumber.anchor.set(0, 0);
                    this._FPSNumber.position.set(debugInfoX, 0);
                    this.cheatContainer.addChild(this._FPSNumber);
                }
                this._FPSNumber.visible = true;
            } else {
                // this.setToggleStatus('Pause Spin', false);
                // this._enableStepByStepSpin = false;

                this._FPSNumber.visible = false;
                this._fpsTimer = 0;
                this._fpsCounter = 0;
                this._fpsMin = 60;
                this._fpsMax = 0;
                this._fpsAvg = 60;
                this._fpsSeconds = 0;
                this._fpsSum = 0;
            }
        });

        // #!if ENV === 'development' || debugDrawCall
        this.addCustomToggleCheatButton('DrawCall', () => {
            this._showingDrawcall = !this._showingDrawcall;
            if (this._showingDrawcall) {
                this._drawCallNumber.visible = true;
            } else {
                this._drawCallNumber.visible = false;
            }
        });

        if (this._DEBUG_VIEWPORT) {
            this.addCustomToggleCheatButton('Viewport', () => {
                this._showingResolution = !this._showingResolution;
                if (this._showingResolution) {
                    this._resolutionInfo.visible = true;
                } else {
                    this._resolutionInfo.visible = false;
                }
            });
        }
        // #!endif

        // listener error/warn log on mobile
        const url = new URL(window.location.href);
        const hasLogParams = url.searchParams.has('l') || url.searchParams.has('log');
        if (hasLogParams || !Utils.isDeveloper()) {
            const logHook: LogHook = new LogHook();
            this.addCustomCheatButton('Logs', () => {
                if (logHook.size() === 0) {
                    alert('Empty Logs!');
                } else {
                    logHook.saveLogFile();
                }
            });
        }
    }

    private _showAllCheatButtons() {
        this._showAll = !this._showAll;
        if (this._showAll) {
            this._cheatText.text = 'X';
            this._cheatText.style.fontSize = 20;
            this._addedButtons.forEach((button) => {
                button.visible = true;
            });
        } else {
            this._cheatText.text = 'CHEAT';
            this._cheatText.style.fontSize = this._BUTTON_TEXT_SIZE;
            this._addedButtons.forEach((button) => {
                button.visible = false;
            });
        }
    }

    private _createRectangleButton(): Graphics {
        const cheatButton = new Graphics();
        // Rectangle + line style 1
        cheatButton.lineStyle(2, 0xFEEB77, 1);
        cheatButton.beginFill(this._BUTTON_COLOR);
        cheatButton.drawRect(0, 0, this._BUTTON_WIDTH, this._BUTTON_HEIGHT);
        cheatButton.endFill();
        cheatButton.alpha = this._BUTTON_ALPHA;
        cheatButton.pivot.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        cheatButton.interactive = true;
        cheatButton.buttonMode = true;

        return cheatButton;
    }

    private _createRoundedRectangleButton(withBigBorder: boolean): Graphics {
        const cheatButton = new Graphics();
        // Rectangle + line style 1
        if (withBigBorder) {
            cheatButton.lineStyle(5, 0xFF0000, 1);
        } else {
            cheatButton.lineStyle(2, 0xFEEB77, 1);
        }

        cheatButton.beginFill(this._BUTTON_COLOR);
        cheatButton.drawRoundedRect(0, 0, this._BUTTON_WIDTH, this._BUTTON_HEIGHT, this._BUTTON_RADIUS);
        cheatButton.endFill();
        cheatButton.alpha = this._BUTTON_ALPHA;
        cheatButton.pivot.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        cheatButton.interactive = true;
        cheatButton.buttonMode = true;

        return cheatButton;
    }

    private _createText(text: string): Text {
        const style = new TextStyle({
            fontFamily: 'intersemibold',
            fontSize: this._BUTTON_TEXT_SIZE,
            fill: '#ffffff'
        });

        const richText = new Text(text, style);
        richText.anchor.set(0.5);
        return richText;
    }

    public onUpdate(dtScalar: number) {
        const dt = dtScalar / settings.TARGET_FPMS / 1000;

        /**
         * Counting game FPS
         */
        if (this._fpsShowing && this._FPSNumber) {
            this._fpsTimer += dt;
            this._fpsCounter++;
            if (this._fpsTimer > 1) {
                // const ping = GameStateMgr.getInstance().pongLatency;
                const ping = -1;
                this._fpsSeconds++;
                this._fpsTimer -= 1;
                this._fpsMin = Math.min(this._fpsMin, this._fpsCounter);
                this._fpsMax = Math.max(this._fpsMax, this._fpsCounter);
                this._fpsSum += this._fpsCounter;
                this._fpsAvg = Math.floor(this._fpsSum / this._fpsSeconds);
                this._FPSNumber.text = `FPS:${this._fpsCounter}, avg:${this._fpsAvg}, min:${this._fpsMin}, max:${this._fpsMax}, ping:${ping}`;
                this._fpsCounter = 0;
            }
        }

        if (this._showingDrawcall) {
            this._drawCountMax = Math.max(this._drawCountMax, this._drawCount);
            this._drawCallNumber.text = `Draw:${this._drawCount}, max:${this._drawCountMax}`;
        }

        this._drawCount = 0;
    }

    // add cheat button on the top right of screen
    public addCustomCheatButton(buttonText: string, callback: CheatButtonCallback, rounded: boolean = true): void {
        // create cheat button
        const button = rounded ? this._createRoundedRectangleButton(false) : this._createRectangleButton();
        // button.name = buttonText;
        button.position.set(- this._BUTTON_WIDTH / 2 - 2, this._cheatButton.y + this._BUTTON_HEIGHT * (this._addedButtons.length + 1) + (this._addedButtons.length + 1) * this._SPACE_BETWEEN_BUTTONS);
        const text = this._createText(buttonText);
        button.addChild(text);
        text.position.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        button.on('pointerdown', callback);
        button.visible = this._showAll;
        this._addedButtons.push(button);

        this.cheatContainer.addChild(button);
    }

    public addCustomToggleCheatButton(buttonText: string, callback: CheatButtonCallback): void {
        // create cheat button
        const button = this._createRoundedRectangleButton(false);
        // button.name = buttonText;
        button.position.set(- this._BUTTON_WIDTH / 2 - 2, this._cheatButton.y + this._BUTTON_HEIGHT * (this._addedButtons.length + 1) + (this._addedButtons.length + 1) * this._SPACE_BETWEEN_BUTTONS);
        const toggleOn = this._createRoundedRectangleButton(true);
        toggleOn.visible = false;
        toggleOn.position.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        const text = this._createText(buttonText);
        button.addChild(toggleOn);
        button.addChild(text);
        text.position.set(this._BUTTON_WIDTH / 2, this._BUTTON_HEIGHT / 2);
        button.on('pointerdown', () => {
            toggleOn.visible = !toggleOn.visible;
            callback();
        });
        button.visible = this._showAll;
        this._addedButtons.push(button);

        this.cheatContainer.addChild(button);
    }

    // eslint-disable-next-line no-unused-vars
    // public setToggleStatus(buttonText: string, status: boolean) : void {
    //     this._addedButtons.forEach(button => {
    //         if(buttonText === button.name && button.children.length > 1)
    //         {
    //             button.getChildAt(0).visible = status;
    //         }
    //     });
    // }

    // show game + real resolution, and scale factor
    public setResolutionInfo(gameWidth: number, gameHeight: number, realWidth: number, realHeight: number, scaleX: number, scaleY: number, stageY: number) {
        const deviceRatio = realHeight / realWidth;
        const topOfFitRatio = this._roundTwo(deviceRatio * 9, 1);
        this._resolutionInfo.text = `Game: ${this._roundTwo(gameWidth)}x${this._roundTwo(gameHeight)}, R: ${Math.round(realWidth)}x${Math.round(realHeight)}, S: ${this._roundTwo(scaleX)}, FR: ${topOfFitRatio}/9`;
    }

    private _roundTwo(num: number, pow: number = 2): number {
        return Math.round((num + Number.EPSILON) * 10 ** pow) / 10 ** pow;
    }
}