export * from '@pixi/constants';
export * from '@pixi/math';
export * from '@pixi/runner';
export * from '@pixi/settings';
export * from '@pixi/ticker';
import * as utils from '@pixi/utils';
export { utils };
export * from '@pixi/display';
export * from '@pixi/core';
import '@pixi/mixin-get-child-by-name';
import '@pixi/mixin-get-global-position';
export * from '@pixi/loaders';
export * from '@pixi/sprite';
export * from '@pixi/app';
export * from '@pixi/graphics';
// export * from '@pixi/mesh-extras';
// import '@pixi/mixin-cache-as-bitmap';
export * from '@pixi/spritesheet';
export * from '@pixi/text-bitmap';
export * from '@pixi/text';
export * from '@pixi/interaction';
export * from '@pixi/layers';
export * from '@pixi/mesh-extras';
// export * from '@pixi/filter-color-matrix';
// export * from '@pixi/sprite-animated';
// export * from '@pixi/sprite-tiling';

// Renderer plugins
import { Renderer } from '@pixi/core';
import { BatchRenderer } from '@pixi/core';
Renderer.registerPlugin('batch', BatchRenderer);
import { InteractionManager } from '@pixi/interaction';
Renderer.registerPlugin('interaction', InteractionManager);
// import { TilingSpriteRenderer } from '@pixi/sprite-tiling';
// Renderer.registerPlugin('tilingSprite', TilingSpriteRenderer);

// Application plugins
import { Application } from '@pixi/app';
import { AppLoaderPlugin } from '@pixi/loaders';
Application.registerPlugin(AppLoaderPlugin);
import { TickerPlugin } from '@pixi/ticker';
Application.registerPlugin(TickerPlugin);

// Loader plugins
import { Loader } from '@pixi/loaders';
import { SpritesheetLoader } from '@pixi/spritesheet';
Loader.registerPlugin(SpritesheetLoader);
// import { BitmapFontLoader } from '@pixi/text-bitmap';
// Loader.registerPlugin(BitmapFontLoader);

// Spine plugins
// import { SpineParser } from '@pixi-spine/loader-3.8';
// SpineParser.registerLoaderPlugin();
// export { Spine, TrackEntry } from '@pixi-spine/runtime-3.8';
// export * as BaseSpine from '@pixi-spine/base';
