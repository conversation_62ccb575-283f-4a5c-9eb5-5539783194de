{"frames": {"ui_popup": {"frame": {"x": 2, "y": 2, "w": 603, "h": 403}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 603, "h": 403}, "sourceSize": {"w": 603, "h": 403}, "pivot": {"x": 0.5, "y": 0.5}}, "win_popup": {"frame": {"x": 2, "y": 409, "w": 396, "h": 284}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 396, "h": 284}, "sourceSize": {"w": 396, "h": 284}, "pivot": {"x": 0.5, "y": 0.5}}, "layout_rank": {"frame": {"x": 402, "y": 409, "w": 274, "h": 396}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 274, "h": 396}, "sourceSize": {"w": 274, "h": 396}, "pivot": {"x": 0.5, "y": 0.5}}, "bg_draw": {"frame": {"x": 609, "y": 2, "w": 176, "h": 224}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 176, "h": 224}, "sourceSize": {"w": 176, "h": 224}, "pivot": {"x": 0.5, "y": 0.5}}, "input_name": {"frame": {"x": 789, "y": 2, "w": 335, "h": 52}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 335, "h": 52}, "sourceSize": {"w": 335, "h": 52}, "pivot": {"x": 0.5, "y": 0.5}}, "ui_button": {"frame": {"x": 802, "y": 341, "w": 224, "h": 75}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 224, "h": 75}, "sourceSize": {"w": 224, "h": 75}, "pivot": {"x": 0.5, "y": 0.5}}, "logo": {"frame": {"x": 845, "y": 2, "w": 292, "h": 36}, "rotated": true, "trimmed": true, "spriteSourceSize": {"x": 0, "y": 10, "w": 292, "h": 36}, "sourceSize": {"w": 292, "h": 49}, "pivot": {"x": 0.5, "y": 0.5}}, "tool_tip": {"frame": {"x": 402, "y": 687, "w": 224, "h": 43}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 1, "y": 1, "w": 224, "h": 43}, "sourceSize": {"w": 226, "h": 45}, "pivot": {"x": 0.5, "y": 0.5}}, "btn_red": {"frame": {"x": 609, "y": 341, "w": 130, "h": 53}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 130, "h": 53}, "sourceSize": {"w": 130, "h": 53}, "pivot": {"x": 0.5, "y": 0.5}}, "button": {"frame": {"x": 630, "y": 687, "w": 130, "h": 53}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 130, "h": 53}, "sourceSize": {"w": 130, "h": 53}, "pivot": {"x": 0.5, "y": 0.5}}, "hint_btn": {"frame": {"x": 802, "y": 569, "w": 80, "h": 79}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 80, "h": 79}, "sourceSize": {"w": 80, "h": 79}, "pivot": {"x": 0.5, "y": 0.5}}, "undo_btn": {"frame": {"x": 802, "y": 652, "w": 80, "h": 79}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 80, "h": 79}, "sourceSize": {"w": 80, "h": 79}, "pivot": {"x": 0.5, "y": 0.5}}, "scroll_bar": {"frame": {"x": 2, "y": 697, "w": 14, "h": 308}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 14, "h": 308}, "sourceSize": {"w": 14, "h": 308}, "pivot": {"x": 0.5, "y": 0.5}}, "klondike_title": {"frame": {"x": 2, "y": 715, "w": 206, "h": 19}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 206, "h": 19}, "sourceSize": {"w": 206, "h": 19}, "pivot": {"x": 0.5, "y": 0.5}}, "bg_arrow": {"frame": {"x": 743, "y": 341, "w": 46, "h": 45}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 46, "h": 45}, "sourceSize": {"w": 46, "h": 45}, "pivot": {"x": 0.5, "y": 0.5}}, "icon_tick": {"frame": {"x": 764, "y": 687, "w": 40, "h": 30}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 40, "h": 30}, "sourceSize": {"w": 40, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "close": {"frame": {"x": 845, "y": 298, "w": 31, "h": 31}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 32, "h": 32}, "pivot": {"x": 0.5, "y": 0.5}}, "newgame": {"frame": {"x": 314, "y": 697, "w": 31, "h": 31}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 31, "h": 31}, "pivot": {"x": 0.5, "y": 0.5}}, "rank": {"frame": {"x": 349, "y": 697, "w": 31, "h": 31}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 31, "h": 31}, "pivot": {"x": 0.5, "y": 0.5}}, "sound_off": {"frame": {"x": 609, "y": 230, "w": 30, "h": 30}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 30, "h": 30}, "sourceSize": {"w": 30, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "sound_on": {"frame": {"x": 609, "y": 264, "w": 30, "h": 30}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 30, "h": 30}, "sourceSize": {"w": 30, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "rules": {"frame": {"x": 212, "y": 715, "w": 23, "h": 29}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 23, "h": 29}, "sourceSize": {"w": 23, "h": 29}, "pivot": {"x": 0.5, "y": 0.5}}, "score": {"frame": {"x": 245, "y": 715, "w": 18, "h": 17}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 18, "h": 17}, "sourceSize": {"w": 18, "h": 17}, "pivot": {"x": 0.5, "y": 0.5}}, "time": {"frame": {"x": 267, "y": 715, "w": 16, "h": 16}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 16, "h": 16}, "sourceSize": {"w": 16, "h": 16}, "pivot": {"x": 0.5, "y": 0.5}}, "highscore": {"frame": {"x": 287, "y": 715, "w": 15, "h": 15}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 15, "h": 15}, "sourceSize": {"w": 15, "h": 15}, "pivot": {"x": 0.5, "y": 0.5}}, "rectangle": {"frame": {"x": 793, "y": 341, "w": 1, "h": 56}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 1, "h": 56}, "sourceSize": {"w": 1, "h": 56}, "pivot": {"x": 0.5, "y": 0.5}}}, "meta": {"app": "http://github.com/odrick/free-tex-packer-core", "version": "0.3.4", "image": "ui_atlas.png", "format": "RGBA8888", "size": {"w": 884, "h": 742}, "scale": 1}}