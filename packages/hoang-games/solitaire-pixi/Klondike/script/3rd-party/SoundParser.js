const path = require('path');
const chalk = require('chalk');

class SoundParser {
    constructor(configObject, packName = undefined, exportDir, soundFolder) {
        const fileInfo = path.parse(configObject.src);

        // load config
        this.src = configObject.src || 'empty.mp3';
        this.filename = fileInfo.name || 'empty';
        this.ext = fileInfo.ext;
        this.pack = packName;
        this.filePack = configObject.pack;
        this.soundFolder = soundFolder;
        this.name = this._normalizeName(configObject.name || this.filename);
        this.dest = (this.pack || this.filePack || this.name) + fileInfo.ext;
        this.loop = configObject.loop || false;
        this.type = (configObject.type || 'sfx') === 'sfx' ? 0 : 1;
        this.volume = configObject.volume || 1;
        this.outputPath = `${exportDir}${this.dest}`;
        this.ignored = configObject.ignore || false;

        // verify
        if (this.volume > 1) {
            this._logWarn(`${this.src}: ${this.volume}, volume is wrong! [0, 1]`);
        }
        if (this.ignored) {
            this._logWarn(`${this.name}(${chalk.yellowBright(this.src)}) ignored!`);
        }
        if (!configObject.src && !this.ignored) {
            this._logWarn(`${this.name}(${chalk.yellowBright(this.src)}) silenced!`);
        }
    }

    _logWarn(msg) {
        console.log(' > ' + chalk.yellow('[WARN] ') + msg);
    }

    _normalizeName(basename) {
        return basename.replace(/  /g, ' ').replace(/--/g, '-').replace(/__/g, '_').replace(/ /g, '_').replace(/-/g, '_').toUpperCase();
    }

    exportSinglePackHowlCfg() {
        const howlCfg = {
            src: `${this.soundFolder}/${this.dest}`,
        };

        if (this.loop) {
            howlCfg.loop = true;
        }
        if (this.volume !== 1) {
            howlCfg.volume = this.volume;
        }

        return howlCfg;
    }

    exportSinglePackSoundMapCfg() {
        let soundMapCfg = `    ${this.name}: ${this.exportSoundConfig()},`;
        return soundMapCfg;
    }

    exportSoundConfig() {
        let config = `{ p: `;
        if (this.pack) {
            config += `'${this.pack}', s: '${this.filename}'`;
        } else if(this.filePack) {
            config += `'${this.filePack}'`;
        } else {
            config += `'${this.name}'`;
        }
        if (this.volume < 1) {
            config += `, v: ${this.volume}`;
        }
        if (this.loop) {
            config += ', l: 1';
        }
        if (this.type !== 0) {
            config += ', m: ' + this.type;
        }
        config += ' }';

        return config;
    }

    exportSoundPackName() {
        return ` '${this.pack || this.filePack || this.name}',`;
    }
}

module.exports = SoundParser;
