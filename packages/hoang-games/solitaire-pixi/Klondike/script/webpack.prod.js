// https://github.com/webpack-contrib/terser-webpack-plugin#terseroptions
const TerserPlugin = require('terser-webpack-plugin');

// mangle patterns
// tool for test regex: https://regexr.com
const JOIN_REGEX = /|/;
const BASE_REGEX = /^_|app|SCREEN|VOLUME|Symbol|WIDTH|width|HEIGHT|height|COLOR|LINE|BINARY|TEXT|SIZE|TYPE|EVENT|_ALPHA|CLIENT|POS|END|START|USE|STATE|COUNT|_ID|ALPHA|STATUS|ONE|DST|RESULT|COMMAND|HOLD|INDEXES|OPACITY|inst/;
const GAME_REGEX = /DURATION|TIME|DELAY|RARE|MC|FONT|SYMBOL|SPIN|PREV|POPUP|WIN|ANIM|GAME|MILESTONE|FADE|SPEED|FLY|NEAR|HISTORY_/;
const MANGLE_REGEX = new RegExp(BASE_REGEX.source + JOIN_REGEX.source + GAME_REGEX.source);

module.exports = (env, isDebug, isBuild, isMangle) => {
    const prodConfig = {
        mode: 'production',
        optimization: {
            minimize: true,
            minimizer: [
                (compiler) => {
                    const TerserPlugin = require('terser-webpack-plugin');
                    new TerserPlugin({
                        test: /\.[jt]sx?$/i,
                        // cache: true,
                        parallel: true,
                        extractComments: false,
                        terserOptions: {
                            compress: {
                                properties: true,
                                pure_getters: true,
                                pure_funcs: [
                                    'console.log',
                                    'console.info',
                                    'console.debug',
                                    'console.warn',
                                    /* 'console.error', keep error logs on product */
                                ],
                            },
                            output: null,
                            mangle: {
                                properties: {
                                    // debug: true,
                                    regex: isMangle ? MANGLE_REGEX : /^_/,
                                    // whitelist of compressing keywords
                                    reserved: [
                                        /* For Howler */
                                        '__default',
                                        '_onend',
                                        '_onfade',
                                        '_onload',
                                        '_onpause',
                                        '_onplay',
                                        '_onstop',
                                        '_onmute',
                                        '_onvolume',
                                        '_onrate',
                                        '_onseek',
                                        '_onunlock',
                                        '_onresume',
                                        '_onstereo',
                                        '_onpos',
                                        '_onorientation',
                                        '_onloaderror',
                                        '_onplayerror',
                                    ],
                                },
                            },
                        },
                    }).apply(compiler);
                },
            ],
        },
    };

    return prodConfig;
};
