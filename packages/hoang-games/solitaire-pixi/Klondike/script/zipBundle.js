const admZip = require('adm-zip');
const fs = require('fs');
const path = require('path');
const packageConfig = require('../package.json');

// define variables
const isRunner = process.env.CI || false;
const suffixName = (isRunner && process.env.CUSTOM_SUFFIX) ? `_${process.env.CUSTOM_SUFFIX}` : '';
const rootDir = path.join(__dirname, '..');
const zipFolder = path.join(rootDir, 'dist');
const gameVer = packageConfig.version.replace(/\./g, '');
const zipName = `${packageConfig.servicePrefix}${packageConfig.serviceId}_v${gameVer}${suffixName}.zip`;
const zipFullPath = path.join(rootDir, zipName);
let zipComment = `Game: ${packageConfig.name}\nVer: ${gameVer}`;
if (isRunner) {
    zipComment += `\nBuild Number: ${process.env.BITBUCKET_BUILD_NUMBER}\nCreated From CI Pipeline`;
    console.warn(zipName);
}

// zip pack
if (!fs.existsSync(zipFolder)) {
    throw new Error(`> ${zipFolder} not exists!`);
}

console.log('> Create pack...');
const zipFile = new admZip();
zipFile.addLocalFolder(zipFolder);
zipFile.addZipComment(zipComment);
zipFile.writeZip(zipFullPath, (error) => {
    if (error) throw error;
    console.log(`> Done '${zipName}'`);
});

// for CI pipeline
if (isRunner) {
    const sharedVars = 'shared_vars.sh';

    fs.appendFile(path.join(rootDir, sharedVars), `\nexport ZIP_NAME=${zipName}`, (err) => {
        if (err) throw err;
        console.log(`> Save name to '${sharedVars}'`);
    });
}