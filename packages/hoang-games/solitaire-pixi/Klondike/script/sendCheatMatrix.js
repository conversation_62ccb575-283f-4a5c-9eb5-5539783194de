const axios = require('axios');
const https = require('https');
const minimist = require('minimist');
const packageConfig = require('../package.json');

// parse args
const args = minimist(process.argv.slice(2), {
    default: {
        user: 18,
        matrix: 'Z,Y,S,T,2,3,A,X,2,6,A,Z,Z,2,A,3,2,3,T,4',
        tableFormat: '4,4,4,4,4',
        stackedTypeMode: 1,
        jackpotType: 0,
    },
    alias: {
        user: 'u',
        matrix: 'm',
        tableFormat: 't',
        stackedTypeMode: 'st',
        jackpotType: 'j',
    },
    unknown: (arg) => {
        if (arg.indexOf('-h') > -1 || arg.indexOf('--help') > -1 || arg.indexOf('-help') > -1 || arg.indexOf('?') > -1) {
            console.log(' **** Tool cheat matrix ****\n\n' +
            'Usage:\n' +
            '  yarn cheat -u [<UserId>] -m [<Matrix>]\n\n' +
            'Params:\n' +
            '  -u, -user\t\t\tUser Id (ex: 18)\n' +
            '  -m, -matrix\t\t\tMatrix Symbols\n' +
            '  -t, -tableFormat\t\tFormat of matrix (ex: 3,3,3,3,3 or 4,4,4,4,4)\n' +
            '  -st, -stackedType\t\tTarget game mode, Normal: 1 - Free: 2\n' +
            '  -j, -jackpotType\t\tExpand param of WereWolf\n' +
            '  -h, --help\t\t\tPrint help\n');
            process.exit();
        }
    },
});

const API_URL = `https://api.damary.net/${packageConfig.serviceId}/inputed?userId=devex_userC_UID&matrixData=C_MATRIX&tableFormat=C_TFORMAT&stackedTypeMode=C_STMODE&jackpotType=C_JPTYPE&goldenReel=C_GREEL`;

function main() {
    return new Promise((resolve, reject) => {
        console.log(' **** Cheat Matrix **** ');
        const requestUrl = API_URL.replace('C_UID', args.user)
            .replace('C_MATRIX', args.matrix)
            .replace('C_TFORMAT', args.tableFormat)
            .replace('C_STMODE', args.stackedTypeMode)
            .replace('C_JPTYPE', args.jackpotType)
            .replace('C_GREEL', '');
        console.log(' > Sending...');
        axios({
            method: 'POST',
            url: requestUrl,
            httpsAgent: new https.Agent({
                rejectUnauthorized: false,
            }),
        }).then((response) => {
            if (response.status === 200) {
                const data = response.data;
                const errorMsg = data.match('(?<=\<h3 style\=\"color:red\"\>Error\: ).*(?=\<\/h3\>)');
                // console.warn(data);
                if (errorMsg) {
                    console.log(' > Error: ' + errorMsg[0]);
                    console.log(requestUrl);
                    process.exit(1);
                } else {
                    console.log(' > Done!');
                }
                resolve();
            } else {
                reject(response);
            }
        }).catch((error) => {
            const data = error.response.data;
            console.log(data);
        });
    });
}

main();
