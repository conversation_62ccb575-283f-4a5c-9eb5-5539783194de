const path = require('path');
const fsPromises = require('fs').promises;
const chalk = require('chalk');

// export dirs
const paylineDir = path.join(__dirname, '../build/raw_assets/Paylines/');
const rawConfigPath = paylineDir + 'paylines.json';
const exportFile = path.join(__dirname, '../src/ResManagers/PaylineAssets.ts');

const NUMBER_OF_REELS = 5;
const NUMBER_SYMBOL_DISPLAYED_ON_REEL = 3;
const exportHeaderStr = '/* auto generate by payline config */\n';
const exportPaylinesStr = 'export const PaylineContants: number[][] = [@LINES@];';
const nativePaylines = [];

function shortingPaylineFormat(jsonData) {
    const paylineData = jsonData.payLines;

    for (let i = 0; i < paylineData.length; i++) {
        const res = paylineData[i];
        const verifyView = res.verifyView;
        const payline = [];
        for (let n = 0; n < NUMBER_OF_REELS; n++) {
            for (let row = 0; row < verifyView.length; row++) {
                if (verifyView[row][n]) {
                    const nativePos = row + n * NUMBER_SYMBOL_DISPLAYED_ON_REEL + 1; //+1 for start row count index = 1
                    payline.push(nativePos);
                }
            }
        }
        nativePaylines.push(payline);
    }
    if (nativePaylines.length > 0) {
        console.log(' > ' + chalk.green('Done: ') + 'Shorting format');
    } else {
        console.log(' > ' + chalk.red('Fail: ') + 'Convert error');
    }
    return nativePaylines;
}

function exportPaylines() {
    let paylines = '\n';
    nativePaylines.forEach((value) => {
        paylines += `    ${JSON.stringify(value)},\n`;
    });

    const data = exportHeaderStr + exportPaylinesStr.replace('@LINES@', paylines);
    fsPromises
        .writeFile(exportFile, data, 'utf8')
        .then(() => {
            console.log(' > ' + chalk.green('Done: ') + 'Export to PaylineAssets');
        })
        .catch((err) => {
            console.error(' > ' + chalk.red('Fail: ' + err.message));
        });
}

function main() {
    console.log(chalk.blue(' **** Export Paylines ... ****'));
    fsPromises
        .readFile(rawConfigPath, 'utf-8')
        .then((content) => {
            const jsonData = JSON.parse(content);
            console.log(' > ' + chalk.green('Done: ') + 'Parse config');
            shortingPaylineFormat(jsonData);
            exportPaylines();
        })
        .catch((err) => {
            console.error(' > ' + chalk.red('Fail: ' + err.message));
        });
}

main();
