const os = require('os');
const publicIp = require('public-ip');
const chalk = require('chalk');

function showInfo(property, value) {
    console.log(`${property}: ${chalk.green(value)}`);
}

function getLocalIp() {
    const interfaces = os.networkInterfaces();
    const connections = new Map();
    for (const iface in interfaces) {
        for (const i in interfaces[iface]) {
            const f = interfaces[iface][i];
            if (f.family === 'IPv4' && f.address !== '127.0.0.1') {
            // if (f.family === 'IPv4') {
                connections.set(iface, f.address);
            }
        }
    }
    return connections;
}

async function run() {
    const internalIp = await publicIp.v4();
    const localIps = getLocalIp();

    console.log(chalk.blue(' **** Network Infos ... ****'));
    showInfo('Hostname', os.hostname());
    showInfo('PublicIp', internalIp);
    console.log('LocalIp:');
    localIps.forEach((ip, iface) => {
        console.log(`\t${chalk.green(iface)}: ${chalk.green(ip)}`);
    });
}

run();