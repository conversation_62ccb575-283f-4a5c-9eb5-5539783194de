pipeline {
    agent any
	environment {
		FTP_DAMARY = credentials('ftp-damary')
	}
	stages {
		stage('Init') {
			steps {
				bat 'git config git-ftp.user %FTP_DAMARY_USR%'
				bat 'git config git-ftp.password %FTP_DAMARY_PSW%'
				bat 'git config git-ftp.url "ftp://***************/dhs/test"'
				bat 'git config git-ftp.syncroot dist/'
				// bat 'git ftp init' /*init for the 1st time*/
				// bat 'git ftp catchup' /*after the 1st time, just catch up if all files are sync*/
			}
		}
		stage('Checkout'){
			steps {
				checkout scm
			}
		}
		stage('Build') {
			steps {
				bat 'yarn install'
				bat 'yarn run build'
			}
		}
		stage('Deploy') {
			steps {
				bat 'git ftp push'
			}
		}
	}
}