# Bitbucket Pipeline Scripts
# author: <EMAIL>

# variables
SHARED_VARS=shared_vars.sh
BUNDLE_FOLDER=dist
PIPE_CACHE=/usr/local/share/pipes
REVIVE_PREFIX=v

# parse variable from package.json
function getPackageVariable {
    echo $(sed -nE 's/^\s*"'$1'":\ "(.*?)",$/\1/p' package.json)
}

# read and push shared vars to environment
function load_vars {
    if [[ -e $SHARED_VARS ]]; then
        echo \> Load Vars...
        source $SHARED_VARS
        cat $SHARED_VARS
    fi
}

# read project config and setup environment variables
function load_config {
    GAME_NAME=$(getPackageVariable title)
    VERSION=$(getPackageVariable version)
    SERVICE_ID=$(getPackageVariable serviceId)
    SERVICE_PREFIX=$(getPackageVariable servicePrefix)
    BUILD_NAME=$SERVICE_PREFIX$SERVICE_ID
    REVIVE_NAME=${BUILD_NAME}_${REVIVE_PREFIX}
    VERSION_NO_DOT=$(echo "$VERSION" | sed "s/\.//g")
    SHORT_COMMIT=$(echo $BITBUCKET_COMMIT | cut -c1-7)

    # export to shared vars
    {
        echo "export GAME_NAME=\"$GAME_NAME\""
        echo "export BUILD_NAME=$BUILD_NAME"
        echo "export REVIVE_NAME=$REVIVE_NAME"
        echo "export VERSION=$VERSION"
        echo "export VERSION_NO_DOT=$VERSION_NO_DOT"
        echo "export SHORT_COMMIT=$SHORT_COMMIT"
    } >>$SHARED_VARS
    cat $SHARED_VARS
}

# setup source, read config, setup env
function setup_source {
    echo \> Install Dependencies:
    yarn install

    echo \> Load config:
    load_vars
    load_config
}

# build game bundle
function build_game {
    if [[ "$RELEASE" == 1 ]]; then
        if [[ "$CHEAT" == 1 ]]; then
            echo \> Release-Cheat...
            yarn release-cheat
        else
            echo \> Release...
            yarn release
            echo \> Zip...
            yarn zip
        fi
    else
        echo \> Build...
        yarn build
    fi
    echo \> Done\!
}

# safe check deploy folder
function find_remote_folder {
    CURRENT_ENV=$BITBUCKET_DEPLOYMENT_ENVIRONMENT
    if [[ $CURRENT_ENV == "staging" || $CURRENT_ENV == "staging-revive" ]]; then
        DEPLOY_URL=$MAPPING_STAGING
    elif [[ $CURRENT_ENV == "test" || $CURRENT_ENV == "test-revive" ]]; then
        DEPLOY_URL=$MAPPING_DEV
    else
        DEPLOY_URL=$DEFAULT_URL
    fi
    if [[ -z ${DEPLOY_FOLDER+x} ]]; then
        DEPLOY_FOLDER=dev
        echo \> Default Folder: $DEPLOY_FOLDER
    fi
}

# setup variable before deploy to server
function setup_deploy {
    find_remote_folder

    # add custom suffix to build name
    if [[ -z "$CUSTOM_SUFFIX" ]]; then
        : # null command to just skip, empty suffix
    else
        BUILD_NAME=${BUILD_NAME}_$CUSTOM_SUFFIX
        echo \> Add suffix to name: $CUSTOM_SUFFIX
        echo \> New name: $BUILD_NAME
        write_info
    fi

    # set deploy pipeline variables
    SFTP_REMOTE_PATH=$DEPLOY_FOLDER/$BUILD_NAME
    SFTP_LOCAL_PATH=$BUNDLE_FOLDER/*

    # print deploy info
    echo \> Env: \'$BITBUCKET_DEPLOYMENT_ENVIRONMENT\'
    echo \> RemotePath: \'$SFTP_REMOTE_PATH\'
    echo
    echo \> GAME VER: $VERSION
    echo \> COMMIT: $SHORT_COMMIT \($BITBUCKET_COMMIT\)
    echo \> DEPLOY URL: $DEPLOY_URL/$BUILD_NAME\?token=
    echo \> Bundle dirs:
    du -ah -d 2 $BUNDLE_FOLDER
    # find $BUNDLE_FOLDER | sed -e "s/[^-][^\/]*\//  |/g" -e "s/|\([^ ]\)/|--\1/"
}

# setup variables before upload zip file to server
function setup_upload {
    DEPLOY_URL=$DEFAULT_URL/$DEPLOY_FOLDER
    # if [[ "$BITBUCKET_DEPLOYMENT_ENVIRONMENT" == "staging-bundle" ]]; then
    #     DEPLOY_URL=$MAPPING_STAGING
    # elif [[ "$BITBUCKET_DEPLOYMENT_ENVIRONMENT" == "test" ]]; then
    #     DEPLOY_URL=$MAPPING_DEV
    # fi
    echo \> Env: \'$BITBUCKET_DEPLOYMENT_ENVIRONMENT\'
    echo \> Local: \'$ZIP_NAME\'
    echo \> RemotePath:\ \'$DEPLOY_FOLDER\'
    echo
    echo \> ZIP URL:
    echo $DEPLOY_URL/$ZIP_NAME
}

# prompt and choose action to remove custom builds with pattern $BUILDNAME_*
function clean_action {
    # input any string to clear custom builds
    if [[ -z "$INPUT_YES_TO_CONFIRM" ]]; then
        echo \> Show targets\!
        CLEAN_ACTION='echo see "$line"'
    else
        if [[ "$INPUT_YES_TO_CONFIRM" == "YES" || "$INPUT_YES_TO_CONFIRM" == "yes" ]]; then
            # setup remove action
            echo \> Set action to remove\!
            CLEAN_ACTION='echo remove "$line"; rm -rf --preserve-root "$line"'
        fi
    fi
}

# export build info to bundle
function write_info {
    INFO_FILE=$BUNDLE_FOLDER/info.html
    {
        echo "<h1>$GAME_NAME</h1>"
        echo "<b>Version: </b>$VERSION<br>"
        echo "<b>Build: </b>$BITBUCKET_BUILD_NUMBER<br>"
        echo "<b>Branch: </b>$BITBUCKET_BRANCH<br>"
        echo "<b>Commit: </b>$SHORT_COMMIT<br>"
        echo "<i>Generate From CI Pipeline</i><br/>"
    } >$INFO_FILE
}

# deploy to other folder, use to wake up old build - kts9945_001
function revive_config {
    BUILD_NAME=${REVIVE_NAME}${VERSION_NO_DOT}
    echo \> Update BUILD_NAME: $BUILD_NAME
    write_info
}

# create cache for pipeline
function create_cache {
    IMAGE_NAME=$1
    CACHE_FILE=$2

    if [[ -z "$CACHE_FILE" ]]; then
        CACHE_FILE="$(echo "$IMAGE_NAME" | cut -d '/' -f 2)"
    fi

    SUB_PATH=$PIPE_CACHE/$CACHE_FILE
    CACHE_PATH=$SUB_PATH/image.tar
    if [[ -d "$SUB_PATH" ]]; then
        echo \> Folder $SUB_PATH exist
    else
        echo \> Create $SUB_PATH
        mkdir $PSUB_PATH
    fi
    if [[ "$(docker images -q --format="exist" $IMAGE_NAME 2>/dev/null)" == "exist" ]]; then
        echo \> Caching $IMAGE_NAME to $CACHE_PATH...
        docker image save $IMAGE_NAME -o $CACHE_PATH 2>/dev/null
        if [[ $? == 0 ]]; then
            echo \> Cache $IMAGE_NAME - \[OK\]
        else
            rm -rf $CACHE_PATH
            echo \> Cache $IMAGE_NAME - \[FAILED\]
        fi
    else
        echo \> Image $IMAGE_NAME not exist!
    fi
}

# load cache from image
function load_cache {
    IMAGE_NAME=$1
    IMAGE_PATH=$PIPE_CACHE/$IMAGE_NAME/image.tar
    echo \> Loading $IMAGE_NAME...
    if [[ -f $IMAGE_PATH ]]; then
        echo \> Load cache $IMAGE_NAME...
        docker image load -i $IMAGE_PATH
        if [[ $? == 1 ]]; then
            echo \> Load $IMAGE_NAME - \[FAILED\]
        fi
    else
        echo \> $IMAGE_NAME not cached!
    fi
}

function cache_all {
    docker images --format "{{.Repository}}" | while read image; do
        short_name="$(echo $image | cut -d '/' -f 2)"
        image_path=$PIPE_CACHE/$short_name/image.tar
        if [[ -f "$image_path" ]]; then
            echo \> $image cached, skip!
        else
            create_cache "$image"
        fi
    done
}

function load_all_cache {
    ls -1 $PIPE_CACHE | sed -e 's/\.tar$//' | while read image; do
        short_name="$(echo $image | cut -d '/' -f 2)"
        load_cache "$short_name"
    done
}

function remove_cached {
    find $PIPE_CACHE -type f -name "*.tar" -print | while read line; do
        cache_folder=$(dirname $line)
        rm -rf --preserve-root "$cache_folder"
        echo removed "$cache_folder"
    done
}

function remove_images {
    docker rmi $(docker images -a -q)
    if [[ $? == 0 ]]; then
        echo \> Remove images finished!
    else
        echo \> Remove images failed!
    fi
}

# cache step function, temporary to fix wrong args when call from main file
function load_ssh_run { load_cache ssh-run; }
function load_rsync_deploy { load_cache rsync-deploy; }
function load_clear_cache { load_cache bitbucket-clear-cache; }

# main script
if [[ $CI == true ]] && [[ "$1" != "" ]]; then
    # parse first param to command id
    CMD_ID=$1
    ARGS="${@:2}"
    # call command
    $CMD_ID $ARGS
else
    echo \> Stop\! Run on CI runner only\!
fi
