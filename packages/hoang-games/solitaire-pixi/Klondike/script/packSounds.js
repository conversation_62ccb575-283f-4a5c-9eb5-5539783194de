/**
 * Use ffmpeg to pack and compress audio files
 * https://ffmpeg.org/ffmpeg-all.html
 */
const os = require('os');
const fs = require('fs');
const fsPromises = require('fs').promises;
const path = require('path');
const chalk = require('chalk');
const AudioSprite = require('./3rd-party/AudioSprite');
const SoundParser = require('./3rd-party/SoundParser');
const { sortObject } = require('./utils');
const { spawn, exec } = require('child_process');
const ffmpegBinary = require('ffmpeg-static');
const { getFiles, compareInLowerCaseFunc } = require('./utils');

// script parameters
const removeUnusedFiles = process.argv.indexOf('--clean') > 1 || process.argv.indexOf('-c') > 1;
const verifyAfterBuild = true;
// global variables
const exportSourceFile = 'SoundAssets.ts';
const exportedConfigFile = 'spcf.json'; //short of sound pack config
const rawConfigFile = 'sound_config.json';
// export dirs
const soundDir = path.join(__dirname, '../build/raw_assets/Sound/');
const exportDir = path.join(__dirname, '../build/assets/Sound/');
const srcDir = path.join(__dirname, '../src/ResManagers/');
const { config: baseConfig, files, packs } = require(soundDir + rawConfigFile);
const exportHeaderStr = '/* auto generate by sound config */';

// change exec path to sound path
process.chdir(soundDir);

// save packed sound to verify config
const useSounds = [];
const ignoreSounds = [];

// save sound id, packs,... to export config
const soundJson = {};
const exportSoundFormat = `export const SoundFormat = [ \'${baseConfig.export}\' ]`;
let exportInGamePacks = 'export const InGamePacks: string[] = [';
let exportPreLoadPacks = 'export const PreLoadPacks: string[] = [';
let exportMusicIds = 'export const MusicIds: SoundId[] = [';
let exportNames = 'export const SoundNames = [ ';
const exportSoundIdType = '// eslint-disable-next-line no-use-before-define\nexport type SoundId = typeof SoundNames[number]';
let exportRawConfigs = 'export const RawConfigs = [\n';
let countProcess = 0;

// methods
function generateSoundJson() {
    return new Promise((resolve, reject) => {
        const sortedData = sortObject(soundJson);
        let jsonStr = JSON.stringify(sortedData);
        jsonStr = jsonStr.replace(/\\\\/g, '/');
        fsPromises
            .writeFile(path.resolve(exportDir, exportedConfigFile), jsonStr, 'utf8')
            .then(() => {
                console.log(' > ' + chalk.green('Done: ') + 'Rewrite Config');
                resolve();
            })
            .catch((err) => reject(err));
    });
}

function generateSoundAssets() {
    return new Promise((resolve, reject) => {
        const sourceContent = `${exportHeaderStr}
${exportSoundFormat};
${exportPreLoadPacks} ];
${exportInGamePacks} ];
${exportSoundIdType};
${exportMusicIds} ];
${exportNames}] as const;
${exportRawConfigs}];`;
        fsPromises
            .writeFile(path.resolve(srcDir, exportSourceFile), sourceContent, 'utf8')
            .then(() => {
                console.log(' > ' + chalk.green('Done: ') + 'Source');
                resolve();
            })
            .catch((err) => reject(err));
    });
}

function compressFiles() {
    return new Promise((resolve) => {
        console.log(chalk.blue(' **** Copying Sounds ... ****'));
        if (files.length === 0) {
            console.log(' > ' + chalk.green('Done: ') + 'No compress files!');
            resolve();
        }
        let countProcess = 0;
        for (let i = 0; i < files.length; i++) {
            const sfx = new SoundParser(files[i], undefined, exportDir, baseConfig.path);
            const preLoad = sfx.preLoad || false;
            const exportTypeCfg = files[i].export || baseConfig.export;
            const rateCfg = files[i].bitrate || baseConfig.bitrate;
            const channelsCfg = baseConfig.channels || 2;

            if (sfx.ignored) {
                ignoreSounds.push(sfx.src);
                continue;
            }

            console.log(` > Build: ${sfx.name}, rate: ${rateCfg}kbps...`);
            let convertLogs = '';
            const convertArgs = [
                '-y' /* overwrite output files */,
                '-i' /* in file */,
                path.resolve(soundDir, sfx.src),
                // '-codec:a' /* audio codec */,
                // 'libmp3lame',
                '-vn' /* disable video */,
                // '-ar' /* sampling rate (Hz) */,
                // sampleCfg,
                '-ac' /* audio channels */,
                channelsCfg,
                '-b:a' /* audio bitrate */,
                rateCfg + 'k',
                '-f' /* force format */,
                exportTypeCfg,
                sfx.outputPath,
                '-hide_banner' /* do not show program banner */,
                '-loglevel' /* logging level */,
                'repeat+level+error',
                // '-report' /* generate a report */,
            ];
            const childProc = spawn(ffmpegBinary, convertArgs);
            childProc.stderr.on('data', (msg) => {
                process.stdout.write(chalk.gray('.'));
                convertLogs += msg;
            });
            childProc.on('exit', async (code) => {
                if (code !== 0) {
                    console.log(`\n > ${chalk.red('[ERROR]')}: '${sfx.outputPath}' something went wrong!`);
                    console.log(' > ' + chalk.bgWhite.red(convertLogs));
                } else {
                    const packName = sfx.exportSoundPackName();
                    useSounds.push(sfx.src);
                    soundJson[sfx.filePack || sfx.name] = sfx.exportSinglePackHowlCfg();
                    if (preLoad) {
                        exportPreLoadPacks += ` '${packName}',`;
                    } else {
                        let formattedName = packName;
                        if (packName.indexOf('\'') === -1) {
                            formattedName = ` '${formattedName}',`;
                        }
                        exportInGamePacks += formattedName;
                    }
                    exportRawConfigs += `    ${sfx.exportSoundConfig()},\n`;
                    exportNames += `'${sfx.name}', `;
                    if (sfx.type === 1) {
                        exportMusicIds += ` '${sfx.name}',`;
                    }

                    const isExists = await fsPromises
                        .access(sfx.outputPath)
                        .then(() => true)
                        .catch(() => false);
                    if (isExists) {
                        countProcess++;
                        // console.log('\n' + chalk.bgWhite.gray(convertLogs));
                        console.log(`\n > ${chalk.green('Done')}: ` + sfx.outputPath);
                        if (countProcess >= files.length) {
                            console.log(' > ' + chalk.green('Done: ') + 'Compress files!');
                            resolve();
                        }
                    } else {
                        const platformName = os.platform();
                        if (platformName === 'darwin') {
                            console.log(`\n > ${chalk.red('Failed')}, ${chalk.blue('rebuild')}: ${sfx.name}, rate: ${rateCfg}kbps...`);
                            let rebuildLog = '';
                            const rebuildCmd = exec(ffmpegBinary + ' ' + convertArgs.join(' '));
                            rebuildCmd.stderr.on('data', (msg) => {
                                process.stdout.write(chalk.blue('.'));
                                rebuildLog += msg;
                            });
                            rebuildCmd.on('exit', (code) => {
                                countProcess++;
                                if (code !== 0) {
                                    console.log(chalk.bgWhite.red(rebuildLog));
                                    console.log(`\n > ${chalk.red('Error')}, no hope!`);
                                } else {
                                    console.log(`\n > ${chalk.green('Done')}: ` + sfx.outputPath);
                                }
                                if (countProcess >= files.length) {
                                    console.log(' > ' + chalk.green('Done: ') + 'Compress files!');
                                    resolve();
                                }
                            });
                        } else {
                            console.log(`\n > ${chalk.red('Error')}, can't compress files on platform ${platformName}, no hope!`);
                        }
                    }
                }
            });
        }
    });
}

function buildSoundPacks() {
    console.log(chalk.blue('\n **** Packing Sounds ... ****'));
    return new Promise((resolve, reject) => {
        const listPacks = Object.keys(packs);
        for (let i = 0; i < listPacks.length; i++) {
            const packName = listPacks[i];

            let fileInPacks = [];
            const buildingFiles = [];
            const soundLoops = [];
            const packSoundCfg = packs[packName];
            const preLoad = packSoundCfg.preLoad || false;
            const rateCfg = packSoundCfg.bitrate || baseConfig.bitrate;
            if (preLoad) {
                exportPreLoadPacks += ` '${packName}',`;
            } else {
                exportInGamePacks += ` '${packName}',`;
            }
            packSoundCfg.files.forEach((file) => {
                if (typeof file !== 'object') {
                    // ignore comment syntax
                    if (file.includes('/*') || file.includes('//')) {
                        // console.log('See comment: ', file);
                    } else {
                        console.log('Something went wrong: ', file);
                    }
                    return;
                }
                const sfx = new SoundParser(file, packName, exportDir, baseConfig.path);
                if (buildingFiles.indexOf(sfx.name) > -1) {
                    console.log(`\n > ${chalk.red('[ERROR]')}: Duplicate element \`${sfx.name}\` (${sfx.src})!`);
                    process.exit(1); // force exit
                } else {
                    buildingFiles.push(sfx.name);
                }
                if (sfx.ignored) {
                    ignoreSounds.push(sfx.src);
                    return;
                }
                fs.access(path.join(soundDir, sfx.src), (err) => {
                    if (err) {
                        const errMsg = err.message.replace('ENOENT: ', '');
                        console.log(`\n > ${chalk.red('[ERROR]')}: Not exists ${sfx.name} (${sfx.src})!`);
                        console.log(' > [LOG] ' + chalk.gray(errMsg));
                        process.exit(1); // force exit
                    }
                });
                useSounds.push(sfx.src);

                // check duplicate sprite in pack
                if (fileInPacks.indexOf(sfx.src) !== -1) {
                    if (verifyAfterBuild) {
                        console.log(` > ${chalk.yellow('[WARN]')} Duplicate sprite '${chalk.cyan(packName)} ${chalk.magenta('>>')} ${chalk.blue(sfx.src)}', skip!`);
                    }
                } else {
                    fileInPacks.push(sfx.src);
                }
                if (sfx.loop) {
                    soundLoops.push(sfx.filename);
                }
                if (sfx.type === 1) {
                    exportMusicIds += ` '${sfx.name}',`;
                }
                exportRawConfigs += `    ${sfx.exportSoundConfig()},\n`;
                exportNames += `'${sfx.name}', `;
            });

            console.log(` > Packing: ${packName}, rate: ${rateCfg}kbps...`);

            const packOptions = {
                ...baseConfig,
                output: exportDir + packName,
                loop: soundLoops,
                bitrate: rateCfg,
                logger: {
                    debug: () => null,
                    info: () => process.stdout.write(chalk.gray('.')),
                    log: () => null,
                },
            };

            // sort list files
            fileInPacks = fileInPacks.sort(compareInLowerCaseFunc);
            if (fileInPacks.length > 0) {
                AudioSprite(fileInPacks, packOptions, function (err, obj) {
                    if (err) {
                        console.log(err);
                        reject(err);
                    }

                    countProcess++;
                    // convert to latest config
                    const data = { src: obj.urls, sprite: obj.sprite };
                    soundJson[packName] = data;
                    console.log('\n > ' + chalk.green('Done: ') + packOptions.output + '.mp3');

                    if (countProcess >= listPacks.length) {
                        resolve(countProcess);
                    }
                });
            } else {
                console.log(` > ${chalk.yellow('[WARN]')} Pack '${packName}' is empty!`);
            }
        }
    });
}

async function generateSource() {
    console.log(chalk.blue('\n **** Generate Source ... ****'));

    await generateSoundJson();
    await generateSoundAssets();

    return Promise.resolve();
}

function validateConfig() {
    console.log(chalk.blue('\n **** Verify Config ... ****'));
    getFiles(soundDir).then((files) => {
        let hasUnusedFiles = false;
        files.forEach((file) => {
            if (file.name.endsWith('.mp3') || file.name.endsWith('.wav')) {
                const isUsed = useSounds.filter(x => x.indexOf(file.name) > -1).length > 0;
                if (!isUsed && (file.name !== 'empty.mp3')) {
                    hasUnusedFiles = true;
                    if (removeUnusedFiles) {
                        fsPromises.rm(file.path).then(() => {
                            console.log(' > ' + chalk.red('DELETE: ') + chalk.yellow(file.name) + ` (${file.path})`);
                        });
                    } else {
                        const msg = ignoreSounds.indexOf(file.name) > -1 ? 'Ignored' : 'Unused';
                        console.log(` > ${chalk.yellow('[WARN] ')}${file.path.replace('/', '\\').replace(soundDir, '')} < ${chalk.yellow(msg)}!`);
                    }
                }
            } else {
                if (file.name !== rawConfigFile) {
                    console.log(' > ' + chalk.yellow('[WARN]: ') + `Others: ${file.path}`);
                }
            }
        });
        if (hasUnusedFiles && !removeUnusedFiles) {
            console.log(' > ' + chalk.yellow('[WARN] ') + 'Please cleanup unused resources or run `yarn sound -c`');
        } else {
            console.log(' > ' + chalk.green('Done: ') + 'Verified config');
        }
    });
}

async function main() {
    await compressFiles();
    await buildSoundPacks();
    await generateSource();

    if (verifyAfterBuild || removeUnusedFiles) {
        validateConfig();
    }
}

main();
