const axios = require('axios');
const https = require('https');
const readline = require('readline');
const chalk = require('chalk');
const clipboardy = require('clipboardy');

// variables
const BASE_URL = 'https://api.damary.net/manage-token-devex';
const GET_ALL = '/getListData';
const GENERATE_NEW = '/generateNewToken?userName=@user@&password=@pass@';
const isGenerateNew = process.argv.indexOf('--generate-new') > 1 || process.argv.indexOf('-g') > 1;

function toNumber(str) {
    const num = Number.parseInt(str);
    if (!Number.isNaN(num)) {
        return num;
    }
    return undefined;
}

function askUserId() {
    return new Promise((resolve, reject) => {
        let userId = -1;

        // get from args
        process.argv.slice(2).forEach((arg) => {
            const numberId = toNumber(arg);
            if (numberId) {
                userId = numberId;
                resolve(userId);
            }
        });

        // read from input
        if (userId === -1) {
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout,
            });

            rl.question('> Input UserID: ', (answer) => {
                const numberId = toNumber(answer);
                if (numberId) {
                    userId = numberId;
                    resolve(userId);
                } else {
                    console.log('> ' + chalk.red('Wrong UserID!!!'));
                }
                rl.close();
            });
        }
    });
}

function printUserInfo(userData) {
    const safeRemoveProperty = (prop, obj) => (prop in obj ? delete obj[prop] : undefined);
    safeRemoveProperty('UserName', userData);
    safeRemoveProperty('Password', userData);
    safeRemoveProperty('Owner', userData);

    console.log('> Info:');
    console.log(userData);

    // copy to clipboard
    clipboardy.write(userData.Token).then(() => {
        console.log('\n> Copied!');
    }).catch(() => {
        console.log('\n> Cant copy!');
    });
}

function generateNewToken(username, password) {
    return new Promise((resolve, reject) => {
        axios({
            method: 'GET',
            url: BASE_URL + GENERATE_NEW.replace('@user@', username).replace('@pass@', password),
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            }),
        })
            .then((response) => {
                const result = response.data;
                if (result.code === 200) {
                    resolve(result.data);
                } else {
                    reject(result.code);
                }
            })
            .catch((error) => {
                reject(error);
            });
    });
}

function getUsers() {
    return new Promise((resolve, reject) => {
        axios({
            method: 'GET',
            url: BASE_URL + GET_ALL,
            httpsAgent: new https.Agent({
                rejectUnauthorized: false
            }),
        })
            .then((response) => {
                const result = response.data;
                if (result.code === 200) {
                    resolve(result.data);
                } else {
                    reject(result.code);
                }
            })
            .catch((error) => {
                reject(error);
            });
    });
}

async function main() {
    console.log(chalk.blue('**** Get Debug Tokens ... ****'));
    console.log(
        `${chalk.yellow('Help:')} yarn token <${chalk.magenta('-g')}> <${chalk.magenta('id')}>\n` +
            '      Args: \n' +
            `        ${chalk.magenta('-g')}: Generate new token\n` +
            `        ${chalk.magenta('id')}: your id number [1-50]`
    );
    console.log(chalk.blue('******************************'));
    const userId = await askUserId();
    getUsers()
        .then((listUsers) => {
            const userData = listUsers.find((x) => x.UserID.indexOf(userId) !== -1);
            if (userData) {
                if (isGenerateNew) {
                    generateNewToken(userData.UserName, userData.Password).then((newListUsers) => {
                        const newUser = newListUsers.find((x) => x.UserID.indexOf(userId) !== -1);
                        printUserInfo(newUser);
                    });
                } else {
                    printUserInfo(userData);
                }
            } else {
                console.log('> ' + chalk.red('User not found!'));
            }
        })
        .catch((error) => {
            console.log('[ERROR]: ' + error.message);
        });
}

main();
