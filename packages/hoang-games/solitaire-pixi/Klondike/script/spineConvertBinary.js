/**
 * http://en.esotericsoftware.com/spine-command-line-interface
 */
const os = require('os');
const fs = require('fs/promises');
const path = require('path');
const { spawn } = require('child_process');
const chalk = require('chalk');
const { hashFile, sortObject, existAsync, readChecksum, saveChecksum, CS_SPINE_JSON_PROPERTY: SAVE_PROPERTY } = require('./utils');

// switch to script path
process.chdir(path.join(__dirname));

const argsPatterns = ['--a', '-a'];
const useAllCpuCores = process.argv.indexOf(argsPatterns[0]) > 1 || process.argv.indexOf(argsPatterns[1]) > 1;
const lastArg = process.argv[process.argv.length - 1];
const fileFilter = process.argv.length > 2 && argsPatterns.indexOf(lastArg) < 0 ? lastArg : null;

/**
 * Get all files in given directory and all sub-directories
 * @param {*} folderPath
 */
async function getFiles(folderPath = './build/assets/', ext = '.') {
    const entries = await fs.readdir(folderPath, { withFileTypes: true });

    // Get files within the current directory and add a path key to the file objects
    const files = entries.filter((file) => !file.isDirectory() && file.name.indexOf(ext) !== -1).map((file) => ({ ...file, folder: folderPath }));

    // Get folders within the current directory
    const folders = entries.filter((folder) => folder.isDirectory());

    /**
     * Add the found files within the subdirectory to the files array by calling the
     *  current function itself
     */
    for (const folder of folders) files.push(...(await getFiles(`${folderPath}${folder.name}/`, ext)));

    return files;
}

async function delay(duration) {
    return new Promise((resolve) => setTimeout(resolve, duration));
}

let needPrintNewLine = false;
let buildingProcess = 0;
let totalOfFiles = 0;
let successProcess = 0;
let isMissingLicense = false;
let isPrintVersion = false;
let isPrintLicense = false;
async function convertBinary(folder, name) {
    return new Promise((resolve, reject) => {
        const filepath = folder + name;
        const child = spawn('spine', ['--hide-license', '--input', filepath, '--output', folder, '--export', 'spineExport.setting.json']);
        buildingProcess++;
        child.stdout.on('data', (chunk) => {
            const data = chunk + '';
            if (data.indexOf('Enter activation') > -1 && !isMissingLicense) {
                isMissingLicense = true;
                const msg = 'Missing license, please open Spine GUI and enter license!';
                console.log(`\n${chalk.yellow(data)}`);
                console.log(' > ' + chalk.red('[ERROR]: ' + msg));
                throw msg;
            } else if (data.indexOf('Launching: ') > -1 && !isPrintVersion) {
                isPrintVersion = true;
                console.log(`\n > ${chalk.bgWhite.black(data.trim().replace('Launching: ', ''))}`);
            } else if (data.indexOf('Licensed') > -1 && !isPrintLicense) {
                isPrintLicense = true;
                console.log(`\n > ${chalk.bgWhite.black(data.trim())}`);
            } else {
                needPrintNewLine = true;
                process.stdout.write(chalk.grey('.'));
            }
        });
        child.stderr.on('error', (err) => {
            console.log(`\n > ${chalk.red('Error:')} ${filepath}, ${err}`);
            reject(err);
        });
        child.stdout.on('close', () => {
            resolve();
        });
    });
}

async function verifyChecksum(checksum) {
    const savedPath = Object.keys(checksum);
    for (let i = 0; i < savedPath.length; i++) {
        const filePath = savedPath[i];
        const isAssetExists = await existAsync(filePath);
        if (!isAssetExists) {
            console.log(` > rm hash: ${filePath}`);
            delete checksum[filePath];
        }
    }
    return checksum;
}

const autoSavePoint = 5;
let savedProcess = 0;
async function main(folderPath) {
    const commonChecksum = await readChecksum();
    const spineChecksum = commonChecksum[SAVE_PROPERTY] || {};

    console.log(chalk.magenta('\n **** Converting spines json to binary ... ****'));
    const numberProcessors = os.cpus().length;
    const maxBuildingAtTime = useAllCpuCores ? numberProcessors : Math.round(numberProcessors * 2 / 3);
    const spineFiles = await getFiles(folderPath, '.json');
    totalOfFiles = spineFiles.length;
    console.log(` > Processors: ${maxBuildingAtTime}/${numberProcessors}, Build files: ${totalOfFiles}\n`);

    for (let i = 0; i < totalOfFiles; i++) {
        if (fileFilter && spineFiles[i].name.indexOf(fileFilter) === -1) {
            continue;
        }
        while (buildingProcess >= maxBuildingAtTime) {
            process.stdout.write(chalk.cyan('.'));
            needPrintNewLine = true;
            await delay(3000);
        }
        const jsonPath = spineFiles[i].folder + spineFiles[i].name;
        await hashFile(jsonPath).then((hash) => {
            const savedHash = spineChecksum[jsonPath];
            if (savedHash !== hash) {
                if (needPrintNewLine) {
                    needPrintNewLine = false;
                    console.log();
                }
                console.log(` > Build: ${jsonPath}`);
                needPrintNewLine = false;
                convertBinary(spineFiles[i].folder, spineFiles[i].name)
                    .then(() => {
                        buildingProcess--;
                        successProcess++;
                        spineChecksum[jsonPath] = hash;
                        if (needPrintNewLine) {
                            needPrintNewLine = false;
                            console.log();
                        }
                        console.log(` > ${chalk.green('DONE')}: ${jsonPath} ${chalk.blue('[')}${successProcess}/${totalOfFiles}${chalk.blue(']')}`);

                        const isBuildDone = successProcess >= totalOfFiles;
                        const isAutoSave = successProcess >= (savedProcess + autoSavePoint);
                        if (isBuildDone || isAutoSave) {
                            if (isAutoSave) { savedProcess = successProcess; }
                            commonChecksum[SAVE_PROPERTY] = sortObject(spineChecksum);
                            saveChecksum(commonChecksum, isAutoSave).then(() => {
                                console.log(isAutoSave ? ` >>> ${chalk.yellow('SAVED PROCESS')} <<<` : '');
                            });
                        }
                    });
            } else {
                successProcess++;
                console.log(` > SKIP: ${jsonPath}`);
            }
        });
    }

    // verify checksum
    console.log('\n **** Verify Checksum ... ****');
    const verifiedChecksum = await verifyChecksum(spineChecksum);
    commonChecksum[SAVE_PROPERTY] = sortObject(verifiedChecksum);
    saveChecksum(commonChecksum, true).then(() => {
        console.log(` >>> ${chalk.yellow('VERIFIED SPINE CHECKSUM')} <<<`);
    });
}

main('../build/assets/Spine/');