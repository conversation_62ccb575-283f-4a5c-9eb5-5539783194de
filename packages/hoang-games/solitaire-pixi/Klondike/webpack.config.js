const fs = require('fs');
const { pathExists } = require('fs-extra');
const path = require('path');
const merge = require('webpack-merge').merge;

// plugins
const webpack = require('webpack');
const TsCheckerPlugin = require('fork-ts-checker-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const HtmlPlugin = require('html-webpack-plugin');
const HtmlReplacePlugin = require('html-replace-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const chokidar = require('chokidar');
const rimraf = require('rimraf');
const AdmZip = require('adm-zip');
const short = require('short-uuid');
const packageConfig = require('./package.json');

// Library Output Config
function generateHash(length = 100) {
    const newHash = short.generate();
    return length ? newHash.substring(0, Math.min(length, newHash.length)) : newHash;
}
const LIB_NAME = (packageConfig.title ?? 'GameLauncher').replace(/ /g, '').toLowerCase();
const OUTPUT_PATH = path.resolve(__dirname, 'dist');
const CHUNK_NAMES = {
    entry: ['entry', generateHash(6)],
    game: ['game', generateHash(6)],
};
const ENTRY_CONFIGS = {
    [CHUNK_NAMES.entry.join('.')]: [path.resolve(__dirname, 'src/launcher.ts')],
    [`${packageConfig.bundleName}/` + CHUNK_NAMES.game.join('.')]: [path.resolve(__dirname, 'src/main.ts')],
};
// write current config to file
const IS_DEBUG_WEBPACK = false;
// check current build mode
const CUR_ENV = process.argv[process.argv.indexOf('--mode') + 1] === 'production' ? 'production' : 'development';
const IS_PROD = CUR_ENV === 'production';

function _packAssetToZip(watch = false) {
    const watchPath = path.resolve(__dirname, 'build/assets');
    const outputZipPath = path.resolve(__dirname, 'build/bundle.zip');
    let assetWatcher = null;
    if (watch) {
        assetWatcher = chokidar.watch(watchPath, {
            persistent: true,
        });
    }

    async function removeZip() {
        const exists = await pathExists(outputZipPath);
        if (exists) {
            console.log('Folder deleted. Removing zip file...');
            rimraf.sync(outputZipPath); // Remove the zip file if the folder is deleted
            console.log(`Zip file removed: ${outputZipPath}`);
        }
    }
    let isCreateZip = false;
    async function zipFolder() {
        if (isCreateZip) { return; }
        isCreateZip = true;
        console.log('Folder change detected. Zipping...');
        await removeZip();
        await new Promise((resolve) => {
            console.log('Start zip...');
            const zipFile = new AdmZip();
            zipFile.addLocalFolder(watchPath);
            zipFile.writeZip(outputZipPath, (err) => {
                if (err) throw err;
                console.log('Folder zipped!');
                isCreateZip = false;
                resolve();
            });
        });
    }
    if (watch) {
        assetWatcher
            .on('add', zipFolder)
            .on('change', zipFolder)
            .on('unlink', zipFolder);
    } else {
        zipFolder();
    }
}

module.exports = async (env) => {
    // Parse argument config
    const IS_DEBUG = /^debug$/i.test(env.mode);
    const IS_BUILD = /^build$/i.test(env.mode);
    const IS_ANALYZER = /^true$/i.test(env.analyzer);
    const shortUUID = IS_DEBUG ? 'assets' : generateHash(16);
    const isMinimizeHtml = false; // !IS_DEBUG; - @phong.dev skip compress html for this client
    const isEnableCheat = env.cheat === 'false' ? false : true;

    let spineExt = '.json';
    let ignoreAssets = ['Spine/*.skel', 'Spine/**/*.skel'];
    if (packageConfig.useBinarySpine && IS_PROD) {
        spineExt = '.skel';
        ignoreAssets = ['Spine/*.json', 'Spine/**/*.json'];
    }

    // watch zip
    await _packAssetToZip(IS_DEBUG);

    const baseConfig = {
        entry: ENTRY_CONFIGS,
        output: {
            path: OUTPUT_PATH,
            filename: '[name].js',
            libraryTarget: 'umd',
            library: LIB_NAME,
            umdNamedDefine: true,
        },
        resolve: {
            extensions: ['.ts', '.tsx', '.js', '.jsx'],
        },
        module: {
            rules: [
                {
                    test: /\.mjs$/,
                    include: /node_modules/,
                    type: 'javascript/auto'
                },
                {
                    test: /\.[jt]sx?$/,
                    loader: 'esbuild-loader',
                    options: {
                        loader: 'tsx',
                        target: 'ES2016',
                    }
                },
                {
                    test: /\.[jt]sx?$/,
                    use: [
                        {
                            loader: 'webpack-preprocessor-loader',
                            options: {
                                debug: !IS_PROD,
                                directives: {
                                    secret: false,
                                },
                                params: {
                                    ENV: CUR_ENV,
                                    isProduction: IS_PROD,
                                    cheatMenu: isEnableCheat,
                                    debugDrawCall: packageConfig.debugDrawCallOnReleaseCheat,
                                },
                                verbose: IS_DEBUG,
                            },
                        },
                    ],
                },
            ],
        },
        plugins: [
            new webpack.EnvironmentPlugin({
                CHUNK_GAME: CHUNK_NAMES.game.join('.'),
                LIB_NAME,
                BUNDLE_NAME: packageConfig.bundleName,
                GAME_NAME: packageConfig.title,
                VERSION: packageConfig.version,
                ASSET_FOLDER: shortUUID,
                // SPINE_EXT: spineExt
            }),
            new TsCheckerPlugin({
                typescript: {
                    configFile: path.resolve(__dirname, './tsconfig.json'),
                    memoryLimit: 10240,
                    context: process.cwd()
                },
                logger: console,
                devServer: IS_DEBUG,
            }),
            new CopyPlugin({
                patterns: [
                    {
                        from: 'build/bundle.zip',
                        to: `${packageConfig.bundleName}/${shortUUID}.zip`,
                        globOptions: {
                            ignore: ignoreAssets,
                            concurrency: 200,
                        },
                    },
                    {
                        from: 'build/favicon.png',
                        to: 'favicon.png',
                    },
                ],
                options: {
                    concurrency: 200,
                }
            }),
            new HtmlPlugin({
                template: 'build/index.html',
                filename: 'index.html',
                chunks: [ CHUNK_NAMES.entry.join('.') ],
                excludeChunks: [],
                scriptLoading: 'defer',
                minify: !isMinimizeHtml ? false : {
                    collapseWhitespace: true,
                    keepClosingSlash: true,
                    removeComments: true,
                    removeTagWhitespace: true,
                    removeRedundantAttributes: true,
                    removeScriptTypeAttributes: true,
                    removeStyleLinkTypeAttributes: true,
                    removeEmptyAttributes: true,
                    removeOptionalTags: true,
                    trimCustomFragments: true,
                    useShortDoctype: true,
                    minifyCSS: true,
                    minifyJS: true,
                    minifyURLs: true,
                },
            }),
            new HtmlReplacePlugin([
                {
                    pattern: '@suffix@',
                    replacement: '' + shortUUID,
                },
                {
                    pattern: '@title@',
                    replacement: packageConfig.title,
                },
                {
                    pattern: '\'@isProd@\'',
                    replacement: IS_PROD,
                },
                {
                    pattern: '\'@isEnableCheat@\'',
                    replacement: isEnableCheat,
                },
                {
                    pattern: '@StaticRootPath@',
                    replacement: `${packageConfig.bundleName}/`,
                },
                {
                    pattern: '@time@',
                    replacement: Date.now(),
                },
            ]),
            new BundleAnalyzerPlugin({
                analyzerMode: IS_ANALYZER ? 'server' : 'disable',
                generateStatsFile: IS_ANALYZER,
                openAnalyzer: IS_ANALYZER,
            })
        ],
        stats: IS_DEBUG ? 'minimal' : 'normal',
    };

    // merge with env config
    const envName = IS_PROD ? 'prod' : 'dev';
    const isMangleProperties = packageConfig.mangleWithCustomRegex || false;
    const envConfig = require(path.resolve(__dirname, `./script/webpack.${envName}.js`))(env, IS_DEBUG, IS_BUILD, isMangleProperties);
    const mergedConfig = merge(baseConfig, envConfig);

    /* Preprocessor loader should be the last loader in use definition
    mergedConfig.module.rules.reverse(); - skip merge loaders
    */

    if (IS_DEBUG_WEBPACK) {
        // eslint-disable-next-line no-use-before-define
        writeCurrentConfig(mergedConfig);
    }

    return mergedConfig;
};

function writeCurrentConfig(config) {
    const jsonData = JSON.stringify(config, null, 4);
    const debugPath = path.join(__dirname, 'webpack.current.json');
    fs.writeFile(debugPath, jsonData, () => console.log('> Exported Webpack Config: ' + debugPath));
}
