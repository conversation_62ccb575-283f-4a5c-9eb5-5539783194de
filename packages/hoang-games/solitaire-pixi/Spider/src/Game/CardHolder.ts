import ResourceManager from '../ResManagers/ResourceManager';
import { Container, IPoint, ISize, Rectangle, Sprite } from '../pixi';

export enum ECardType {
    NONE = 0,
    NORMAL,
    COIN,
    BOMB,
    SIZE,
}
export class CardHolder extends Container {
    private readonly _DIM_COLOR = 0x777777; // 0xa5a5a5 - 0x757575 - 0x363636
    private readonly _UN_DIM_COLOR = 0xffffff;
    private readonly _FRAME_MAPPING = {
        [ECardType.NORMAL]: 'normal',
        [ECardType.COIN]: 'coin',
        [ECardType.BOMB]: 'bomb',
    };
    private readonly _SHAKE_CONFIG = {
        duration: 10000,
        frequency: 40,
        amplitude: { x: 8, y: 4 },
    };

    private _isHighlight: boolean;
    private _targetTint = this._UN_DIM_COLOR;
    private _type: ECardType;
    private _sprite: Sprite;
    private _realSize: ISize;
    private _hitRect: Rectangle;
    private _originPos: IPoint;
    private _shakeTimer: { r: number };
    private _isPickUp: boolean;
    private _isHover: boolean;

    constructor() {
        super();
        this._sprite = new Sprite();
        this._sprite.anchor.set(0.5);
        this._sprite.zIndex = 4;
        // this._sprite.interactive = true;
        this.addChild(this._sprite);
    }

    public init() {
        this._realSize = {
            width: 120,
            height: 150,
        };

        this._sprite.texture = ResourceManager.getAtlasFrame('gui_klondike_atlas', 'Untitled-2');
        this._sprite.scale.set(0.6);

        this._isPickUp = false;
    }
}
