import AppConstants, { DrawMode, SuitMode } from '../AppConstants';
import { GameEvents, UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import { PopupManager } from '../Popup/PopupManager';
import ResourceManager from '../ResManagers/ResourceManager';
import AutoUpdateContainer from '../Scenes/AutoUpdateContainer';
import { SoundManager } from '../SoundManager';
import UIManager from '../UI/UIManager';
import { ObjectPool } from '../Utils/ObjectPool';
import Utils from '../Utils/Utils';
import { Container, Graphics, IPointData, InteractionEvent, Sprite } from '../pixi';
import { Card, ECardStatus } from './Card';
import { CardHolder } from './CardHolder';
import { MultiResolutionHandler, ScreenMetadata } from '../Scenes/MultiResolutionHandler';
import GameConfigs from '../GameConfigs';
import Localization from '../Localization/Localization';

const COLUMNS = 10;
const CARD_MOVE_DURATION = 150;

export default class SpiderGameBoard extends AutoUpdateContainer {
    private _cardPool: ObjectPool<Card>;
    private _gameBg: Sprite;
    private _cardContainer: Container;

    private _deck: Card[]; // contains all cards for checking
    private _cards: Card[];
    private _drawedCards: Card[] = [];
    private _listCardWin: Card[] = [];

    private _tableauColumns: Container[] = [];
    private _winColumns: Container[] = [];
    private _startColumn: CardHolder;
    private _drawedColumn: CardHolder;

    private _tableauStacks: Card[] = [];
    private _winStacks: Card[] = [null, null, null, null, null, null, null, null];
    private _undoStacks = [];

    private _mode: SuitMode = SuitMode.TWO;
    private _isCheat: boolean = false;
    private _powerMode: boolean = false;

    private _scoreGame: number = GameConfigs.SCORE_START_GAME;
    private _listTimeStamp: number[] = [];
    private _timer: number = 0;
    private _isStartCountTimer: boolean = false;
    private _cardArray = [];
    private _isCardLifter: boolean = true;


    constructor() {
        super();
        MultiResolutionHandler.inst.addResizeListener(this._onResize);
        Events.on(UIEvents.CHOOSE_DRAW_GAME, this._loadDrawGame.bind(this));
        Events.on(GameEvents.NEW_GAME, this._dealCards.bind(this));
        Events.on(UIEvents.ADD_INFO, () => {
            PopupManager.rankPopup.open(true);
        });
        Events.on(UIEvents.CLICK_CHANGE_LEVEL, this._changeLevel.bind(this));
        Events.on(UIEvents.CLICK_START, this._onClickStart.bind(this));
        Events.on(UIEvents.UNDO, this._undo.bind(this));
        Events.on(UIEvents.HINT, this.showHint.bind(this));
        Events.on(UIEvents.POWER_UP, this._togglePowerMode.bind(this));
        Events.on(UIEvents.SHOW_CHOOSE_GAME, () => {
            PopupManager.newGamePopup.open(true);
        });
        Events.on(UIEvents.CHANGE_STATE_CARD_LIFTER_BUTTON, () => {
            this._isCardLifter = true;
        });
    }

    public init() {
        this._gameBg = new Sprite(ResourceManager.getTexture('splash-sprite'));
        this._gameBg.anchor.set(0.5);
        this.addChild(this._gameBg);

        // SoundManager.inst.playMusic('BGM');

        // init start stack
        this._startColumn = new CardHolder();
        this._startColumn.name = 'StartColumn';
        this._startColumn.init();
        this._startColumn.x = AppConstants.BASE_SCREEN_WIDTH / 2.2 - this._startColumn.width / 3;
        this._startColumn.y = -AppConstants.BASE_SCREEN_HEIGHT / 2 + this._startColumn.height / 2 + 122;
        this._startColumn.visible = false;
        this.addChild(this._startColumn);

        // init win stacks
        for (let i = 0; i < 8; i++) {
            const winStack = new CardHolder();
            winStack.init();
            winStack.name = 'WinStack#' + i;
            winStack.x = -AppConstants.BASE_SCREEN_WIDTH / 3 - 90 + i * (winStack.width + 9);
            winStack.y = this._startColumn.y + 4;
            winStack.visible = false;
            this.addChild(winStack);
            this._winColumns.push(winStack);
        }

        // init tableau stacks
        for (let i = 0; i < 10; i++) {
            const tableauStack = new CardHolder();
            tableauStack.init();
            tableauStack.name = 'TableauStack#' + i;
            tableauStack.x = (i - 4.5) * (tableauStack.width + 9);
            tableauStack.y = -AppConstants.BASE_SCREEN_HEIGHT / 15 + 44;

            this.addChild(tableauStack);
            this._tableauColumns.push(tableauStack);
        }

        this._cardContainer = new Container();
        this._cardContainer.width = AppConstants.BASE_SCREEN_WIDTH;
        this._cardContainer.height = AppConstants.BASE_SCREEN_HEIGHT;
        this._cardContainer.name = 'CardContainer';
        this._cardContainer.sortableChildren = true;
        this.addChild(this._cardContainer);

        const cardArray = this._getRandomArray();
        this._cardArray = cardArray;
        // create pool
        const bufferSize = cardArray.length;
        this._cardPool = new ObjectPool<Card>(bufferSize, () => {
            const card = new Card();
            card.openCallback = this._startCallback.bind(this);
            card.drawCallback = this._drawCallback.bind(this);
            card.moveCallback = this._moveCallback.bind(this);
            card.endCallback = this._endCallback.bind(this);
            card.doubleClickCallback = this._doubleClickCallback.bind(this);
            card.rejectUndoCallback = this._rejectLastUndo.bind(this);
            card.gameRule = this._gameRule.bind(this);
            return card;
        }, (obj: Card) => {
            obj.stopWaitAnim();
            obj.reset();
        }, null, 'BLP', Math.round(bufferSize * 0.85));

        // create cards
        this._createCard(cardArray);

        this._deck = this._cards.slice();

        UIManager.inst.activeSpinPanel();

    }
    private _onResize = (screen: ScreenMetadata) => {

    };

    private _createCard(cardArray) {
        // create cards
        this._cards = [];
        cardArray.forEach((i) => {
            const card = this._cardPool.take();
            card.init(i);
            card.position.set(this._startColumn.x, this._startColumn.y);
            card.parentGroup = UIManager.inst.showingGroup;
            this._cardContainer.addChild(card);
            this._cards.push(card);
        });

        this._deck = this._cards.slice();
    }

    private _startCallback(card: Card) {
        if (this._powerMode) {
            card.parentGroup = UIManager.inst.movingGroup;
            card.doubleClickCallback = null;
        } else {
            card.setParentGroup(UIManager.inst.movingGroup);
        }

        const globalPos = this._cardContainer.toLocal(card.parent.toGlobal(card.position));

        if (this._powerMode) {
            const parent = card.parent.parent;

            this.powerMoveFrom = globalPos.y;
            this.powerMoveTo = this._cardContainer.toLocal(card.getBottomCard().parent.toGlobal(card.getBottomCard().position)).y;
            card.setParent(this._cardContainer);
            card.position.set(globalPos.x, globalPos.y);

            const child = (card.getChildByName('bottomContainer') as Container).children[0] as Card;
            if (parent instanceof Card) {
                const pos = parent.getChildByName('bottomContainer').toLocal(child.parent.toGlobal(child.position));
                parent.setBottomChild(child, true);
                child.position.y = pos.y;
            } else {
                const pos = this._cardContainer.toLocal(child.parent.toGlobal(child.position));
                child.setParent(this._cardContainer);
                child.position.set(pos.x, pos.y);
            }

            this.powerPart = child;
            this._undoStacks.push({
                node: card,
                idx: this._getTableauStackIndex(globalPos.x),
                from: 'power',
                parent: parent instanceof Card ? parent : null,
                child,
            });
            return;
        }

        let stackIndex;
        let fromStack;

        if (globalPos.y < this._winColumns[0].y + this._winColumns[0].height / 2) {
            if (globalPos.x > this._winColumns[0].x - this._winColumns[0].width / 2) {
                stackIndex = this._getTableauStackIndex(globalPos.x);
                fromStack = 'win';
            } else {
                fromStack = 'drawedCards';
            }
        } else {
            stackIndex = this._getTableauStackIndex(globalPos.x);
            fromStack = 'tableau';
        }

        const parent = (card.parent.parent as Card) ?? null;
        // console.log('PUSH UNDO', card, stackIndex, fromStack, parent);
        this._undoStacks.push({
            node: card,
            idx: stackIndex,
            from: fromStack,
            parentStatus: fromStack === 'tableau' && parent ? parent.isFront : false,
        });
    }

    private powerPart: Card;
    private powerMoveFrom: number = 0;
    private powerMoveTo: number = 0;

    private _moveCallback(ev: InteractionEvent, newPos: IPointData) {
        const globalPos = this._cardContainer.toLocal(ev.currentTarget.parent.toGlobal(newPos));

        if (this._powerMode) {
            this._onMovePowerUp(ev, newPos);

            return;
        }

        if (Math.abs(globalPos.x) > Math.abs(AppConstants.BASE_SCREEN_WIDTH / 2) || Math.abs(globalPos.y) > Math.abs(AppConstants.BASE_SCREEN_HEIGHT / 2)) {
            //console.log('out of bound');
            return;
        }

        ev.currentTarget.position.x = newPos.x;
        ev.currentTarget.position.y = newPos.y;
    }

    private _endCallback(ev: InteractionEvent, onlyPowerMode: boolean = false) {
        const card = ev.currentTarget as Card;

        if (onlyPowerMode && !this._powerMode) return;

        if (this._powerMode) {
            this._onEndPowerUp(card, onlyPowerMode);
            return;
        }

        const globalPos = this._cardContainer.toLocal(ev.currentTarget.parent.toGlobal(ev.currentTarget.position));

        if (globalPos.y < this._winColumns[0].y + this._winColumns[0].height / 2) {
            if (globalPos.x > this._winColumns[0].x - this._winColumns[0].width / 2) {
                const stackIndex = this._getWinStackIndex(globalPos.x);
                //console.log('to Win Col', stackIndex);
                this._moveToWinColumn(card, stackIndex);
            } else {
                this._rejectLastUndo();
                card.reset();
            }
        } else {
            const stackIndex = this._getTableauStackIndex(globalPos.x);

            if (!this._tableauStacks[stackIndex]) {
                //console.log('to empty stack', stackIndex);
                this._moveToEmptyStack(card, stackIndex);
            } else if (card === this._tableauStacks[stackIndex]) {
                //console.log('to same stack', stackIndex);
                this._rejectLastUndo();
                card.reset();
            } else {
                //console.log('to tableau stack', stackIndex);
                this._moveToTableauColumn(card, stackIndex, this._isCheat);
                this._completeSuit(stackIndex);
            }
        }

        this._highlightCards();
    }

    private _drawCallback(node: Card) {
        if (this._powerMode) return;
        if (this._tableauStacks.some((card) => card === null)) {
            // if (this.isHighlightStacks) return;
            // this.isHighlightStacks = true;
            this._tableauStacks.forEach((cr, idx) => {
                if (cr === null) {
                    // TODO: check this
                    Utils._zoomEffect(this._tableauColumns[idx], 1.5);
                    // cc.tween(this.highlightStacks.children[idx])
                    //     .set({ active: true })
                    //     .to(0.5, { scale: 1.5, opacity: 0 })
                    //     .call(() => {
                    //         this.highlightStacks.children[idx].active = false;
                    //         this.highlightStacks.children[idx].scale = 1;
                    //         this.highlightStacks.children[idx].opacity = 255;
                    //         this.isHighlightStacks = false;
                    //     })
                    //     .start();
                }
            });
            // if (PopupManager.inst.isPopupShowing()) return;
            const content = Localization.getText('WARNING_DRAW_CARD');
            PopupManager.informationPopup.open(content);
            return;
        }

        const listDeal = [];
        PopupManager.blockInputEvent.open();
        for (let i = 0; i < 10; i++) {
            const toPosition = this._tableauColumns[i].position;
            const cardNode = this._cards.pop();
            listDeal.push(cardNode);
            cardNode.tweenTo({ x: toPosition.x, y: toPosition.y + this._tableauStacks[i].getBottomCount() * 13 }, CARD_MOVE_DURATION, 100 * i, () => {
                SoundManager.inst.playSfx('DEAL_CARD');
                const parent = this._tableauStacks[i];
                parent.setBottomChild(cardNode);
                cardNode.turnToFront();
                this._completeSuit(i);
                cardNode.status = ECardStatus.IN_PLAY;

                if (i === 9) {
                    PopupManager.blockInputEvent.close();
                    this._highlightCards();
                    if (this._isCardLifter) {
                        const disableCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY && !card.isLastBottomChild);
                        UIManager.inst.getSpinPanel().setCardLifterBtn(disableCards.length > 0);
                    }
                }
            });
        }
        this._undoStacks.push({ node, from: 'deck', listDeal });
        // const num = this.undoStacks.length > 0 ? 1 : 0;
        // this.btnUndo.spriteFrame = this.undoSprite[num];
        UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
    }

    private _moveToEmptyStack(card: Card, stackIndex: number) {
        card.setParent(this._cardContainer);
        this._tableauStacks[stackIndex] = card;

        const toPosition = this._tableauColumns[stackIndex].position;
        card.position.set(toPosition.x, toPosition.y);
        this._tableauStacks.forEach((stackNode, index) => {
            if (stackNode === card && index !== stackIndex) {
                this._tableauStacks[index] = null;
            }
        });

        this._openLastCardInStack();
    }

    private async _moveToWinColumn(card: Card, stackIndex: number) {
        this._winStacks[stackIndex] = card;

        const toPosition = this._winColumns[stackIndex].position;

        card.position.set(toPosition.x, toPosition.y);
        card.status = ECardStatus.IN_FINISH;
        card.interactive = false;
        this._tableauStacks.forEach((stackNode, index) => {
            if (stackNode === card) {
                //console.log('remove card from old stack', index);
                this._tableauStacks[index] = null;
            }
        });
    }

    private _moveToTableauColumn(card: Card, stackIndex: number, ignoreRule: boolean = false) {
        const stackCard = this._tableauStacks[stackIndex];
        if (stackCard) {
            if (stackCard.setBottomChild(card, ignoreRule)) {
                this._scoreGame--;
                Events.emit(UIEvents.UPDATE_SCORE_GAME, this._scoreGame);
                card.status = ECardStatus.IN_PLAY;
                card.setParentGroup(UIManager.inst.showingGroup);
                UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
                if (this._isCardLifter) {
                    const disableCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY && !card.isLastBottomChild);
                    UIManager.inst.getSpinPanel().setCardLifterBtn(disableCards.length > 0);
                }
            } else {
                // cc.warn("pop");
                this._rejectLastUndo();
            }
        }

        this._tableauStacks.forEach((stack, index) => {
            stack && stack.adjustColumnHeight();
            if (stack === card) {
                this._tableauStacks[index] = null;
            }
        });

        this._winStacks.forEach((stackNode, index) => {
            if (stackNode === card) {
                this._winStacks[index] = null;
            }
        });

        this._openLastCardInStack();
    }

    // TODO: check if the loop is necessary
    private _openLastCardInStack(excludeIdx?: number) {
        // console.log('open last card in stack');
        this._tableauStacks.forEach((card, idx) => {
            if (card && idx !== excludeIdx) {
                if (card) {
                    card.turnLastBottomChildToFront();
                    // TODO: auto-solve
                    // this.checkSolve();
                }
            }
        });
    }

    private _changeLevel(mode: DrawMode) {
        // TODO: change mode
    }

    private async _onClickStart(mode: DrawMode) {
        this._reset();
        await this._loadMatch();
    }

    private _dealCards() {
        this._cardContainer.removeChildren();
        Events.emit(UIEvents.UPDATE_HIGHSCORE, this._mode);
        this._tableauStacks = [];
        this._listCardWin = [];
        this._winStacks = [null, null, null, null, null, null, null, null];
        this._undoStacks = [];
        this._deck = []; // contains all cards for checking
        this._cards = [];
        this._drawedCards = [];
        this._timer = 0;
        this._listTimeStamp = [];
        this._scoreGame = GameConfigs.SCORE_START_GAME;
        this._isCardLifter = true;
        this._cardArray = this._getRandomArray();
        this._createCard(this._cardArray);
        UIManager.inst.getSpinPanel().setStatusUndoBtn(false);
        UIManager.inst.getSpinPanel().setCardLifterBtn(false);

        for (let i = 0; i < 44; i++) {
            const col = i % 10;
            const toPosition = this._tableauColumns[col].position;
            const cardNode = this._cards.pop();
            cardNode.status = ECardStatus.IN_PLAY;

            cardNode.parentGroup = UIManager.inst.showingGroup;
            cardNode.tweenTo({ x: toPosition.x, y: toPosition.y + Math.floor(i / 10) * 13 }, CARD_MOVE_DURATION, i * 50, () => {
                SoundManager.inst.playSfx('DEAL_CARD');
                if (i < 10) {
                    this._tableauStacks.push(cardNode);
                } else {
                    const parent = this._tableauStacks[col];
                    parent.setBottomChild(cardNode);
                }
            });
        }

        for (let i = 0; i < 10; i++) {
            const toPosition = this._tableauColumns[i].position;
            const cardNode = this._cards.pop();
            cardNode.status = ECardStatus.IN_PLAY;

            cardNode.parentGroup = UIManager.inst.showingGroup;
            cardNode.tweenTo({ x: toPosition.x, y: toPosition.y + Math.floor((44 + i) / 10) * 13 }, CARD_MOVE_DURATION, (44 + i) * 50, () => {
                SoundManager.inst.playSfx('DEAL_CARD');
                const parent = this._tableauStacks[i];
                parent.setBottomChild(cardNode);
                cardNode.turnToFront();
                //console.log(this._cards[this._cards.length - 1]);

                if (i === 9) {
                    this._highlightCards();
                    this._isStartCountTimer = true;
                    PopupManager.blockInputEvent.close();
                    Events.emit(UIEvents.START_COUNT_TIME, this._mode);
                }
            });
        }

        for (let i = 0; i < 5; i++) {
            this._cards.slice(-10 * (i + 1), -10 * i).forEach((card) => {
                card.status = ECardStatus.DRAWED_FROM_DECK;
                card.position.x = this._startColumn.x - 15 * i;
                // card.parentGroup = UIManager.inst.drawedGroup;
                // card.tweenTo({ x: this._startColumn.x - 15 * i }, CARD_MOVE_DURATION, 54 * 100 + i * 100);
            });
        }
    }

    private _rejectLastUndo() {
        // console.trace('POP UNDO', this._undoStacks[this._undoStacks.length - 1]);
        return this._undoStacks.pop();
    }

    removeCardFromOldStack(node: Card) {
        this._tableauStacks.forEach((stackNode, index) => {
            if (stackNode === node) {
                //console.log('remove card from old stack', index);
                this._tableauStacks[index] = null;
            }
        });

        this._winStacks.forEach((stackNode, index) => {
            if (stackNode === node) {
                this._winStacks[index] = null;
            }
        });
    }

    private _undo() {
        // SoundManager.inst.playEffect('CLICK_BTN');
        const record = this._rejectLastUndo();
        UIManager.inst.getSpinPanel().setStatusUndoBtn(this._undoStacks.length > 0);
        if (record) {
            switch (record.from) {
                case 'tableau':
                    const stackCard = this._tableauStacks[record.idx];
                    if (stackCard) {
                        {
                            this._moveToTableauColumn(record.node, record.idx, true);
                            const parent = record.node.parent.parent;
                            if (parent && !record.parentStatus) record.node.parent.parent.turnToBack();
                        }
                    } else {
                        this.removeCardFromOldStack(record.node);
                        this._moveToEmptyStack(record.node, record.idx);
                    }
                    break;
                case 'deck':
                    // this.blockTouch.active = this.gameMode === SuitMode.ONE;
                    const startPositionX = this._cards.length > 0 ? this._cards[this._cards.length - 1].position.x + 15 : this._startColumn.position.x - 15 * 4;

                    //cc.warn(record.listDeal);
                    while (record.listDeal.length > 0) {
                        const cardNo = record.listDeal.pop();
                        if (!cardNo) return;

                        cardNo.status = ECardStatus.IN_DECK;
                        cardNo.setParent(this._cardContainer);
                        cardNo.position.set(startPositionX, this._startColumn.position.y);
                        this._cards.push(cardNo);
                        cardNo.turnToBack();
                        this.removeCardFromOldStack(cardNo);
                    }

                    break;
                case 'reset':
                    record.completedSuit.reverse();
                    record.completedSuit.forEach((card, idx) => {
                        // card.getComponent(CardSpider).isInWin = false;
                        card.interactive = true;
                        card.turnToFront();
                        const stackCard = this._tableauStacks[record.idx];
                        if (stackCard) {
                            this._moveToTableauColumn(card, record.idx, true);

                            if (idx === 0 && card.parent.parent && !record.parentStatus) {
                                card.parent.parent.turnToBack();
                            }
                            this.removeCardFromOldStack(card);
                        } else {
                            this.removeCardFromOldStack(card);
                            this._moveToEmptyStack(card, record.idx);
                        }
                    });
                    this._undo();
                    break;
                default: // case power
                    record.node.setParent(this._cardContainer);
                    record.child.setParent(this._cardContainer);

                    if (record.parent) {
                        record.parent.setBottomChild(record.node, true);
                    } else {
                        record.node.position = record.child.position;
                        this._tableauStacks.forEach((stackNode, index) => {
                            if (stackNode === record.child) {
                                this._tableauStacks[index] = record.node;
                            }
                        });
                    }

                    record.node.setBottomChild(record.child, true);

                    this._highlightCards();
            }

            this._highlightCards();
        }
        if (this._isCardLifter) {
            const disableCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY && !card.isLastBottomChild);
            UIManager.inst.getSpinPanel().setCardLifterBtn(disableCards.length > 1);
        }
    }

    private _reset() {
    }

    private async _loadMatch() {
        this._dealCards();
    }

    public async onActive() {
        PopupManager.newGamePopup.open(true);
    }

    private async _loadDrawGame(mode: number) {
        PopupManager.blockInputEvent.open();
        switch (mode) {
            case SuitMode.ONE:
                this._mode = SuitMode.ONE;
                break;
            case SuitMode.TWO:
                this._mode = SuitMode.TWO;
                break;
            case SuitMode.FOUR:
                this._mode = SuitMode.FOUR;
                break;
            default:
                this._mode = SuitMode.ONE;
                break;
        }
        await this._loadMatch();
    }

    public onPause() {}

    public onResume() {}

    public update(dt: number) {
        if (PopupManager.isShowingInformationPopup()) {
            if (this._tableauStacks.findIndex((cr) => cr === null) < 0) {
                PopupManager.informationPopup.close();
            }
        }
        if (!this._isStartCountTimer) return;
        this._timer += dt;
    }


    private _getRandomArray(): string[] {
        const pattern = [];
        let newDeck = [];
        const suit = ['spades', 'hearts', 'clubs', 'diamonds'];

        for (let i = 1; i <= 13; i++) {
            pattern[i] = i.toString().padStart(2, '0');
        }

        for (let i = 0; i < this._mode; i++) {
            pattern.forEach(function (item) {
                newDeck.push(suit[i] + '_' + item);
            });
        }

        while (104 / newDeck.length > 1) {
            newDeck = newDeck.concat(newDeck);
        }

        // shuffle
        let k = newDeck.length;
        let j, t;
        while (k) {
            j = Math.floor(k-- * Math.random());
            t = newDeck[k];
            newDeck[k] = newDeck[j];
            newDeck[j] = t;
        }

        //console.log(newDeck);
        return newDeck;
    }

    private _getWinStackIndex(worldX: number): number {
        let result = 0;
        let distance = 0;
        let minDistance = 0;
        let stackWorldX = 0;
        for (let i = 0; i < 4; i++) {
            stackWorldX = this._winColumns[i].position.x;
            distance = Math.abs(stackWorldX - worldX);
            if (i === 0 || minDistance > distance) {
                minDistance = distance;
                result = i;
            }
        }
        return result;
    }

    private _getTableauStackIndex(worldX: number): number {
        let result = 0;
        let distance = 0;
        let minDistance = 0;
        let stackWorldX = 0;
        for (let i = 0; i < COLUMNS; i++) {
            stackWorldX = this._tableauColumns[i].x;
            distance = Math.abs(stackWorldX - worldX);
            if (i === 0 || minDistance > distance) {
                minDistance = distance;
                result = i;
            }
        }
        return result;
    }

    //====================================================================================================
    //=============================================== HINT ================================================

    private isShowHint: boolean = false;
    private noMovePredicts: boolean = false;

    private _findPossibleMove(card: Card): {
        to: string;
        parent?: Card;
        target: Card;
        dest?: IPointData;
        idx: number;
        priority: number;
        stackHeight?: number;
        callback?: () => void;
    }[] {
        const moves = [];
        for (let i = 0; i < this._tableauStacks.length; i++) {
            if (!this._tableauStacks[i]) {
                if (card.getParentCount(true) > 0 || (card.interactive && card.isFront)) {
                    const priority = 1;
                    moves.push({ to: 'empty', idx: i, priority, target: card, dest: this._tableauColumns[i].position, callback: this._moveToEmptyStack.bind(this, card, i) });
                }
            } else {
                const tableauCard = this._tableauStacks[i];
                if (tableauCard && tableauCard.isBottomChild(card)) {
                    if (card.interactive && card.isFront && !card.isDisable && card.status !== ECardStatus.IN_FINISH) {
                        const priority = tableauCard.getBottomCard().suit === card.suit ? 2 : 0;
                        const toPosition = this._cardContainer.toLocal(tableauCard.getBottomCard().toGlobal(tableauCard.getBottomCard().getChildByName('bottomContainer').position));

                        moves.push({
                            to: 'card',
                            parent: tableauCard.getBottomCard(),
                            target: card,
                            idx: i,
                            priority,
                            stackHeight: tableauCard.getBottomCard().countEnableHeight(),
                            dest: { x: toPosition.x, y: toPosition.y },
                            callback: this._moveToTableauColumn.bind(this, card, i),
                        });
                    }
                }
            }
        }

        return moves;
    }

    highlightNode(from: Card, dest: Card) {
        // console.log('highlight node', from, dest);

        from.shake();
        dest?.shake();

        this.isShowHint = false;

        // else if (dest.getChildByName('boder_highlight')) {
        //     cc.tween(dest.getChildByName('boder_highlight'))
        //         .set({ active: true })
        //         .blink(1, 2)
        //         .call(() => {
        //             dest.getChildByName('boder_highlight').active = false;
        //             this.isShowHint = false;
        //         })
        //         .start();
        // }
    }

    showHint(playAnim: boolean = true) {
        // SoundManager.inst.playEffect('CLICK_BTN');
        if (this.isShowHint) return;
        // console.log('show hint');
        this.isShowHint = playAnim;
        if (this.noMovePredicts) {
            //console.log('GAME OVER');
            // this.isStartGame = false;
            // PopupManager.inst.openNotification();
            return;
        }

        const remainingCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY && !card.isDisable);

        //console.log(remainingCards);

        const possibleMoves = [];
        for (const card of remainingCards) {
            if (!card) continue;
            const result = this._findPossibleMove(card);
            if (result.length > 0) possibleMoves.push(...result);
        }

        //console.log(possibleMoves);
        if (possibleMoves.length > 0) {
            possibleMoves
                .sort(function () {
                    return 0.5 - Math.random();
                })
                .sort((a, b) => b.priority - a.priority);

            //cc.log(possibleMoves);

            const move = possibleMoves[0];

            //cc.log("hint", move);

            if (move.to === 'card') {
                if (playAnim) {
                    this.highlightNode(move.target, move.parent);
                }
            } else {
                if (playAnim) {
                    this.highlightNode(move.target, this._tableauStacks[move.idx]);
                }
            }

            return true;
        }

        const firstCardInDeck = this._cards[this._cards.length - 1];
        if (playAnim && firstCardInDeck) {
            //console.log('no move', firstCardInDeck.point);
            firstCardInDeck.shake();
            this.isShowHint = false;
        } else {
            this.isShowHint = false;
        }

        return false;
    }

    _doubleClickCallback(card: Card) {
        // console.log('double');
        // this.blockTouch.active = true;
        // this.lockTimerClickCard = DOUBLE_CLICK_TIME;

        const possibleMoves = this._findPossibleMove(card);
        if (possibleMoves.length > 0) {
            possibleMoves
                .sort(function () {
                    return 0.5 - Math.random();
                })
                .sort((a, b) => b.priority - a.priority)
                .sort((a, b) => b.stackHeight - a.stackHeight);
        }
        //console.log(possibleMoves);

        const possibleMove = possibleMoves[0];
        if (possibleMove) {
            possibleMove.target.tweenTo(possibleMove.target.parent.toLocal(this._cardContainer.toGlobal(possibleMove.dest)), CARD_MOVE_DURATION, 0, () => {
                possibleMove.callback && possibleMove.callback();
                this._highlightCards();
                this._completeSuit(possibleMove.idx);
                // this.blockTouch.active = false;
            });
        } else {
            card.shake();
            this._rejectLastUndo();
            // cc.warn('pop', this.undoStacks);
            // this.blockTouch.active = false;
        }
    }

    private _gameRule(point: number, suit: number, childPoint: number, childSuit: number): boolean {
        return childPoint == point - 1;
    }

    // check if the column is solved
    private _completeSuit(stackIdx: number) {
        //console.log('complete suit', stackIdx);
        if (!this._tableauStacks[stackIdx]) return false;
        const lastCard = this._tableauStacks[stackIdx].getBottomCard();

        // if (lastCard.point !== 1) return false;

        let parent = null;
        let currCard = lastCard;
        const completedSuit = [];
        completedSuit.push(currCard);
        for (let i = 0; i < 12; i++) {
            parent = currCard.parent.parent;
            if (!parent || parent.suit !== currCard.suit || parent.point !== currCard.point + 1 || !parent.isFront) {
                return false;
            }
            // if (!parent || !parent.suit) {
            // break;
            // }
            currCard = parent;
            completedSuit.push(currCard);
        }

        // console.log(completedSuit);

        this._undoStacks.push({
            from: 'reset',
            parentStatus: currCard.isFront,
            idx: stackIdx,
            completedSuit,
        });

        const winSlotIdx = Math.max(
            this._winStacks.findIndex((child) => child === null),
            0,
        );
        //console.warn(winSlotIdx);
        this._scoreGame += 100;
        Events.emit(UIEvents.UPDATE_SCORE_GAME, this._scoreGame);
        PopupManager.blockInputEvent.open();
        completedSuit.forEach((card, idx) => {
            // card.isInWin = true;
            const globalPosition = this._cardContainer.toLocal(card.parent.toGlobal(card.position));
            card.setParent(this._cardContainer);
            card.position = globalPosition;
            card.tweenTo({ x: this._winColumns[winSlotIdx].position.x, y: this._winColumns[winSlotIdx].position.y }, CARD_MOVE_DURATION, 100 * idx, () => {
                SoundManager.inst.playSfx('MOVE_WIN');
                this._moveToWinColumn(card, winSlotIdx);
                if (idx === completedSuit.length - 1) {
                    this._openLastCardInStack();
                    this._highlightCards();
                    this._tableauStacks.forEach((stackNode, index) => {
                        stackNode && stackNode.adjustColumnHeight();
                    });
                    PopupManager.blockInputEvent.close();
                    // win
                    if (winSlotIdx === 7) {
                        const listHighScore = Utils.getScoreList(this._mode);
                        UIManager.inst.getSpinPanel().setGameWin();
                        this._isStartCountTimer = false;
                        let item = [];
                        if (listHighScore && listHighScore.length > 0) {
                            item = listHighScore.find((item) => this._scoreGame > item.score);
                        }
                        if (!listHighScore || item || listHighScore.length < 10) {
                            PopupManager.inputNamePopup.open(this._scoreGame, this._timer, this._mode, Utils.getTimer());
                        } else {
                            PopupManager.winPopup.open(true, this._scoreGame, this._timer);
                        }
                        SoundManager.inst.playSfx('WIN');
                    }
                }
            });
        });
        // this.listTimeStamp.push(parseFloat(this.timer.toFixed(2)));
        // this.boardScore.getComponent(BoardScore).setScore(this.score, KEY_HIGH_SCORE_GAME);
        return true;
    }

    private _highlightCards() {
        this._tableauStacks.forEach((stack) => {
            if (stack) {
                const lastCard = stack.getBottomCard();

                let parent = null;
                let currCard = lastCard;
                let active = true;
                currCard.setDim(false);

                for (let i = 0; i < stack.getBottomCount(); i++) {
                    parent = currCard.parent.parent;
                    if (!parent.isFront) {
                        currCard = parent;
                        currCard.setDim(false);
                    } else {
                        if (active && (!parent || parent.suit !== currCard.suit || parent.point !== currCard.point + 1)) {
                            active = false;
                        }
                        currCard = parent;
                        active ? currCard.setDim(false) : currCard.setDim(true);
                    }
                }
            }
        });
    }

    //====================================================================================================
    //=========================================== POWER UP ===============================================

    private _togglePowerMode(on: boolean) {
        //console.log('toggle power mode', on);
        if (typeof on === 'undefined') {
            on = !this._powerMode;
        }
        this._powerMode = on;
        if (this._powerMode) {
            UIManager.inst.getSpinPanel().blockButton(true);
            // TODO: setDim to Enabled cards,
            const disableCards = this._deck.filter((card) => card.isFront && card.status === ECardStatus.IN_PLAY);
            disableCards.forEach((card) => {
                card.setDim(false);
            });

            this._tableauStacks.forEach((card) => {
                if (!card) return;
                card.getBottomCard().setDim(true);
            });
        } else {
            // TODO: reset state, start countdown
            UIManager.inst.getSpinPanel().blockButton(false);
            this._highlightCards();
            this._isCardLifter = false;
            UIManager.inst.getSpinPanel().setStatusCardLifterBtn(true);
        }
    }

    private _onMovePowerUp(ev: InteractionEvent, newPos: IPointData) {
        const globalPos = this._cardContainer.toLocal(ev.currentTarget.parent.toGlobal(newPos));
        if (globalPos.y > this.powerMoveTo || globalPos.y < this.powerMoveFrom) {
            return;
        }

        const deltaY = newPos.y - ev.currentTarget.position.y;
        ev.currentTarget.position.y = newPos.y;
        this.powerPart.position.y -= deltaY / this.powerPart.getBottomCount(1);
    }

    private _onEndPowerUp(card: Card, isCancel: boolean = false) {
        const parent = this.powerPart.parent.parent as Card;
        card.doubleClickCallback = this._doubleClickCallback.bind(this);
        card.clearDoubleClickCounter();

        const globalPos = this._cardContainer.toLocal(card.parent.toGlobal(card.position));
        if ((globalPos.y / this.powerMoveTo) * 100 > 75) {
            if (parent instanceof Card) {
                this.powerPart.position.set(0, 0);
            } else {
                const stackIdx = this._tableauStacks.findIndex((stack) => stack === card);
                this._tableauStacks[stackIdx] = this.powerPart;
            }
            this.powerPart.setBottomChild(card, true);
            this.powerPart.setParentGroup(UIManager.inst.showingGroup);
        } else {
            // reset
            this.powerPart.setParent(this._cardContainer);
            if (parent instanceof Card) {
                parent.setBottomChild(card, true);
                card.position.y = 0;
            } else {
                card.position.y = this._tableauColumns[this._getTableauStackIndex(globalPos.x)].position.y;
            }
            card.setBottomChild(this.powerPart, true);
            this._rejectLastUndo();
            card.setParentGroup(UIManager.inst.showingGroup);
        }

        if (isCancel) {
            return;
        }
        UIManager.inst.getSpinPanel().setStatusCardLifterBtn(true);
        this._isCardLifter = false;
        this._togglePowerMode(false);
    }
}
