import ResourceManager from '../ResManagers/ResourceManager';
import { Shake } from '../Utils/Shake';
import { Tween } from '../core/utility/tween/Tween';
import { Container, Graphics, Group, IPoint, IPointData, ISize, InteractionEvent, Point, Rectangle, Sprite, Text } from '../pixi';
import UIManager from '../UI/UIManager';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import { SoundManager } from '../SoundManager';

export enum ECardStatus {
    IN_DECK = 0,
    DRAWED_FROM_DECK,
    IN_PLAY,
    IN_FINISH,
}

export type OpenCallback = (card: Card) => void;
export type MoveCallback = (ev: InteractionEvent, newPos: IPointData) => void;
export type EndCallback = (ev: InteractionEvent, powerMode?: boolean) => void;
export type CardHandleCallback = (card: Card) => void;
export type GameRule = (point: number, suit: number, childPoint: number, childSuit: number) => boolean;

const SUITS = ['diamonds', 'clubs', 'hearts', 'spades'];
const JQKNAMES = ['jack', 'queen', 'king'];

export class Card extends Container {
    private readonly _DIM_COLOR = 0x777777; // 0xa5a5a5 - 0x757575 - 0x363636
    private readonly _UN_DIM_COLOR = 0xffffff;

    private readonly _SHAKE_CONFIG = {
        duration: 10000,
        frequency: 40,
        amplitude: { x: 8, y: 4 },
    };

    private _suit: number;
    private _point: number;
    private _isFront: boolean;
    private _isUnder: boolean = false; // for double click check
    private _winFlag: boolean = false; // card is moved to win column
    private _hasMoved: boolean = false;

    private _centerContainer: Container;
    private _bottomContainer: Container;
    private _rightContainer: Container;

    private _isHighlight: boolean;
    private _targetTint = this._UN_DIM_COLOR;
    private _status: ECardStatus = ECardStatus.IN_DECK;
    private _sprite: Sprite;
    private _realSize: ISize;
    // private _hitRect: Rectangle;
    private _openCallback: OpenCallback;
    private _onMoveCallback: MoveCallback;
    private _endCallback: EndCallback;
    private _drawCallback: CardHandleCallback;
    private _doubleClickCallback: CardHandleCallback;
    private _gameRule: GameRule;

    private _rejectUndoCallback: Function;
    private _orgiginBeforeAnim: IPoint;
    private _originPos: IPoint;
    private _originGroup: Group;
    private _tempPos: IPoint;
    private _shakeTimer: { r: number };
    private _isPickUp: boolean;
    private _isHover: boolean;
    private _debugGraph: Graphics;
    private _scheduler: any;

    private _lastClickTime: number = 0;
    private _currentClickTime: number = 0;
    private _doubleClick: boolean = false;

    // Spider
    private _isDisable: boolean = false;

    constructor() {
        super();
        this._sprite = new Sprite();
        this._sprite.name = 'sprite';
        this._sprite.anchor.set(0.5);
        this.addChild(this._sprite);

        this._centerContainer = new Container();
        this._centerContainer.name = 'centerContainer';
        this.addChild(this._centerContainer);

        this._bottomContainer = new Container();
        this._bottomContainer.y = 25;
        this._bottomContainer.name = 'bottomContainer';
        this.addChild(this._bottomContainer);

        this._rightContainer = new Container();
        this._rightContainer.x = 15;
        this._rightContainer.name = 'rightContainer';
        this.addChild(this._rightContainer);

        this.setInteractive(true);

        this.on('pointerdown', this._onPointerDown.bind(this));

        this._debugGraph = new Graphics();
        this.addChild(this._debugGraph);
    }

    public init(data: number): void;
    public init(data: string): void;
    public init(data: number | string) {
        this._realSize = {
            width: 100,
            height: 140,
        };

        this._status = ECardStatus.IN_DECK;
        this._isPickUp = false;

        if (typeof data === 'number') {
            this._suit = Math.floor((data - 1) / 13);
            this._point = (data - this._suit * 13) % 13;
        } else {
            this._suit = SUITS.indexOf(data.split('_')[0]);
            this._point = parseInt(data.split('_')[1], 10);
        }

        if (this._point === 0) this._point = 13;

        this._loadImage();
        this.turnToBack();
    }

    private _loadImage() {
        // console.log('Card init', this._suit, this._point);

        const suit = SUITS[this._suit];
        let number = '';
        if (this._point > 10) {
            number = JQKNAMES[this._point - 10 - 1];
        } else {
            number = this._point == 1 ? 'ace' : this._point.toString().padStart(2, '0');
        }

        this.name = `${suit}_${number}`;

        // console.log('Card init', suit, number);
        this._sprite.texture = ResourceManager.getAtlasFrame('cards_atlas', `${suit}_${number}`);
    }

    public setParentGroup(group: Group) {
        this.parentGroup = group;
        if (this._bottomContainer.children.length > 0) {
            if (this._bottomContainer.children[0] instanceof Card) (this._bottomContainer.children[0] as Card).setParentGroup(group);
        }
    }

    public getParentCount(includedNotFront: boolean = false, accumulator: number = 0) {
        const card = this.parent.parent as Card;

        if (card && card._isFront === !includedNotFront) {
            accumulator++;
            return card.getParentCount(includedNotFront, accumulator);
        }

        return accumulator;
    }

    public getBottomCount(accumulator: number = 0) {
        const card = this._bottomContainer.children[0] as Card;
        if (card) {
            accumulator++;
            return card.getBottomCount(accumulator);
        }

        return accumulator;
    }

    setHeight(height) {
        if (this._isFront) this._bottomContainer.y = height;
        if (this._bottomContainer.children.length > 0) {
            return (this._bottomContainer.children[0] as Card).setHeight(height);
        }
    }

    public adjustColumnHeight() {
        const bottomCount = this.getBottomCount();
        if (bottomCount > 22) {
            this.setHeight(14);
        } else if (bottomCount > 20) {
            this.setHeight(16);
        } else if (bottomCount > 18) {
            this.setHeight(19);
        } else if (bottomCount > 16) {
            this.setHeight(20);
        } else if (bottomCount > 14) {
            this.setHeight(22);
        } else if (bottomCount > 12) {
            this.setHeight(23);
        } else if (bottomCount > 10) {
            this.setHeight(24);
        } else {
            this.setHeight(25);
        }
    }

    private _onPointerDown(ev: InteractionEvent) {
        ev.stopPropagation();

        if (this._isUnder) return;
        // TODO: Verify whether 'isDisabled' can be used as a replacement.
        if (this.isDisable) {
            return;
        }

        // console.log('Card down', this._point, this._suit);
        // console.log(this._currentClickTime, Date.now() - this._currentClickTime);

        if (!this._currentClickTime) {
            this._currentClickTime = Date.now();
        } else {
            this._doubleClick = Date.now() - this._currentClickTime < 600;
            this._currentClickTime = Date.now();
            //console.log('doubleClick', this._doubleClick);
        }

        if (!this._canTouch) {
            this._originGroup = this.parentGroup;
            this._onPointerEnd(ev);
            return;
        }

        // console.log('Card down', (ev.currentTarget as Card)._point);

        this._originPos = this.position.clone();
        this._originGroup = this.parentGroup;
        this.setParentGroup(UIManager.inst.movingGroup);

        this._tempPos = ev.data.getLocalPosition(this.parent);
        this.on('pointermove', this._onPointerMove, this);
        this.on('pointerup', this._onPointerEnd.bind(this));
        this.on('pointerupoutside', this._onPointerEnd.bind(this));

        if (this._openCallback) {
            this._openCallback(this);
        }
    }

    private _onPointerMove(ev: InteractionEvent) {
        //console.log('Card move', this.parentGroup.zIndex);
        ev.stopPropagation();
        this.clearDoubleClickCounter();
        if (!this._hasMoved) {
            SoundManager.inst.playSfx('CARD');
        }
        this._hasMoved = true;

        const touchPos = ev.data.getLocalPosition(this.parent);
        const newPos = { x: this.position.x + touchPos.x - this._tempPos.x, y: this.position.y + touchPos.y - this._tempPos.y };

        this._onMoveCallback(ev, newPos);

        this._tempPos = ev.data.getLocalPosition(this.parent);
    }

    private _onPointerEnd(ev) {
        // console.log('Card end');

        if (this._isHover) {
            this._isHover = false;
        }

        ev.stopPropagation();
        this.off('pointerup');
        this.off('pointerupoutside');
        this.off('pointermove', this._onPointerMove, this);

        this.setParentGroup(this._originGroup);

        if (!this._canTouch) {
            // Draw card
            if (this._drawCallback && this._bottomContainer.children.length <= 0 && this._rightContainer.children.length <= 0 && !this._isUnder) {
                // console.log('Draw card', this.point, this.suit);
                this._drawCallback(this);
            }
            return;
        }

        if (this._doubleClick && this._doubleClickCallback) {
            this._doubleClick = false;
            this._currentClickTime = null;
            this._doubleClickCallback(this);
            return;
        }

        // console.log(this._hasMoved);
        if (this._hasMoved) {
            if (SoundManager.inst.isPlaying('CARD')) {
                SoundManager.inst.fade('CARD', 'AUTO', 0, 0.1, true);
            }
            SoundManager.inst.playSfx('CARD_FLIP');
            this._endCallback(ev);
            this._hasMoved = false;
            return;
        } else {
            this._endCallback(ev, true);
        }
        SoundManager.inst.playSfx('CARD_CLICK');
        this.setParentGroup(this._originGroup);
        this._rejectUndoCallback && this._rejectUndoCallback();
    }

    public setBottomChild(child: Card, ignoreRule: boolean = true) {
        if (this._bottomContainer.children.length > 0) {
            return (this._bottomContainer.children[0] as Card).setBottomChild(child, ignoreRule);
        } else if (child === this) {
            this.reset();
            return false;
        } else {
            if (ignoreRule) {
                this._bottomContainer.addChild(child);
                child.position.set(0, 0);
                this.adjustColumnHeight();
                return true;
            } else {
                if (this._gameRule(this.point, this.suit, child.point, child.suit)) {
                    this._bottomContainer.addChild(child);
                    child.position.set(0, 0);
                    this.adjustColumnHeight();
                    return true;
                } else {
                    child.reset();
                    return false;
                }
            }
        }
    }

    public setCenterChild(child: Card, ignoreRule: boolean = true) {
        if (this._centerContainer.children.length > 0) {
            return (this._centerContainer.children[0] as Card).setCenterChild(child, ignoreRule);
        } else if (child === this) {
            this.reset();
            return false;
        } else {
            const childCard = child;
            if (ignoreRule || (childCard.point == this.point + 1 && childCard.suit == this.suit)) {
                child.setParent(this._centerContainer);
                child.position.set(0, 0);
                return true;
            } else {
                childCard.reset();
                return false;
            }
        }
    }

    public turnToFront() {
        if (this._isFront) {
            return;
        }

        this._bottomContainer.y = 25;
        this._isFront = true;
        this._loadImage();
    }

    public turnToBack() {
        this._bottomContainer.y = 8;
        this._isFront = false;
        this._sprite.texture = ResourceManager.getAtlasFrame('cards_atlas', 'spider_red');
    }

    public tweenTo(props, duration: number = 100, delay: number = 0, finishCallback: Function = null) {
        const orgInteractive = this.interactive;
        const orgParentGroup = this.parentGroup;

        if (this.scheduler) {
            this.stopWaitAnim();
            Scheduler.clearTimeout(this.scheduler);
            this.scheduler = null;
        }

        if (orgParentGroup.zIndex < UIManager.inst.movingGroup.zIndex) {
            this.parentGroup = UIManager.inst.movingGroup;
        }

        // this.setInteractive(!orgInteractive);
        Tween.to(duration, this.position, props, delay, () => {
            // this.setInteractive(orgInteractive);
            this.parentGroup = orgParentGroup;
            finishCallback && finishCallback();
        });
    }

    public playWaitAnim(isTurbo: boolean) {
        const animSpeed = isTurbo ? 1.5 : 1;
        const xShake = new Shake(this._SHAKE_CONFIG.duration, this._SHAKE_CONFIG.frequency * animSpeed);
        const yShake = new Shake(this._SHAKE_CONFIG.duration, this._SHAKE_CONFIG.frequency * animSpeed);
        this._orgiginBeforeAnim = this.position.clone();
        this._originGroup = this.parentGroup;
        this._shakeTimer = { r: 0 };
        Tween.to(this._SHAKE_CONFIG.duration, this._shakeTimer, { r: this._SHAKE_CONFIG.duration }, 0, this.playWaitAnim.bind(this), () => {
            const _x = xShake.getAmplitude(this._shakeTimer.r) * this._SHAKE_CONFIG.amplitude.x;
            const _y = yShake.getAmplitude(this._shakeTimer.r) * this._SHAKE_CONFIG.amplitude.y;
            this.x = _x + this._orgiginBeforeAnim.x;
            this.y = _y + this._orgiginBeforeAnim.y;
        });
    }

    public stopWaitAnim() {
        // console.log('stopWaitAnim');
        if (!this._shakeTimer) return;
        Tween.stopTweenFor(this._shakeTimer);
        this.position.copyFrom(this._orgiginBeforeAnim);
        this._shakeTimer = null;
        this.parentGroup = this._originGroup;
        this._orgiginBeforeAnim = null;
        // console.log('stopWaitAnim');
    }

    public setInteractive(enable: boolean) {
        this.interactive = enable;
    }

    public showStaticFrame() {
        this._sprite.visible = true;
    }

    public reset() {
        // this._isHighlight = false;
        // this._isPickUp = false;
        // this._sprite.visible = true;
        // this.setDim(true, true);
        // this.setInteractive(true);
        // this.alpha = 1;
        this._currentClickTime = null;
        this.setInteractive(false);
        //console.log('RESET', this._point, this._suit);
        this.tweenTo({ x: this._originPos.x, y: this._originPos.y }, 100, 0, () => {
            this.setInteractive(true);
            // this.setParentGroup(this._originGroup);
        });
    }

    // Note: even if "isForceDim = true" but during respin, still skip dim
    // and checking "_targetTint" is not correct for case ending spin with combination, so adding "isForceDimHighestRule" for this case (rush time, will refactor later).
    public setDim(isDim: boolean = true, isForceDim: boolean = false, isForceDimHighestRule: boolean = false) {
        this._isDisable = isDim;

        //no need to do dim symbol during respin
        if (isDim && this._targetTint === this._DIM_COLOR && !isForceDimHighestRule) {
            return;
        }

        const nextTint = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        const currentTint = this._sprite.tint; //this._skeleton.tint;
        if (!isForceDim && (currentTint === nextTint || this._isHighlight)) {
            return;
        }
        if (!isForceDim) {
            const dim = { color: 0xff & currentTint };
            const color = 0xff & nextTint;
            Tween.to(100, dim, { color: color }, 0, null, () => {
                const dimColor = ((dim.color << 16) & 0xff0000) | ((dim.color << 8) & 0x00ff00) | (dim.color & 0x0000ff);

                this._sprite.tint = dimColor; //some symbol does not have skeleton active so we also need to dim sprite
            });
        } else {
            this._sprite.tint = nextTint;
        }

        /* if (isDim) {
            this._storeBlendCache();
        } else {
            this._restoreBlendCache();
        } */
    }

    public setTint(color: number) {
        if (this._sprite) {
            this._sprite.tint = color;
        }
    }

    public turnLastBottomChildToFront() {
        this._lastBottomChild.turnToFront();
    }

    private get _lastBottomChild(): Card {
        if (this._bottomContainer.children.length > 0) {
            return (this._bottomContainer.children[0] as Card)._lastBottomChild;
        } else {
            return this;
        }
    }

    private get _canTouch(): Boolean {
        // return false if is not front or is shown in draw 3
        if (!this._isFront || this._rightContainer.children.length > 0 || this._isUnder) return false;
        return true;
    }

    public get isHighlight(): boolean {
        return this._isHighlight;
    }
    public set isHighlight(value: boolean) {
        this._isHighlight = value;
    }

    public set openCallback(value: OpenCallback) {
        this._openCallback = value;
    }

    public set moveCallback(value: MoveCallback) {
        this._onMoveCallback = value;
    }

    public set endCallback(value: EndCallback) {
        this._endCallback = value;
    }

    public set drawCallback(value: CardHandleCallback) {
        this._drawCallback = value;
    }

    public set rejectUndoCallback(value: Function) {
        this._rejectUndoCallback = value;
    }

    public get status(): ECardStatus {
        return this._status;
    }

    public set status(value: ECardStatus) {
        this._status = value;
    }

    public set isPickUp(value: boolean) {
        this._isPickUp = value;
    }

    public get isPickUp(): boolean {
        return this._isPickUp;
    }

    public get point(): number {
        return this._point;
    }

    public get suit(): number {
        return this._suit;
    }

    public get isUnder(): boolean {
        return this._isUnder;
    }

    public set isUnder(value: boolean) {
        this._isUnder = value;
    }

    public get isFront(): boolean {
        return this._isFront;
    }

    public get winFlag(): boolean {
        return this._winFlag;
    }

    public set winFlag(value: boolean) {
        this._winFlag = value;
    }

    public getBottomCard(): Card {
        if (this._bottomContainer.children.length > 0) {
            return (this._bottomContainer.children[0] as Card).getBottomCard();
        } else {
            return this;
        }
    }

    public getRightCard(): Card {
        if (this._rightContainer.children.length > 0) {
            return (this._rightContainer.children[0] as Card).getRightCard();
        } else {
            return this;
        }
    }

    public get isLastBottomChild(): boolean {
        return this._bottomContainer.children.length <= 0;
    }

    public isCenterChild(card: Card): boolean {
        if (this._centerContainer.children.length > 0) {
            return (this._centerContainer.children[0] as Card).isCenterChild(card);
        } else if (card === this) {
            return false;
        } else {
            if (card.point == this.point + 1 && card.suit == this.suit) {
                return true;
            } else {
                return false;
            }
        }
    }

    public isBottomChild(card: Card): boolean {
        if (this._bottomContainer.children.length > 0) {
            return (this._bottomContainer.children[0] as Card).isBottomChild(card);
        } else if (card === this) {
            return false;
        } else {
            if (this._gameRule(this.point, this.suit, card.point, card.suit)) {
                return true;
            } else {
                return false;
            }
        }
    }

    public setRightChild(child: Card) {
        if (this._rightContainer.children.length > 0) {
            return (this._rightContainer.children[0] as Card).setRightChild(child);
        }
        child.setParent(this._rightContainer);
        child.position.set(0, 0);
        return true;
    }

    public get scheduler(): any {
        return this._scheduler;
    }

    public set scheduler(value: any) {
        this._scheduler = value;
    }

    public shake() {
        if (this.scheduler) {
            return;
        }

        this.setInteractive(false);
        this.playWaitAnim(true);

        this.scheduler = Scheduler.setTimeout(200, () => {
            this.stopWaitAnim();
            this.setInteractive(true);
            this.scheduler = null;
        });
    }

    public set doubleClickCallback(value: CardHandleCallback) {
        this._doubleClickCallback = value;
    }

    public set gameRule(value: GameRule) {
        this._gameRule = value;
    }

    // Spider

    public get isDisable(): boolean {
        return this._isDisable;
    }

    public countEnableHeight(includedNotFront: boolean = false, accumulator: number = 0) {
        const card = this.parent.parent as Card;

        if (card && card.isFront === !includedNotFront && card.suit == this.suit && !card.isDisable) {
            accumulator++;
            return card.getParentCount(includedNotFront, accumulator);
        }

        return accumulator;
    }

    private checkSequences() {
        if (this._bottomContainer.children.length > 0) {
            const child = this._bottomContainer.children[0] as Card;

            if (this.suit !== child.suit || this.point - 1 !== child.point) {
                return false;
            }
            return child.checkSequences();
        }

        return true;
    }

    public clearDoubleClickCounter() {
        this._doubleClick = false;
        this._currentClickTime = null;
    }
}
