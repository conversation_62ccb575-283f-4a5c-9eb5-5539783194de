import { Container } from '../pixi';
export default abstract class AutoUpdateContainer extends Container {
    public static activatedContainers: AutoUpdateContainer[] = [];

    constructor() {
        super();
        AutoUpdateContainer.activatedContainers.push(this);
    }

    destroy() {
        AutoUpdateContainer.activatedContainers.forEach((el, idx, arr) => {
            if (this === el) {
                arr.splice(idx, 1);
            }
        });
        super.destroy();
    }

    /**
     * All subclasses should call super.update(dt) in update function
     */
    public update(dt: number): void {}
}
