export type ISize = { width: number, height: number };

export class HtmlResizeListener {
    private _sensor: ResizeObserver;
    private _target: HTMLElement;
    private _callback: (size: ISize) => void;
    private _size: ISize;

    constructor(cb: (size: ISize) => void, target?: HTMLElement) {
        this._callback = cb;
        this._target = target;

        if (target) {
            this._buildSensor();
        }
        this._onResize();
    }

    private _buildSensor() {
        let taskId: NodeJS.Timeout = null;
        this._sensor = new ResizeObserver((entries) => {
            clearTimeout(taskId);
            taskId = setTimeout(() => {
                const viewRatio = this._calculateRatio(this._target);
                this._target.style.height = `${this._target.clientWidth / viewRatio}px`;
                const dimensions = this._target.getClientRects()[0];
                this._size = dimensions;
                this._onResize();
                taskId = null;
            }, 5);
        });

        // get current size
        const style = getComputedStyle(this._target);
        this._size = {
            width: Number.parseFloat(style.width),
            height: Number.parseFloat(style.height)
        };
    }

    private _onResize = () => {
        if (!this._target) {
            this._size = this._getFrameSize();
        }
        this._callback?.(this._size);
    };

    private _getFrameSize(): ISize {
        const clientWidth = document.documentElement.clientWidth ?? document.body.clientWidth;
        const clientHeight = document.documentElement.clientHeight ?? document.body.clientHeight;
        const width = clientWidth ?? window.innerWidth;
        const height = clientHeight ?? window.innerHeight;
        return { width, height };
    }

    private _calculateRatio(node: HTMLElement): number {
        const style = getComputedStyle(node);
        const asRatioCfg = style.aspectRatio;
        let ratio = Number.parseFloat(asRatioCfg);
        if (asRatioCfg.includes('*')) {
            const [a, b] = asRatioCfg.split('*').map((x) => Number.parseFloat(x.trim()));
            ratio = a * b;
        } else if (asRatioCfg.includes('/')) {
            const [a, b] = asRatioCfg.split('/').map((x) => Number.parseFloat(x.trim()));
            ratio = a / b;
        }
        return ratio;
    }

    public bind() {
        if (this._target) {
            this._sensor.observe(this._target);
        } else {
            this._size = this._getFrameSize();
            window.addEventListener('resize', this._onResize);
        }
        window.addEventListener('orientationchange', this._onResize);
    }

    public unbind() {
        if (this._target) {
            this._sensor.unobserve(this._target);
        } else {
            window.removeEventListener('resize', this._onResize);
        }
        window.removeEventListener('orientationchange', this._onResize);
    }
}