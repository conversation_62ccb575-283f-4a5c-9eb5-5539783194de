/**
 * Help to support multiple resolution from 16/9 to 19.5/9
 * https://docs.cocos.com/creator/manual/en/ui/multi-resolution.html
 */

// #!if cheatMenu
import CheatMenu from '../core/utility/cheat/CheatMenu';
// #!endif
import AppConstants from '../AppConstants';
import { AbstractRenderer, Application, Container, IPointData, ISize, Renderer } from '../pixi';
import { AutoAlignContainer } from './AutoAlignContainer';
import { HtmlResizeListener } from './HtmlResizeListener';

/* eslint-disable no-unused-vars */
export type ScreenMetadata = {
    baseWidth?: number;
    baseHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
    designedWidth?: number;
    designedHeight?: number;
    width?: number;
    height?: number;
    scaleX?: number;
    scaleY?: number;
    realWidth?: number;
    realHeight?: number;
    offsetX?: number;
    offsetY?: number;
    isPortrait?: boolean;
};

export enum ContainerType {
    PROPORTION_TO_FRAME,
    EQUAL_TO_FRAME,
}

export enum ContentType {
    EXACT_FIT,
    SHOW_ALL,
    NO_BORDER,
    FIXED_HEIGHT,
    FIXED_WIDTH,
}

export type ResolutionPolicy = {
    container: ContainerType;
    content: ContentType;
};

export type ResizeCallback = (screen: ScreenMetadata) => void;
/* eslint-enable no-unused-vars */

export class MultiResolutionHandler {
    public static inst: MultiResolutionHandler = null;

    private readonly _RATIO_16_9: number = 16 / 9;
    private readonly _RATIO_195_9: number = 19.5 / 9;

    private _container: HTMLElement;
    private _canvas: HTMLCanvasElement;
    private _renderer: Renderer | AbstractRenderer;
    private _stage: Container;
    private _htmlResizeListener: HtmlResizeListener;
    private _resizeListeners: ResizeCallback[];
    private _frameSize: ISize;
    private _isPortrait: boolean = false;

    private _screen: ScreenMetadata = {
        baseWidth: AppConstants.BASE_SCREEN_WIDTH,
        baseHeight: AppConstants.BASE_SCREEN_HEIGHT,
        maxWidth: AppConstants.MAX_CANVAS_WIDTH,
        maxHeight: AppConstants.MAX_CANVAS_HEIGHT,
    };

    constructor(app: Application, autoStart: boolean = true) {
        if (MultiResolutionHandler.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
            return MultiResolutionHandler.inst;
        }
        MultiResolutionHandler.inst = this;

        // Init
        this._canvas = app.view;
        this._container = app.view.parentElement;
        this._renderer = app.renderer;
        this._stage = app.stage;
        this._resizeListeners = [];

        // Lock rotation
        this._container.style['-webkit-transform'] = 'rotate(0deg)';
        this._container.style.transform = 'rotate(0deg)';

        // Register events
        const targetNode = globalThis.jg.getRootDiv?.();
        this._htmlResizeListener = new HtmlResizeListener(this.onResize, targetNode);
        this._htmlResizeListener.bind();
    }

    public onResize = (size?: ISize) => {
        console.log('Resizing...');

        if (size) { this._frameSize = size; }
        const baseWidth = AppConstants.BASE_SCREEN_WIDTH;
        const baseHeight = AppConstants.BASE_SCREEN_HEIGHT;
        const maxHeight = AppConstants.MAX_CANVAS_HEIGHT;
        const maxWidth = AppConstants.MAX_CANVAS_WIDTH;
        const { width: frameW, height: frameH } = this._frameSize;
        const ratio: number = this._isPortrait ? (frameH / frameW) : (frameW / frameH);

        let designedResolution: ISize = { width: baseWidth, height: baseHeight };
        let policy: ResolutionPolicy;
        if (this._isPortrait) {
            // compute aspect ratios
            if (ratio <= this._RATIO_16_9) {
                designedResolution = { width: baseWidth, height: baseHeight };
                policy = { container: ContainerType.PROPORTION_TO_FRAME, content: ContentType.NO_BORDER };
            } else if (ratio <= this._RATIO_195_9) {
                designedResolution = { width: baseWidth, height: maxHeight };
                policy = { container: ContainerType.EQUAL_TO_FRAME, content: ContentType.FIXED_WIDTH };
            } else {
                designedResolution = { width: baseWidth, height: maxHeight };
                policy = { container: ContainerType.PROPORTION_TO_FRAME, content: ContentType.NO_BORDER };
            }
        } else {
            if (ratio <= this._RATIO_16_9) {
                policy = { container: ContainerType.PROPORTION_TO_FRAME, content: ContentType.FIXED_HEIGHT };
            } else if (ratio <= this._RATIO_195_9) {
                designedResolution.width = maxWidth;
                policy = { container: ContainerType.EQUAL_TO_FRAME, content: ContentType.FIXED_HEIGHT };
            } else {
                designedResolution.width = maxWidth;
                policy = { container: ContainerType.PROPORTION_TO_FRAME, content: ContentType.FIXED_HEIGHT };
            }
        }

        this._setDesignedResolution(designedResolution, policy);
        this._alignStageToCenter();
        this._updateContainers();
        this._dispatchResizeEvent(this._screen);
        // #!if cheatMenu
        this._debugViewportInfo();
        // #!endif
    };

    private _setDesignedResolution(resolution: ISize, policy: ResolutionPolicy): void {
        this._applyContainer(policy.container, resolution);
        this._applyContent(policy.content, resolution);
    }

    private _setupContainer(width: number, height: number): void {
        const pixelRatio = globalThis.devicePixelRatio ?? 1;

        this._container.style.width = `${width}px`;
        this._container.style.height = `${height}px`;

        this._canvas.width = width * pixelRatio;
        this._canvas.height = height * pixelRatio;
    }

    private _applyContainer(mode: ContainerType, designedResolution: ISize): void {
        const { width: frameW, height: frameH } = this._frameSize;
        const designW = designedResolution.width;
        const designH = designedResolution.height;
        const containerStyle = this._container.style;

        let scaleX = 1;
        let scaleY = 1;
        let containerW;
        let containerH;
        switch (mode) {
            case ContainerType.PROPORTION_TO_FRAME:
                scaleX = frameW / designW;
                scaleY = frameH / designH;
                if (scaleX < scaleY) {
                    containerW = frameW;
                    containerH = designH * scaleX;
                } else {
                    containerW = designW * scaleY;
                    containerH = frameH;
                }

                // Adjust container size with integer value
                const offX = Math.round((frameW - containerW) / 2);
                const offY = Math.round((frameH - containerH) / 2);
                containerW = frameW - 2 * offX;
                containerH = frameH - 2 * offY;

                this._setupContainer(containerW, containerH);
                containerStyle.margin = '0px';
                containerStyle.paddingLeft = `${offX}px`;
                containerStyle.paddingRight = `${offX}px`;
                containerStyle.paddingTop = `${offY}px`;
                containerStyle.paddingBottom = `${offY}px`;
                break;
            case ContainerType.EQUAL_TO_FRAME:
                this._setupContainer(frameW, frameH);
                containerStyle.padding = '0px 0px';
                containerStyle.margin = '0px';
                break;
        }
    }

    private _applyContent(type: ContentType, designedResolution: ISize): void {
        const designW = designedResolution.width;
        const designH = designedResolution.height;
        const containerW = this._canvas.width;
        const containerH = this._canvas.height;

        let scaleX = 1;
        let scaleY = 1;
        let scale = 0;
        let contentW;
        let contentH;
        switch (type) {
            case ContentType.EXACT_FIT:
                scaleX = containerW / designW;
                scaleY = containerH / designH;
                contentW = containerW;
                contentH = containerH;
                break;
            case ContentType.SHOW_ALL:
                scaleX = containerW / designW;
                scaleY = containerH / designH;
                if (scaleX < scaleY) {
                    scale = scaleX;
                    contentW = containerW;
                    contentH = designH * scale;
                } else {
                    scale = scaleY;
                    contentW = designW * scale;
                    contentH = containerH;
                }
                scaleX = scaleY = scale;
                break;
            case ContentType.NO_BORDER:
                scaleX = containerW / designW;
                scaleY = containerH / designH;
                if (scaleX < scaleY) {
                    scale = scaleY;
                    contentW = designW * scale;
                    contentH = containerH;
                } else {
                    scale = scaleX;
                    contentW = containerW;
                    contentH = designH * scale;
                }
                scaleX = scaleY = scale;
                break;
            case ContentType.FIXED_HEIGHT:
                scale = containerH / designH;
                contentW = containerW;
                contentH = containerH;
                scaleX = scaleY = scale;
                break;
            case ContentType.FIXED_WIDTH:
                scale = containerW / designW;
                contentW = containerW;
                contentH = containerH;
                scaleX = scaleY = scale;
                break;
        }

        // Makes content fit better the canvas
        if (Math.abs(containerW - contentW) < 2) {
            contentW = containerW;
        }
        if (Math.abs(containerH - contentH) < 2) {
            contentH = containerH;
        }
        // const viewport = new Rectangle(Math.round((containerW - contentW) / 2), Math.round((containerH - contentH) / 2), contentW, contentH);

        this._renderer.resize(contentW, contentH);
        this._stage.scale.set(scaleX, scaleY);
        this._setMetadata(designW, designH, contentW, contentH, scaleX, scaleY);
    }

    private _setMetadata(designW: number, designH: number, contentW: number, contentH: number, scaleX: number, scaleY: number): void {
        this._screen.designedWidth = designW;
        this._screen.designedHeight = designH;
        this._screen.width = contentW;
        this._screen.height = contentH;
        this._screen.scaleX = scaleX;
        this._screen.scaleY = scaleY;
        this._screen.realWidth = Math.min(Math.floor(contentW / scaleX), designW);
        this._screen.realHeight = Math.floor(contentH / scaleY);
    }

    // Align main container to center
    private _alignStageToCenter(): void {
        const deltaW = Math.floor(this._screen.realWidth) - this._screen.baseWidth;
        const deltaH = Math.floor(this._screen.realHeight) - this._screen.baseHeight;
        const offsetX = Math.max((deltaW / 2) / this._screen.scaleX, 0);
        const offsetY = Math.max((deltaH / 2) / this._screen.scaleY, 0);

        this._stage.position.set(offsetX, offsetY);
        this._screen.offsetX = offsetX;
        this._screen.offsetY = offsetY;
    }

    // Update auto align containers
    private _updateContainers(): void {
        AutoAlignContainer.onSizeChanged(this._screen);
    }

    private _dispatchResizeEvent(screen: ScreenMetadata): void {
        this._resizeListeners.forEach(listener => {
            listener(screen);
        });
    }

    public addResizeListener(listener: ResizeCallback, startNow: boolean = false) {
        if (this._resizeListeners.includes(listener)) {
            console.warn('[RESIZE] Duplicate listener', listener);
        }
        this._resizeListeners.push(listener);
        if (startNow && this._screen) {
            listener(this._screen);
        }
    }

    public removeListener(listener: ResizeCallback): void {
        for (let i = this._resizeListeners.length - 1; i >= 0; i--) {
            if (this._resizeListeners[i] === listener) {
                this._resizeListeners.splice(i, 1);
                // console.warn('[RESIZE] Remove listener', listener);
            }
        }
    }

    public removeAllListeners(): void {
        this._resizeListeners.length = 0;
    }

    public getScreen(): ScreenMetadata {
        return this._screen;
    }

    public destroy() {
        // window.removeEventListener('resize', this.onResize);
        this._resizeListeners.length = 0;
        AutoAlignContainer.activatedContainers.length = 0;
        MultiResolutionHandler.inst = null;
    }

    //#!if cheatMenu
    private _debugViewportInfo() {
        CheatMenu.inst?.setResolutionInfo(this._screen.width, this._screen.height, this._screen.realWidth, this._screen.realHeight, this._screen.scaleX, this._screen.scaleY, this._screen.offsetY);
    }
    // #!endif
}
