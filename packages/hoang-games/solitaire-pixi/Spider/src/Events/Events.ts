import { utils } from '../pixi';
import { GameEvents, UIEvents, NetworkEvents } from './EventTypes';

export type EventTypes = GameEvents | UIEvents | NetworkEvents;

export class Events {
    private static _router: utils.EventEmitter<EventTypes> = new utils.EventEmitter();

    /**
     * Return an array listing the events for which the emitter has registered
     * listeners.
     */
    public static eventNames(): EventTypes[] {
        return this._router.eventNames();
    }

    /**
     * Return the listeners registered for a given event.
     */
    public static listeners(event: EventTypes): utils.EventEmitter.ListenerFn[] {
        return this._router.listeners(event);
    }

    /**
     * Return the number of listeners listening to a given event.
     */
    public static listenerCount(event: EventTypes): number {
        return this._router.listenerCount(event);
    }

    /**
     * Calls each of the listeners registered for a given event.
     */
    public static emit(event: EventTypes, ...args: any[]): boolean {
        return this._router.emit(event, ...args);
    }

    /**
     * Add a listener for a given event.
     */
    public static on(event: EventTypes, fn: utils.EventEmitter.ListenerFn, context?: any): Events {
        this._router.on(event, fn, context);
        return this;
    }

    public static addListener(event: EventTypes, fn: utils.EventEmitter.ListenerFn, context?: any): Events {
        this._router.addListener(event, fn, context);
        return this;
    }

    /**
     * Add a one-time listener for a given event.
     */
    public static once(event: EventTypes, fn: utils.EventEmitter.ListenerFn, context?: any): Events {
        this._router.once(event, fn, context);
        return this;
    }

    /**
     * Remove the listeners of a given event.
     */
    public static removeListener(event: EventTypes, fn?: utils.EventEmitter.ListenerFn, context?: any, once?: boolean): Events {
        this._router.removeListener(event, fn, context);
        return this;
    }

    public static off(event: EventTypes, fn?: utils.EventEmitter.ListenerFn, context?: any, once?: boolean): Events {
        this._router.off(event, fn, context);
        return this;
    }

    /**
     * Remove all listeners, or those of the specified event.
     */
    public static removeAllListeners(event?: EventTypes): Events {
        this._router.removeAllListeners(event);
        return this;
    }

    public static destroy() {
        if (this._router) {
            delete this._router;
        }
    }
}

