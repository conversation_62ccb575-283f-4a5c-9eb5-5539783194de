/* eslint-disable no-unused-vars */
// !Important: Add comment to your event type

enum GameEvents {
    PAUSE = 'PAUSE',
    RESUME = 'RESUME',
    OPEN_SUCCESS = 'OPEN_SUCCESS',
    OPEN_FAIL = 'OPEN_FAIL',
    CHANGE_LEVEL = 'CHANGE_LEVEL',
    RETRY_LEVEL = 'RETRY_LEVEL',
    GAME_OVER = 'GAME_OVER',
    WIN_MILESTONE = 'WIN_MILESTONE',
    NEW_GAME = 'NEW_GAME'
}

enum UIEvents {
    OPEN = 'OPEN',
    CLOSE = 'CLOSE',
    CLICK_START = 'CLICK_START',
    CLICK_CANCEL = 'CLICK_CANCEL',
    CLICK_CASH_OUT = 'CLICK_CASH_OUT',
    CLICK_CHANGE_LEVEL = 'CLICK_CHANGE_LEVEL',
    ENABLE_TURBO = 'ENABLE_TURBO',
    TOTAL_WIN_END = 'TOTAL_WIN_END',
    LOCK_SPIN_PANE = 'LOCK_SPIN_PANE',
    CHANGE_BET = 'CHANGE_BET',
    CHANGE_VOLUME = 'CHANGE_VOLUME',
    TOGGLE_SOUND = 'TOGGLE_SOUND',
    LOAD_SOUND = 'LOAD_SOUND',
    UNDO = 'UNDO',
    HINT = 'HINT',
    CHOOSE_DRAW_GAME = 'CHOOSE_DRAW_GAME',
    START_COUNT_TIME = 'START_COUNT_TIME',
    UPDATE_SCORE_GAME = 'UPDATE_SCORE_GAME',
    ADD_INFO = 'ADD_INFO',
    UPDATE_HIGHSCORE = 'UPDATE_HIGHSCORE',
    POWER_UP = 'POWER_UP',
    SHOW_CARD_LIFTER_POPUP = 'SHOW_CARD_LIFTER_POPUP',
    SHOW_CHOOSE_GAME = 'SHOW_CHOOSE_GAME',
    CHANGE_STATE_CARD_LIFTER_BUTTON = 'CHANGE_STATE_CARD_LIFTER_BUTTON'
}

enum NetworkEvents {
    RECEIVE_CONFIG = 'RECEIVE_CONFIG',
}

export { GameEvents, UIEvents, NetworkEvents };
