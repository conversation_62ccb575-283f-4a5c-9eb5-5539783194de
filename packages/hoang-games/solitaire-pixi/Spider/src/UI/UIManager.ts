import { Container, Text, Sprite, settings, Point, Layer, Group } from '../pixi';
import AppConstants from '../AppConstants';
import { MainApp } from '../main';
import ResourceManager from '../ResManagers/ResourceManager';
import SpinPanel from './SpinPanel/SpinPanel';
import { PopupBase } from '../Popup/PopupBase';
import { AutoAlignContainer } from '../Scenes/AutoAlignContainer';

export default class UIManager {
    public static inst: UIManager = null;
    public uiContainer: Container = null;
    public popupContainer: Container = null;
    public topNotifyContainer: AutoAlignContainer = null;

    public showingGroup: Group = null;
    public movingGroup: Group = null;
    public drawedGroup: Group = null;
    public onTopGroup: Group = null;

    private _spinPanel: SpinPanel = null;
    private _isSpinPanelActived: boolean = false;
    private _isAutoSpinActived: boolean = false;

    constructor() {
        if (UIManager.inst) {
            console.warn(`Several constructions of singleton ${this.constructor.name}!!!`);
            return UIManager.inst;
        }
        UIManager.inst = this;

        this.uiContainer = new Container();
        // #!debug
        this.uiContainer.name = 'UI';
        this.popupContainer = new Container();
        this.popupContainer.name = 'Popups';
        this.popupContainer.zIndex = 9999;

        //TODO: try to use single AutoAlignContainer for all popup to optimize
        this.topNotifyContainer = new AutoAlignContainer({ vertical: 'TOP' });
        this.topNotifyContainer.name = 'NotifyContainer';
        MainApp.inst.app.ticker.add(this.onUpdate, this);
    }

    public initUIComponents() {
        this._spinPanel = new SpinPanel();

        // const versionNumber = new Text('v' + process.env.VERSION, {
        //     fontFamily: 'lexendbold',
        //     fontSize: 20,
        //     fontWeight: 'normal',
        //     align: 'right',
        //     fill: [0xccd0d2], //0x7E6024, 0x414141
        // });
        // versionNumber.anchor.set(1, 0);
        // versionNumber.alpha = 0.4;
        // versionNumber.position.set(AppConstants.BASE_SCREEN_WIDTH - 10, 0);
        // const bottomContainer = new AutoAlignContainer('BOTTOM', -(versionNumber.height + 5));
        // bottomContainer.name = 'VersionInfo';
        // bottomContainer.addChild(versionNumber);
        // this.popupContainer.addChild(bottomContainer);
    }

    public onUpdate(dtScalar: number) {
        const dt = dtScalar / settings.TARGET_FPMS / 1000;

        if (this._isSpinPanelActived) {
            this._spinPanel.update(dt);
        }
    }

    public addPopup(popup: PopupBase) {
        this.popupContainer.addChild(popup.containerBG);
        this.popupContainer.addChild(popup.container);
    }

    public removePopup(popup: PopupBase) {
        this.popupContainer.removeChild(popup.containerBG);
        this.popupContainer.removeChild(popup.container);
    }

    public activeSpinPanel(): void {
        if (!this._isSpinPanelActived) {
            //this.uiContainer.addChild(this._paylineInfo);

            this._spinPanel.onActive();
            this.uiContainer.addChild(this._spinPanel.container);
            this._isSpinPanelActived = true;

            // this._winPanel.onActive();

            // const spinPanelNotifyContainer = this._spinPanel.getNotifyContainer();
            // spinPanelNotifyContainer.position.set(this._spinPanel.position.x, this._spinPanel.position.y - 150);
            // this.uiContainer.addChild(spinPanelNotifyContainer);
        }
    }

    public deactiveSpinPanel(): void {
        if (this._isSpinPanelActived) {
            this.uiContainer.removeChild(this._spinPanel.container);
            this._spinPanel.onDeactive();
            this._isSpinPanelActived = false;
        }
    }

    public setAutoSpinActive(isAuto: boolean) {
        this._isAutoSpinActived = isAuto;
    }

    public isAutoSpinActive() {
        return this._isAutoSpinActived;
    }

    public updateSoundSetting(isSound) {
        this._spinPanel.updateSoundSetting(isSound);
    }

    public getSpinPanel(): SpinPanel {
        return this._spinPanel;
    }

    public makeTop(container: Container) {
        this.uiContainer.removeChild(container);
        this.uiContainer.addChild(container);
    }
}
