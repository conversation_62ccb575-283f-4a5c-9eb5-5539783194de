import { Container, Text, Sprite, InteractionEvent, ObservablePoint, Graphics, ITextStyle, IBitmapTextStyle, BitmapText } from '../../pixi';
import * as UI from '../core/UI';
import ResourceManager from '../../ResManagers/ResourceManager';
import AppConstants, { SuitMode } from '../../AppConstants';
import Utils from '../../Utils/Utils';
import { SoundManager } from '../../SoundManager';
import { Events } from '../../Events/Events';
import { GameEvents, UIEvents } from '../../Events/EventTypes';
import { PopupManager } from '../../Popup/PopupManager';
import { AutoAlignContainer } from '../../Scenes/AutoAlignContainer';
import Localization from '../../Localization/Localization';
import GameConfigs from '../../GameConfigs';

export default class SpinPanel {
    public container: AutoAlignContainer;
    private readonly _DIM_COLOR = 0x777777; // 0xa5a5a5 - 0x757575 - 0x363636
    private readonly _UN_DIM_COLOR = 0xffffff;
    private readonly _CUSTOM_TEXT_FONT = 'intersemibold';

    // bar top
    private _highScore: BitmapText;
    private _score: BitmapText;
    private _time: BitmapText;
    private _currentTime: number = 0;
    private _startCountTime: boolean = false;
    private _isSoundOn: boolean = true;

    //spin panel
    private _spinPanelContainer: Container;

    //Menu option
    private _newGameBtn: UI.Button;
    private _rankBtn: UI.Button;
    private _soundBtn: UI.Button;
    private _rulesBtn: UI.Button;
    private _cardLifterBtn: UI.Button;
    //layoutLeft

    private _undoBtn: UI.Button;
    private _hintBtn: UI.Button;
    private _cardLifterText: Text;
    private _undoText: Text;
    private _hintText: Text;

    private _isShowPopupCardLifter: boolean = true;
    private _timerLifter: Text;
    private _underLine: Graphics;
    private _iconButtonLifter: Sprite;
    private _iconButtonUndo: Sprite;
    private _iconButtonHint: Sprite;
    private _isCountDownCardLifter: boolean = false;
    private _timerCountDown: number = 120;

    private _barHeight: number = 65;

    constructor() {
        this.container = new AutoAlignContainer({ vertical: 'TOP' }, { x: 0, y: 0 });

        Events.on(UIEvents.START_COUNT_TIME, () => {
            this._startCountTime = true;
        });
        Events.on(UIEvents.UPDATE_HIGHSCORE, this._setHighScore.bind(this));
        Events.on(GameEvents.NEW_GAME, this._resetMenu.bind(this)), Events.on(UIEvents.CHOOSE_DRAW_GAME, this._resetMenu.bind(this));
        Events.on(UIEvents.UPDATE_SCORE_GAME, this.updateScoreGame.bind(this));
        Events.on(UIEvents.SHOW_CARD_LIFTER_POPUP, (isShow) => {
            this._isShowPopupCardLifter = !isShow;
        });
        this.init();
    }

    public init() {
        const { realWidth, realHeight, maxWidth, maxHeight } = this.container.getScreenData();
        const centerW = realWidth / 2;
        const centerH = realHeight / 2;
        const bgWidth = maxWidth * 1.2;
        const background = new Graphics();
        background.beginFill(0x000, 0.5);
        background.drawRect(-bgWidth / 2, 0, bgWidth, this._barHeight);
        this.container.addChild(background);

        this._spinPanelContainer = new Container();
        this.container.addChild(this._spinPanelContainer);
        // logo
        const logo = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'logo'));
        logo.anchor.set(0, 0.5);
        logo.position.set(-centerW + 23, this._barHeight / 2 - 3);

        this.container.addChild(logo);

        // layout score
        for (let i = 0; i < 4; i++) {
            const splitLine = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'rectangle'));
            splitLine.anchor.set(0.5);
            splitLine.position.set(-195 + 130 * i, this._barHeight / 2);
            this.container.addChild(splitLine);
        }
        const barTextStyle: Partial<ITextStyle> = {
            fontFamily: this._CUSTOM_TEXT_FONT,
            fontSize: 15,
            align: 'center',
            fill: 0xffffff,
        };

        const highScoreLogo = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'highscore'));
        highScoreLogo.position.set(-177.5, 7);
        const highscoreText = new Text(Localization.getText('HIGH_SCORE'), barTextStyle);
        highscoreText.position.set(-177.5 + highScoreLogo.width + 3, 5);
        this.container.addChild(highScoreLogo);
        this.container.addChild(highscoreText);

        const scoreLogo = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'score'));
        scoreLogo.position.set(-32, 5);
        const scoreText = new Text(Localization.getText('SCORE'), barTextStyle);
        scoreText.position.set(-32 + scoreLogo.width + 3, 5);
        this.container.addChild(scoreLogo);
        this.container.addChild(scoreText);

        const timeLogo = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'time'));
        timeLogo.position.set(104, 5);
        const timeText = new Text(Localization.getText('TIME'), barTextStyle);
        timeText.position.set(104 + timeLogo.width + 3, 5);
        this.container.addChild(timeLogo);
        this.container.addChild(timeText);

        const textStyle: Partial<IBitmapTextStyle> = {
            fontName: 'Inter-SemiBold',
            fontSize: 24,
            tint: 0xecff0e,
            align: 'center',
        };

        this._highScore = new BitmapText('0', textStyle);
        this._highScore.anchor.set(0.5);
        this._highScore.position.set(-128, this._barHeight - 23);
        this.container.addChild(this._highScore);

        this._score = new BitmapText('0', textStyle);
        this._score.anchor.set(0.5);
        this._score.position.set(1, this._barHeight - 23);
        this.container.addChild(this._score);

        this._time = new BitmapText('0:00', textStyle);
        this._time.anchor.set(0.5);
        this._time.position.set(132.5, this._barHeight - 23);
        this.container.addChild(this._time);

        //layout menu game
        const newGameIcon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'newgame'));
        this._newGameBtn = new UI.Button({
            background: newGameIcon,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            PopupManager.newGamePopup.open();
        });
        this._newGameBtn.getContainer().anchor.set(0, 0.5);
        this._newGameBtn.getContainer().y = this._barHeight / 2 - 7;
        const newGameText = new Text(Localization.getText('NEW'), barTextStyle);
        newGameText.anchor.set(0.5, 0);
        newGameText.position.set(this._newGameBtn.getContainer().width / 2, 17);
        this._newGameBtn.getContainer().addChild(newGameText);
        this.container.addChild(this._newGameBtn.getContainer());

        const rankIcon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'rank'));
        this._rankBtn = new UI.Button({
            background: rankIcon,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            PopupManager.rankPopup.open();
        });
        this._rankBtn.getContainer().anchor.set(0, 0.5);
        this._rankBtn.getContainer().y = this._barHeight / 2 - 7;
        const rankText = new Text(Localization.getText('RANK'), barTextStyle);
        rankText.anchor.set(0.5, 0);
        rankText.position.set(this._rankBtn.getContainer().width / 2, 17);
        this._rankBtn.getContainer().addChild(rankText);
        this.container.addChild(this._rankBtn.getContainer());

        const soundIcon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'sound_on'));
        this._soundBtn = new UI.Button({
            background: soundIcon,
        }).on('click', (e: InteractionEvent) => {
            this._isSoundOn = !this._isSoundOn;
            soundIcon.texture = ResourceManager.getAtlasFrame('ui_atlas', this._isSoundOn ? 'sound_on' : 'sound_off');
            SoundManager.inst.setSoundOption(this._isSoundOn);
            SoundManager.inst.playSfx('BUTTON_CLICK');
        });
        this._soundBtn.getContainer().anchor.set(0, 0.5);
        this._soundBtn.getContainer().y = this._barHeight / 2 - 7;
        const soundText = new Text(Localization.getText('SOUND'), barTextStyle);
        soundText.anchor.set(0.5, 0);
        soundText.position.set(this._soundBtn.getContainer().width / 2, 17);
        this._soundBtn.getContainer().addChild(soundText);
        this.container.addChild(this._soundBtn.getContainer());

        const ruleIcon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'rules'));
        this._rulesBtn = new UI.Button({
            background: ruleIcon,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            //PopupManager.inputNamePopup.open(52, 100, DrawMode.ONE, Utils.getTimer());
            PopupManager.rulesPopup.open();
        });
        this._rulesBtn.getContainer().anchor.set(0, 0.5);
        this._rulesBtn.getContainer().y = this._barHeight / 2 - 7;
        const rulesText = new Text(Localization.getText('RULES'), barTextStyle);
        rulesText.anchor.set(0.5, 0);
        rulesText.position.set(this._rulesBtn.getContainer().width / 2, 17);
        this._rulesBtn.getContainer().addChild(rulesText);
        this.container.addChild(this._rulesBtn.getContainer());

        //layout left
        const buttonStyle: Partial<ITextStyle> = {
            fontFamily: this._CUSTOM_TEXT_FONT,
            fontSize: 18,
            fill: [0xffffff],
            align: 'center',
        };

        this._iconButtonUndo = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'undo_btn'));
        this._undoBtn = new UI.Button({
            background: this._iconButtonUndo,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            Events.emit(UIEvents.UNDO, false);
        });
        this._undoBtn.getContainer().anchor.set(0, 0.5);
        this._undoBtn.getContainer().y = this._barHeight / 2 + 68;
        this._undoBtn.getContainer().x = -centerW + 10;
        this._undoText = new Text(Localization.getText('UNDO'), buttonStyle);
        this._undoText.position.set(69.5, -12.5);
        this._undoBtn.getContainer().addChild(this._undoText);
        this.container.addChild(this._undoBtn.getContainer());

        this._iconButtonHint = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'hint_btn'));
        this._hintBtn = new UI.Button({
            background: this._iconButtonHint,
        }).on('click', (e: InteractionEvent) => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            Events.emit(UIEvents.HINT);
        });
        this._hintBtn.getContainer().anchor.set(0, 0.5);
        this._hintBtn.getContainer().y = this._barHeight / 2 + 68;
        this._hintBtn.getContainer().x = this._undoBtn.getContainer().x + this._undoBtn.getContainer().width / 2 + 90;
        this._hintText = new Text(Localization.getText('HINT'), buttonStyle);
        this._hintText.position.set(72, -12.5);
        this._hintBtn.getContainer().addChild(this._hintText);
        this.container.addChild(this._hintBtn.getContainer());

        this._iconButtonLifter = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'card_lifter_btn'));
        this._cardLifterBtn = new UI.Button({
            background: this._iconButtonLifter,
        }).on('click', (e: InteractionEvent) => {
            if (!this._startCountTime) return;
            SoundManager.inst.playSfx('BUTTON_CLICK');
            if (this._isShowPopupCardLifter) {
                PopupManager.cardLifterPopup.open();
            } else {
                Events.emit(UIEvents.POWER_UP);
                this._iconButtonLifter.interactive = false;
            }
        });
        this._underLine = new Graphics()
            .lineStyle({ color: 0xecff0e, width: 2.5 })
            .moveTo(this._cardLifterBtn.getContainer().x + 15, this._cardLifterBtn.getContainer().y + this._cardLifterBtn.getContainer().height / 2 - 9)
            .lineTo(this._cardLifterBtn.getContainer().x + this._cardLifterBtn.getContainer().width - 15, this._cardLifterBtn.getContainer().y + this._cardLifterBtn.getContainer().height / 2 - 9)
            .endFill();
        this._timerLifter = new Text('0:00', {
            fontFamily: 'intersemibold',
            fontSize: 13,
            fill: [0xecff0e],
            align: 'center',
        });
        this._timerLifter.anchor.set(0.5);
        this._timerLifter.position.set(82.5, 22.5);
        this._timerLifter.visible = false;
        this._underLine.visible = false;
        this._cardLifterText = new Text(Localization.getText('CARD_LIFTER'), buttonStyle);
        this._cardLifterText.position.set(47.5, -12.5);
        this._cardLifterBtn.getContainer().addChild(this._cardLifterText);
        this._cardLifterBtn.getContainer().addChild(this._underLine);
        this._cardLifterBtn.getContainer().addChild(this._timerLifter);
        this._cardLifterBtn.getContainer().anchor.set(0, 0.5);
        this._cardLifterBtn.getContainer().y = this._barHeight / 2 + 68;
        this._cardLifterBtn.getContainer().x = this._hintBtn.getContainer().x + this._hintBtn.getContainer().width / 2 + 90;
        this.container.addChild(this._cardLifterBtn.getContainer());

        // handle resize
        this.container.addResizeListener((screen) => {
            const { realWidth, realHeight, maxWidth, maxHeight } = screen;
            const centerW = realWidth / 2,
                centerH = realHeight / 2;

            logo.x = -centerW + 23;
            this._undoBtn.getContainer().x = -centerW + 10;
            this._hintBtn.getContainer().x = this._undoBtn.getContainer().x + this._undoBtn.getContainer().width / 2 + 90;
            this._cardLifterBtn.getContainer().x = this._hintBtn.getContainer().x + this._hintBtn.getContainer().width / 2 + 90;
            this._rulesBtn.getContainer().x = realWidth / 3 + 160;
            this._soundBtn.getContainer().x = realWidth / 3 + 90;
            this._rankBtn.getContainer().x = realWidth / 3 + 18;
            this._newGameBtn.getContainer().x = realWidth / 3 - 52;
        });
        this.container.dryUpdate();
    }

    public setStatusCardLifterBtn(isDim) {
        this._iconButtonLifter.interactive = !isDim;
        this._isCountDownCardLifter = isDim;
        this._timerLifter.visible = isDim;
        this._underLine.visible = isDim;
        this._iconButtonLifter.tint = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._cardLifterText.style.fill = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        if (isDim) {
            this._timerCountDown = 120;
        }
    }

    public setStatusUndoBtn(isActive) {
        this._iconButtonUndo.interactive = isActive;
        this._iconButtonUndo.tint = !isActive ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._undoText.style.fill = !isActive ? this._DIM_COLOR : this._UN_DIM_COLOR;
    }

    public setCardLifterBtn(isActive) {
        this._iconButtonLifter.interactive = isActive;
        this._iconButtonLifter.tint = !isActive ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._cardLifterText.style.fill = !isActive ? this._DIM_COLOR : this._UN_DIM_COLOR;
    }

    public blockButton(isDim) {
        this._iconButtonUndo.interactive = !isDim;
        this._iconButtonLifter.interactive = !isDim;
        this._iconButtonHint.interactive = !isDim;
        this._iconButtonLifter.tint = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._iconButtonUndo.tint = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._iconButtonHint.tint = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._cardLifterText.style.fill = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._undoText.style.fill = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
        this._hintText.style.fill = isDim ? this._DIM_COLOR : this._UN_DIM_COLOR;
    }
    public onActive() {}

    public onDeactive() {}

    public setGameWin() {
        this._startCountTime = false;
    }

    private _setHighScore(mode: SuitMode) {
        const userTop = Utils.getScoreList(mode);
        if (userTop && userTop.length > 0) {
            this._highScore.text = userTop[0].score;
        } else {
            this._highScore.text = '0';
        }
    }

    private _resetMenu() {
        this._startCountTime = false;
        this._currentTime = 0;
        this._time.text = '0:00';
        this._score.text = `${GameConfigs.SCORE_START_GAME}`;
        this.setStatusCardLifterBtn(false);
        this._timerCountDown = 120;
    }

    public update(dt: number) {
        if (!this._startCountTime) return;
        this._currentTime += dt;
        this._time.text = Utils.toMMSS(this._currentTime);
        if (this._isCountDownCardLifter) {
            this._timerCountDown -= dt;
            this._timerLifter.text = Utils.toMMSS(this._timerCountDown);
            if (this._timerCountDown <= 0) {
                this.setStatusCardLifterBtn(false);
                Events.emit(UIEvents.CHANGE_STATE_CARD_LIFTER_BUTTON);
            }
        }
    }

    public updateScoreGame(score: number) {
        this._score.text = score.toString();
    }

    public updateSoundSetting(isSound) {
        //this._soundToggle.setToggle(isSound);
    }

    get position(): ObservablePoint {
        return this.container.position;
    }
}
