import { Container, Graphics, ISize, Rectangle, settings, Point, InteractionEvent } from '../../pixi';
import { MainApp } from '../../main';
import Utils from '../../Utils/Utils';
import { InteractiveWidget } from './InteractiveWidget';
import { ScrollBar } from './UI';

export interface IScrollWidgetOptions {
    background: Container;
    clipSize: ISize;
    contentSize: ISize;
    alwayShowScrollBar?: boolean;
}

export class ScrollWidget extends InteractiveWidget {
    constructor(options: IScrollWidgetOptions) {
        super(options);

        this._scrollView = new Container();
        this.getContainer().addChild(this._scrollView);
        this._clipSize = options.clipSize;
        this._contentSize = options.contentSize;
        this._isAlwayShowScrollbar = options.alwayShowScrollBar;
        this._initClipping();
        this._isInitializedClip = true;

        this._scrollBar = new ScrollBar({
            width: 10,
            color: 0x000000,
            view: this._clipSize,
            content: this._contentSize
        });
        this.getContainer().buttonMode = false;
        this._scrollBar.setRenderable(false || this._isAlwayShowScrollbar);
        this._scrollBar.getContainer().x = this._clipSize.width / 2 - 13;
        this.getContainer().addChild(this._scrollBar.getContainer());

        this._mouseWheelHandle = this._onMouseWheel.bind(this);

        return this;
    }

    private _scrollView: Container;
    private _scrollBar: ScrollBar;
    private _pressedPosition: Point = null;
    private _touchEnter: boolean;
    private _clipSize: ISize;
    private _contentSize: ISize;
    private _gClipping: Graphics;
    private _isInitializedClip: boolean = false;
    private _isAlwayShowScrollbar: boolean = false;
    private _scrollEndLimitCallBack: Function;
    private _scrollTopLimitCallBack: Function;

    private _innerBounds: Rectangle = new Rectangle(0);
    private _curPosition: Point = new Point(720 / 2, 0);

    private _mouseWheelHandle: any;

    public addWidget(container: Container) {
        this._scrollView.addChild(container);
        this._getInnerBounds();
    }

    public removeWidget(container: Container) {
        const childContainer = this._scrollView.removeChild(container);
        this._getInnerBounds();
        return childContainer;
    }


    public updateContentLength(newSize: number) {
        this._contentSize.height = newSize;
        this._scrollBar.updateContentHeight(newSize);
    }
    public updateScrollViewHeight(newSize: number) {
        //this._scrollView.height = newSize;
        this._scrollBar.updateViewHeight(newSize);
    }

    public subscribeMouseWheelEvent() {
        document.addEventListener('mousewheel', this._mouseWheelHandle, true);
        document.addEventListener('DOMMouseScroll', this._mouseWheelHandle, true);
    }

    public unsubscribeMouseWheelEvent() {
        document.removeEventListener('mousewheel', this._mouseWheelHandle, true);
        document.removeEventListener('DOMMouseScroll', this._mouseWheelHandle, true);
    }

    public registerEventScrollEnd(callBack: Function) {
        this._scrollEndLimitCallBack = callBack;
    }

    private _getInnerBounds() {

        const bounds = this.getContainer().getBounds();
        this._innerBounds.height = bounds.height;
        this._innerBounds.width = bounds.width;
    }

    onPointerPress(e: InteractionEvent) {
        this._disableMoveUpdate();
        super.onPointerPress(e);
        this.showScrollBar(true);
        this._touchEnter = true;
        this._pressedPosition = new Point(e.data.global.x, e.data.global.y);
        this._lastMoveOffset = 0;
    }

    onPointerMove(e: InteractionEvent) {
        super.onPointerMove(e);
        if (this._pressedPosition !== null) {
            this._lastMoveOffset = this._pressedPosition.y - e.data.global.y;
            this._moveScrollView(this._lastMoveOffset);
            this._pressedPosition.set(e.data.global.x, e.data.global.y);
        }
    }

    private _moveInertia: Point = new Point(0, 0);
    private _inertiaDirection: number;
    private _lastMoveOffset: number = 0;
    private _wasBindingUpdateMethod: boolean = false;

    onPointerRelease(e: InteractionEvent) {
        super.onPointerRelease(e);
        /**
         * After user release touch scroll will keep moving until the inertia is gone
         */
        this._inertiaDirection = (this._lastMoveOffset < 0) ? -1 : 1;
        this._moveInertia.y = Math.abs(this._lastMoveOffset);
        this._disableMoveUpdate();
        MainApp.inst.app.ticker.add(this._onInertiaMoveUpdate, this);
        this._wasBindingUpdateMethod = true;

        this._pressedPosition = null;
    }

    private _onInertiaMoveUpdate(dtScalar: number): void {
        const dt = dtScalar / settings.TARGET_FPMS;
        const decelerationSpd = 6;
        this._moveScrollView(this._moveInertia.y * this._inertiaDirection);
        this._moveInertia.y = Math.max(0, this._moveInertia.y - decelerationSpd * dt / 1000);
        if (this._moveInertia.y <= 0) {
            this._disableMoveUpdate();
        }
    }

    onPointerExit(e: InteractionEvent) {
        super.onPointerExit(e);
        this.showScrollBar(false || this._isAlwayShowScrollbar);
        this._pressedPosition = null;
        this._touchEnter = false;
    }

    onPointerEnter(e: InteractionEvent) {
        super.onPointerEnter(e);
        this.showScrollBar(true);
        this._touchEnter = true;
    }

    resetScrollView() {
        this._scrollView.position.y = 0;
        this._curPosition.y = 0;
        this.showScrollBar(false || this._isAlwayShowScrollbar);
        this._touchEnter = true;
        this._scrollBar.updateBarPosition(0);
    }

    stopAllAction(): void {
        this._moveInertia.set(0, 0);
        this._disableMoveUpdate();
    }

    private _onMouseWheel(event) {
        // cross-browser wheel delta
        // Chrome / IE: both are set to the same thing - WheelEvent for Chrome, MouseWheelEvent for IE
        // Firefox: first one is undefined, second one is MouseScrollEvent
        const e = window.event || event;
        // Chrome / IE: first one is +/-120 (positive on mouse up), second one is zero
        // Firefox: first one is undefined, second one is -/+3 (negative on mouse up)
        const delta = Math.max(-1, Math.min(1, e.wheelDelta || -e.detail));

        this._disableMoveUpdate();

        // Do something with `delta`
        if (this._touchEnter) {
            this._moveScrollView(-delta * 100);
        }

        //e.preventDefault();
    }

    private _disableMoveUpdate(): void {
        if (this._wasBindingUpdateMethod) {
            MainApp.inst.app.ticker.remove(this._onInertiaMoveUpdate, this);
            this._wasBindingUpdateMethod = false;
        }
    }

    showScrollBar(show) {
        if (this._contentSize.height < this._clipSize.height) {
            return;
        }
        this._scrollBar.setRenderable(show);
    }

    private _moveScrollView(move: number) {
        if (this._contentSize.height < this._clipSize.height) {
            return;
        }
        this._curPosition.y -= move;
        const bottomLimit = -this._contentSize.height + this._clipSize.height;
        this._curPosition.y = Utils.clamp(this._curPosition.y, bottomLimit, 0);

        //call back trigger
        if (bottomLimit === this._curPosition.y && this._scrollEndLimitCallBack) {
            this._scrollEndLimitCallBack();
            // Reduce inertia when the scroll hit the botton limit
            this._moveInertia.y /= 1.3;
        } else if (this._curPosition.y === 0 && this._scrollTopLimitCallBack) {
            this._scrollTopLimitCallBack();
        }

        this._scrollView.position.y = this._curPosition.y;
        this._scrollBar.updateBarPosition(-this._curPosition.y);
        this.showScrollBar(true);
    }

    private _initClipping() {
        if (!this._isInitializedClip) {
            this._gClipping = new Graphics();
            this.getContainer().addChild(this._gClipping);
            this.getContainer().mask = this._gClipping;
        } else {
            this._gClipping.clear();
        }
        const clipRect = [-this._clipSize.width / 2, 0, this._clipSize.width, this._clipSize.height];
        this._gClipping.beginFill(0x8bc5ff, 0.4);
        this._gClipping.moveTo(clipRect[0], clipRect[1]);
        this._gClipping.lineTo(clipRect[0], clipRect[3]);
        this._gClipping.lineTo(clipRect[2], clipRect[3]);
        this._gClipping.lineTo(clipRect[2], clipRect[1]);
    }

    public expandClipArea(newWidth: number, newHeight: number) {
        this._clipSize = { width: newWidth, height: newHeight };
        this.updateScrollViewHeight(newHeight);
        this._initClipping();
    }
}