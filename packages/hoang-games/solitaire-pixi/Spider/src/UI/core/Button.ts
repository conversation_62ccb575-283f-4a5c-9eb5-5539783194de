import { Container, Texture, Text, InteractionEvent } from '../../pixi';
import { IInteractiveWidgetOptions, InteractiveWidget } from './InteractiveWidget';

export interface IButtonOptions extends IInteractiveWidgetOptions {
}

export class <PERSON><PERSON> extends InteractiveWidget {

    constructor(options: IButtonOptions) {
        super(options);

        return this;
    }

    /*
    * Button still have function but no respond when click
    */
    disableRespondEffect(isDim) {
        this._tintColor = isDim ? InteractiveWidget.HOLD_MUL_COLOR : 0xFFFFFF;
        this._setTint(this._tintColor);
    }

    onClick(e: InteractionEvent) {
        super.onClick(e);
        this.emit('click', e);
    }
}