import { Container, Texture, Text, Sprite, InteractionEvent } from '../../pixi';
import { IWidgetOptions, Widget } from './Widget';

export interface IInteractiveWidgetOptions extends IWidgetOptions {
    offTexture?: Texture;
    text?: Text;
    disableSwitchTexture?: boolean;
}

export class InteractiveWidget extends Widget {

    public static readonly HOLD_MUL_COLOR = 0xa5a5a5;
    public static readonly DISABLE_MUL_COLOR = 0x878787;
    public static readonly ENABLE_MUL_COLOR = 0xFFFFFF;

    private _text: Text = null;
    protected _tintColor = 0xFFFFFF;

    protected _defaultTexture: Texture = null;
    protected _offTexture: Texture = null;

    constructor(options: IInteractiveWidgetOptions) {
        super(options);

        if (options.text) {
            this._text = this.contentContainer.addChild(options.text);
            this._text.anchor.set(0.5);
        }

        if (options.background) {
            this._defaultTexture = options.background['texture'];
        }

        if (options.offTexture) {
            this._offTexture = options.offTexture;
        }

        this.insetContainer.interactive = true;
        this.insetContainer.buttonMode = true;
    }

    public setInteractable(enable: boolean) {
        const skipChangeTexture = (this._options as IInteractiveWidgetOptions).disableSwitchTexture || false;
        if (enable) {
            this.insetContainer.interactive = true;
            if (this._defaultTexture && this.insetContainer['texture'] && !skipChangeTexture) {
                this.insetContainer['texture'] = this._defaultTexture;
                this._setTint(this._tintColor);
            } else {
                this._setTint(this._tintColor);
            }
        } else {
            this.insetContainer.interactive = false;
            if (this._offTexture && this.insetContainer['texture'] && !skipChangeTexture) {
                this.insetContainer['texture'] = this._offTexture;
                this._setTint(InteractiveWidget.HOLD_MUL_COLOR);
            } else {
                this._setTint(InteractiveWidget.DISABLE_MUL_COLOR);
            }
        }
    }

    public getContainer(): Sprite {
        return <Sprite> this.insetContainer;
    }

    public getText(): Text {
        return this._text;
    }

    onPointerPress(e: InteractionEvent) {

        super.onPointerPress(e);
        if (this.insetContainer.interactive === true) {
            this._setTint(InteractiveWidget.HOLD_MUL_COLOR);
        }
        return;
    }

    onPointerRelease(e: InteractionEvent) {

        super.onPointerRelease(e);
        if (this.insetContainer.interactive === true) {
            this._setTint(this._tintColor);
        }
        return;
    }

    onPointerExit(e: InteractionEvent) {

        super.onPointerExit(e);
        if (this.insetContainer.interactive === true) {
            this._setTint(this._tintColor);
        }
        return;
    }

    protected _setTint(tint: number) {
        if (this.insetContainer['tint']) {
            this.insetContainer['tint'] = tint;
        }
        if (this._text) {
            this._text.tint = tint;
        }
    }
}