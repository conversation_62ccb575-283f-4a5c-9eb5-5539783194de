import { Container, Graphics, Text, Sprite, } from '../pixi';
import ResourceManager from '../ResManagers/ResourceManager';
import Localization from '../Localization/Localization';

export class LoadingBar {
    private readonly _EFFECT_MOVING_SPEED = 500;

    constructor() {
        this.container = new Container();
        // #!debug
        this.container.name = this.constructor.name;

        this._loadingBackground = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_background'));
        this._loadingBackground.anchor.set(0.5);
        this.container.addChild(this._loadingBackground);

        this._clipContainer = this.container.addChild(new Container());
        this._loadinProgress = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_progress'));
        this._loadinProgress.anchor.set(0);
        this._loadinProgress.position.set(-this._loadinProgress.width / 2 + 1, -this._loadinProgress.height / 2);
        this._clipContainer.addChild(this._loadinProgress);

        this._loadingMask = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_progress_mask'));
        this._loadingMask.anchor.set(0);
        this._loadingMask.position.set(this._loadinProgress.x, this._loadinProgress.y);
        this._clipContainer.mask = this._loadingMask;
        this._clipContainer.addChild(this._loadingMask);

        this._clipStartX = this._loadinProgress.x;
        this._clipWidth = this._loadinProgress.width;

        this._loadingProgress = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_progress'));
        this._loadingProgress.anchor.set(0.5);
        this._clipContainer.addChild(this._loadingProgress);

        this._loadingProgEnd = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_progress_end'));
        this._loadingProgEnd.anchor.set(1, 0.5);
        this._clipContainer.addChild(this._loadingProgEnd);

        this._loadingEffect = new Sprite(ResourceManager.getAtlasFrame('loading_atlas', 'loading_effect'));
        this._loadingEffect.anchor.set(1, 0.5);
        this._effectStartX = -this._loadingBackground.width / 2;
        this._effectDistance = this._loadingBackground.width * 3;
        this._loadingEffect.x = this._effectStartX;
        this._clipContainer.addChild(this._loadingEffect);

        this._loadingText = new Text(`${Localization.getText('LOADING')}0%`, {
            fontFamily: 'intersemibold',
            fontSize: 25,
            fill: ['#ffffff']
        });
        this._loadingText.anchor.set(0.5);
        this._loadingText.y = 50;
        this.container.addChild(this._loadingText);
    }

    public container: Container = null;

    private _clipContainer: Container = null;
    private _loadingMask: Sprite;
    private _loadinProgress: Sprite;
    private _clipStartX: number = 0;
    private _clipWidth: number = 0;

    private _loadingBackground: Sprite = null;
    private _loadingProgress: Sprite = null;
    private _loadingProgEnd: Sprite = null;
    private _loadingText: Text = null;

    // private _gClippingEffect: Graphics;
    private _loadingEffect: Sprite = null;
    private _effectStartX: number = 0;
    private _effectDistance: number = 0;

    private _progress: number = 0;
    public get progress(): number {
        return this._progress;
    }
    public set progress(prog: number) {
        this._progress = prog;
        this._updateProgress(prog);
    }

    public update(dt: number) {
        this._loadingEffect.x += this._EFFECT_MOVING_SPEED * dt;
        if (this._loadingEffect.x > this._effectStartX + this._effectDistance) {
            this._loadingEffect.x = this._effectStartX;
        }
    }

    public reset() {
        this._clippingUpdate(0);
    }

    private _updateProgress(prog: number) {
        this._loadingText.text = `${Localization.getText('LOADING')}${prog}%`;
        this._loadingProgEnd.x = this._clipStartX + prog * this._clipWidth / 100;
        this._clippingUpdate(prog);
    }

    private _clippingUpdate(progress: number) {
        this._loadingMask.scale.x = progress / 100;
    }
}