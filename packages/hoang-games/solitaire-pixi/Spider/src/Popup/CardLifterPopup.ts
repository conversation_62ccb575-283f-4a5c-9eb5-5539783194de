import AppConstants from '../AppConstants';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import { Graphics, ITextStyle, NineSlicePlane, Rectangle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class CardLifterPopup extends PopupBase {
    private readonly _WIDTH_BG = 510;
    private readonly _HEIGHT_BG = 395;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 20,
        align: 'center',
        fill: 0x00000,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 16,
        align: 'center',
        fill: 0x00000,
    };

    private _panelBg: NineSlicePlane;
    private _titleTxt: Text;
    private _btnClose: Button;
    private _icon_check: Sprite;
    private _isCheck: boolean = false;

    constructor() {
        super();
    }

    public init() {
        this._initUI();
        this.container.visible = false;
    }

    private _initUI() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._panelBg = new NineSlicePlane(sprite, 20, 20, 20, 20);
        this._panelBg.width = this._WIDTH_BG;
        this._panelBg.height = this._HEIGHT_BG;
        this._panelBg.pivot.x = 0.5 * this._panelBg.width;
        this._panelBg.pivot.y = 0.5 * this._panelBg.height;
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = true;

        this._titleTxt = new Text(Localization.getText('CARD_LIFTER'), this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(this._panelBg.width / 2, 35);
        this._panelBg.addChild(this._titleTxt);

        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        this._btnClose = new Button({
            background: closeSprite,
        });
        this._btnClose.getContainer().anchor.set(0.5);
        this._btnClose.getContainer().scale.set(0.7);
        this._btnClose.getContainer().position.set(this._panelBg.width - closeSprite.width / 2 - 18, closeSprite.height / 2 + 18);
        this._btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        const [tW, tH] = [this._btnClose.getContainer().width * 1.7, this._btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        this._btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._panelBg.addChild(this._btnClose.getContainer());

        const content = new Text(Localization.getText('CONTENT_CARD_LIFTER'), this._TITLE_STYLE);
        content.anchor.set(0.5);
        content.position.set(this._panelBg.width / 2, this._panelBg.height / 2 - 90);
        this._panelBg.addChild(content);

        const costTxt = new Text(Localization.getText('COST_CARD_LIFTER'), this._TITLE_STYLE);
        costTxt.anchor.set(0.5);
        costTxt.position.set(this._panelBg.width / 2, this._panelBg.height / 2 - 24);
        this._panelBg.addChild(costTxt);

        const coolDownTxt = new Text(Localization.getText('COOLDOWN_CARD_LIFTER'), this._TITLE_STYLE);
        coolDownTxt.anchor.set(0.5);
        coolDownTxt.position.set(this._panelBg.width / 2, this._panelBg.height / 2 + 4);
        this._panelBg.addChild(coolDownTxt);

        const noteTxt = new Text(Localization.getText('NOTE_CARD_LIFTER'), this._VALUE_STYLE);
        noteTxt.anchor.set(0.5);
        noteTxt.position.set(this._panelBg.width / 2 + 24, this._panelBg.height / 2 + 67);
        this._panelBg.addChild(noteTxt);

        const icon = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'check_box'));
        const check_box = new Button({
            background: icon,
        });
        check_box.getContainer().anchor.set(0.5);
        check_box.getContainer().scale.set(0.8);
        check_box.getContainer().position.set(this._panelBg.width / 2 - 87, this._panelBg.height / 2 + 69);

        check_box.on('click', () => {
            this._isCheck = !this._isCheck;
            this._icon_check.visible = this._isCheck;
        });

        this._icon_check = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'icon_tick'));
        this._icon_check.anchor.set(0.5);
        check_box.getContainer().addChild(this._icon_check);
        this._icon_check.visible = false;
        this._panelBg.addChild(check_box.getContainer());

        const buttonNewGameText = new Text(Localization.getText('OK'), {
            fontFamily: 'intersemibold',
            fontSize: 23,
            fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonNewGameText.anchor.set(0.5);
        buttonNewGameText.position.set (0, -4);
        const newGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        newGame.anchor.set(0.5);
        const submitButton = new Button({
            background: newGame,
        });
        submitButton.getContainer().position.set(this._panelBg.width / 2, this._panelBg.height / 2 + 140);
        submitButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            Events.emit(UIEvents.SHOW_CARD_LIFTER_POPUP, this._isCheck);
            Events.emit(UIEvents.POWER_UP);
            this.close();
        });
        submitButton.getContainer().addChild(buttonNewGameText);
        this._panelBg.addChild(submitButton.getContainer());
    }
    public open(isHideClose: boolean = false) {
        this._btnClose.getContainer().visible = !isHideClose;
        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(50);
        super.close();
    }
}
