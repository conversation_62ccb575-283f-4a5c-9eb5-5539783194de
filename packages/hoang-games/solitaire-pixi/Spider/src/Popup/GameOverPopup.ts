import { Events } from '../Events/Events';
import AppConstants from '../AppConstants';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import { Graphics, ITextStyle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';
import { GameEvents } from '../Events/EventTypes';

export class GameOverPopup extends PopupBase {
    private readonly _TEXT_COLOR: number = 0x89efef;
    private readonly _TEXT_COLOR_2: number = 0x76aaaa;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 40,
        align: 'center',
        fill: 0x000000,
        fontWeight: 'bold',
        dropShadow: false,
        dropShadowColor: 0x00000,
        dropShadowBlur: 3,
        dropShadowAngle: Math.PI / 6,
        dropShadowDistance: 3,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 22,
        fontWeight: 'bold',
        align: 'center',
        fill: 0x00000,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 100,
        fontWeight: 'bold',
        align: 'center',
        fill: 0xffffff,
    };

    private _panelBg: Sprite;
    private _titleTxt: Text;
    private _score: Text;
    private _timer: Text;

    constructor() {
        super();
    }

    public init() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);
        this._background.interactive = true;

        this._panelBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._panelBg.anchor.set(0.5);
        // this._panelBg.position.set(0, 0);
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = true;

        this._titleTxt = new Text('No Moves Remaining', this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(0, -this._panelBg.height / 2 + 50);
        this._panelBg.addChild(this._titleTxt);

        const notifi = new Text('You can undo some steps to win the game.', this._TITLE_STYLE);
        notifi.anchor.set(0.5);
        notifi.position.set(0, -30);
        this._panelBg.addChild(notifi);

        const buttonNewGameText = new Text('New Game', {
            fontFamily: 'intersemibold',
            fontSize: 30,
            fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonNewGameText.anchor.set(0.5);

        const newGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        newGame.anchor.set(0.5);
        const submitButton = new Button({
            background: newGame,
        });
        submitButton.getContainer().position.set(-80, 87.5);
        submitButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            Events.emit(GameEvents.NEW_GAME);
            this.close();
        });
        submitButton.getContainer().addChild(buttonNewGameText);
        this._panelBg.addChild(submitButton.getContainer());

        const buttonCountinueText = new Text('Continue', {
            fontFamily: 'intersemibold',
            fontSize: 30,
            fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonCountinueText.anchor.set(0.5);

        const continueBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        continueBg.anchor.set(0.5);
        const continueButton = new Button({
            background: continueBg,
        });
        continueButton.getContainer().position.set(80, 87.5);
        continueButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        continueButton.getContainer().addChild(buttonCountinueText);
        this._panelBg.addChild(continueButton.getContainer());
        this.container.visible = false;
    }

    public open() {
        this._appear();
    }

    // public close(): void {
    //     this.setFadeoutTime(50);
    //     Events.emit(GameEvents.NEW_GAME);
    //     super.close();
    // }
}
