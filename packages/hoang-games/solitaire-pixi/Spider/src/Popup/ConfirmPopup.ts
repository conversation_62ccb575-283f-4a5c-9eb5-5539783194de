import { Graphics, Text, Sprite } from '../pixi';
import { PopupBase, TextInfo } from './PopupBase';
import * as UI from '../UI/core/UI';
import AppConstants from '../AppConstants';
import { AppState } from '../AppState';
import ResourceManager from '../ResManagers/ResourceManager';
import LoadingScene from '../Scenes/LoadingScene';
import GameConfigs from '../GameConfigs';
import { SoundManager } from '../SoundManager';
export interface RoundedRect {
    x: number;
    y: number;
    w: number;
    h: number;
    r: number;
}
export class ConfirmPopup extends PopupBase {
    constructor() {
        super();
    }

    private _infoText: Text = null;
    private _confirmButton: UI.Button;
    private _stackConfirmPopup: any[] = [];
    private _closeCallBack: Function = null;
    private _curentPriority: number = 0;

    public init(infoText: TextInfo, confirmText: string) {
        this._background = new Graphics();
        this._background.beginFill(0x000000, 0.6);
        this._background.drawRect(-AppConstants.BASE_SCREEN_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.BASE_SCREEN_WIDTH, AppConstants.MAX_CANVAS_HEIGHT);
        this._background.position.y = -AppConstants.MAX_CANVAS_MARGIN_Y;
        this.container.addChild(this._background);
        this._background.interactive = true;

        this._infoText = new Text(infoText.text, infoText.style);
        if (infoText.position) {
            this._infoText.position.copyFrom(infoText.position);
        }
        this._infoText.anchor.set(0.5);

        if (ResourceManager.getPreloadData('ui_atlas').error === null && LoadingScene.inst.getNumberOfErrors() === 0) {
            const background = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_popup'));
            background.anchor.set(0.5);
            this.container.addChild(background);
        } else {
            const background = new Graphics();
            background.beginFill(AppConstants.POPUP_BACKGROUND_COLOR, 1.0);
            background.drawRoundedRect(-AppConstants.POPUP_WIDTH / 2, -AppConstants.POPUP_HEIGHT / 2, AppConstants.POPUP_WIDTH, AppConstants.POPUP_HEIGHT, 20);
            this.container.addChild(background);
        }
        this.setFadeoutTime(150);

        this.container.zIndex = 10;
        this.container.addChild(this._infoText);
        this.initBase();
    }

    /**
     * Show a question select to screen
     * @param infoText message infomation text
     * @param confirmCallback call back funtion when close popup
     */
    public open(infoText: string, confirmText: string, confirmCallback?: Function, priority: number = 0) {
        if (this.isOpenning()) {
            //Check compare to current prio and show.
            if (priority > this._curentPriority) {
                //backup current
                this._stackConfirmPopup.push({ infoText: this._infoText.text, confirmText: this._confirmButton.getText().text, confirmCallback : this._closeCallBack, priority: this._curentPriority });

                //force to open new
                this._infoText.text = infoText;
                this._closeCallBack = confirmCallback;
                this._confirmButton.getText().text = confirmText;
                this._curentPriority = priority;
            } else {
                //stack for next popup
                this._stackConfirmPopup.push({ infoText: infoText, confirmText: confirmText, confirmCallback : confirmCallback, priority: priority });
            }
        } else {
            this._infoText.text = infoText;
            if (this._confirmButton) {
                this.container.removeChild(this._confirmButton.getContainer());
                this._confirmButton.getContainer().destroy();
            }
            this._confirmButton = this.createButton(confirmText, confirmCallback);
            this.container.addChild(this._confirmButton.getContainer());
            this._appear();
        }
    }

    stackPopShowPopup() {
        if (this._stackConfirmPopup.length) {
            //@TODO sort by prio before open new one
            const popupInfo = this._stackConfirmPopup.shift();
            this._infoText.text = popupInfo.infoText;
            this._closeCallBack = popupInfo.confirmCallback;
            this._confirmButton.getText().text = popupInfo.confirmText;
            this._curentPriority = popupInfo.priority;
        } else {
            this._confirmButton.setInteractable(false);
            this.close();
        }
    }

    createButton(text: string, callback) {
        const textPixi = new Text(text, {
            fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
            fontSize: AppConstants.POPUP_BUTTON_TXT_FONT_SIZE,
            fill: [GameConfigs.BUTTON_TEXT_COLOR]
        });
        let button;
        if (ResourceManager.getPreloadData('ui_atlas').error === null && LoadingScene.inst.getNumberOfErrors() === 0) {
            button = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_button'));
        } else {
            button = new Graphics();
            button.beginFill(0xecb263, 1.0);
            const rect = { x:-100, y: 100, w:200, h:70, r:8 };
            button.drawRoundedRect(rect.x, rect.y, rect.w, rect.h, rect.r);
            textPixi.position.set(rect.x + rect.w / 2, rect.y + rect.h / 2);
        }
        if (callback) {
            this._closeCallBack = callback;
        } else {
            this._closeCallBack = null;
        }
        const uiButton = new UI.Button({
            background: button,
            text : textPixi
        }).on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            if (this._closeCallBack) {
                this._closeCallBack();
            }
            this.stackPopShowPopup();
        });
        if (ResourceManager.getPreloadData('ui_atlas').error === null && LoadingScene.inst.getNumberOfErrors() === 0) {
            uiButton.getContainer().anchor.set(0.5);
            uiButton.getContainer().y = AppConstants.POPUP_HEIGHT * 0.3;
        }
        return uiButton;
    }
}