import { Graphics, Text, Sprite } from '../pixi';
import AppConstants from '../AppConstants';
import { BGColor, PopupBase, TextInfo } from './PopupBase';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import Localization from '../Localization/Localization';
import * as UI from '../UI/core/UI';
import ResourceManager from '../ResManagers/ResourceManager';
import GameConfigs from '../GameConfigs';

export class BlockInputPopup extends PopupBase {
    constructor() {
        super();
    }

    private _titleText: Text = null;

    /**
     * Initialize InputBlockingPopup
     * @param background set background color & alpha
     * @param textInfo set title
     */
    public init(background: BGColor, textInfo: TextInfo) {
        this._background = new Graphics();
        this._background.beginFill(background.color, background.alpha);
        this._background.drawRect(-AppConstants.BASE_SCREEN_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.BASE_SCREEN_WIDTH, AppConstants.MAX_CANVAS_HEIGHT);
        this._background.position.y = -AppConstants.MAX_CANVAS_MARGIN_Y;
        this.container.addChild(this._background);
        this._background.interactive = true;

        const bg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_popup'));
        bg.anchor.set(0.5);
        this.container.addChild(bg);

        this._titleText = new Text(textInfo.text, textInfo.style);
        if (textInfo.position) {
            this._titleText.position.copyFrom(textInfo.position);
        }
        this._titleText.anchor.set(0.5, 1);
        this.container.addChild(this._titleText);

        const textPixi = new Text(Localization.getText('RELOAD'), {
            fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
            fontSize: AppConstants.POPUP_BUTTON_TXT_FONT_SIZE,
            fill: [GameConfigs.BUTTON_TEXT_COLOR]
        });
        const confirmButton = new UI.Button({
            background: new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'ui_button')),
            text: textPixi,
        }).on('click', () => {
            location.reload();
        });
        confirmButton.getContainer().anchor.set(0.5);
        confirmButton.getContainer().y = AppConstants.POPUP_HEIGHT * 0.3;
        this.container.addChild(confirmButton.getContainer());

        this.container.zIndex = 10;
        this.initBase();
    }

    /**
     * Show popup
     * @param text set title text.
     * @param time time to show the popup. Should be display forever with -1.
     * @param callback given function should be called when close Popup.
     */
    public open(text?: string, time?: number, callback?: Function) {
        if (text !== undefined) {
            this._titleText.text = text;
        }
        this._appear();

        if (time && time > -1) {
            Scheduler.setTimeout(time, () => {
                this.close();
                if (callback) {
                    callback();
                }
            });
        }
    }
}