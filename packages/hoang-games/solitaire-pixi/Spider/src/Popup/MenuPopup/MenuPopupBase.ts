import AppConstants from '../../AppConstants';
import { Easing, Tween } from '../../core/utility/tween/Tween';
import GameConfigs from '../../GameConfigs';
import { AlignPosition } from '../../Scenes/AutoAlignContainer';
import { ScreenMetadata } from '../../Scenes/MultiResolutionHandler';
import { PopupAnimationType, PopupBase } from '../PopupBase';

export class MenuPopupBase extends PopupBase {
    public _popupPosY: number = 0;
    public _popupPosX: number = 0;

    protected _startPosY: number;
    protected _startPosX: number;

    private _isSlideShow: boolean = true;

    constructor(alignType: Partial<AlignPosition> = { vertical: 'CENTER', horizontal: 'CENTER' }, isSlice: boolean = true) {
        super(alignType);
        this._isSlideShow = isSlice;
    }

    public open() {
        this._appear();

        if (this._isSlideShow) {
            if (this.animation === PopupAnimationType.BOTTON_TO_TOP) {
                this.container.position.y = this._startPosY ? this._startPosY : AppConstants.MAX_CANVAS_HEIGHT;
            } else {
                this.container.position.x = this._startPosX ? this._startPosX : AppConstants.BASE_SCREEN_WIDTH;
            }
        } else {
            this.container.scale.set(0, 0);
        }

        this._isClosing = false;
        this.flyShow(true);
    }

    public close() {
        this.flyShow(false);
    }

    protected _onResize(screen: ScreenMetadata) {
        super._onResize(screen);
        this._popupPosY = this.container.position.y;
    }

    private _isClosing: boolean = false;
    public flyShow(isIn) {
        if (isIn) {
            if (this.onPopupOpen.length > 0) {
                this.onPopupOpen.forEach((listener) => {
                    if (listener.start) {
                        listener.start();
                    }
                });
            }
        } else {
            if (this._isClosing) {
                return;
            }
            this._isClosing = true;
            if (this.onPopupClose.length > 0) {
                this.onPopupClose.forEach((listener) => {
                    if (listener.start) {
                        listener.start();
                    }
                });
            }
        }

        const duration = GameConfigs.SHOW_POPUP_TIME;
        //const posX = isIn ? this._popupPosX : this._startPosX ? this._startPosX : AppConstants.BASE_SCREEN_WIDTH;

        if (this._isSlideShow) {
            if (this.animation === PopupAnimationType.BOTTON_TO_TOP) {
                const posY = isIn ? this._popupPosY : this._startPosY ? this._startPosY : AppConstants.MAX_CANVAS_HEIGHT;
                Tween.to(duration, this.container.position, { y: posY }, 0, () => {
                    if (!isIn) {
                        if (this.onPopupClose.length > 0) {
                            this.onPopupClose.forEach((listener) => {
                                if (listener.end) {
                                    listener.end();
                                }
                            });
                        }
                        //close
                        super.close();
                    } else {
                        if (this.onPopupOpen.length > 0) {
                            this.onPopupOpen.forEach((listener) => {
                                if (listener.end) {
                                    listener.end();
                                }
                            });
                        }
                    }
                });
            } else {
                const posX = isIn ? this._popupPosX : this._startPosX ? this._startPosX : AppConstants.BASE_SCREEN_WIDTH;
                Tween.to(duration, this.container.position, { x: posX + AppConstants.BASE_SCREEN_WIDTH / 2 }, 0, () => {
                    if (!isIn) {
                        if (this.onPopupClose.length > 0) {
                            this.onPopupClose.forEach((listener) => {
                                if (listener.end) {
                                    listener.end();
                                }
                            });
                        }
                        //close
                        super.close();
                    } else {
                        if (this.onPopupOpen.length > 0) {
                            this.onPopupOpen.forEach((listener) => {
                                if (listener.end) {
                                    listener.end();
                                }
                            });
                        }
                    }
                });
            }
        } else {
            const pScale = isIn ? 1 : 0;
            Tween.easeTo(duration, this.container.scale, { x: pScale, y: pScale }, 0, () => {
                if (!isIn) {
                    if (this.onPopupClose.length > 0) {
                        this.onPopupClose.forEach((listener) => {
                            if (listener.end) {
                                listener.end();
                            }
                        });
                    }
                    //close
                    super.close();
                } else {
                    if (this.onPopupOpen.length > 0) {
                        this.onPopupOpen.forEach((listener) => {
                            if (listener.end) {
                                listener.end();
                            }
                        });
                    }
                }
            }, null, isIn ? Easing.Back.Out : Easing.Back.In);

        }
    }
}
