import AppConstants, { SuitMode } from '../AppConstants';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import Utils from '../Utils/Utils';
import { Container, Graphics, ISize, ITextStyle, NineSlicePlane, Rectangle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class RankPopup extends PopupBase {
    private readonly _WIDTH_BG = 916;
    private readonly _HEIGHT_BG = 554;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 18,
        align: 'center',
        fill: 0x00000,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 14,
        align: 'center',
        fill: 0x00000,
    };

    private readonly _TIME_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 12,
        align: 'center',
        fill: 0xa1aabf,
    };

    private readonly _BUTTON_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 20,
        align: 'center',
        fill: 0x00000,
    };

    //rank popup
    private _rankBg: NineSlicePlane;
    private _titleRankTxt: Text;

    private _listRankScore1Suit = [];
    private _listRankScore2Suits = [];
    private _listRankTimer1Suit = [];
    private _listRankTimer2Suits = [];
    private _listRankScore4Suits = [];
    private _listRankTimer4Suits = [];
    private _isWin: boolean = false;

    //confirm popup
    private _bgConfirm: Sprite;
    private _titleConfirm: Text;
    private _contentConfirm: Text;
    private _backgroundConfirm: Graphics;

    constructor() {
        super();
    }

    public init() {
        this._initUI();
        this._initConfirm();
        this.container.visible = false;
    }

    private _initUI() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._rankBg = new NineSlicePlane(sprite, 20, 20, 20, 20);
        this._rankBg.width = this._WIDTH_BG;
        this._rankBg.height = this._HEIGHT_BG;
        this._rankBg.pivot.x = 0.5 * this._rankBg.width;
        this._rankBg.pivot.y = 0.5 * this._rankBg.height;
        this.container.addChild(this._rankBg);
        this._rankBg.interactive = false;

        this._titleRankTxt = new Text(Localization.getText('RANK'), this._MENU_TEXT_STYLE);
        this._titleRankTxt.anchor.set(0.5);
        this._titleRankTxt.position.set(this._rankBg.width / 2, 35);
        this._rankBg.addChild(this._titleRankTxt);

        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        const btnClose = new Button({
            background: closeSprite,
        });
        btnClose.getContainer().anchor.set(0.5);
        btnClose.getContainer().scale.set(0.65);
        btnClose.getContainer().position.set(this._rankBg.width - closeSprite.width / 2 - 18, closeSprite.height / 2 + 18);
        btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        const [tW, tH] = [btnClose.getContainer().width * 1.7, btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._rankBg.addChild(btnClose.getContainer());
        const resetText = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            fontWeight: 'bold',
            align: 'center',
            fill: 0x7c7c7c,
            textBaseline: 'bottom',
        });
        resetText.anchor.set(0.5);
        const btnReset = new Button({
            background: null,
        });
        const underline = new Graphics()
            .lineStyle({ color: 0x7c7c7c, width: 1 })
            .moveTo(resetText.x - resetText.width / 2, resetText.y + resetText.height / 2 - 5)
            .lineTo(resetText.x + resetText.width / 2, resetText.y + resetText.height / 2 - 5)
            .endFill();
        btnReset.getContainer().addChild(underline);
        btnReset.getContainer().width = resetText.width;
        btnReset.getContainer().addChild(resetText);
        btnReset.getContainer().position.set(closeSprite.width / 2 + 30, closeSprite.height / 2 + 18);
        btnReset.on('click', () => {
            this._openResetPanel();
            SoundManager.inst.playSfx('BUTTON_CLICK');
        });
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        btnReset.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._rankBg.addChild(btnReset.getContainer());

        const titleGame = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'spider_text'));
        titleGame.anchor.set(0.5);
        titleGame.position.set(this._rankBg.width / 2, 97);
        this._rankBg.addChild(titleGame);

        //rank content
        const bgDraw1 = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'board_rank'));
        bgDraw1.anchor.set(0.5);
        bgDraw1.position.set(this._rankBg.width / 2 - 287.5, this._rankBg.height / 2 + 47);
        this._rankBg.addChild(bgDraw1);

        const title_Draw1 = new Text(Localization.getText('1_SUIT'), this._TITLE_STYLE);
        title_Draw1.anchor.set(0.5);
        title_Draw1.position.set(0, -bgDraw1.height / 2 + 14);
        bgDraw1.addChild(title_Draw1);

        const scoreTitle1 = new Text(Localization.getText('SCORE'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -scoreTitle1.width / 2 - 8, -bgDraw1.height / 2 + 44, bgDraw1);
        this._underLine(bgDraw1.width / 2 - 12, scoreTitle1.width / 2 + 8, -bgDraw1.height / 2 + 44, bgDraw1);
        scoreTitle1.anchor.set(0.5);
        scoreTitle1.position.set(0, -bgDraw1.height / 2 + 43);
        bgDraw1.addChild(scoreTitle1);

        const speedTitle1 = new Text(Localization.getText('SPEED_RUN'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -speedTitle1.width / 2 - 8, 22, bgDraw1);
        this._underLine(bgDraw1.width / 2 - 12, speedTitle1.width / 2 + 8, 22, bgDraw1);
        speedTitle1.anchor.set(0.5);
        speedTitle1.position.set(0, 20);
        bgDraw1.addChild(speedTitle1);

        const startY = -bgDraw1.height / 2 + 62;
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw1.width / 2 - 24, 0);

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startY + 15 * i);
            this._listRankScore1Suit.push(lineUser);
            bgDraw1.addChild(lineUser);
        }

        const startYSpend = 41;
        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw1.width / 2 - 24, 0);

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startYSpend + 15 * i);
            this._listRankTimer1Suit.push(lineUser);
            bgDraw1.addChild(lineUser);
        }

        // layout right
        const bgDraw2 = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'board_rank'));
        bgDraw2.anchor.set(0.5);
        bgDraw2.position.set(this._rankBg.width / 2, this._rankBg.height / 2 + 47);
        this._rankBg.addChild(bgDraw2);

        const title_Draw2 = new Text(Localization.getText('2_SUITS'), this._TITLE_STYLE);
        title_Draw2.anchor.set(0.5);
        title_Draw2.position.set(0, -bgDraw2.height / 2 + 14);
        bgDraw2.addChild(title_Draw2);

        const scoreTitle2 = new Text(Localization.getText('SCORE'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -scoreTitle2.width / 2 - 8, -bgDraw1.height / 2 + 44, bgDraw2);
        this._underLine(bgDraw1.width / 2 - 12, scoreTitle2.width / 2 + 8, -bgDraw1.height / 2 + 44, bgDraw2);
        scoreTitle2.anchor.set(0.5);
        scoreTitle2.position.set(0, -bgDraw2.height / 2 + 43);
        bgDraw2.addChild(scoreTitle2);

        const speedTitle2 = new Text(Localization.getText('SPEED_RUN'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -speedTitle2.width / 2 - 8, 22, bgDraw2);
        this._underLine(bgDraw1.width / 2 - 12, speedTitle2.width / 2 + 8, 22, bgDraw2);
        speedTitle2.anchor.set(0.5);
        speedTitle2.position.set(0, 20);
        bgDraw2.addChild(speedTitle2);

        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw2.width / 2 - 24, 0);

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startY + 15 * i);
            this._listRankScore2Suits.push(lineUser);
            bgDraw2.addChild(lineUser);
        }

        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw2.width / 2 - 24, 0);

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startYSpend + 15 * i);
            this._listRankTimer2Suits.push(lineUser);
            bgDraw2.addChild(lineUser);
        }

        //4 suit
        const bgDraw3 = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'board_rank'));
        bgDraw3.anchor.set(0.5);
        bgDraw3.position.set(this._rankBg.width / 2 + 287.5, this._rankBg.height / 2 + 47);
        this._rankBg.addChild(bgDraw3);

        const title_Draw3 = new Text(Localization.getText('4_SUITS'), this._TITLE_STYLE);
        title_Draw3.anchor.set(0.5);
        title_Draw3.position.set(0, -bgDraw3.height / 2 + 14);
        bgDraw3.addChild(title_Draw3);

        const scoreTitle3 = new Text(Localization.getText('SCORE'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -scoreTitle3.width / 2 - 8, -bgDraw1.height / 2 + 44, bgDraw3);
        this._underLine(bgDraw1.width / 2 - 12, scoreTitle3.width / 2 + 8, -bgDraw1.height / 2 + 44, bgDraw3);
        scoreTitle3.anchor.set(0.5);
        scoreTitle3.position.set(0, -bgDraw3.height / 2 + 44);
        bgDraw3.addChild(scoreTitle3);

        const speedTitle3 = new Text(Localization.getText('SPEED_RUN'), this._VALUE_STYLE);
        this._underLine(-bgDraw1.width / 2 + 12, -speedTitle3.width / 2 - 8, 22, bgDraw3);
        this._underLine(bgDraw1.width / 2 - 12, speedTitle3.width / 2 + 8, 22, bgDraw3);
        speedTitle3.anchor.set(0.5);
        speedTitle3.position.set(0, 20);
        bgDraw3.addChild(speedTitle3);

        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw3.width / 2 - 24, 0);

            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startY + 15 * i);
            this._listRankScore4Suits.push(lineUser);
            bgDraw3.addChild(lineUser);
        }

        for (let i = 0; i < 10; i++) {
            const lineUser = new Container();
            //name
            const name = new Text('???', this._VALUE_STYLE);
            // name.anchor.set(0.5);
            name.position.set(-bgDraw1.width / 2 + 12, -10);
            //timer
            const timer = new Text('- - - -', this._TIME_STYLE);
            timer.anchor.set(0.5);

            //score
            const score = new Text('???', this._VALUE_STYLE);
            score.anchor.set(0.5);
            score.position.set(bgDraw2.width / 2 - 24, 0);
            lineUser.addChild(name);
            lineUser.addChild(timer);
            lineUser.addChild(score);
            lineUser.position.set(0, startYSpend + 15 * i);
            this._listRankTimer4Suits.push(lineUser);
            bgDraw3.addChild(lineUser);
        }
    }

    private _resetRank() {
        const defaultInfo = '???';
        const defaultTimer = '- - - -';
        for (let i = 0; i < 10; i++) {
            this._listRankScore1Suit[i].children[0].text = defaultInfo;
            this._listRankScore1Suit[i].children[1].text = defaultTimer;
            this._listRankScore1Suit[i].children[2].text = defaultInfo;

            this._listRankTimer1Suit[i].children[0].text = defaultInfo;
            this._listRankTimer1Suit[i].children[1].text = defaultTimer;
            this._listRankTimer1Suit[i].children[2].text = defaultInfo;

            this._listRankScore2Suits[i].children[0].text = defaultInfo;
            this._listRankScore2Suits[i].children[1].text = defaultTimer;
            this._listRankScore2Suits[i].children[2].text = defaultInfo;

            this._listRankTimer2Suits[i].children[0].text = defaultInfo;
            this._listRankTimer2Suits[i].children[1].text = defaultTimer;
            this._listRankTimer2Suits[i].children[2].text = defaultInfo;

            this._listRankScore4Suits[i].children[0].text = defaultInfo;
            this._listRankScore4Suits[i].children[1].text = defaultTimer;
            this._listRankScore4Suits[i].children[2].text = defaultInfo;

            this._listRankTimer4Suits[i].children[0].text = defaultInfo;
            this._listRankTimer4Suits[i].children[1].text = defaultTimer;
            this._listRankTimer4Suits[i].children[2].text = defaultInfo;
        }
    }

    public open(isWin: boolean = false) {
        this._isWin = isWin;
        const listRankScore = Utils.getScoreList(SuitMode.ONE);
        for (let i = 0; i < 10; i++) {
            if (listRankScore && listRankScore[i]) {
                this._listRankScore1Suit[i].children[0].text = listRankScore[i].name.slice(0, 8);
                this._listRankScore1Suit[i].children[1].text = listRankScore[i].date;
                this._listRankScore1Suit[i].children[2].text = listRankScore[i].score;
            }
        }
        if (listRankScore) {
            listRankScore.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            if (listRankScore && listRankScore[i]) {
                this._listRankTimer1Suit[i].children[0].text = listRankScore[i].name.slice(0, 8);
                this._listRankTimer1Suit[i].children[1].text = listRankScore[i].date;
                this._listRankTimer1Suit[i].children[2].text = Utils.toMMSS(listRankScore[i].timer);
            }
        }
        // 2 suits
        const listRankScoreDraw3 = Utils.getScoreList(SuitMode.TWO);
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                this._listRankScore2Suits[i].children[0].text = listRankScoreDraw3[i].name.slice(0, 8);
                this._listRankScore2Suits[i].children[1].text = listRankScoreDraw3[i].date;
                this._listRankScore2Suits[i].children[2].text = listRankScoreDraw3[i].score;
            }
        }

        if (listRankScoreDraw3) {
            listRankScoreDraw3.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw3 && listRankScoreDraw3[i]) {
                this._listRankTimer2Suits[i].children[0].text = listRankScoreDraw3[i].name.slice(0, 8);
                this._listRankTimer2Suits[i].children[1].text = listRankScoreDraw3[i].date;
                this._listRankTimer2Suits[i].children[2].text = Utils.toMMSS(listRankScoreDraw3[i].timer);
            }
        }

        //4 suits
        const listRankScoreDraw4 = Utils.getScoreList(SuitMode.FOUR);
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw4 && listRankScoreDraw4[i]) {
                this._listRankScore4Suits[i].children[0].text = listRankScoreDraw4[i].name.slice(0, 8);
                this._listRankScore4Suits[i].children[1].text = listRankScoreDraw4[i].date;
                this._listRankScore4Suits[i].children[2].text = listRankScoreDraw4[i].score;
            }
        }

        if (listRankScoreDraw4) {
            listRankScoreDraw4.sort((a, b) => {
                return a.timer - b.timer;
            });
        }
        for (let i = 0; i < 10; i++) {
            if (listRankScoreDraw4 && listRankScoreDraw4[i]) {
                this._listRankTimer4Suits[i].children[0].text = listRankScoreDraw4[i].name.slice(0, 8);
                this._listRankTimer4Suits[i].children[1].text = listRankScoreDraw4[i].date;
                this._listRankTimer4Suits[i].children[2].text = Utils.toMMSS(listRankScoreDraw4[i].timer);
            }
        }

        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(50);
        if (this._isWin) {
            Events.emit(UIEvents.SHOW_CHOOSE_GAME);
        }
        super.close();
    }

    private _initConfirm() {
        this._backgroundConfirm = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._backgroundConfirm.visible = false;
        this._backgroundConfirm.interactive = true;
        this.container.addChild(this._backgroundConfirm);

        this._bgConfirm = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._bgConfirm.anchor.set(0.5);
        this._bgConfirm.interactive = true;
        this.container.addChild(this._bgConfirm);

        const size: ISize = this._bgConfirm;
        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        const btnClose = new Button({
            background: closeSprite,
        });
        btnClose.getContainer().anchor.set(0.5);
        btnClose.getContainer().scale.set(0.65);
        btnClose.getContainer().position.set(size.width / 2 - closeSprite.width / 2 - 18, -size.height / 2 + closeSprite.height / 2 + 18);
        btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        const [tW, tH] = [btnClose.getContainer().width * 1.7, btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + btnClose.getContainer().x, -tH / 2 + btnClose.getContainer().y, tW, tH).endFill());
        btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._bgConfirm.addChild(btnClose.getContainer());

        this._titleConfirm = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 30,
            align: 'center',
            fill: 0x00000,
        });
        this._titleConfirm.anchor.set(0.5);
        this._titleConfirm.position.set(0, -this._bgConfirm.height / 2 + 35);
        this._bgConfirm.addChild(this._titleConfirm);

        this._contentConfirm = new Text('', this._BUTTON_STYLE);
        this._contentConfirm.anchor.set(0.5);
        this._contentConfirm.position.set(0, -15);
        this._bgConfirm.addChild(this._contentConfirm);

        this._bgConfirm.visible = false;

        const buttonCancelText = new Text(Localization.getText('CANCEL'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        buttonCancelText.anchor.set(0.5);
        buttonCancelText.y = -4;

        const cancel_bg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        cancel_bg.anchor.set(0.5);
        const cancelButton = new Button({
            background: cancel_bg,
        });
        cancelButton.getContainer().position.set(-70, 85);
        cancelButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        cancelButton.getContainer().addChild(buttonCancelText);
        this._bgConfirm.addChild(cancelButton.getContainer());

        const resetText = new Text(Localization.getText('RESET'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        resetText.anchor.set(0.5);
        resetText.y = -4;

        const resetBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'btn_red'));
        resetBg.anchor.set(0.5);
        const resetButton = new Button({
            background: resetBg,
        });
        resetButton.getContainer().position.set(70, 85);
        resetButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            localStorage.removeItem(AppConstants.GAME_NAME);
            this._resetRank();
            this._backgroundConfirm.visible = false;
            Utils._fadeOut(this._bgConfirm, 150);
        });
        resetButton.getContainer().addChild(resetText);
        this._bgConfirm.addChild(resetButton.getContainer());
    }

    private _openResetPanel() {
        this._titleConfirm.text = Localization.getText('RESET');
        this._contentConfirm.text = Localization.getText('RESET_CONTENT');
        this._backgroundConfirm.visible = true;
        Utils._fadeIn(this._bgConfirm, 150);
    }

    private _underLine(posStart, posEnd, y, target) {
        const line = new Graphics().lineStyle({ color: 0xd2c6c6, width: 1 }).moveTo(posStart, y).lineTo(posEnd, y).endFill();
        target.addChild(line);
    }
}
