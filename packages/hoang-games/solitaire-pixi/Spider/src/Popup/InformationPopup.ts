import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import ResourceManager from '../ResManagers/ResourceManager';
import { Container, NineSlicePlane, Sprite, Text } from '../pixi';
import Localization from '../Localization/Localization';

export class InformationPopup extends Container {
    private readonly _WIDTH_BG = 646;
    private readonly _HEIGHT_BG = 101;
    private _panelBg: NineSlicePlane;
    private _content: Text;
    public isShowing: boolean = false;

    constructor() {
        super();

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._panelBg = new NineSlicePlane(sprite, 10, 10, 10, 10);
        this._panelBg.width = this._WIDTH_BG;
        this._panelBg.height = this._HEIGHT_BG;
        this._panelBg.pivot.x = 0.5 * this._panelBg.width;
        this._panelBg.pivot.y = 0.5 * this._panelBg.height;
        this._panelBg.position.set(200, 345);
        this.addChild(this._panelBg);

        const cancelText = new Text(Localization.getText('CLOSE'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            align: 'center',
            fill: 0xffffff,
        });
        cancelText.anchor.set(0.5);
        cancelText.y = -4;

        const cancel_bg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        cancel_bg.anchor.set(0.5);
        cancel_bg.scale.set(0.85);
        const cancelButton = new Button({
            background: cancel_bg,
        });
        cancelButton.getContainer().position.set(this._panelBg.width - 75, this._panelBg.height / 2 + 21.5);
        cancelButton.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        cancelButton.getContainer().addChild(cancelText);
        this._panelBg.addChild(cancelButton.getContainer());
        this._content = new Text('', {
            fontFamily: 'intersemibold',
            fontSize: 18,
            align: 'center',
            fill: 0x00000,
        });
        this._content.position.set(30, this._panelBg.height / 2 - 38);
        this._panelBg.addChild(this._content);
        this.visible = false;
    }

    public open(content: string) {
        this._content.text = content;
        this.alpha = 1;
        this.visible = true;
        this.isShowing = true;
    }

    public close() {
        this.visible = false;
        this.isShowing = false;
    }
}
