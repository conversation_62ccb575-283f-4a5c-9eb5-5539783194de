import AppConstants from '../AppConstants';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import { Graphics, ITextStyle, NineSlicePlane, Rectangle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class NewGamePopup extends PopupBase {
    private readonly _HEIGHT_BG = 405;
    private readonly _WIDTH_BG = 728;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fontWeight:'bold'
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0xffffff,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 60,
        align: 'center',
        fill: 0xffffff,
    };

    private _panelBg: NineSlicePlane;
    private _titleTxt: Text;
    private _btnClose: Button;

    constructor() {
        super();
    }

    public init() {
        this._initUI();
        this.container.visible = false;
    }

    private _initUI() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.MAX_CANVAS_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);

        const sprite = ResourceManager.getAtlasFrame('ui_atlas', 'win_popup');
        this._panelBg = new NineSlicePlane(sprite, 20, 20, 20, 20);
        this._panelBg.width = this._WIDTH_BG;
        this._panelBg.height = this._HEIGHT_BG;
        this._panelBg.pivot.x = 0.5 * this._panelBg.width;
        this._panelBg.pivot.y = 0.5 * this._panelBg.height;
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = true;

        this._titleTxt = new Text(Localization.getText('CHOOSE_GAME'), this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(this._panelBg.width / 2, 40);
        this._panelBg.addChild(this._titleTxt);

        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        this._btnClose = new Button({
            background: closeSprite,
        });
        this._btnClose.getContainer().anchor.set(0.5);
        this._btnClose.getContainer().scale.set(0.7);
        this._btnClose.getContainer().position.set(this._panelBg.width - closeSprite.width / 2 - 18, closeSprite.height / 2 + 18);
        this._btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        const [tW, tH] = [this._btnClose.getContainer().width * 1.7, this._btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        this._btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._panelBg.addChild(this._btnClose.getContainer());
        const listSuit = [1, 2, 4];
        for (let i = 0; i < listSuit.length; i++) {
            const drawTitle = new Text(Localization.getText('SUIT'), this._TITLE_STYLE);
            drawTitle.text = listSuit[i] < 2 ? Localization.getText('SUIT') : Localization.getText('SUITS');
            const numberDraw = new Text(`${listSuit[i]}`, this._VALUE_STYLE);
            drawTitle.position.set(0, 34);
            numberDraw.position.set(0, -20);
            drawTitle.anchor.set(0.5);
            numberDraw.anchor.set(0.5);
            const drawBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'bg_draw'));
            drawBg.anchor.set(0.5);
            drawBg.addChild(drawTitle);
            drawBg.addChild(numberDraw);
            const drawButton = new Button({
                background: drawBg,
            });
            drawButton.getContainer().position.set(this._panelBg.width / 2 - drawBg.width - 20 + (drawBg.width + 18) * i, this._panelBg.height / 2 + 26);
            drawButton.on('click', () => {
                Events.emit(UIEvents.CHOOSE_DRAW_GAME, listSuit[i]);
                SoundManager.inst.playSfx('BUTTON_CLICK');
                this.close();
            });
            this._panelBg.addChild(drawButton.getContainer());
        }
    }
    public open(isHideClose: boolean = false) {
        this._btnClose.getContainer().visible = !isHideClose;
        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(50);
        super.close();
    }
}
