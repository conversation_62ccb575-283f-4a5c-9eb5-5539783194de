import AppConstants from '../AppConstants';
import { Graphics } from '../pixi';
import { PopupBase } from './PopupBase';

export class BlockInputEvent extends PopupBase {
    constructor() {
        super();
    }

    public init() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.01)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);
        this.container.visible = false;
    }

    public open() {
        this._appear();
    }

    public close(): void {
        super.close();
    }
}