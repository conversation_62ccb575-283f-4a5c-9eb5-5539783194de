import { Graphics, InteractionEvent } from '../pixi';
import AppConstants from '../AppConstants';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import { Keyboard } from '../Utils/Keyboard';
import { BGColor, PopupBase } from './PopupBase';


/**
 * Simply show information to user. The popup will be closed after user click on screen.
 */
export class AsyncTimePopup extends PopupBase {
    constructor() {
        super();
    }

    protected _isTapped: boolean = false;
    protected _newCloseTime: number = 0;
    private _keyboard: Keyboard = null;

    /**
     * Initialize NoticePopup
     * @param background set background color & alpha
     * @param textInfo set title
     */
    public init(background: BGColor) {
        this._background = new Graphics();
        this._background.beginFill(background.color, background.alpha);
        this._background.drawRect(-AppConstants.BASE_SCREEN_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.BASE_SCREEN_WIDTH, AppConstants.MAX_CANVAS_HEIGHT);
        this._background.position.y = -AppConstants.MAX_CANVAS_MARGIN_Y;
        this._background.interactive = true;
        this.container.addChild(this._background);

        this.initBase();

        this._background.addListener('pointerup', this.onPointerUp.bind(this));

        this._keyboard = new Keyboard();
        this._keyboard.addKey('Space', this.onKeyDown.bind(this));
    }

    /**
     * Show popup
     * @param time wait time, after that the Popup will return
     */
    public async open(time: number, callback?: Function): Promise<any> {
        this._appear();
        let closeTimer = '';
        let checkCloseInLastWaitingTime: NodeJS.Timeout = null;
        this._isTapped = false;
        this._newCloseTime = 0;

        return new Promise<void>((resolve, reject) => {
            const closeTask = () => {
                clearInterval(checkCloseInLastWaitingTime);
                this.close();
                if (callback) {
                    callback();
                }
            };

            // handle for the popup allows to tap (to switch status) such as big win popup
            const checkNewCloseTime = setInterval(() => {
                if (this._newCloseTime > 0) {
                    // console.log('NEW CLOSE TIME POPUP was set');
                    clearInterval(checkNewCloseTime);
                    Scheduler.clearTimeout(closeTimer);
                    // set new timer to close
                    closeTimer = Scheduler.setTimeout(this._newCloseTime, () => {
                        closeTask();
                        resolve();
                    });

                    // check if tapped to close popup during the last waiting time
                    checkCloseInLastWaitingTime = setInterval(() => {
                        if (this._isTapped) {
                            // console.log('Tapped in last waiting time');
                            Scheduler.clearTimeout(closeTimer);
                            closeTask();
                            resolve();
                        }
                    }, 50);
                }
            }, 50);

            closeTimer = Scheduler.setTimeout(time, () => {
                clearInterval(checkNewCloseTime);
                closeTask();
                resolve();
            });
        });
    }

    public onPointerUp(event: InteractionEvent) {
        this._isTapped = true;
    }

    public isTapped(): boolean {
        return this._isTapped;
    }

    public resetTap() {
        this._isTapped = false;
    }

    public setNewCloseTime(newTime?: number) {
        this._newCloseTime = newTime;
    }

    public onKeyDown(event: KeyboardEvent) {
        switch (event.code) {
            case 'Space':
                this._isTapped = true;
                break;
        }
    }
}