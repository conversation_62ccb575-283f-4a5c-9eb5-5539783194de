import { TextStyle, Point, ISize } from '../pixi';
import AppConstants from '../AppConstants';
import { ConfirmPopup } from './ConfirmPopup';
import { QuestionPopup } from './NotificationPopup/QuestionPopup';

import { LoadingResourcePopup } from './MenuPopup/LoadingResourcePopup';
import { MultiResolutionHandler } from '../Scenes/MultiResolutionHandler';
import { NewGamePopup } from './NewGamePopup';
import { InputNamePopup } from './InputNamePopup';
import { WinGamePopup } from './WinPopup';
import { GameOverPopup } from './GameOverPopup';
import { RankPopup } from './RankPopup';
import { CardLifterPopup } from './CardLifterPopup';
import { InformationPopup } from './InformationPopup';
import { BlockInputEvent } from './BlockTouchEvent';
import UIManager from '../UI/UIManager';
import { RulesPopup } from './MenuPopup/RulesPopup';

export class PopupManager {
    //MenuPopup
    public static rulesPopup: RulesPopup = null;
    //NotificationPopup
    public static questionPopup: QuestionPopup = null;

    public static loadingResourcePopup: LoadingResourcePopup = null;

    public static newGamePopup: NewGamePopup = null;
    public static inputNamePopup: InputNamePopup = null;
    public static winPopup: WinGamePopup = null;
    public static gameOverPopup: GameOverPopup = null;
    public static rankPopup: RankPopup = null;
    public static cardLifterPopup: CardLifterPopup = null;
    public static informationPopup: InformationPopup = null;
    public static blockInputEvent: BlockInputEvent = null;

    public static confirmPopup: ConfirmPopup = null;
    public static getConfirmPopup(): ConfirmPopup {
        if (!PopupManager.confirmPopup) {
            this._initConfirmPopup();
        }
        return PopupManager.confirmPopup;
    }

    public static initializePopups() {
        PopupManager._initMenuPopups();
        PopupManager._initQuestionPopup();
        PopupManager._initLoadingResourcePopup();
        PopupManager._initNewGamePopup();
        PopupManager._initInputNamePopup();
        PopupManager._initWinPopup();
        PopupManager._initGameOverPopup();
        PopupManager._initRankPopup();
        PopupManager._initInformationPopup();
        // top of all others popup
        PopupManager._initConfirmPopup();
        PopupManager._initBlockInputEvent();

        // TODO: find other solution to avoid recall resize event, to align all popups
        MultiResolutionHandler.inst.onResize();
    }

    public static onPause() {}

    public static onResume() {}

    public static onVisibilityChange(isVisible: boolean) {
        if (isVisible) {
            this.onResume();
        } else {
            this.onPause();
        }
    }

    private static _initConfirmPopup() {
        if (!this.confirmPopup) {
            this.confirmPopup = new ConfirmPopup();
            this.confirmPopup.container.zIndex = 1; //make confirm on top
            this.confirmPopup.init(
                {
                    text: '',
                    position: new Point(0, -50),
                    style: new TextStyle({
                        fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                        fontSize: AppConstants.POPUP_TXT_FONT_SIZE,
                        fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                        fill: '0xFFFFFF',
                        //stroke: '#4a1850',
                        //strokeThickness: 5,
                        wordWrap: true,
                        wordWrapWidth: 440,
                        align: 'center'
                    })
                }, 'Ok');
        }
    }

    private static _initInformationPopup() {
        this.informationPopup = new InformationPopup();
        this.informationPopup.position.set(AppConstants.BASE_SCREEN_WIDTH / 2, AppConstants.BASE_SCREEN_HEIGHT / 2 - 100);
        UIManager.inst.popupContainer.addChild(this.informationPopup);
    }

    private static _initMenuPopups() {
        this.rulesPopup = new RulesPopup();
        this.rulesPopup.init();

        this.cardLifterPopup = new CardLifterPopup();
        this.cardLifterPopup.init();
    }

    private static _initLoadingResourcePopup() {
        this.loadingResourcePopup = new LoadingResourcePopup();
        this.loadingResourcePopup.init({
            color: 0x000000,
            alpha: 0.75
        }, {
            text: '',
            style: new TextStyle({
                fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                fontSize: AppConstants.POPUP_TXT_FONT_SIZE + 5,
                fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                fill: '0xffc133', // gradient
                //stroke: '#4a1850',
                //strokeThickness: 5,
                wordWrap: true,
                wordWrapWidth: 440,
                align: 'center'
            }),
            position: new Point(0, 50)
        });
    }

    private static _initNewGamePopup() {
        this.newGamePopup = new NewGamePopup();
        this.newGamePopup.init();
    }

    private static _initInputNamePopup() {
        this.inputNamePopup = new InputNamePopup();
        this.inputNamePopup.init();
    }

    private static _initWinPopup() {
        this.winPopup = new WinGamePopup();
        this.winPopup.init(true, 0, 0);
    }

    private static _initRankPopup() {
        this.rankPopup = new RankPopup();
        this.rankPopup.init();
    }

    private static _initGameOverPopup() {
        this.gameOverPopup = new GameOverPopup();
        this.gameOverPopup.init();
    }

    private static _initBlockInputEvent() {
        this.blockInputEvent = new BlockInputEvent();
        this.blockInputEvent.init();
    }

    private static _initQuestionPopup() {
        this.questionPopup = new QuestionPopup();
        this.questionPopup.init(
            {
                text: '',
                position: new Point(0, -50),
                style: new TextStyle({
                    fontFamily: AppConstants.POPUP_CUSTOM_TEXT_FONT,
                    fontSize: AppConstants.POPUP_TXT_FONT_SIZE,
                    fontWeight: AppConstants.POPUP_TXT_FONT_WEIGHT,
                    fill: '0xFFFFFF',
                    //stroke: '#4a1850',
                    //strokeThickness: 5,
                    wordWrap: true,
                    wordWrapWidth: 440,
                    align: 'center',
                    leading: 10
                })
            }, 'YES', 'NO');
    }

    public static isShowingInformationPopup() {
        switch (true) {
            case this.informationPopup.isShowing:
                return true;
            default:
                return false;
        }
    }
}
