import { TextInput } from '../UI/TextInput';
import AppConstants, { SuitMode } from '../AppConstants';
import { UIEvents } from '../Events/EventTypes';
import { Events } from '../Events/Events';
import Localization from '../Localization/Localization';
import ResourceManager from '../ResManagers/ResourceManager';
import { SoundManager } from '../SoundManager';
import { Button } from '../UI/core/Button';
import Utils from '../Utils/Utils';
import { Graphics, ITextStyle, Rectangle, Sprite, Text } from '../pixi';
import { PopupBase } from './PopupBase';

export class InputNamePopup extends PopupBase {
    private readonly _TEXT_COLOR: number = 0x89efef;
    private readonly _TEXT_COLOR_2: number = 0x76aaaa;
    private readonly _MENU_TEXT_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 30,
        align: 'center',
        fill: 0x000000,
    };
    private readonly _TITLE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 20,
        align: 'center',
        fill: 0x00000,
    };
    private readonly _VALUE_STYLE: Partial<ITextStyle> = {
        fontFamily: 'intersemibold',
        fontSize: 100,
        align: 'center',
        fill: 0xffffff,
    };

    private _panelBg: Sprite;
    private _titleTxt: Text;
    private _btnClose: Button;
    private _nameInput: TextInput;
    private _currentText: string = '';

    private _timer: number = 0;
    private _score: number = 0;
    private _date: string = '';
    private _mode: SuitMode = SuitMode.ONE;

    constructor() {
        super();
    }

    public init() {
        this._initUI();
        this.container.visible = false;
    }

    private _initUI() {
        this._background = new Graphics()
            .beginFill(0x000000, 0.3)
            .drawRect(-AppConstants.MAX_CANVAS_WIDTH / 2, -AppConstants.BASE_SCREEN_HEIGHT / 2, AppConstants.MAX_CANVAS_WIDTH, AppConstants.MAX_CANVAS_HEIGHT)
            .endFill();
        this._background.interactive = true;
        this.container.addChild(this._background);
        this._background.interactive = true;

        this._panelBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'win_popup'));
        this._panelBg.anchor.set(0.5);
        // this._panelBg.position.set(0, 0);
        this.container.addChild(this._panelBg);
        this._panelBg.interactive = false;

        this._titleTxt = new Text(Localization.getText('NEW_HIGHSCORE'), this._MENU_TEXT_STYLE);
        this._titleTxt.anchor.set(0.5);
        this._titleTxt.position.set(0, -this._panelBg.height / 2 + 35);
        this._panelBg.addChild(this._titleTxt);

        const closeSprite = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'close'));
        this._btnClose = new Button({
            background: closeSprite,
        });
        this._btnClose.getContainer().anchor.set(0.5);
        this._btnClose.getContainer().scale.set(0.7);
        this._btnClose.getContainer().position.set(this._panelBg.width / 2 - closeSprite.width / 2 - 18, -this._panelBg.height / 2 + closeSprite.height / 2 + 18);
        this._btnClose.on('click', () => {
            SoundManager.inst.playSfx('BUTTON_CLICK');
            this.close();
        });
        const [tW, tH] = [this._btnClose.getContainer().width * 1.7, this._btnClose.getContainer().height * 1.7];
        // this._panelBg.addChild(new Graphics().beginFill(0x8bc5ff, 0.3).drawRect(-tW / 2 + this._btnClose.getContainer().x, -tH / 2 + this._btnClose.getContainer().y, tW, tH).endFill());
        this._btnClose.getContainer().hitArea = new Rectangle(-tW / 2, -tH / 2, tW, tH);
        this._panelBg.addChild(this._btnClose.getContainer());

        const iputNameText = new Text(Localization.getText('INPUT_NAME'), this._TITLE_STYLE);
        iputNameText.anchor.set(0.5);
        iputNameText.position.set(0, -45);
        this._panelBg.addChild(iputNameText);

        const bgInputName = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'input_name'));
        bgInputName.anchor.set(0.5);
        bgInputName.scale.set(0.8);
        bgInputName.position.set(0, 0);
        this._panelBg.addChild(bgInputName);
        this._nameInput = new TextInput({
            input: {
                fontFamily: 'intersemibold',
                fontSize: '25px',
                padding: '1px',
                width: '300px',
                color: '#5cc6c6',
                align: 'center',
            },
        }).on('input', (data) => {
            this._currentText = data;
        });

        this._nameInput.text = '';
        this._nameInput.position.set(0, 0);
        this._nameInput.pivot.x = this._nameInput.width / 2;
        this._nameInput.pivot.y = this._nameInput.height / 2;
        bgInputName.addChild(this._nameInput);

        const submitText = new Text(Localization.getText('SUBMIT'), {
            fontFamily: 'intersemibold',
            fontSize: 20,
            //fontWeight: 'bold',
            align: 'center',
            fill: 0xffffff,
        });
        submitText.anchor.set(0.5);
        submitText.y = -4;

        const submitBg = new Sprite(ResourceManager.getAtlasFrame('ui_atlas', 'button'));
        submitBg.anchor.set(0.5);
        const submitButton = new Button({
            background: submitBg,
        });
        submitButton.getContainer().position.set(0, 87.5);
        submitButton.on('click', () => {
            if (this._currentText && this._currentText.trim() !== '') {
                SoundManager.inst.playSfx('BUTTON_CLICK');
                console.log(this._currentText, this._score, this._timer, this._date);
                Utils.saveNewScore(this._score, this._currentText, this._mode, this._date, this._timer);
                Events.emit(UIEvents.ADD_INFO, this._currentText);
                this.close();
            }
        });
        submitButton.getContainer().addChild(submitText);
        this._panelBg.addChild(submitButton.getContainer());
    }

    public open(score: number, timer: number, mode: SuitMode, date: string) {
        (this._score = score), (this._timer = timer);
        this._date = date;
        this._mode = mode;
        this._appear();
    }

    public close(): void {
        this.setFadeoutTime(10);
        super.close();
    }
}
