import { ILoaderResource, Loader, Texture, utils } from '../pixi';
import { MainApp } from '../main';
import FontFaceObserver from 'fontfaceobserver';
import { ZipLoader } from './ZipLoader';

const assetFolder = process.env.ASSET_FOLDER;
export type LoaderResourceStorage = utils.Dict<ILoaderResource>;

const PACK_NAME: string[] = ['common'];
interface Asset {
    name: string;
    data: any;
}
export interface GetAssetRequest {
    owner: any;
    assets: Asset[];
    callback: Function;
}

export default class ResourceManager {
    public static _getAssetRequests: GetAssetRequest[] = [];

    private static _lazyResourceConfig: any = null;
    private static _initialized: boolean = false;

    public static init() {
        this._lazyResourceConfig = ResourceManager.getJsonData('resource-cfg');
        if (!this._lazyResourceConfig) {
            console.error('No Resource Config');
            return;
        }
        this._initialized = true;
    }

    public static loadWebFonts(): Promise<void> {
        if (!this._initialized) {
            console.error('Cant load fonts before initialization!');
            return;
        }
        return new Promise<void>((resolve) => {
            const fonts = this._lazyResourceConfig?.FontFaces;
            const total = fonts.length;
            let finish = 0;
            const checkEnd = (err?: unknown) => {
                if (++finish >= total) {
                    resolve();
                }
            };
            for (let i = 0; i < total; i++) {
                const font = fonts[i];
                const urls = font.urls.map((path) => `${assetFolder}/${path}`);
                this._loadRawFont(font.name, urls, font.weight, font.style)
                    .then(() => {
                        checkEnd();
                    })
                    .catch((err) => {
                        console.error(`Load failed: ${font.name}`);
                        checkEnd(err);
                    });
            }
        });
    }

    public static lazyLoadResourceByPack(bundle: 'Loading' | 'PreGame' | 'InGame', pack?: string) {
        if (Object.keys(this._lazyResourceConfig).includes(bundle.toString())) {
            const resourceBundle = this._lazyResourceConfig[bundle];
            if (resourceBundle) {
                //If have a pack need to load first
                if (pack) {
                    const priorityPackData = resourceBundle[pack];
                    if (priorityPackData) {
                        priorityPackData.forEach((res) => {
                            const metadata = res.type === 'Spine' && res?.atlasFile ? { metadata: { spineAtlasFile: assetFolder + '/' + res.atlasFile } } : null;
                            if (res?.name?.length > 0) {
                                //@ts-ignore
                                this._getLoader().add(res.name, assetFolder + '/' + res.path + (res.type === 'Spine' ? process.env.SPINE_EXT : ''), metadata);
                            } else {
                                //@ts-ignore
                                this._getLoader().add(assetFolder + '/' + res.path + (res.type === 'Spine' ? process.env.SPINE_EXT : ''), metadata);
                            }
                        });
                    }
                }
            }
        }

        this._getLoader().load((loader, resources) => {
        });
    }

    public static async loadZip(url: string): Promise<utils.Dict<ILoaderResource>> {
        const loader = MainApp.inst.app.loader;
        return ZipLoader.loadFromUrl(loader, url);
    }

    /**
     * Call lazy loading resources
     * @param bundle bundle name
     * @param exceptPack pack name excluded from the list
     */
    public static lazyLoadResource(bundle: 'Loading' | 'PreGame' | 'InGame', exceptPack?: string) {
        // console.warn(`[ASSET] Load Resources: ${bundle}`);
        if (Object.keys(this._lazyResourceConfig).includes(bundle.toString())) {
            const resourceBundle = this._lazyResourceConfig[bundle];
            if (resourceBundle) {
                for (let i = 0; i < PACK_NAME.length; ++i) {
                    //Skip priority pack sine we add it before
                    const packName = PACK_NAME[i];
                    if (exceptPack && exceptPack === packName) {
                        continue;
                    }

                    const dataPack = resourceBundle[packName];
                    if (dataPack && dataPack.length > 0) {
                        dataPack.forEach((res) => {
                            const metadata = res.type === 'Spine' && res?.atlasFile ? { metadata: { spineAtlasFile: assetFolder + '/' + res.atlasFile } } : null;
                            if (res?.name?.length > 0) {
                                //@ts-ignore
                                this._getLoader().add(res.name, assetFolder + '/' + res.path + (res.type === 'Spine' ? process.env.SPINE_EXT : ''), metadata);
                            } else {
                                //@ts-ignore
                                this._getLoader().add(assetFolder + '/' + res.path + (res.type === 'Spine' ? process.env.SPINE_EXT : ''), metadata);
                            }
                        });
                    }
                }
            }
        }

        this._getLoader().load((loader, resources) => {
        });
    }

    public static async loadLoadingResources(): Promise<Partial<Record<string, any>>> {
        return new Promise((resolve) => {
            this._getLoader()
                .add('loading_atlas', assetFolder + '/Atlas/loading_atlas.json')
                .add('ui_atlas', assetFolder + '/Atlas/ui_atlas.json')
                .add('sound-cfg', assetFolder + '/Sound/spcf.json')
                .add('resource-cfg', assetFolder + '/resources.json')
                .load((loader, resources) => {
                    resolve(resources);
                });
        });
    }

    public static async preloadResources(): Promise<Partial<Record<string, any>>> {
        return new Promise((resolve) => {
            const lang = MainApp.inst.language;
            this._getLoader()
                .add('splash-sprite', assetFolder + '/Game/game_bg.jpg')
                .add(lang, `${assetFolder}/Language/${lang}.json`)
                .load((loader, resources) => {
                    resolve(resources);
                });
        });
    }

    private static _loadRawFont(name: string, urls: string[], weight: number = 700, style: string = 'normal') {
        const fontTag = document.querySelector(`style#${name}`);
        if (!fontTag) {
            const element = document.createElement('style');
            let innerText = `@font-face{font-family:'${name}';src:`;
            urls.forEach((fontUrl, idx, arr) => {
                if (idx > 0 && idx < arr.length) {
                    innerText += ',';
                }
                const fontVer = fontUrl.endsWith('woff2') ? '2' : '';
                innerText += `url(\'${fontUrl}\') format(\'woff${fontVer}\')`;
            });
            innerText += `;font-weight:${weight};font-style:${style};}`;
            element.id = name;
            element.innerText = innerText;
            document.head.append(element);
        }

        const fontObserver = new FontFaceObserver(name);
        return fontObserver.load('just test string', 1000);
    }

    public static getTexture(name: string): Texture {
        const result = this._getResourceLoader(name)?.texture;
        if (!result) {
            console.error(`Missing texture '${name}'`);
        }
        return result;
    }

    public static getJsonData(name: string): any {
        const result = this._getResourceLoader(name)?.data;
        if (!result) {
            console.error(`Missing res: '${name}'`);
        }
        return result;
    }

    public static getPreloadData(name: string) {
        const result = this._getResourceLoader(name);
        if (!result) {
            console.error(`Missing res: '${name}'`);
        }
        return result;
    }

    public static getAtlasFrame(atlas: string, frame: string) {
        const result = this._getResourceLoader(atlas).textures[frame];
        if (!result) {
            console.error(`Missing ${atlas}['${frame}']`);
        }
        return result;
    }

    public static isResourceAvailable(name: string): boolean {
        const resource = this._getResourceLoader(name);
        return resource !== undefined && resource !== null;
    }

    public static getTextureAsync(name: string, _class: any): Promise<Texture> {
        return new Promise((resolve) => {
            if (this._getResourceLoader(name) && this._getResourceLoader(name).texture) {
                resolve(this._getResourceLoader(name).texture);
            } else {
                this._getAssetRequests.push({
                    owner: _class,
                    assets: [{ name: name, data: undefined }],
                    callback: (resource) => {
                        resolve(resource[0].texture);
                    },
                });
            }
        });
    }

    public static getAtlasAsync(atlas: string, _class: any): Promise<any> {
        return new Promise((resolve) => {
            const atlasData = this._getResourceLoader(atlas);
            if (atlasData?.data && atlasData?.textures) {
                resolve(atlasData.textures);
            } else {
                this._getAssetRequests.push({
                    owner: _class,
                    assets: [{ name: atlas, data: undefined }],
                    callback: (resources) => {
                        resolve(resources[0].textures);
                    },
                });
            }
        });
    }

    public static getMultiAtlasAsync(atlas: string[], _class: any): Promise<any[]> {
        return new Promise((resolve) => {
            const result: any[] = [];
            let missingResource: boolean = false;
            for (let i = 0; i < atlas.length; ++i) {
                if (this._getResourceLoader(atlas[i])?.data) {
                    result.push(this._getResourceLoader(atlas[i]));
                } else {
                    missingResource = true;
                    break;
                }
            }
            if (!missingResource) {
                resolve(result);
            } else {
                const assets: Asset[] = [];
                for (let i = 0; i < atlas.length; ++i) {
                    assets.push({
                        name: atlas[i],
                        data: this._getResourceLoader(atlas[i]) ? this._getResourceLoader(atlas[i]) : undefined,
                    });
                }
                this._getAssetRequests.push({
                    owner: _class,
                    assets: assets,
                    callback: (resources) => {
                        resolve(resources);
                    },
                });
            }
        });
    }

    /* public static getMultiSpinesAsync(spine: string[], _class: any): Promise<any[]> {
        return new Promise((resolve) => {
            const result: any[] = [];
            let missingResource: boolean = false;
            for (let i = 0; i < spine.length; ++i) {
                if (this._getResourceLoader(spine[i])?.spineData) {
                    result.push(this._getResourceLoader(spine[i]));
                } else {
                    missingResource = true;
                    break;
                }
            }
            if (!missingResource) {
                resolve(result);
            } else {
                const assets: Asset[] = [];
                for (let i = 0; i < spine.length; ++i) {
                    assets.push({
                        name: spine[i],
                        data: this._getResourceLoader(spine[i])?.spineData ? this._getResourceLoader(spine[i]) : undefined,
                    });
                }
                this._getAssetRequests.push({
                    owner: _class,
                    assets: assets,
                    callback: (resources) => {
                        resolve(resources);
                    },
                });
            }
        });
    } */

    /* public static getSpineAsync(spine: string, _class: any): Promise<any> {
        return new Promise((resolve) => {
            if (this._getResourceLoader(spine)?.spineData) {
                resolve(this._getResourceLoader(spine));
            } else {
                this._getAssetRequests.push({
                    owner: _class,
                    assets: [{ name: spine, data: undefined }],
                    callback: (resource) => {
                        resolve(resource[0]);
                    },
                });
            }
        });
    } */

    public static isAssetReady(_class: any): boolean {
        if (this._getAssetRequests.length > 0) {
            for (let i = 0; i < this._getAssetRequests.length; ++i) {
                //The request asset still here, mean asset is not ready yet
                if (this._getAssetRequests[i].owner === _class) {
                    return false;
                }
            }
        }
        return true;
    }


    public static getResources(): LoaderResourceStorage {
        const resStorage = this._getLoader()?.resources ?? null;
        if (!resStorage) {
            throw new Error('ResourceStorage is null');
        }
        return resStorage;
    }

    private static _getResourceLoader(name: string): ILoaderResource {
        return this.getResources()[name];
    }

    private static _cacheLoader: Loader;
    private static _getLoader(): Loader {
        if (!this._cacheLoader) {
            this._cacheLoader = MainApp.inst.app.loader;
        }
        return this._cacheLoader;
    }

    private static _getAssetFolder(): string {
        return process.env.ASSET_FOLDER;
    }
}
