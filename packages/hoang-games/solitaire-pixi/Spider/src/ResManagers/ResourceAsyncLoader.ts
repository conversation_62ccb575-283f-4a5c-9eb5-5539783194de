import Localization from '../Localization/Localization';
import { PopupManager } from '../Popup/PopupManager';
import ResourceManager from './ResourceManager';

export class ResourceAsyncLoader {
    constructor() {
    }

    /* protected async _loadSpineAsync(spineName: string, cb: Function) {
        ResourceManager.getSpineAsync(spineName, this).then((resources) => {
            cb(resources);
        });
    } */

    protected async _loadAtlasAsync(atlas: string, cb: Function) {
        ResourceManager.getAtlasAsync(atlas, this).then((resource) => {
            cb(resource);
        });
    }

    protected async _loadMultiAtlasAsync(atlas: string[], cb: Function) {
        ResourceManager.getMultiAtlasAsync(atlas, this).then((resources) => {
            cb(resources);
        });
    }

    /* protected async _loadMultiSpinesAsync(spines: string[], cb: Function) {
        ResourceManager.getMultiSpinesAsync(spines, this).then((resources) => {
            cb(resources);
        });
    } */

    protected async _loadTextureAsync(texName: string, cb: Function) {
        ResourceManager.getTextureAsync(texName, this).then((resource) => {
            cb(resource);
        });
    }

    protected async _waitForAllResourcesReady(): Promise<void> {
        return new Promise<void>(resolve => {
            if (ResourceManager.isAssetReady(this)) {
                resolve();
            } else {
                PopupManager.loadingResourcePopup.open(Localization.getText('LOADING'), -1, () => {
                    resolve();
                }, this);
            }
        });
    }
}