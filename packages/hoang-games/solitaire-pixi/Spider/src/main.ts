// #!if cheatMenu
import CheatMenu from './core/utility/cheat/CheatMenu';
// #!endif
import AppConstants from './AppConstants';
import { AppState } from './AppState';
import { PopupManager } from './Popup/PopupManager';
import { MultiResolutionHandler } from './Scenes/MultiResolutionHandler';
import SceneManager from './Scenes/SceneManager';
import { SoundManager } from './SoundManager';
import UIManager from './UI/UIManager';
import { Scheduler } from './core/utility/scheduler/Scheduler';
import { Tween } from './core/utility/tween/Tween';
import { Application, Group, Layer, Stage, utils } from './pixi';

export class MainApp {
    public static inst: MainApp = null;

    public app: Application;
    public language: string = 'en_US'; //'vi_VN';

    private _interruptListeners: { onVisibilityChange(isVisible: boolean) }[] = [];

    /* Entry point, it is triggered by the window.onload event found at the bottom of this class */
    public constructor() {
        if (MainApp.inst) {
            console.warn(`[APP] Several constructions of singleton ${this.constructor.name}!!!`);
            return MainApp.inst;
        }
        MainApp.inst = this;
        AppState.pushState(AppState.State.Init);

        utils.skipHello();
        const canvas = <HTMLCanvasElement>document.getElementById('game-canvas');
        this.app = new Application({
            backgroundColor: 0xefe1de,
            width: AppConstants.BASE_SCREEN_WIDTH,
            height: AppConstants.MAX_CANVAS_HEIGHT,
            view: canvas,
            antialias: false,
            sharedLoader: true,
        });
        // document.body.appendChild(this.app.view);
        globalThis.gCanvas = canvas;

        // this.app.ticker.maxFPS = 60.1;
        // this.app.ticker.add(this.update, this, UPDATE_PRIORITY.HIGH);


        this.app.stage.destroy();
        this.app.stage = new Stage();
        this.app.stage.sortableChildren = true;
        new MultiResolutionHandler(this.app);

        const sceneManager = new SceneManager();
        const uiManager = new UIManager();

        uiManager.movingGroup = new Group(2, (node) => {
            node.zOrder = node.getGlobalPosition().y;
        });

        uiManager.showingGroup = new Group(1, (node) => {
            node.zOrder = node.getGlobalPosition().y;
        });

        uiManager.drawedGroup = new Group(1, (node) => {
            node.zOrder = node.getGlobalPosition().x;
        });

        uiManager.onTopGroup = new Group(3, (node) => {
            node.zOrder = node.getGlobalPosition().x;
        });

        this.app.stage.addChild(sceneManager.container);
        this.app.stage.addChild(uiManager.uiContainer);
        this.app.stage.addChild(uiManager.popupContainer);
        this.app.stage.addChild(uiManager.topNotifyContainer);
        this.app.stage.addChild(new Layer(uiManager.movingGroup));
        this.app.stage.addChild(new Layer(uiManager.showingGroup));
        this.app.stage.addChild(new Layer(uiManager.drawedGroup));
        this.app.stage.addChild(new Layer(uiManager.onTopGroup));
        // #!if cheatMenu
        this.app.stage.addChild(new CheatMenu().cheatContainer);
        // #!endif

        const soundManager = new SoundManager();
        this._interruptListeners.push(sceneManager, soundManager);
        console.log('[APP] MainApp constructor');

        /*
        const loadingIcon = document.getElementById('loading-icon');
        loadingIcon.hidden = true;
        loadingIcon.parentElement.hidden = true; */

        SceneManager.inst.goToScene(SceneManager.inst.loadingScene);
        // this.app.view.hidden = false;

        //Ticker update for functions run frame by frame with dt time
        this.app.ticker.add(Scheduler.onUpdate, Scheduler);
        this.app.ticker.add(Tween.onUpdate, Tween);

        // Load assets one by one to avoid some strange issue with slow network
        this.app.loader.concurrency = 10;

        // Interrupt events
        document.addEventListener('visibilitychange', () => {
            const isVisible = !document.hidden;
            this._interruptListeners.forEach((listener) => listener.onVisibilityChange(isVisible));
            PopupManager.onVisibilityChange(isVisible);
        });

        // #!if ENV === 'development'
        this.app.stage.name = 'Root';
        globalThis.__PIXI_APP__ = this.app;
        // #!endif
    }
}