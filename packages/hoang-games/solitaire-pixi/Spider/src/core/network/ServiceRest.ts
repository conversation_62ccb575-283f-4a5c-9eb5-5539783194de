
// Rest API to connect to server

class ServiceRest {
    private _request: XMLHttpRequest;

    constructor() {
        this._request = new XMLHttpRequest();
    }
    encodeQueryData(data) {
        return Object.keys(data).map(function (key) {
            return [key, data[key]].map(encodeURIComponent).join('=');
        }).join('&');
    }

    get({ url = '', params = {}, callback = (data) => {}, apiUrl = '', callbackErr = (e) => {} }) {
        const querystring = Object.keys(params).length > 0 ? '?' + this.encodeQueryData(params) : '';
        const fullURL = apiUrl + url + querystring;
        const request = new XMLHttpRequest();
        request.open('GET', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
        request.onreadystatechange = function () {
            if (request.readyState == 4) {
                //get status text
                if (callback) {
                    if (request.responseText) {
                        callback(JSON.parse(request.responseText));
                    } else {
                        callbackErr(-1);
                    }
                }
            } else if (request.readyState === 0) {
                callbackErr(-2);
            }
            if (request.status !== 200) {
                callbackErr(request.status);
            }
        };
        request.ontimeout = function (e) {
            callbackErr(e);
        };
        request.onerror = (e) => {
            callbackErr(e);
        };
        request.send();
    }

    getWithHeader({ url = '', params = {}, headers = {}, callback = (data) => {}, apiUrl = '', callbackErr = (e) => {} }) {
        const querystring = '?' + this.encodeQueryData(params);
        const fullURL = apiUrl + url + querystring;
        const request = new XMLHttpRequest();
        request.open('GET', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');

        Object.keys(headers).forEach((key) => {
            request.setRequestHeader(key, headers[key]);
        });

        request.onreadystatechange = function () {
            if (request.readyState == 4) {
                //get status text
                if (callback) {
                    if (request.responseText) {
                        callback(JSON.parse(request.responseText));
                    } else {
                        callbackErr(-1);
                    }
                }
            } else if (request.readyState === 0) {
                callbackErr(-2);
            }
            if (request.status !== 200) {
                callbackErr(request.status);
            }
        };
        request.ontimeout = function () {
            callbackErr('timeout');
        };
        request.onerror = (ev) => {
            callbackErr(ev);
        };
        request.send();
    }

    post({ url = '', data = {}, callback = (data) => {}, apiUrl = '', callbackErr = () => {} }) {
        const dataPost = this.encodeQueryData(data);
        const fullURL = apiUrl + url;
        const request = new XMLHttpRequest();
        request.open('POST', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        request.onreadystatechange = function () {
            if (request.readyState == 4) {
                if (request.responseText) {
                    callback({
                        status: request.status,
                        data: JSON.parse(request.responseText)
                    });
                } else {
                    callbackErr();
                }
            } else if (request.readyState === 0) {
                callbackErr();
            }
            if (request.status !== 200) {
                callbackErr();
            }
        };
        request.ontimeout = function (e) {
            callbackErr();
        };
        request.onerror = (e) => {
            callbackErr();
        };
        request.send(dataPost);
    }

    postWithHeader({ url = '', params = {}, headers = {}, data = {}, callback = (data) => {}, apiUrl = '', callbackErr = (e) => {} }) {
        const dataPost = JSON.stringify(data);
        const querystring = Object.keys(params).length > 0 ? '?' + this.encodeQueryData(params) : '';
        const fullURL = apiUrl + url + querystring;
        const request = new XMLHttpRequest();
        request.open('POST', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-type', 'application/json');

        Object.keys(headers).forEach((key) => {
            request.setRequestHeader(key, headers[key]);
        });

        request.onreadystatechange = function () {
            if (request.readyState == 4) {
                if (request.responseText) {
                    callback({
                        status: request.status,
                        data: JSON.parse(request.responseText)
                    });
                } else {
                    callbackErr(-1);
                }
            } else if (request.readyState === 0) {
                callbackErr(-2);
            }
            if (request.status !== 200) {
                callbackErr(request.status);
            }
        };
        request.ontimeout = function (e) {
            callbackErr(e);
        };
        request.onerror = (e) => {
            callbackErr(e);
        };
        request.send(dataPost);
    }

    postRaw({ url = '', data = {}, callback = (data) => {}, apiUrl = '', callbackErr = () => {} }) {
        const dataPost = data;
        const fullURL = apiUrl + url;
        const request = new XMLHttpRequest();
        request.open('POST', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        request.onreadystatechange = function () {
            if (request.status == 200) {
                callback({
                    status: request.status,
                    data: request.responseText
                });
            } else {
                callbackErr();
            }
        };
        request.ontimeout = function (e) {
            callbackErr();
        };
        request.onerror = (e) => {
            callbackErr();
        };
        //@ts-ignore
        request.send(dataPost);
    }

    put({ url = '', data = {}, callback = (data) => {}, apiUrl = '', callbackErr = () => {} }) {
        const dataPost = this.encodeQueryData(data);
        const fullURL = apiUrl + url;
        const request = new XMLHttpRequest();
        request.open('PUT', fullURL, true);
        request.timeout = 15000;
        request.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
        request.onreadystatechange = function () {
            if (request.readyState == 4) {
                if (request.responseText) {
                    callback({
                        status: request.status,
                        data: JSON.parse(request.responseText)
                    });
                } else {
                    callbackErr();
                }
            } else if (request.readyState === 0) {
                callbackErr();
            }
            if (request.status !== 200) {
                callbackErr();
            }
        };
        request.ontimeout = function (e) {
            callbackErr();
        };
        request.onerror = (e) => {
            callbackErr();
        };
        request.send(dataPost);
    }
}

const apiObject = new ServiceRest();
export default apiObject;
