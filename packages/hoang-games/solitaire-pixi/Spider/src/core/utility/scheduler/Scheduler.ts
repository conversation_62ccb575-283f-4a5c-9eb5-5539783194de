import { settings } from '../../../pixi';
import Utils from '../../../Utils/Utils';


interface TimeOut {
    func: Function;
    time: number;
}

interface Interval {
    func: Function;
    delay: number;
    time: number;
}

/**
 * Class implement functions to call events on a schedule using app Ticker update.
 * Replace pure JavaScript setTimeout & setInterval
 */
export class Scheduler {

    public static onUpdate(dtScalar: number) {

        const dt = dtScalar / settings.TARGET_FPMS;
        Scheduler._updateTimeoutEvents(dt);
        Scheduler._updateIntervalEvents(dt);

    }

    private static _timeoutMap: Map<string, TimeOut> = new Map();

    /**
     * Call a function after a duration time using app frame update
     * @param time delay time in millisecond
     * @param callback callback function will be called after time duration
     */
    public static setTimeout(time: number, callback: Function): string {

        const uuid = Utils.shortUuidv4();
        const event = {
            func: callback,
            time: time
        };
        this._timeoutMap.set(uuid, event);

        return uuid;
    }

    /**
     * Clear a timeout event by a key
     * @param key uuid string return from setTimeOut function
     */
    public static clearTimeout(key: string) {
        this._timeoutMap.delete(key);
    }

    private static _updateTimeoutEvents(dt: number) {

        const removeIndexes = [];
        this._timeoutMap.forEach((e, key) => {
            e.time -= dt;
            if (e.time < 0) {
                e.func();
                removeIndexes.push(key);
            }
        });

        for (let i = removeIndexes.length - 1; i >= 0; i--) {
            this._timeoutMap.delete(removeIndexes[i]);
        }
    }

    private static _intervalList: Map<string, Interval> = new Map();

    /**
     * Call a function in a schedule
     * @param delay delay time for each call
     * @param callback callback function called during schedule
     */
    public static setInterval(delay: number, callback: Function): string {

        const uuid = Utils.shortUuidv4();
        const event = {
            func: callback,
            delay,
            time: 0
        };
        this._intervalList.set(uuid, event);

        return uuid;
    }

    /**
     * Clear an interval event by a key
     * @param key uuid string return from setInterval function
     */
    public static clearInterval(key: string) {
        this._intervalList.delete(key);
    }

    private static _updateIntervalEvents(dt: number) {

        this._intervalList.forEach((e, idx, arr) => {
            e.time += dt;
            if (e.time > e.delay) {
                e.func();
                e.time -= e.delay;
            }
        });
    }
}