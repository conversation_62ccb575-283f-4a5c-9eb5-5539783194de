/**
 * Help to record all console log in game
 * https://stackoverflow.com/questions/19846078/how-to-read-from-chromes-console-in-javascript
 */
import FileSaver from 'file-saver';
import Utils from '../../../Utils/Utils';

export type LogType = 'log' | 'error' | 'warn' | 'info' | 'debug' | 'trace';

export default class LogHook {
    public static inst: LogHook;

    private static _isRecording: boolean = false;
    private static _wasSaveCrashLog: boolean = false;
    private static _useServerTimeZone: boolean = false;

    private readonly _USE_OBJECT_TYPE: boolean = false;
    private readonly _DEFAULT_LOG_TYPES: LogType[] = ['log', 'error', 'warn', 'debug'];
    private readonly _MUTED_LOGS: string[] = ['"jackpot-updated"', '"jud"', '"jackpot-win"', '"JPA"', '"wallet-updated"', 'my wallet update', 'Out of pool size of symbol', ': ping', ': pong'];

    private _recordTypes: LogType[];
    private _isIgnoreMutedLogs: boolean = true;

    constructor(recordLogs?: LogType[], autoStart: boolean = true) {
        if (LogHook.inst) {
            console.warn(`[CHEAT] Several constructions of singleton ${this.constructor.name}!!!`);
            return LogHook.inst;
        }
        LogHook.inst = this;

        this._recordTypes = recordLogs || this._DEFAULT_LOG_TYPES;
        this._init(this._recordTypes);
        if (autoStart) this.startCapture(false);
    }

    private _init(recordLogs: LogType[]) {
        if (console['everything']) {
            console.warn('[CHEAT] Debug log holder is exists!');
            return;
        }
        console['everything'] = [];

        // register events
        window.onerror = (event, source, line, col, error) => {
            const message = error.message;
            const callstack = error.stack;
            const info = [event, source, line, col, message, callstack];
            console['everything'].push({
                type: 'Exception',
                timeStamp: LogHook._timestamp(),
                value: info,
            });
            if (!Utils.isDeveloper() && !LogHook._wasSaveCrashLog) {
                LogHook._wasSaveCrashLog = true;
                this.saveLogFile();
            }
            return false;
        };
        window.onunhandledrejection = (event) => {
            const reason = event.reason;
            const message = reason.message;
            const callstack = reason.stack;
            const info = [message, callstack];
            console['everything'].push({
                type: 'UnhandledRejection',
                timeStamp: LogHook._timestamp(),
                value: info,
            });
        };
        window.onmessageerror = (event) => {
            console['everything'].push({
                type: 'MessageError',
                timeStamp: LogHook._timestamp(),
                value: event,
            });
        };
        window.onrejectionhandled = (event) => {
            console['everything'].push({
                type: 'RejectionHandled',
                timeStamp: LogHook._timestamp(),
                value: event,
            });
        };

        recordLogs.forEach((logType) => {
            // console[logType] = this._createHook(logType);
        });

        // check get all param
        const url = new URL(window.location.href);
        const isGetIgnoreLogs = url.searchParams.has('a') || url.searchParams.has('all');
        this._isIgnoreMutedLogs = !isGetIgnoreLogs;
    }

    private _createHook(logType) {
        const original = console[logType].bind(console);
        return function () {
            if (LogHook._isRecording) {
                console['everything'].push({
                    type: logType,
                    timeStamp: LogHook._timestamp(),
                    value: Array.from(arguments),
                });
            }
            original.apply(console, arguments);
        };
    }

    private static _timestamp() {
        const liveTimeZone = this._useServerTimeZone ? 'UTC' : 'Asia/Ho_Chi_Minh';
        const time = new Date().toLocaleString('sv', { timeZone: liveTimeZone });
        const formatTime = time.replace(/\-/g, '').replace(/\:/g, '').replace(/ /g, '_');
        return formatTime;
    }

    public startCapture(saveMessage: boolean = true): void {
        LogHook._isRecording = true;
        if (saveMessage) {
            console['everything'].push({
                type: 'command',
                timeStamp: LogHook._timestamp(),
                value: 'Start Record',
            });
        }
    }

    public stopCapture(saveMessage: boolean = true): void {
        LogHook._isRecording = false;
        if (saveMessage) {
            console['everything'].push({
                type: 'command',
                timeStamp: LogHook._timestamp(),
                value: 'Stop Record',
            });
        }
    }

    private _clearlyLogs(): string {
        let lines = '';
        console['everything'].forEach((obj) => {
            const type = obj['type'];
            const msg = obj['value'];

            let msgStr = '';
            if (msg instanceof Array) {
                msgStr = '';
                msg.forEach((arg, index) => {
                    let parsedArg;
                    if (typeof arg === 'object') {
                        parsedArg = JSON.stringify(arg);
                    } else {
                        parsedArg = arg.toString();
                    }

                    msgStr += parsedArg;
                    if (index > 0 && index < msg.length - 1) {
                        msgStr += ', ';
                    }
                });
            } else {
                msgStr = JSON.stringify(msg);
            }
            // ignore spam logs
            let ignore = false;
            if (this._isIgnoreMutedLogs) {
                this._MUTED_LOGS.forEach((keyword) => {
                    if (msgStr.indexOf(keyword) > -1) {
                        ignore = true;
                        return;
                    }
                });
            }
            if (!ignore) {
                lines += type.toUpperCase() + ' - ' + msgStr + '\n';
            }
        });

        return lines;
    }

    private _parseDataToJson(): string {
        return JSON.stringify(console['everything'], null, 2);
    }

    public clear(): void {
        console['everything'].length = 0;
    }

    public size(): number {
        return (console['everything'] && console['everything'].length) || 0;
    }

    public saveLogFile(): void {
        if (this.size() > 0) {
            const logStr: string = this._USE_OBJECT_TYPE ? this._parseDataToJson() : this._clearlyLogs();
            const filename: string = `${process.env.GAME_NAME.trim()}_${LogHook._timestamp()}.txt`;
            const blob: Blob = new Blob([logStr], { type: 'application/json' });
            FileSaver.saveAs(blob, filename);
        } else {
            console.log('[CHEAT] Empty logs: [' + this._recordTypes + ']');
        }
    }
}
