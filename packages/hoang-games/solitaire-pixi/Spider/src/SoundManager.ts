// #!if ENV === 'development'
// @ts-ignore
import { Howl, Howler, HowlOptions } from 'howler';
// #!else
// howler.core: without extension method: `stereo, pos, orientation`
// @ts-ignore
// eslint-disable-next-line no-redeclare
import { Howl, Howler, HowlOptions } from 'howler/dist/howler.core.min';
// #!endif
import { Scheduler } from './core/utility/scheduler/Scheduler';
import ResourceManager from './ResManagers/ResourceManager';
import {
    SoundFormat,
    InGamePacks,
    PreLoadPacks,
    SoundId,
    MusicIds,
    SoundNames,
    RawConfigs,
} from './ResManagers/SoundAssets';
import { SafeCollection } from './Utils/SafeCollection';
import Utils from './Utils/Utils';
import { Events } from './Events/Events';
import { UIEvents } from './Events/EventTypes';
import Globals from './Globals';

/* eslint-disable no-unused-vars */
export type SoundInfo = {
    pack: string;
    sprite: string;
    loop: boolean;
    volume: number;
    isMusic: boolean;
};
type SoundStartCallback = (info: SoundInfo, howl: Howl, id: number) => void;
type SoundEndCallback = (id: number) => void;

type AUTO_CONFIG = 'AUTO';
/* eslint-enable no-unused-vars */

export class SoundManager {
    public static inst: SoundManager = null;

    private readonly _PROP_IS_FADING = '_isFading';
    private readonly _PROP_END_EVENT = '_endEvent';
    private readonly _NUMBER_OF_PERCENT_LOADING_TO_INIT = 80;
    private readonly _DEFAULT_VOLUME: number = 1;
    private readonly _SUSPEND_CONTEXT_DEVICES: string[] = [
        'SM-N970', // Galaxy Note 10
    ];

    private _resources: Map<string, Howl> = new Map();
    private _soundChannels: Map<SoundId, [Howl, number | SafeCollection<number>]> = new Map();
    private _pendingEndCallback: Map<SoundId, Howl> = new Map();
    private _curMusicChannel: SoundId;
    private _volume: number = 0;
    private _isSoundOn: boolean = true;
    private _isSuspendContext: boolean = false;
    private _isDisableMusic: boolean = false;
    private _isInitialized: boolean = false;
    private _isLoadedPreGame: boolean = false;
    // #!if cheatMenu
    private _loadSoundProcess: number = 0;
    // #!endif

    constructor() {
        if (SoundManager.inst) {
            console.warn(`[SOUND] Several constructions of singleton ${this.constructor.name}!!!`);
            return SoundManager.inst;
        }
        SoundManager.inst = this;

        // console.log('[SOUND] User agent: ' + navigator.userAgent);
        this._SUSPEND_CONTEXT_DEVICES.forEach((device) => {
            if (navigator.userAgent.indexOf(device) !== -1) {
                this._isSuspendContext = true;
                // console.log('[SOUND] Use Suspend Context');
                return;
            }
        });

        // #!if ENV === 'development'
        // validate sound asset
        if (SoundNames.length !== RawConfigs.length) {
            console.error('[SOUND] Sound data wrong!');
        }
        // #!endif

        // disable auto suspend AudioContext after 30s
        Howler.autoSuspend = false;
        this.setVolume(this._DEFAULT_VOLUME);

        Events.on(UIEvents.CHANGE_VOLUME, this.setVolume.bind(this));
    }

    public init() {
        if (this._isInitialized) {
            return;
        }

        const configJson = ResourceManager.getJsonData('spcf');
        if (!configJson) {
            console.error('[SOUND] Cant read config!');
            return;
        }

        const soundPacks = [...PreLoadPacks, ...InGamePacks];
        soundPacks.forEach((name) => {
            if (name in configJson) {
                const config = configJson[name];
                const howlOption: HowlOptions = this._parseConfig(config, name);
                const howlInst: Howl = new Howl(howlOption);
                this._resources.set(name, howlInst);
            } else {
                console.error('[SOUND] Not found config ' + name);
            }
        });

        this._isInitialized = true;
    }

    public loadSetting() {
        // console.warn('[SOUND] load sound setting');
        const savedFlag = undefined; // Utils.getGameSetting('sound');
        const isEnable = (savedFlag === undefined || savedFlag === '') ? true : savedFlag === '1';
        this.setSoundOption(isEnable);
    }

    public loadSounds(bundleType: 'PreGame' | 'InGame') {
        if (!this._isInitialized) {
            console.error('[SOUND] Cant load before init!');
            return;
        }

        const loadPack = bundleType === 'PreGame' ? PreLoadPacks : InGamePacks;
        let task = loadPack.length;
        loadPack.forEach((name) => {
            const howlInst: Howl = this._resources.get(name);
            howlInst.once('load', () => {
                if (--task <= 0) {
                    Events.emit(UIEvents.LOAD_SOUND, bundleType);
                }
            });
            howlInst.load();
        });

        // update load status for pack pregame
        if (bundleType === 'PreGame') {
            this._isLoadedPreGame = true;
        }
    }

    private _parseConfig(config: object, name: string): HowlOptions {
        const howlOption: HowlOptions = config;

        // update asset folder
        const asset = Globals.ExtraResources[name];
        const overrideUrl = asset?.url;
        if (howlOption.src instanceof Array) {
            howlOption.src.forEach((src, idx, arr) => {
                arr[idx] = overrideUrl ?? `${process.env.ASSET_FOLDER}/${src}`;
            });
        } else {
            howlOption.src = overrideUrl ?? `${process.env.ASSET_FOLDER}/${howlOption.src}`;
        }

        // don't load sound on create howl object
        howlOption.preload = false;
        howlOption.format = SoundFormat;
        // register events
        // #!if cheatMenu
        howlOption.onload = () => {
            this._loadSoundProcess++;
            // console.log(`[SOUND] Load ${name} Done! [${this._loadSoundProcess}/${this._resources.size}]`);
            if (this._loadSoundProcess === this._resources.size) {
                console.log('[SOUND] Load Sound Success!');
            }
        };
        // #!endif
        howlOption.onloaderror = (id, err) => {
            console.error(`[SOUND] Load ${name} Failed! (${err})`);
        };
        howlOption.onplayerror = (id, err) => {
            console.error(`[SOUND] Play ${name} Failed! (${err})`);
        };

        return howlOption;
    }

    public onLoadingProgress(progress: number) {
        if (this._isInitialized && progress >= this._NUMBER_OF_PERCENT_LOADING_TO_INIT && !this._isLoadedPreGame) {
            this.loadSounds('PreGame');
        }
    }

    public playMusic(
        soundId: SoundId,
        volume?: number,
        startCallback?: SoundStartCallback,
        endCallback?: SoundEndCallback,
        resumeInPoolSound?: boolean
    ): number {
        let playId = -1;
        //Stop others bg music
        this.pause(this._curMusicChannel);

        //Play selected bg music
        if (!this._soundChannels.has(soundId)) {
            let startCallbackRepeater = startCallback;
            if (!this._isSoundOn) {
                startCallbackRepeater = (info: SoundInfo, howl: Howl, id: number) => {
                    howl.pause(id);
                    if (startCallback) {
                        startCallback(info, howl, id);
                    }
                };
            }
            playId = this._howlPlay(soundId, volume, null, startCallbackRepeater, endCallback, false);
        } else {
            const soundInfo = this._getSound(soundId);
            const [howl, ids] = this._soundChannels.get(soundId);
            volume = volume !== undefined ? volume : soundInfo.volume;
            if (resumeInPoolSound === undefined || !resumeInPoolSound) {
                howl.seek(0, <number>ids);
            }
            const newId = howl.play(<number>ids);
            howl.volume(volume, newId);
            if (startCallback) {
                startCallback(soundInfo, howl, newId);
            }
        }
        this._curMusicChannel = soundId;

        return playId;
    }

    public playSfx(
        soundId: SoundId,
        volume?: number,
        rate?: number,
        startCallback?: SoundStartCallback,
        endCallback?: SoundEndCallback
    ): number {
        if (this._isSoundOn) {
            return this._howlPlay(soundId, volume, rate, startCallback, endCallback, true);
        }
        return -1;
    }

    public isPlaying(soundId: SoundId, includePaused: boolean = false): boolean {
        let result: boolean = false;
        if (this._soundChannels.has(soundId)) {
            const [howl, ids] = this._soundChannels.get(soundId);
            if (ids instanceof SafeCollection) {
                ids.forEach((id) => {
                    if (howl.playing(id) || (includePaused && this._isHowlPause(howl, id))) {
                        result = true;
                        return;
                    }
                });
            } else {
                result = howl.playing(<number>ids) || (includePaused && this._isHowlPause(howl, <number>ids));
            }
        }
        return result;
    }

    private _isHowlPause(howl: Howl, id: number): boolean {
        const nativeSounds: unknown[] = howl['_sounds'].filter(x => x._id === id);
        if (nativeSounds.length > 0) {
            return nativeSounds.map(x => x['_node'] ? x['_node'].paused : false).reduce((prev, next) => prev || next);
        }
        return false;
    }

    public stop(soundId: SoundId) {
        if (this._soundChannels.has(soundId)) {
            const [howl, ids] = this._soundChannels.get(soundId);
            if (ids instanceof SafeCollection) {
                ids.forEach((x) => howl.stop(x));
            } else {
                howl.stop(ids);
                // console.warn(`[SOUND] ${soundId}(${id}) stopped!`);
            }
        }
    }

    public pause(soundId: SoundId): void {
        if (this._soundChannels.has(soundId)) {
            const [howl, ids] = this._soundChannels.get(soundId);
            if (ids instanceof SafeCollection) {
                ids.forEach((x) => howl.pause(x));
            } else {
                howl.pause(ids);
                // console.warn(`[SOUND] ${soundId}(${id}) stopped!`);
            }
        }
    }

    public resume(soundId: SoundId, verifyVolume: boolean = false): void {
        if (this._soundChannels.has(soundId)) {
            const [howl, ids] = this._soundChannels.get(soundId);
            const resumePlayer = (howl: Howl, id: number) => {
                howl.play(id);
                if (verifyVolume && howl.volume(id) === 0) {
                    const soundInfo = this._getSound(soundId);
                    howl.volume(soundInfo.volume, id);
                }
            };
            if (ids instanceof SafeCollection) {
                ids.forEach((id) => resumePlayer(howl, id));
            } else {
                resumePlayer(howl, ids);
                // console.warn(`[SOUND] ${soundId}(${ids}) play, vol: ${howl.volume(ids)}!`);
            }
        }
    }

    /**
     * Get/set the rate of playback for a sound
     * @param soundId The sound ID. If none is passed, playback rate of all sounds in group will change.
     * @param rate The rate of playback. 0.5 to 4.0, with 1.0 being normal speed.
     */
    public rate(soundId: SoundId, rate: number): void {
        if (this._soundChannels.has(soundId)) {
            const [howl, ids] = this._soundChannels.get(soundId);
            if (ids instanceof SafeCollection) {
                ids.forEach((id) => howl.rate(rate, id));
            } else {
                howl.rate(rate, ids);
                // console.warn(`[SOUND] ${soundId}(${ids}) rate to ${rate}!`);
            }
        }
    }

    private _howlPlay(
        soundId: SoundId,
        volume?: number,
        rate?: number,
        startCallback?: SoundStartCallback,
        endCallback?: SoundEndCallback,
        isSfx?: boolean
    ): number {
        if (!this._isSoundIdValid(soundId)) {
            console.error(`[SOUND] ${soundId} not found!`);
            return -1;
        }
        const soundInfo: SoundInfo = this._getSound(soundId);
        const howl: Howl = this._resources.get(soundInfo.pack);
        if (!howl) {
            console.error(`[SOUND] ${soundId} inst is null!`);
            return -1;
        } else {
            if (howl.state() !== 'loaded' && isSfx) {
                // force stop when sound pack is loading
                console.warn(`[SOUND] ${soundId} Can't play, ${soundInfo.pack} not load!, ${howl.state()}`);
                return -1;
            }
            //@ts-ignore
            // const queue: [] = howl._queue;
            // if(queue && queue.length > 0) {
            //     console.warn(queue);
            // }
        }
        const id = howl.play(soundInfo.sprite);
        // check play sound error
        if (id === null) {
            console.error(`[SOUND] Can't play ${soundId}!`);
            return -1;
        }
        if (rate !== undefined && rate !== null) {
            howl.rate(rate, id);
        }
        const volumeCfg = volume || soundInfo.volume;
        howl.volume(volumeCfg, id);
        // just to get id for fade music
        if (volumeCfg === 0) {
            howl.seek(0, id);
            howl.pause(id);
        }

        // register sound event to update list sound playings
        const endEventHandler = (playingId) => {
            // check sound in playing list to avoid error when play sound silence
            if (this._soundChannels.has(soundId)) {
                // eslint-disable-next-line prefer-const
                let [savedHowl, savedIds] = this._soundChannels.get(soundId);
                if (savedIds instanceof SafeCollection && savedIds.length() > 1) {
                    this._soundChannels.set(soundId, [savedHowl, savedIds.remove(id)]);
                } else {
                    this._soundChannels.delete(soundId);
                }
                // console.warn(`[SOUND] Remove ${soundId}(${id})!`);
            } else {
                console.warn(`[SOUND] Stop non-play: ${soundId}(${id})`);
            }
            howl.off('end', null, id);
            howl.off('stop', null, id);

            // trigger listener
            if (endCallback) {
                if (howl[this._PROP_IS_FADING] === 1) {
                    howl[this._PROP_END_EVENT] = [howl, id, endCallback];
                    this._pendingEndCallback.set(soundId, howl);
                } else {
                    endCallback(id);
                }
            }
        };
        howl.once('stop', endEventHandler.bind(this), id);
        if (!soundInfo.loop) {
            howl.once('end', endEventHandler.bind(this), id);
        }

        if (this._soundChannels.has(soundId)) {
            // eslint-disable-next-line prefer-const
            let [savedHowl, playingIds] = this._soundChannels.get(soundId);
            if (playingIds instanceof SafeCollection) {
                playingIds.push(id);
            } else {
                const prevId = playingIds;
                playingIds = new SafeCollection<number>(prevId, id);
            }
            if (savedHowl !== howl) {
                console.warn('[SOUND] Difference hosts of sound ' + soundId);
            }
            this._soundChannels.set(soundId, [savedHowl, playingIds]);
        } else {
            this._soundChannels.set(soundId, [howl, id]);
        }
        if (startCallback) {
            startCallback(soundInfo, howl, id);
        }

        // console.warn(`[SOUND] Play ${soundId}(${id}), vol: ${volumeCfg}`);
        return id;
    }

    /**
     * Fade in/out audio
     * @param soundId id of sound
     * @param from from volume, `'AUTO'` to get current volume
     * @param to target volume, `'AUTO'` to load setting from config
     * @param duration fading time
     * @param stopFadeOut if true, will stop sound when fade ending, otherwise will pause, just use this for fade volume to 0 only
     * @param endCallback function will be invoked when fade end
     * @param nearEndCallback function when the near fade end, at point the fade process got nearFactor * 100 percent
     * @param nearFactor factor to trigger `nearEndCallback`, in range [0, 1]
     */
    public fade(
        soundId: SoundId,
        from: number | AUTO_CONFIG,
        to: number | AUTO_CONFIG,
        duration: number = 500,
        stopFadeOut: boolean = true,
        endCallback?: () => void,
        nearEndCallback?: () => void,
        nearFactor: number = 0.75
    ): void {
        if (!this._soundChannels.has(soundId)) {
            const callbackOwner = this._pendingEndCallback.get(soundId);
            if (callbackOwner && callbackOwner[this._PROP_END_EVENT]) {
                const [endHost, endId, pendingEndCallback] = callbackOwner[this._PROP_END_EVENT];
                pendingEndCallback(endId);

                callbackOwner[this._PROP_END_EVENT] = undefined;
                this._pendingEndCallback.delete(soundId);
            } else {
                console.error(`[SOUND] ${soundId} not play, can't fade!`);
            }
            return;
        }
        const [howl, ids] = this._soundChannels.get(soundId);
        const playId = <number>ids;
        const info: SoundInfo = this._getSound(soundId);
        if (howl.state() !== 'loaded') {
            console.warn(`[SOUND] ${soundId} fade during pack loading!`);
            return;
        }
        if (this._isDisableMusic && soundId === this._curMusicChannel) {
            console.warn(`[SOUND] Game music is disabled, cant fade ${soundId}!`);
            return;
        }

        // verify config
        from = from === 'AUTO' ? <number>howl.volume(playId) : from;
        to = to === 'AUTO' ? info.volume : to;
        const fadeInfo = `${soundId} (${playId}: ${from} => ${to})`;
        if (!this._isValidVolume(from) || !this._isValidVolume(to)) {
            console.error(`[SOUND] ${fadeInfo}, error volume range!`);
            return;
        }
        if (from === to) {
            console.warn(`[SOUND] ${fadeInfo} is cancel, recheck this!`);
            if (nearEndCallback) {
                nearEndCallback();
            }
            if (endCallback) {
                endCallback();
            }
            return;
        }
        // just fade if enable sound
        if (!this._isSoundOn) {
            if (info.isMusic) {
                this._curMusicChannel = soundId;
            }
            return;
        }

        // clear current fading callback
        if (howl[this._PROP_IS_FADING] === 1) {
            howl[this._PROP_IS_FADING] = 0;
            howl.off('fade', null, playId);
        }
        // play sound if it's paused
        if (!howl.playing(playId)) {
            howl.play(playId);
        }
        // setup fade event and callback
        howl.once('fade', (_id) => {
            // check this fade is fade down, to stop or pause sound
            if (from > to) {
                if (stopFadeOut && to === 0) {
                    howl.stop(_id);
                } else {
                    howl.pause(_id);
                }
            }

            if (endCallback) {
                endCallback();
            }

            howl[this._PROP_IS_FADING] = 0;
            // console.warn(`[SOUND] ${fadeInfo} done!`);
        }, playId);
        if (to > 0 && ((info && info.isMusic) || MusicIds.indexOf(soundId) !== -1)) {
            this._curMusicChannel = soundId;
        }
        if (nearEndCallback) {
            if (nearFactor >= 0 && nearFactor <= 1) {
                Scheduler.setTimeout(duration * nearFactor, nearEndCallback);
            } else {
                console.error(`[SOUND] ${fadeInfo}, nearFactor must in range [0, 1]!`);
            }
        }
        // start fade
        howl.volume(from, playId);
        howl.fade(from, to, duration, playId);
        howl[this._PROP_IS_FADING] = 1;
        // console.warn(`[SOUND] ${fadeInfo}, at: ${Utils._roundTwo(<number>howl.seek(undefined, playId))}/${Utils._roundTwo(howl.duration(playId))}`);
    }

    public setSoundOption(isOn: boolean) {
        this._isSoundOn = isOn;
        if (isOn) {
            this.setVolume(this._volume || this._DEFAULT_VOLUME, true);
            this.resume(this._curMusicChannel, true);
        } else {
            this.setVolume(0, true, true);
            this.pause(this._curMusicChannel);
        }
    }

    private _isValidVolume(value: number | AUTO_CONFIG): boolean {
        if (value === 'AUTO' || (value >= 0 && value <= 1)) {
            return true;
        }
        return false;
    }

    /**
     * On game resume
     */
    private _onGameResume() {
        this.checkResumeContext()
            .then(() => {
                let isResumeMusic: boolean = false;
                this._soundChannels.forEach(([howl, ids], soundId) => {
                    const isMusic = MusicIds.indexOf(soundId) !== -1;
                    if (
                        isMusic &&
                        (soundId !== this._curMusicChannel ||
                            (soundId === this._curMusicChannel && this._isDisableMusic))
                    ) {
                        return;
                    }
                    if (ids instanceof SafeCollection) {
                        const duplicateMusics: number[] = [];
                        ids.forEach((id) => {
                            if (isMusic) {
                                if (!isResumeMusic) {
                                    isResumeMusic = true;
                                    howl.play(id);
                                } else {
                                    duplicateMusics.push(id);
                                }
                            } else {
                                howl.play(id);
                            }
                        });
                        if (isMusic && duplicateMusics.length > 0) {
                            //TODO: hotfix for bug duplicate music on 6s+
                            duplicateMusics.forEach((id) => howl.stop(id));
                        }
                    } else {
                        if (howl) {
                            howl.play(ids);
                        } else {
                            console.warn(`[SOUND] ${soundId} cant resume!`);
                        }
                    }
                    // console.warn(`[SOUND] ${soundId} resumed!`);
                });
                Howler.mute(false);
            })
            .catch((reason) => {
                Howler.mute(false);
                console.error('[SOUND] Cant Resume Context! ' + reason);
            });
    }

    // On game interrupt
    private _onInterrupt() {
        this._soundChannels.forEach(([howl, ids]) => {
            if (ids instanceof SafeCollection) {
                ids.forEach((id) => howl.pause(id));
            } else {
                howl.pause(ids);
            }
            // console.warn(`[SOUND] ${soundId} paused!`);
        });

        Howler.mute(true);

        if (Howler.ctx) {
            if (this._isSuspendContext) {
                Howler.ctx
                    .suspend()
                    // #!if ENV === 'development'
                    .then(() => {
                        console.log('[SOUND] InterruptAC: Manual Suspended!');
                    })
                    // #!endif
                    .catch((reason) => {
                        console.warn('[SOUND] InterruptAC: Manual Suspended, Error ' + reason);
                    });
            } else {
                console.log('[SOUND] InterruptAC: ' + Howler.ctx.state);
            }
        } else {
            console.warn('[SOUND] InterruptAC: Context is null!');
        }
    }

    /**
     * Check and resume audio context
     */
    public checkResumeContext(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            if (Howler.ctx) {
                if (Howler.ctx.state !== 'running') {
                    Howler.ctx
                        .resume()
                        .then(() => {
                            // console.warn('[SOUND] ResumeAC: Resume success!');
                            resolve();
                        })
                        .catch((reason) => {
                            console.error(`[SOUND] ResumeAC: Resume failed! (${reason})`);
                            reject(reason);
                        });
                } else {
                    // console.warn(`[SOUND] ResumeAC: Skip (${Howler.ctx.state})`);
                    resolve();
                }
            } else {
                if (this._soundChannels.size > 0) {
                    console.error('[SOUND] ResumeAC: AC null');
                    reject('AudioContext is null');
                } else {
                    // No sound need to resume, on start game, skip
                    console.warn('[SOUND] ResumeAC: AC null on start game, skip');
                    resolve();
                }
            }
        });
    }

    public get isDisableMusic(): boolean {
        return this._isDisableMusic;
    }

    public disableMusic(): void {
        this._isDisableMusic = true;
    }

    public enableMusic(): void {
        this._isDisableMusic = false;
    }

    private _getSound(sound: SoundId): SoundInfo {
        const configIdx = SoundNames.indexOf(sound);
        if (configIdx === -1) {
            console.error(`[SOUND] ${sound} not found!`);
            return null;
        }
        const rawConfig = RawConfigs[configIdx];
        const info: SoundInfo = {
            pack: rawConfig.p,
            sprite: 's' in rawConfig ? rawConfig['s'] : undefined,
            loop:('l' in rawConfig ? Number.parseInt(rawConfig['l'] + '') : 0) === 1,
            volume: 'v' in rawConfig ? Number.parseFloat(rawConfig['v'] + '') : 1,
            isMusic: ('m' in rawConfig ? Number.parseInt(rawConfig['m'] + '') : 0) === 1,
        };
        return info;
    }

    private _isSoundIdValid(soundId: SoundId): boolean {
        return SoundNames.indexOf(soundId) !== -1;
    }

    public onVisibilityChange(isVisible: boolean) {
        if (isVisible) {
            this._onGameResume();
        } else {
            this._onInterrupt();
        }
    }

    public getCurrentMusicId(): SoundId {
        return this._curMusicChannel;
    }

    public setVolume(vol: number, isToggle: boolean = false, isPause: boolean = false) {
        console.warn(`[SOUND] Set Vol: ${vol}`);
        !isPause && (this._volume = vol);
        Howler.volume(vol);
        if (isToggle) {
            Events.emit(UIEvents.TOGGLE_SOUND, vol);
        } else if (vol > 0 && !this._isSoundOn) {
            this.setSoundOption(true);
        }
    }
}
