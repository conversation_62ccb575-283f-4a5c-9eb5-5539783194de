export interface Key {
    keyCode: string;
    isDown: boolean;
    isUp: boolean;
    press: any; //function on press
    release: any; //function on release
}

export class Keyboard {
    constructor() {
        this.addEventListeners();
    }

    private _keyList: Key[] = [];

    public addEventListeners() {
        window.addEventListener('keydown', this._keyDownHandler.bind(this), false);
        window.addEventListener('keyup', this._keyUpHandler.bind(this), false);
    }

    public removeEventListeners() {
        window.removeEventListener('keydown', this._keyDownHandler);
        window.removeEventListener('keyup', this._keyUpHandler);
    }

    /**
     * Add events handler for a given key code
     * @param keyCode KeyboardEvent.code
     * @param pressed callback for keydown event
     * @param released callback for keyup event
     */
    public addKey(keyCode: string, pressed: () => {} = undefined, released: () => {} = undefined) {
        const key: Key = {
            keyCode: keyCode,
            isDown: false,
            isUp: true,
            press: pressed,
            release: released
        };

        this._keyList.push(key);

        return key;
    }

    public removeKey(value: Key) {
        const index: number = this._keyList.indexOf(value);
        if (index !== -1) {
            this._keyList.splice(index, 1);
        }
    }

    private _keyDownHandler(event: KeyboardEvent) {
        for (let i: number = 0; i < this._keyList.length; i++) {
            const key: Key = this._keyList[i];

            if (event.code === key.keyCode) {
                if (key.isUp && key.press) key.press(event);
                key.isDown = true;
                key.isUp = false;
                event.preventDefault();

                break;
            }
        }
    }

    private _keyUpHandler(event: KeyboardEvent) {
        for (let i: number = 0; i < this._keyList.length; i++) {
            const key: Key = this._keyList[i];

            if (event.code === key.keyCode) {
                if (key.isDown && key.release) key.release(event);
                key.isDown = false;
                key.isUp = true;
                event.preventDefault();
                break;
            }
        }
    }
}