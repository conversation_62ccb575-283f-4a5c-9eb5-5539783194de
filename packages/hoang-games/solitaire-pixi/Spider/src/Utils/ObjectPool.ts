/**
 * Based from Parallel Extension Extras and other ObjectPool implementations.
 * Uses .add(T) and .take():T
 */

const ABSOLUTE_MAX_SIZE = 65536;

export type GenerateCallback<T> = (...args: any[]) => T;
export type RecycleCallback<T> = (object: T) => void;
export type DestroyCallback<T> = (poolObjects: T[]) => void;

export class ObjectPool<T> {
    private _name: string;
    private _maxSize: number;
    private _pool: T[];
    private _generator: GenerateCallback<T>;
    private _recycler: RecycleCallback<T>;
    private _destroyer: DestroyCallback<T>;
    private _localAbsMaxSize: number;
    private _initialized: boolean;
    private _debug: boolean;

    constructor(maxSize: number, generator: GenerateCallback<T>, recycler: RecycleCallback<T>, destroyer: DestroyCallback<T>, name: string, createInstances?: number, debug?: boolean) {
        if (isNaN(maxSize) || maxSize < 1) {
            throw new Error('Must be at valid number least 1');
        }
        if (maxSize > ABSOLUTE_MAX_SIZE) {
            throw new Error(`Must be less than or equal to ${ABSOLUTE_MAX_SIZE}`);
        }

        this._maxSize = maxSize;
        this._generator = generator;
        this._recycler = recycler;
        this._localAbsMaxSize = Math.min(maxSize * 2, ABSOLUTE_MAX_SIZE);
        this._name = name;
        this._debug = debug;

        this._pool = [];
        this._initialized = false;
        if (createInstances > 0) {
            this.setup(createInstances);
        }
    }

    public get maxSize(): number {
        return this._maxSize;
    }

    public get count(): number {
        return this._pool?.length || 0;
    }

    public clear(): void {
        this._pool.length = 0;
    }

    public toArrayAndClear(): T[] {
        const p = this._pool;
        this._pool = [];
        return p;
    }

    public take(factory?: () => T): T {
        this._checkInit();
        if (!this._generator && !factory) {
            throw new Error('Must provide a factory if on was not provided at construction time');
        }
        this._debug && console.warn(`[POOL] ${this._name} take, current size: ${this.count}`);
        return this._pool.pop() || (factory && factory()) || this._generator();
    }

    public return(object: T): void {
        this._checkInit();
        if (this._pool.length >= this._localAbsMaxSize) {
            console.warn(`[POOL] Overhead of pool '${this._name}', status: ${this._pool.length}/${this._maxSize}`);
        } else {
            if (this._recycler) {
                this._recycler(object);
            }
            this._pool.push(object);
            this._debug && console.warn(`[POOL] ${this._name} return, new size: ${this.count}`);
        }
    }

    public setup(count: number) {
        if (this._pool.length < count) {
            const diff = count - this._pool.length;
            for (let i = 0; i < diff; i++) {
                this._pool.push(this._generator());
            }
            this._debug && console.warn(`[POOL] ${this._name} create ${diff} item to prepare, size: ${this.count}`);
        }
        this._initialized = true;
    }

    public destroy() {
        this._generator = null;
        this._recycler = null;
        this._destroyer?.(this._pool);
        this._pool.length = 0;
    }

    private _wasLogErr: boolean = false;
    private _checkInit(): void {
        if (!this._initialized && !this._wasLogErr) {
            this._wasLogErr = true;
            console.error(`[POOL] '${this._name}' not init yet.`);
        }
    }
}
