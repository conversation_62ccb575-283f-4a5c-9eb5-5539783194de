import AppConstants, { SuitMode } from '../AppConstants';
import { Scheduler } from '../core/utility/scheduler/Scheduler';
import { Tween } from '../core/utility/tween/Tween';

export interface IPoint {
    x: number;
    y: number;
}
export type DisplayableObject = {alpha: number, visible: boolean};
export type LineEquation = { a: number; b: number; c: number;};

export default class Utils {

    private static _numberFormatter: Intl.NumberFormat = null;

    public static convertToMoneyFormat(amount: number): string {
        if (this._numberFormatter === null) {
            this._numberFormatter = new Intl.NumberFormat('en', {
                // style: 'number',
                currency: 'USD',
                maximumFractionDigits: 2,
                minimumFractionDigits: 2
            });
        }

        return this._numberFormatter.format(amount);
    }

    public static convertTimeUnixToDate(unix_timestamp: number, oneLine: boolean = false) {
        //#847185 StackOverflow
        // multiplied by 1000 so that the argument is in milliseconds, not seconds.
        const date = new Date(unix_timestamp);
        // Hours part from the timestamp
        const hours = date.getHours();
        // Minutes part from the timestamp
        const minutes = ('0' + date.getMinutes()).substr(-2);
        // Seconds part from the timestamp
        const seconds = ('0' + date.getSeconds()).substr(-2);

        const day = ('0' + date.getUTCDate()).substr(-2);
        const month = ('0' + (date.getMonth() + 1)).substr(-2);
        const year = date.getUTCFullYear();

        // Will display time in 10:30:23 format
        const lineBreak = (oneLine) ? ' ' : '\n';
        const formattedTime = hours + ':' + minutes + ':' + seconds + lineBreak + day + '/' + month + '/' + year;

        return formattedTime;
    }

    public static getDateFromTime(unix_timestamp: number, needYear: boolean = false): string {
        const date = new Date(unix_timestamp);
        const day = ('0' + date.getUTCDate()).substr(-2);
        const month = ('0' + (date.getMonth() + 1)).substr(-2);
        const year = date.getUTCFullYear();

        const yearStr = (needYear) ? '/' + year : '';
        return `${day}/${month}` + yearStr;
    }

    public static getTimeUnixDate(unix_timestamp: number) {
        const date = new Date(unix_timestamp);
        const hours = date.getHours();
        const minutes = ('0' + date.getMinutes()).substr(-2);
        const seconds = ('0' + date.getSeconds()).substr(-2);

        return hours + ':' + minutes + ':' + seconds;
    }

    public static getDateUnixDate(unix_timestamp: number) {
        const date = new Date(unix_timestamp);
        const day = ('0' + date.getUTCDate()).substr(-2);
        const month = ('0' + (date.getMonth() + 1)).substr(-2);
        const year = date.getUTCFullYear();

        return day + '/' + month + '/' + year;
    }

    public static shortUuidv4() {
        return 'xxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    public static clamp(val: number, min: number, max: number): number {
        return val > max ? max : val < min ? min : val;
    }

    public static _round(num: number, pow: number = 2): number {
        return Math.round((num + Number.EPSILON) * 10 ** pow) / 10 ** pow;
    }

    public static shortFormatMoneyNumber(value: number, totalOfNumber: number = 5, decimalSize: number = 2): string {
        const suffixes = ['', 'K', 'M', 'B'];
        let suffixIndex = 0;
        while (value >= 1000 && suffixIndex < suffixes.length - 1) {
            value /= 1000;
            suffixIndex++;
        }
        let floorVal = value;
        const strVal = `${floorVal}`;
        const dotCharIdx = strVal.indexOf('.');
        if (dotCharIdx > -1) {
            const wholePart = strVal.substring(0, dotCharIdx);
            const decimalPart = strVal.substring(dotCharIdx + 1, strVal.length);
            const trimDecimalLength = this.clamp(totalOfNumber - (wholePart.length + 1), 0, decimalSize);
            const resultDecimal = decimalPart.substring(0, trimDecimalLength);
            floorVal = Number.parseFloat(`${wholePart}.${resultDecimal}`);
        }
        const suffix = suffixes[suffixIndex];
        return `${this.convertToMoneyFormat(floorVal)}${suffix}`;
    }

    public static randomRange(min: number, max: number, int: boolean = false) {
        const delta = max - min;
        const rnd = Math.random();
        let result = min + rnd * delta;

        if (int) {
            result = Math.round(result);
        }

        return result;
    }

    public static isDeveloper(): boolean {
        const host: string = window.location.hostname;
        const port = window.location.port;
        const isLocalServer = (host.search('192.168') !== -1 && port.substring(0, 3) === '808');
        if (host === 'localhost' || isLocalServer) {
            return true;
        }
        return false;
    }

    public static round(num: number, pow: number = 2): number {
        return Math.round((num + Number.EPSILON) * 10 ** pow) / 10 ** pow;
    }

    public static async Delay(timeDelay: number) {
        return new Promise<void>((resolve) => {
            Scheduler.setTimeout(timeDelay, () => { resolve(); });
        });
    }

    public static _fadeIn(obj: DisplayableObject, duration: number = 100, endCallback?: Function) {
        obj.visible = true;
        obj.alpha = 0;
        Tween.to(duration, obj, { alpha: 1 }, 0, () => {
            if (endCallback) {
                endCallback();
            }
        });
    }

    public static _fadeOut(obj: DisplayableObject, duration: number = 100, endCallback?: Function, resetAlphaAfterDone: boolean = true) {
        Tween.to(duration, obj, { alpha: 0 }, 0, () => {
            obj.visible = false;
            if (resetAlphaAfterDone) {
                obj.alpha = 1;
            }
            if (endCallback) {
                endCallback();
            }
        });
    }

    public static _zoomEffect(obj: any, effTime: number = 100, callback: Function = null, toScale: number = 1.2) {
        Tween.to(effTime, obj.scale, { x: toScale, y: toScale }, 0, () => {
            Tween.to(effTime, obj.scale, { x: 1, y: 1 }, 0, () => {
                if (callback) {
                    callback();
                }
            }, null);
        }, null);
    }

    public static _roundTwo(num: number, pow: number = 2): number {
        return Math.round((num + Number.EPSILON) * 10 ** pow) / 10 ** pow;
    }

    public static toMMSS(seconds, useHour = false) {
        const h = Math.floor((seconds % (3600 * 24)) / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = Math.floor(seconds % 60);

        const hDisplay = h + ':';
        let mDisplay;
        if (useHour) {
            mDisplay = m > 9 ? m + ':' : '0' + m + ':';
        } else {
            mDisplay = m + ':';
        }
        const sDisplay = s > 9 ? s + '' : '0' + s;

        if (useHour) {
            return hDisplay + mDisplay + sDisplay;
        }
        return mDisplay + sDisplay;
    }

    public static saveNewScore(score: number, name: string, mode: SuitMode, date: string, timer: number) {
        const scoreList = JSON.parse(localStorage.getItem(AppConstants.GAME_NAME));
        const newScore = {
            score,
            name,
            mode,
            date,
            timer
        };
        if (scoreList) {
            scoreList.push(newScore);
            scoreList.sort((a, b) => {
                return b.score - a.score;
            });
            localStorage.setItem(AppConstants.GAME_NAME, JSON.stringify(scoreList));
        } else {
            localStorage.setItem(AppConstants.GAME_NAME, JSON.stringify([newScore]));
        }
    }

    public static getScoreList(mode: SuitMode) {
        const scoreList = JSON.parse(localStorage.getItem(AppConstants.GAME_NAME));
        if (scoreList) {
            const scoreListByMode = scoreList.filter((item) => item.mode === mode).slice(0, 10);
            return scoreListByMode;
        }
        return scoreList;
    }

    public static getTimer() {
        const today = new Date();

        const date = today.getDate();
        const month = today.getMonth() + 1;
        const year = today.getFullYear();
        const hours = today.getHours();
        const minutes = today.getMinutes();
        const m = minutes > 9 ? minutes : '0' + minutes;
        const formattedDateTime = `${date}-${month}-${year}, ${hours}:${m}`;
        return formattedDateTime;
    }

    public static getURLParam(name: string): string {
        const url = new URL(window.location.href);
        return url.searchParams.get(name);
    }
}