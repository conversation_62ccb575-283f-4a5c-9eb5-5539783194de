
export enum DrawMode {
    ONE = 1,
    THREE = 3,
}

export enum SuitMode {
    ONE = 1,
    TWO = 2,
    FOUR = 4,
}

export interface ResultData {
    score: number;
    highScore: number;
    time: string;
}

export default class AppConstants {
    static readonly GAME_NAME = `${process.env.BUNDLE_NAME}`;
    static readonly BASE_SCREEN_WIDTH = 1280;
    static readonly BASE_SCREEN_HEIGHT = 720;
    static readonly MAX_CANVAS_WIDTH = 1560;
    static readonly MAX_CANVAS_HEIGHT = 720;
    static readonly MAX_CANVAS_MARGIN_Y = 0; // (MAX_CANVAS_HEIGHT - BASE_SCREEN_HEIGHT) / 2;

    // Popup style
    static readonly POPUP_HEIGHT = 397;
    static readonly POPUP_WIDTH = 597;
    static readonly POPUP_BACKGROUND_COLOR = 0x30303c;
    static readonly POPUP_TEXT_COLOR = 0xffffff;
    static readonly POPUP_TEXT_HIGHLIGHT_COLOR = 0xecb263;
    static readonly POPUP_TEXT_FONT = 'intersemibold';
    static readonly POPUP_CUSTOM_TEXT_FONT = 'intersemibold';
    static readonly POPUP_TXT_FONT_SIZE = 27; // (Popup content)
    static readonly POPUP_TXT_FONT_WEIGHT = 'normal';
    static readonly POPUP_BUTTON_TXT_FONT_SIZE = 27; // (Popup button)

    // Other font size
    static readonly MENU_TXT_FONT_SIZE = 20;

    static readonly CENT_PER_DOLLAR: number = 100;
}
