import ResourceManager from '../ResManagers/ResourceManager';

/**
 * Class that help on multi language management.
 */
export default class Localization {

    private static _languageData: any = null;
    private static _defaultLang = 'en_US';

    /**
     * Load all data of given language
     * @param lang language key: vi_VN, en_US, etc...
     */
    public static loadLanguage(lang: string) {
        // TODO: if the given lang data aren't loaded let load it directly
        if (ResourceManager.isResourceAvailable(lang)) {
            this._languageData = ResourceManager.getJsonData(lang);
        } else {
            this._languageData = ResourceManager.getJsonData(Localization._defaultLang);
        }
    }

    /**
     * Get text of the current language with given key
     * @param key text key in json file
     */
    public static getText(key: string, ...params: any[]): string {
        let strText: string = this._languageData[key];
        if (!strText || strText.length === 0) {
            console.error(`[TEXT] Missing string Id: ${key}`);
            return 'NaN';
        }
        if (params.length) {
            for (let i = 0; i < params.length; i++) {
                strText = strText.replace('%param%', String(params[i]));
            }
        }
        return strText;
    }
}