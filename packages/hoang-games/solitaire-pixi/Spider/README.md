## Slot Games

Using PixiJS rendering engine and TypeScript. No game editor.

---
## Setup project
After clone project please install all needed packages using command: `yarn install`

---
## How to build project

Build scripts are defined in package.json. Run this command to build:
- `yarn build` (build project without optimization)
- `yarn release` (build project with optimization and minimize JS/HTML codes)

---
## How to start development
Run this command: yarn run start. Then play game at: http://localhost:8080/
It will run webpack-dev-server service and listen for new code changes in code then re-load & build automatically.

## Pre-build scripts
|       Command       |                    Description                   |                                         Note                                         |
|:-------------------:|:------------------------------------------------:|:------------------------------------------------------------------------------------:|
| `ver`               | Change bundle version                            |                                                                                      |
| `atlas`             | Generate atlas sprites                           |                                                                                      |
| `tinify`            | Optimize textures in assets folder using TiniPNG | Run before send build or only when there are some new assets need to optimize        |
| `sound`             | Generate sound packs                             |                                                                                      |
| `spine`             | Build binary spine (.skel)                       | Run before release, turn on flag `useBinarySpine` in `package.json` to use this type |
| `payline`           | Generate payline from JSON to code               |                                                                                      |
| `start`             | Run debug game via localhost                     |                                                                                      |
| `start-ip`          | Run debug game and sharing on LAN (use LocalIP)  |                                                                                      |
| `stat`              | Analyze debug bundle                             |                                                                                      |
| `stat-release`      | Analyze release bundle                           |                                                                                      |
| `kill`              | Kill all process using port 8080                 |                                                                                      |
| `network`           | Get local & public IP address                    |                                                                                      |
| `token <id_number>` | Get/Generate token for input user id             |                                                                                      |

Tool generate table: [Talble Generator](https://www.tablesgenerator.com/markdown_tables#)

## Libraries
### [Preprocessor](https://www.npmjs.com/package/webpack-preprocessor-loader)
- Bring the awesome "Conditional Compilation" to the Webpack, and more.
- [Example:](https://www.npmjs.com/package/webpack-preprocessor-loader#usage)
    ```
    // #!if ENV === 'development'
    import someModule from 'module-name';
    // #!else
    const anotherModule = import('another-module-name');
    // #!endif

    // #!if ENV === 'development'
    console.log(someModule);
    // #!endif
    ```
### [Eslint]
- Multiline scopes
    ```
    /* eslint-disable no-unused-vars */
    // define variables
    /* eslint-enable no-unused-vars */
    ```

## Debug Tools
### Pixi Inspector
- Chrome Ext: [Pixi.js devtools](https://chrome.google.com/webstore/detail/pixijs-devtools/aamddddknhcagpehecnhphigffljadon)
- Repos: [pixi-inspector](https://github.com/bfanger/pixi-inspector)

### Weinre (!Just available with HTTP protocol)
- Install:
  - npm install -g weinre
  - Run server: `weinre --boundHost ************ --httpPort 8888 --verbose`
  - Open website: `http://************:8888`
  - Copy target script, similar to: `http://************:8888/target/target-script-min.js#anonymous`
  - Press button `weinre` in cheat menu, and parse that URL
  - Open website: `http://************:8888/client/#anonymous`, and choose `Console`

### Apply feature patch:
- Command: (Run and check file *.rej)
  ```
  git apply --reject --whitespace=fix patchs/<file_name>
  ```
