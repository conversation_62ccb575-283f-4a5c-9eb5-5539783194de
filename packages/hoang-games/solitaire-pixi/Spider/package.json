{"name": "solitaire", "description": "", "version": "1.0.0", "title": "Solitaire Spider", "bundleName": "Spider", "main": "main.js", "useBinarySpine": true, "mangleWithCustomRegex": false, "debugDrawCallOnReleaseCheat": false, "tinifyApiKey": "V1mwNfJvlvzVMf6YKyJpWvnNFnmzyCZT", "scripts": {"release": "yarn clean & webpack --mode production --env mode=production --env cheat=false --progress", "release-cheat": "yarn release --env cheat=true --progress", "build": "yarn clean & webpack --env mode=build --env cheat=true --progress", "clean": "rimraf dist/*", "start": "webpack serve --env mode=debug --env cheat=true --progress", "start-ip": "yarn start --host 0.0.0.0", "make-bundle": "node script/makeBundle.js", "local": "npx http-server -p 8181 dist", "network": "node script/networkInfo.js", "kill": "npx kill-port 8080", "stat": "yarn build --env analyzer=true", "stat-release": "yarn release --env analyzer=true", "zip": "node script/zipBundle.js", "tinify": "node script/tinifyTextures.js", "atlas": "node script/packTextures.js", "spine": "node script/spineConvertBinary.js", "sound": "yarn clean-sound & node script/packSounds.js", "clean-sound": "rimraf build/assets/Sound/*", "payline": "node script/convertPaylines.js", "ver": "yarn version --no-git-tag-version --no-commit-hooks", "lint": "eslint --ext .ts,.js .", "lint:fix": "eslint --fix --ext .ts,.js .", "token": "node script/getDebugToken.js", "cheat": "node script/sendCheatMatrix.js", "viewer": "java -jar script/skeletonViewer-3.8.99.jar", "analyze-bundle": "node script/analyzeBundle.js", "postver": "yarn tinify & yarn spine", "init-env": "node script/ci_setupEnv.js env", "init-schedule": "node script/ci_setupEnv.js schedule", "init-ci": "node script/ci_setupEnv.js all", "send-notify": "node script/sendNotify.js", "check-size": "du -ah -d 2 dist | sort -h -r"}, "license": "ISC", "author": "@hoang.nguyen", "private": true, "dependencies": {"@pixi/app": "6.0.4", "@pixi/constants": "6.0.4", "@pixi/core": "6.0.4", "@pixi/display": "6.0.4", "@pixi/graphics": "6.0.4", "@pixi/interaction": "6.0.4", "@pixi/layers": "1.0.11", "@pixi/loaders": "6.0.4", "@pixi/math": "6.0.4", "@pixi/mesh-extras": "6.0.4", "@pixi/mixin-cache-as-bitmap": "6.0.4", "@pixi/mixin-get-child-by-name": "6.0.4", "@pixi/mixin-get-global-position": "6.0.4", "@pixi/runner": "6.0.4", "@pixi/settings": "6.0.4", "@pixi/sprite": "6.0.4", "@pixi/spritesheet": "6.0.4", "@pixi/text": "6.0.4", "@pixi/text-bitmap": "6.0.4", "@pixi/ticker": "6.0.4", "@pixi/utils": "6.0.4", "fontfaceobserver": "2.1.0", "howler": "2.2.3", "jszip": "^3.10.1", "uuid": "3.4.0"}, "devDependencies": {"@types/axios": "0.14.0", "@types/events": "3.0.0", "@types/file-saver": "2.0.3", "@types/fontfaceobserver": "0.0.6", "@types/howler": "2.2.3", "@types/platform": "1.3.4", "@typescript-eslint/eslint-plugin": "4.32.0", "@typescript-eslint/parser": "4.32.0", "adm-zip": "^0.5.7", "axios": "0.20.0", "chokidar": "^3.5.3", "clipboardy": "2.3.0", "copy-webpack-plugin": "11.0.0", "esbuild-loader": "2.21.0", "eslint": "8.46.0", "file-saver": "2.0.5", "fork-ts-checker-webpack-plugin": "8.0.0", "free-tex-packer-core": "0.3.4", "fs-extra": "^8.1.0", "html-replace-webpack-plugin": "^2.6.0", "html-webpack-plugin": "5.5.0", "http-server": "^14.1.1", "is-image": "3.0.0", "public-ip": "4.0.4", "rimraf": "3.0.2", "short-uuid": "4.2.0", "style-loader": "2.0.0", "terser-webpack-plugin": "5.3.9", "tinify": "^1.7.1", "typescript": "4.4.3", "underscore": "^1.13.1", "webpack": "5.88.2", "webpack-bundle-analyzer": "4.9.0", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1", "webpack-merge": "5.9.0", "webpack-preprocessor-loader": "1.3.0"}}