const axios = require('axios');
const chalk = require('chalk');
const readline = require('readline');

const BITBUCKET_API_URL = 'https://api.bitbucket.org/2.0/repositories';
const DEPLOYMENT_ENV = [
    ['test', 'Test'],
    ['test-revive', 'Test'],
    ['staging', 'Staging'],
    ['staging-bundle', 'Staging'],
    ['staging-revive', 'Staging'],
    // ['production', 'Production'],
];
const DEPOLOY_FOLDER_MAPPING = {
    test: 'dev',
    staging: 'staging',
    production: '',
};
const SCHEDULER_CONFIGS = [
    // compute and set cron on timze +0 (hour -7 for Vietnam)
    {
        branch: 'dev',
        pipeline: 'dev-build',
        cron: '0 0 4 ? * MON-FRI *', // 11:00:00 on working day
    },
    {
        branch: 'dev',
        pipeline: 'dev-build',
        cron: '0 0 10 ? * MON-FRI *', // 17:00:00 on working day
    },
];
const WORKSPACE_UUID = '{f1e84dc0-5cf3-4917-93e0-3a0074e6a6ae}';
const BasicAuthConfig = {
    username: '',
    password: 'VFddyRJGjaqBa7CPXgSV', //<EMAIL>
};
const liveSchedulers = [];
let repositorySlug;
let apiUrl;

// get user and app token has full Pipelines permissions
function askUser() {
    console.log(chalk.blue(' **** Input user token ... ****'));
    const inputInterface = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
    });
    const onUserInputEnded = () => {
        apiUrl = `${BITBUCKET_API_URL}/${WORKSPACE_UUID}/${repositorySlug}`;
        console.log(` > ${chalk.green('finished')}\n`);
    };
    return new Promise((resolve) => {
        inputInterface.question('Repository Slug: ', (slug) => {
            repositorySlug = slug;
            inputInterface.question('Username: ', (name) => {
                BasicAuthConfig.username = name;
                inputInterface.question('AppPassword: ', (token) => {
                    BasicAuthConfig.password = token || BasicAuthConfig.password;
                    inputInterface.close();
                    onUserInputEnded();
                    resolve();
                });
            });
        });
    });
}

// get all live environments
function getEnvironments() {
    return new Promise((resolve, reject) => {
        const envInfos = [];
        axios({
            url: `${apiUrl}/environments/`,
            method: 'get',
            auth: BasicAuthConfig,
        })
            .then((res) => {
                if (res.status === 200) {
                    const environments = res.data.values;
                    for (let i = 0; i < environments.length; i++) {
                        const envCfg = environments[i];
                        envInfos.push({ slug: envCfg.slug, uuid: envCfg.uuid, type: envCfg.environment_type.name });
                    }
                }
                resolve(envInfos);
            })
            .catch((err) => {
                reject(err);
            });
    });
}

// remove all live environments
async function clearEnvironments() {
    const envInfos = await getEnvironments();
    return new Promise((resolve) => {
        if (envInfos.length > 0) {
            let finisedTask = 0;
            const checkTaskComplete = () => {
                finisedTask++;
                if (finisedTask === envInfos.length) {
                    console.log(` > ${chalk.green('finished')}\n`);
                    resolve();
                }
            };
            console.log(chalk.blue(' **** Remove live env ... ****'));
            for (let i = 0; i < envInfos.length; i++) {
                const env = envInfos[i];
                // console.log(` > delete ${env.slug}...`);
                axios({
                    url: `${apiUrl}/environments/${env.uuid}`,
                    method: 'delete',
                    auth: BasicAuthConfig,
                }).then((res) => {
                    if (res.status === 204) {
                        console.log(` > ${chalk.yellow('removed')} ${env.slug}`);
                        checkTaskComplete();
                    } else {
                        console.log(` > remove ${env.slug} failed - ${res.status} - ${res}`);
                        checkTaskComplete();
                    }
                });
            }
        }
    });
}

// create development environments from template
async function createEnvironments() {
    return new Promise((resolve) => {
        let finisedTask = 0;
        const checkTaskComplete = () => {
            finisedTask++;
            if (finisedTask === DEPLOYMENT_ENV.length) {
                console.log(` > ${chalk.green('finished')}\n`);
                return resolve();
            }
        };
        console.log(chalk.blue(' **** Create Deployment Env ... ****'));
        for (let i = 0; i < DEPLOYMENT_ENV.length; i++) {
            const slug = DEPLOYMENT_ENV[i][0];
            const type = DEPLOYMENT_ENV[i][1];
            axios({
                url: `${apiUrl}/environments/`,
                method: 'post',
                auth: BasicAuthConfig,
                data: {
                    name: slug,
                    environment_type: {
                        type: 'deployment_environment_type',
                        name: type,
                    },
                },
            })
                .then((res) => {
                    if (res.status === 201) {
                        console.log(` > ${chalk.yellow('created')} ${slug}`);
                    } else {
                        console.log(' > something went wrong!', res);
                    }
                    checkTaskComplete();
                })
                .catch((err) => {
                    const status = err.response.status;
                    const msg = err.response.data;
                    if (status === 404) {
                        console.log(` > service not found ${status}`, msg);
                    } else if (status === 400 || status === 409) {
                        console.log(' > env slug is exist! ', msg);
                    }
                    checkTaskComplete();
                });
        }
    });
}

// list out all variables
async function getVariables() {
    const liveEnvs = await getEnvironments();

    for (let i = 0; i < liveEnvs.length; i++) {
        const env = liveEnvs[i];
        axios({
            url: `${apiUrl}/deployments_config/environments/${env.uuid}/variables`,
            method: 'get',
            auth: BasicAuthConfig,
        })
            .then((res) => {
                if (res.status === 200) {
                    const data = res.data;
                    console.log(`${env.slug} variables: `, data.values);
                } else {
                    console.log(' > something went wrong!', res);
                }
            })
            .catch((err) => {
                console.log(' > something went wrong!', err);
            });
    }
}

// setup variable for all deployment environments
async function addVariables() {
    console.log(chalk.blue(' **** Add variables config ... ****'));
    const liveEnvs = await getEnvironments();

    return new Promise((resolve) => {
        let finisedTask = 0;
        const checkTaskComplete = () => {
            finisedTask++;
            if (finisedTask === liveEnvs.length) {
                console.log(` > ${chalk.green('finished')}\n`);
                resolve();
            }
        };
        for (let i = 0; i < liveEnvs.length; i++) {
            const env = liveEnvs[i];
            const deployFolderKey = 'DEPLOY_FOLDER';
            const deployFolder = DEPOLOY_FOLDER_MAPPING[env.type.toLowerCase()];
            if (deployFolder.length > 0) {
                axios({
                    url: `${apiUrl}/deployments_config/environments/${env.uuid}/variables`,
                    method: 'post',
                    auth: BasicAuthConfig,
                    data: {
                        key: deployFolderKey,
                        value: deployFolder,
                        secured: false,
                    },
                })
                    .then((res) => {
                        const data = res.data;
                        if (res.status === 201) {
                            console.log(
                                ` > ${chalk.yellow('set')} ${deployFolderKey}(${chalk.grey(env.slug)}): ${deployFolder}`
                            );
                        } else {
                            console.log(' > something went wrong!', data);
                        }
                        checkTaskComplete();
                    })
                    .catch((err) => {
                        const status = err.response.status;
                        if (status === 409) {
                            console.log(' > variable exist!');
                        } else {
                            console.log(' > something went wrong!', err);
                        }
                        checkTaskComplete();
                    });
            }
        }
    });
}

async function getSchedulers() {
    return new Promise((resolve, reject) => {
        console.log(chalk.blue(' **** Get all schedulers ... ****'));
        axios({
            url: `${apiUrl}/pipelines_config/schedules/`,
            method: 'get',
            auth: BasicAuthConfig,
        })
            .then((res) => {
                if (res.status === 200) {
                    const schedulers = res.data.values;
                    // remove all elements
                    liveSchedulers.length = 0;
                    schedulers.forEach((cfg) => {
                        liveSchedulers.push({
                            uuid: cfg.uuid,
                            branch: cfg.target.ref_name,
                            pipeline: cfg.target.selector.pattern,
                            cron: cfg.cron_pattern,
                        });
                    });
                    console.log(` > ${chalk.green('finished')}\n`);
                    resolve();
                } else {
                    console.log(' > something went wrong!', res);
                    reject(res);
                }
            })
            .catch((err) => {
                const status = err.response.status;
                const msg = err.response.data;
                if (status === 404) {
                    console.log(` > service not found ${status}`, msg);
                } else if (status === 400 || status === 409) {
                    console.log(' > env slug is exist! ', msg);
                } else {
                    console.log(' > something went wrong!', msg);
                }
                reject(err);
            });
    });
}

async function removeSchedulers() {
    return new Promise((resolve, reject) => {
        if (liveSchedulers.length > 0) {
            console.log(chalk.blue(' **** Remove all schedulers ... ****'));
            let finisedTask = 0;
            const checkTaskComplete = () => {
                finisedTask++;
                if (finisedTask === liveSchedulers.length) {
                    console.log(` > ${chalk.green('finished')}\n`);
                    resolve();
                }
            };
            liveSchedulers.forEach((cfg) => {
                axios({
                    url: `${apiUrl}/pipelines_config/schedules/${cfg.uuid}`,
                    method: 'delete',
                    auth: BasicAuthConfig,
                })
                    .then((res) => {
                        if (res.status === 204) {
                            console.log(` > ${chalk.yellow('removed')} ${cfg.branch}: ${cfg.pipeline}: ${cfg.cron}`);
                        } else {
                            console.log(' > something went wrong!', res);
                        }
                        checkTaskComplete();
                    })
                    .catch((err) => {
                        const status = err.response.status;
                        const msg = err.response.data;
                        console.log(' > something went wrong!', status, msg);
                        reject(err);
                    });
            });
        } else {
            resolve();
        }
    });
}

async function createSchedulers() {
    return new Promise((resolve, reject) => {
        console.log(chalk.blue(' **** Create schedulers ... ****'));
        let finisedTask = 0;
        const checkTaskComplete = () => {
            finisedTask++;
            if (finisedTask === SCHEDULER_CONFIGS.length) {
                console.log(` > ${chalk.green('finished')}\n`);
                return resolve();
            }
        };
        SCHEDULER_CONFIGS.forEach((config) => {
            axios({
                url: `${apiUrl}/pipelines_config/schedules/`,
                method: 'post',
                auth: BasicAuthConfig,
                data: {
                    cron_pattern: config.cron,
                    enabled: false,
                    target: {
                        ref_name: config.branch,
                        ref_type: 'branch',
                        selector: {
                            pattern: config.pipeline,
                            type: 'custom',
                        },
                        type: 'pipeline_ref_target',
                    },
                    type: 'pipeline_schedule',
                },
            })
                .then((res) => {
                    if (res.status === 201) {
                        console.log(
                            ` > ${chalk.yellow('created')} on ${config.branch}, ${config.pipeline}: ${config.cron}`
                        );
                    } else {
                        console.log(' > something went wrong!', res);
                    }
                    checkTaskComplete();
                })
                .catch((err) => {
                    const status = err.response.status;
                    const msg = err.response.data;
                    console.log(' > something went wrong! ', status, msg);
                    reject(err);
                });
        });
    });
}

// feature controls
async function setupEnvironments() {
    await askUser();
    await clearEnvironments();
    await createEnvironments();
    await addVariables();
    // getVariables();
    return Promise.resolve();
}

async function setupSchedulers(isAskUser = true) {
    if (isAskUser) {
        await askUser();
    }
    await getSchedulers();
    await removeSchedulers();
    await createSchedulers();
}

async function setupAll() {
    await setupEnvironments();
    setupSchedulers(false);
}

if (process.argv.length > 2) {
    const remoteCommand = process.argv[2];
    switch (remoteCommand) {
        case 'env':
            setupEnvironments();
            break;
        case 'schedule':
            setupSchedulers();
            break;
        case 'all':
            setupAll();
            break;
        default:
            console.log(` > Unknown arg ${remoteCommand}, choose: env|schedule|all`);
            break;
    }
} else {
    console.log(' > Missing parameter, choose: env|schedule|all');
}
