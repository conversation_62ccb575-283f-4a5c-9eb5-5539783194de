const fs = require('fs/promises');
const crypto = require('crypto');
const chalk = require('chalk');

const checkSumFilePath = 'checksum.json';
const CS_TINIFY_PROPERTY = 'tinify';
const CS_ATLAS_PROPERTY = 'atlas';
const CS_SPINE_JSON_PROPERTY = 'spineJson';

/**
 * Get all files in given directory and all sub-directories
 * @param {*} path
 */
async function getFiles(path = '../build/assets/') {
    const entries = await fs.readdir(path, { withFileTypes: true });

    // Get files within the current directory and add a path key to the file objects
    const files = entries.filter((file) => !file.isDirectory()).map((file) => ({ ...file, path: path + file.name }));

    // Get folders within the current directory
    const folders = entries.filter((folder) => folder.isDirectory());

    /**
     * Add the found files within the subdirectory to the files array by calling the
     *  current function itself
     */
    for (const folder of folders) files.push(...(await getFiles(`${path}${folder.name}/`)));

    return files;
}

async function hashFile(filePath, algorithm = 'sha256') {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath).then((buffer) => {
            const sum = crypto.createHash(algorithm);
            sum.update(buffer);
            const hex = sum.digest('hex');
            resolve(hex);
        }).catch((err) => {
            console.log(`hash err: ${err}`);
            reject(err);
        });
    });
}

async function existAsync(filepath) {
    const isExist = await fs.access(filepath).then(() => true).catch(() => false);
    return isExist;
}

// sort object by keys method
const sortObject = (o) => o ? Object.keys(o).sort().reduce((r, k) => ((r[k] = o[k]), r), {}) : null;
const compareFunc = (a, b) => (a > b ? 1 : a < b ? -1 : 0);
const compareInLowerCaseFunc = (a, b) => {
    const lowerA = a.toLowerCase();
    const lowerB = b.toLowerCase();
    return compareFunc(lowerA, lowerB);
};

async function readChecksum(isSilent = false) {
    if (!isSilent) {
        console.log(chalk.magenta(' **** Reading checksum file ... ****'));
    }
    let checksumJson = {};
    let checksumContent = '';
    const isExist = await existAsync(checkSumFilePath);
    if (isExist) {
        checksumContent = await fs.readFile(checkSumFilePath);
    }
    return new Promise((resolve, reject) => {
        try {
            if (checksumContent) {
                checksumJson = JSON.parse(checksumContent);
                console.log(' > DONE');
            } else {
                console.log(' > Error, Create another one!');
            }
            resolve(checksumJson);
        } catch (err) {
            console.log(' > Error: Cant read file checksum!');
            reject(err);
        }
    });
}

async function saveChecksum(content, isSilent = false) {
    if (!isSilent) {
        console.log(chalk.magenta(' **** Writing checksum file ... ****'));
    }
    let isSuccess = false;
    await fs.writeFile(checkSumFilePath, JSON.stringify(content, null, 2), 'utf-8')
        .then(() => {
            if (!isSilent) {
                console.log(' > DONE');
            }
        })
        .catch((error) => {
            isSuccess = false;
            console.log(' > Error: ' + error.message);
        });
    return Promise.resolve(isSuccess);
}

module.exports = {
    getFiles,
    hashFile,
    sortObject,
    compareFunc,
    compareInLowerCaseFunc,
    existAsync,
    readChecksum,
    saveChecksum,
    CS_ATLAS_PROPERTY,
    CS_TINIFY_PROPERTY,
    CS_SPINE_JSON_PROPERTY
};
