const fs = require('fs/promises');
const JSZip = require('jszip');

async function make() {
    const zip = new JSZip();

    try {
        zip.file('ui_soldier_2.png', await fs.readFile('script/ui_soldier_2.png'));
        zip.file('ui_soldier_2.atlas', await fs.readFile('script/ui_soldier_2.atlas'));
        zip.file('ui_soldier_2.skel', await fs.readFile('script/ui_soldier_2.skel'));
        zip.file('logo.png', await fs.readFile('script/logo.png'));
        const zipBlob = await zip.generateAsync({ type: 'uint8array' });
        await fs.writeFile('build/assets/Game/bundle.zip', zipBlob);
        console.warn(' >> DONE <<');
    } catch (error) {
        console.error('Error:', error.message);
    }
}

make();

// utf8 example
// const zip = new JSZip();
// zip.file('amount.txt', '€15');
// zip.file('amount.txt').async('string'); // a promise of "€15"
// zip.file('amount.txt').async('arraybuffer'); // a promise of an ArrayBuffer containing €15 encoded as utf8
// zip.file('amount.txt').async('uint8array'); // a promise of an Uint8Array containing €15 encoded as utf8

// // with folders
// zip.folder('sub').file('file.txt', 'content');
// zip.file('sub/file.txt'); // the file
// // or
// zip.folder('sub').file('file.txt'); // the file
