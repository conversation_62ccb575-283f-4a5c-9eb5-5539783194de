const https = require('https');

const args = process.argv.slice(2);
const skipBranches = ['livetest/'];
let botToken;
let commitMessage;

function getCommitMessage(commit) {
    return new Promise((resolve, reject) => {
        const workspace = process.env.BITBUCKET_REPO_OWNER_UUID;
        const repo_slug = process.env.BITBUCKET_REPO_SLUG;
        const username = process.env.CI_USER;
        const password = process.env.CI_PASSWORD;
        const options = {
            method: 'GET',
            hostname: 'api.bitbucket.org',
            port: 443,
            path: `/2.0/repositories/${workspace}/${repo_slug}/commit/${commit}`,
            headers: {
                Authorization: 'Basic ' + Buffer.from(username + ':' + password, 'utf8').toString('base64'),
            },
        };
        const req = https.request(options, (res) => {
            res.on('data', (chunk) => {
                const rawDataStr = chunk + '';
                if (res.statusCode === 200) {
                    const resObj = JSON.parse(rawDataStr);
                    let commitMsg = '';
                    if (resObj['message']) {
                        commitMsg = resObj.message.toString().replace('\n', '');
                        resolve(commitMsg);
                    } else {
                        const errMsg = 'Missing message property in response';
                        console.log(`[ERROR]: ${errMsg}\n${rawDataStr}`);
                        reject(rawDataStr);
                    }
                }
            });
            if (res.statusCode === 200) {
                if (res.statusMessage !== 'OK') {
                    const errMsg = `${res.statusMessage}`;
                    console.error(`[ERROR]: ${errMsg}`);
                    reject(errMsg);
                }
            } else {
                const errMsg = `${res.statusCode} - ${res.statusMessage}`;
                console.log(`[ERROR]: ${errMsg}`);
                reject(res.statusCode);
            }
        });
        req.on('error', (error) => {
            console.error(`[ERROR]: ${error}`);
            reject(error);
        });
        req.end();
    });
}

function sendMessage(chatId, message) {
    console.log(' > Send message:');
    const encodeMsg = encodeURI(message);
    const options = {
        hostname: 'api.telegram.org',
        port: 443,
        path: `/bot${botToken}/sendMessage?chat_id=${chatId}&text=${encodeMsg}&disable_web_page_preview=true`,
        method: 'GET',
    };
    const req = https.request(options, (res) => {
        if (res.statusCode === 200) {
            if (res.statusMessage === 'OK') {
                console.log('DONE.');
            } else {
                console.log(data);
            }
        } else {
            console.log(`[ERROR]: ${res.statusCode} - ${res.statusMessage}`);
        }
    });
    req.on('error', (error) => {
        console.error(`[ERROR]: ${error}`);
    });
    req.end();
}

function generateReport(buildUrl) {
    const buildStatus = 'SUCCESS 🟢'; //'FAILURE';
    const pipelineUrl = `https://bitbucket.org/${process.env.BITBUCKET_REPO_FULL_NAME}/pipelines/results/${process.env.BITBUCKET_BUILD_NUMBER}/`;
    let commitInfo = process.env.SHORT_COMMIT;
    if (commitMessage) {
        const maxLength = 40;
        let shortCommitMessage = commitMessage;
        if (commitMessage.length > maxLength) {
            shortCommitMessage = shortCommitMessage.substring(0, Math.min(commitMessage.length, maxLength)) + '...';
        }
        commitInfo = `${commitInfo} - ${shortCommitMessage}`;
    }
    const reportMsg = `«[ BITBUCKET_CI ]»\n\
${process.env.GAME_NAME} #${process.env.BITBUCKET_BUILD_NUMBER} <= ${buildStatus}\n\
Env: ${process.env.DEPLOY_FOLDER} - branch: ${process.env.BITBUCKET_BRANCH} 📦\n\
Version: ${process.env.VERSION}\n\
Commit: ${commitInfo}\n\
Started: ${process.env.BUILD_TIME}\n\
Pipeline: ${pipelineUrl}\n\
---\n\
Url: ${buildUrl}`;
    return reportMsg;
}

async function main() {
    // check runner
    if (process.env.CI !== 'true') {
        console.error('[ERROR]: Failed, run on CI runner only!');
        process.exit();
    }

    // check env variables
    if (process.env.TELE_TOKEN) {
        botToken = process.env.TELE_TOKEN;
    } else {
        console.error('[ERROR]: Missing telegram report api!');
        process.exit();
    }

    const isIgnore = skipBranches.some((pattern) => `${process.env.BITBUCKET_BRANCH}`.indexOf(pattern) > -1);
    if (isIgnore) {
        console.error('[WARN]: Skip notify for this branch prefix!');
        process.exit();
    }

    if (args.length !== 2) {
        console.error('[ERROR]: Wrong input arguments!');
    }

    try {
        commitMessage = await getCommitMessage(process.env.SHORT_COMMIT);
    } catch (err) {}
    const chatIds = args[0];
    const buildUrl = args[1];
    const buildReport = generateReport(buildUrl);
    let channelIds = [];
    if (chatIds.indexOf(',') > -1) {
        channelIds = chatIds.split(',');
    } else {
        channelIds.push(chatIds);
    }
    channelIds.forEach((chatId) => {
        sendMessage(chatId, buildReport);
    });
}

main();
