/**
 * https://tinypng.com/developers
 */
const fs = require('fs/promises');
const path = require('path');
const tinify = require('tinify');
const isImage = require('is-image');
const { getFiles, hashFile, readChecksum, saveChecksum, CS_TINIFY_PROPERTY: SAVE_PROPERTY } = require('./utils');
const packageConfig = require('../package.json');

// load tinify key
tinify.key = packageConfig.tinifyApiKey;

let numberOfTinifyTextures = 0;
let numberOfDone = 0;
let commonChecksum = null;

async function verifyAndSaveChecksum(checksumJson) {
    console.log('\n **** Verify Checksum ... ****');
    for (const filepath in checksumJson) {
        if (isImage(filepath)) {
            const exists = await fs.access(filepath).then(() => true).catch(() => false);
            if (!exists) {
                console.log(` > rm hash: ${filepath}`);
                delete checksumJson[filepath];
            }
        }
    }

    console.log(' > DONE\n');
    commonChecksum[SAVE_PROPERTY] = checksumJson;
    await saveChecksum(commonChecksum);
}

async function tinifyTexture(path, checksumJson) {
    numberOfTinifyTextures ++;
    tinify.fromFile(path).toFile(path).then(() => {

        console.log(` > Compress: ${path}`);
        /* Generate hash and store to file .hash */
        hashFile(path).then((hash) => {
            numberOfDone ++;
            checksumJson[path] = hash;

            if (numberOfDone >= numberOfTinifyTextures) {
                verifyAndSaveChecksum(checksumJson);
            }
        });

    }).catch((err) => {
        console.log(` > Error while tinify ${path}: ${err}`);
    });
}

function compareWithOldHash(path, checksumJson) {
    hashFile(path).then((hash) => {
        const isIgnoreByJson = checksumJson[path] === 'ignore' || checksumJson[path] === 'skip';
        if (isIgnoreByJson || hash === checksumJson[path]) {
            /* There is no changes in texture just skip */
            const forceSkipMsg = isIgnoreByJson ? ' <= [Skip by JSON]' : '';
            console.log(` > Skip: ${path}${forceSkipMsg}`);
        } else {
            /* Last hash is different so texture are updated, so let tinify it */
            tinifyTexture(path, checksumJson);
        }
    });
}

async function tinifyAssetsByPath(path) {
    commonChecksum = await readChecksum();
    const tinifyChecksum = commonChecksum[SAVE_PROPERTY] || {};

    console.log('\n **** Tinifying textures ... ****');
    const files = await getFiles(path);

    // add favicon png in the parent folder of asset
    if (path.indexOf('/assets/') > -1) {
        files.push({ path: path + '../favicon.png', name: 'favicon' });
    }

    for (let i = 0; i < files.length; i++) {
        const filePath = files[i].path;
        if (isImage(filePath)) {
            /**
             * TiniPNG doesn't support GIF format, skip to avoid error
             */
            const extention = filePath.slice(-3);
            if (extention === 'gif') {
                continue;
            }
            if (tinifyChecksum[filePath]) {
                /* If there is a hash file for that file then we will compare it with the latest hash */
                compareWithOldHash(filePath, tinifyChecksum);
            } else {
                /* If no hash file generated yet just start tinify texture */
                tinifyTexture(filePath, tinifyChecksum);
            }
        }
    }
}

// switch to script path
process.chdir(path.join(__dirname));

let targetPath = '../build/assets/';
if (process.argv.length >= 3) {
    targetPath = process.argv[2];
}

tinifyAssetsByPath(targetPath);