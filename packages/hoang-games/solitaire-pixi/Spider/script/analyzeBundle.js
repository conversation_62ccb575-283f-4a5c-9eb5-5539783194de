const path = require('path');
const fs = require('fs');

const FILTER_LENGTH = 5;
const FILTER_FREQUENCY = 20;
const FILTER_WARNING_LENGTH = 20;
const FILTER_REGEX = [
    /(?<=\.)\w+/g, // methods
    /(?<=\.)([A-Z]+)\w+_([A-Z]+)/g, // constants
];
const FILTER_OBJECT_PARAMETER_REGEX = [
    /\{[\w\:\,\[\]\"\.\_\$\? \t\r\n]+\}/g, // {x: 1, y: 2, c: 3}
];
const ALLOW_LISTS = [
    /* don't compressor keywords */
    'prototype',
    'length',
    'defineProperty',
    'exports',
    'forEach',
    'replace',
    'indexOf',
    'height',
    'position',
    'visible',
    'anchor',
    'constructor',
];
let duplicateArrays = [];
const objectParameters = [];

async function readDir(dirPath, filterStr) {
    const infos = fs.readdirSync(dirPath);
    if (infos.length > 0) {
        let result;
        infos.forEach((info) => {
            if (info.indexOf(filterStr) > -1) {
                result = info;
                return;
            }
        });
        return result;
    }
    return null;
}

async function readFile(filepath) {
    const buffer = await fs.readFileSync(filepath);
    return buffer + '';
}

function _matchRegex(data, regex) {
    let matchStr = data.match(regex);
    matchStr = matchStr.filter((x) => ALLOW_LISTS.indexOf(x) === -1 && x.length > FILTER_LENGTH);
    return matchStr;
}

function checkDuplicateElements() {
    if (duplicateArrays.length < 1) {
        console.log('Too little data!');
        return;
    }

    let report = [];
    while (duplicateArrays.length > 0) {
        const checkingElement = duplicateArrays[0];
        const count = duplicateArrays.filter((x) => x === checkingElement).length;

        if (count > FILTER_FREQUENCY || checkingElement.length >= FILTER_WARNING_LENGTH) {
            report.push([checkingElement, count]);
        }
        duplicateArrays = duplicateArrays.filter((x) => x !== checkingElement);
    }

    if (report.length > 0) {
        console.log(' > DUPLICATE KEYWORDS:');
        report = report.sort((a, b) => a[1] - b[1]);
        report.forEach(([a, b]) => {
            console.log(a, '\t', b);
        });
    }
}

function warningObjectParameters() {
    if (objectParameters.length > 0) {
        console.log(' > OBJECT PARAMETERS:');
        objectParameters.forEach(obj => {
            console.log(obj);
        });
    } else {
        console.log('Not found object init!');
    }
}

function filterAndSaveData(data) {
    duplicateArrays = [];
    FILTER_REGEX.forEach((regex) => {
        const result = _matchRegex(data, regex);
        duplicateArrays.push(...result);
    });

    FILTER_OBJECT_PARAMETER_REGEX.forEach((regex) => {
        const result = _matchRegex(data, regex);
        objectParameters.push(...result);
    });
}

async function main() {
    const gameBinary = await readDir(path.resolve(__dirname, '../dist'), '.js');
    const content = await readFile(path.resolve(__dirname, '../dist/' + gameBinary));
    filterAndSaveData(content);
    checkDuplicateElements();
    warningObjectParameters();
}

main();
