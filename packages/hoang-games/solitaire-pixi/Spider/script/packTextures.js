/*
 * Referent: https://npm.io/package/free-tex-packer-core
 */
const fs = require('fs/promises');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');
const { packAsync } = require('free-tex-packer-core');
const { getFiles, hashFile, existAsync, sortObject, readChecksum, saveChecksum, CS_ATLAS_PROPERTY: SAVE_PROPERTY } = require('./utils');

process.chdir(path.join(__dirname)); // go to script path

const isForceMode = process.argv.indexOf('--force') > 1 || process.argv.indexOf('-f') > 1;
const atlasAssetsFolder = '../build/raw_assets/Atlas/';
const outputPath = '../build/assets/Atlas/';
const tinifyScriptPath = 'tinifyTextures.js';
const { script, baseConfig, packs } = require(path.join(__dirname, atlasAssetsFolder + 'atlas'));
const autoTinifyAfterPack = process.argv.indexOf('--tinify') > 1 || process.argv.indexOf('-t') > 1 || script.autoTinifyAfterPack;
const atlasBatch = [];

async function packTextures() {
    // Read checksum
    const commonChecksum = await readChecksum();
    let atlasChecksum = commonChecksum[SAVE_PROPERTY] || {};

    // Pack textures
    console.log(chalk.magenta('\n **** Packing Textures ... ****'));
    let willUpdateChecksum = false;
    let countPack = 0;
    for (let i = 0; i < atlasBatch.length; i++) {
        const atlasPath = atlasBatch[i].path;
        const atlasConfig = atlasBatch[i].config;
        const files = await getFiles(atlasPath);

        const hashFiles = {};
        const images = [];
        let willPackAtlas = false;
        hashFiles[atlasPath] = {};
        if (typeof atlasChecksum[atlasPath] !== 'object') {
            atlasChecksum[atlasPath] = {};
        }
        if (files.length !== Object.keys(atlasChecksum[atlasPath]).length) {
            willPackAtlas = true;
        }
        for (let j = 0; j < files.length; j++) {
            const filepath = files[j].path;
            const filename = files[j].name;
            const hash = await hashFile(filepath);

            if (hash !== atlasChecksum[atlasPath][filename]) {
                willPackAtlas = true;
            }
            hashFiles[atlasPath][filename] = hash;
            images.push({
                path: filepath,
                contents: await fs.readFile(filepath),
            });
        }

        if (willPackAtlas || isForceMode) {
            console.log(` > Pack: ${atlasPath}`);
            const packResult = await packAsync(images, atlasConfig).catch((error) => {
                if (error) {
                    console.log(' > ' + chalk.red('[ERROR]: ' + error.description));
                    throw error.description;
                }
            });

            for (let k = 0; k < packResult.length; k++) {
                const atlasFile = packResult[k];
                await fs.writeFile(outputPath + atlasFile.name, atlasFile.buffer, 'utf-8').catch((error) => {
                    console.log(' > Error: ' + error.message);
                });
            }

            countPack++;
            atlasChecksum = { ...atlasChecksum, ...hashFiles };
            willUpdateChecksum = true;
        } else {
            console.log(` > Skip ${atlasPath}`);
        }
    }
    console.log(' > Finished! - ' + (countPack > 0 ? `[Pack: ${countPack}/${atlasBatch.length}]` : '[Skip All]'));

    // Verify checksum
    console.log('\n **** Verify Checksum ... ****');
    const savedPath = Object.keys(atlasChecksum);
    for (let i = 0; i < savedPath.length; i++) {
        const folderPath = savedPath[i];
        const isAssetExists = await existAsync(folderPath);
        if (!isAssetExists) {
            console.log(` > rm hash: ${folderPath}`);
            delete atlasChecksum[folderPath];
            willUpdateChecksum = true;
        }
    }

    // Update checksum
    if (willUpdateChecksum) {
        console.log();
        commonChecksum[SAVE_PROPERTY] = sortObject(atlasChecksum);
        await saveChecksum(commonChecksum);
    }

    if (autoTinifyAfterPack) {
        console.log();
        const child = spawn('node', [tinifyScriptPath, outputPath]);
        child.stdout.on('data', (data) => {
            process.stdout.write('' + data);
        });
        child.stderr.on('data', (data) => {
            console.log(`Error: ${data}`);
        });
    }
}

// read config from json file
packs.forEach((packInfo) => {
    const filepath = atlasAssetsFolder + packInfo.path;
    const packConfig = { ...baseConfig, ...packInfo.config };

    atlasBatch.push({ path: filepath, config: packConfig });
});

packTextures();
