const TerserPlugin = require('terser-webpack-plugin');

module.exports = (env, isDebug, isBuild, isMangle) => {
    const devConfig = {
        mode: 'development',
        optimization: {
            minimize: isBuild,
            minimizer: [
                (compiler) => {
                    const TerserPlugin = require('terser-webpack-plugin');
                    new TerserPlugin({
                        parallel: true,
                        extractComments: false,
                        terserOptions: {
                            output: null,
                        },
                    });
                }
            ],
        },
        devtool: isDebug ? 'inline-source-map' : 'hidden-source-map',
        devServer: {
            headers: {
                'Cache-Control': 'no-store',
            },
            port: 8080,
            compress: isBuild,
            client: {
                overlay: true
            }
        },
    };
    return devConfig;
};
