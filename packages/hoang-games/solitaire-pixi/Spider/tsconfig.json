{
  "compilerOptions": {
    /* Basic Options */
    "target": "es2015",
    "module": "commonjs",
    // "rootDir": "./src",
    "moduleResolution": "node",
    // Required for importing 3rd-party dependencies like EventEmitter3
    "esModuleInterop": true,
    // Loaders needs this to use the more strict mini-signal types
    "paths": {
      "mini-signals": [
        "node_modules/resource-loader/typings/mini-signals.d.ts"
      ]
    },
    "baseUrl": "./"
  },
  "exclude": [
    "dist",
    "node_modules",
    "webpack.config.*.js",
    "script/*.js",
  ]
}