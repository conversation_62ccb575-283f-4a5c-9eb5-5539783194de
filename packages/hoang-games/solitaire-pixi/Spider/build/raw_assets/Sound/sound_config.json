{"guidelines": ["Range of bitrate: 8|16|24|32|40|48|64|80|96|112|128|160|192|224|256|320", "[config.grap]: silence time between sounds in pack", "[files/pack].preLoad: property to load pack in loading game", ">> NOTE: <PERSON><PERSON> SURE NAMES AND <PERSON><PERSON><PERSON> PATHS ARE SHORT AND <PERSON><PERSON><PERSON>, AVOID INCLUDING UNNECESSARY SUFFIXES, TO OP<PERSON>MI<PERSON><PERSON><PERSON> PURPOSE <<"], "template": {"files": [{"pack": "spk0", "name": "<audio_name>", "src": "<relative_path>", "type": "music", "loop": true, "volume": 0.4, "__comment__": ""}], "packs": {"spk999": {"bitrate": "96", "preLoad": true, "files": [{"name": "<audio_name>", "src": "<relative_path>", "type": "music", "volume": 1, "__comment__": ""}]}}}, "config": {"format": "howler", "export": "mp3", "path": "Sound", "channels": 2, "bitrate": 64, "gap": 0.001}, "files": [], "packs": {"spk0": {"preLoad": true, "files": [{"name": "button_click", "src": "click-btn.mp3", "volume": 1}, {"name": "card", "src": "card.mp3", "volume": 1}, {"name": "win", "src": "win.mp3", "volume": 1}, {"name": "card_click", "src": "card_click.mp3", "volume": 1}, {"name": "card_flip", "src": "card_flip.mp3", "volume": 1}, {"name": "no_hint", "src": "no_hint.mp3", "volume": 1}, {"name": "move_win", "src": "move_win.mp3", "volume": 1}, {"name": "deal_card", "src": "deal_card.mp3", "volume": 0.6}]}}}