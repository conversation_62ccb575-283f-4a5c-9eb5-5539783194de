{"frames": {"ui_popup": {"frame": {"x": 2, "y": 2, "w": 603, "h": 403}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 603, "h": 403}, "sourceSize": {"w": 603, "h": 403}, "pivot": {"x": 0.5, "y": 0.5}}, "win_popup": {"frame": {"x": 2, "y": 409, "w": 396, "h": 284}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 396, "h": 284}, "sourceSize": {"w": 396, "h": 284}, "pivot": {"x": 0.5, "y": 0.5}}, "board_rank": {"frame": {"x": 609, "y": 2, "w": 273, "h": 394}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 273, "h": 394}, "sourceSize": {"w": 273, "h": 394}, "pivot": {"x": 0.5, "y": 0.5}}, "input_name": {"frame": {"x": 402, "y": 409, "w": 335, "h": 52}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 335, "h": 52}, "sourceSize": {"w": 335, "h": 52}, "pivot": {"x": 0.5, "y": 0.5}}, "scroll_bar": {"frame": {"x": 402, "y": 465, "w": 14, "h": 308}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 14, "h": 308}, "sourceSize": {"w": 14, "h": 308}, "pivot": {"x": 0.5, "y": 0.5}}, "logo": {"frame": {"x": 741, "y": 400, "w": 256, "h": 41}, "rotated": true, "trimmed": true, "spriteSourceSize": {"x": 0, "y": 11, "w": 256, "h": 41}, "sourceSize": {"w": 256, "h": 52}, "pivot": {"x": 0.5, "y": 0.5}}, "bg_draw": {"frame": {"x": 402, "y": 483, "w": 176, "h": 224}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 176, "h": 224}, "sourceSize": {"w": 176, "h": 224}, "pivot": {"x": 0.5, "y": 0.5}}, "tool_tip": {"frame": {"x": 786, "y": 400, "w": 224, "h": 43}, "rotated": true, "trimmed": true, "spriteSourceSize": {"x": 1, "y": 1, "w": 224, "h": 43}, "sourceSize": {"w": 226, "h": 45}, "pivot": {"x": 0.5, "y": 0.5}}, "ui_button": {"frame": {"x": 2, "y": 697, "w": 224, "h": 75}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 224, "h": 75}, "sourceSize": {"w": 224, "h": 75}, "pivot": {"x": 0.5, "y": 0.5}}, "spider_text": {"frame": {"x": 714, "y": 465, "w": 166, "h": 23}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 166, "h": 23}, "sourceSize": {"w": 166, "h": 23}, "pivot": {"x": 0.5, "y": 0.5}}, "card_lifter_btn": {"frame": {"x": 833, "y": 400, "w": 158, "h": 48}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 158, "h": 48}, "sourceSize": {"w": 158, "h": 48}, "pivot": {"x": 0.5, "y": 0.5}}, "hint_btn": {"frame": {"x": 833, "y": 562, "w": 158, "h": 49}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 158, "h": 49}, "sourceSize": {"w": 158, "h": 49}, "pivot": {"x": 0.5, "y": 0.5}}, "undo_btn": {"frame": {"x": 230, "y": 724, "w": 158, "h": 48}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 158, "h": 48}, "sourceSize": {"w": 158, "h": 48}, "pivot": {"x": 0.5, "y": 0.5}}, "btn_red": {"frame": {"x": 630, "y": 635, "w": 130, "h": 53}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 130, "h": 53}, "sourceSize": {"w": 130, "h": 53}, "pivot": {"x": 0.5, "y": 0.5}}, "button": {"frame": {"x": 687, "y": 660, "w": 130, "h": 53}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 130, "h": 53}, "sourceSize": {"w": 130, "h": 53}, "pivot": {"x": 0.5, "y": 0.5}}, "rectangle": {"frame": {"x": 392, "y": 769, "w": 1, "h": 56}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 1, "h": 56}, "sourceSize": {"w": 1, "h": 56}, "pivot": {"x": 0.5, "y": 0.5}}, "check_box": {"frame": {"x": 687, "y": 724, "w": 42, "h": 42}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 42, "h": 42}, "sourceSize": {"w": 42, "h": 42}, "pivot": {"x": 0.5, "y": 0.5}}, "icon_tick": {"frame": {"x": 733, "y": 724, "w": 36, "h": 29}, "rotated": true, "trimmed": true, "spriteSourceSize": {"x": 0, "y": 0, "w": 36, "h": 29}, "sourceSize": {"w": 36, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "close": {"frame": {"x": 766, "y": 724, "w": 31, "h": 31}, "rotated": false, "trimmed": true, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 32, "h": 32}, "pivot": {"x": 0.5, "y": 0.5}}, "newgame": {"frame": {"x": 801, "y": 724, "w": 31, "h": 31}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 31, "h": 31}, "pivot": {"x": 0.5, "y": 0.5}}, "rank": {"frame": {"x": 836, "y": 724, "w": 31, "h": 31}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 31, "h": 31}, "sourceSize": {"w": 31, "h": 31}, "pivot": {"x": 0.5, "y": 0.5}}, "sound_off": {"frame": {"x": 392, "y": 697, "w": 30, "h": 30}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 30, "h": 30}, "sourceSize": {"w": 30, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "sound_on": {"frame": {"x": 402, "y": 663, "w": 30, "h": 30}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 30, "h": 30}, "sourceSize": {"w": 30, "h": 30}, "pivot": {"x": 0.5, "y": 0.5}}, "rules": {"frame": {"x": 687, "y": 483, "w": 23, "h": 29}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 23, "h": 29}, "sourceSize": {"w": 23, "h": 29}, "pivot": {"x": 0.5, "y": 0.5}}, "score": {"frame": {"x": 687, "y": 635, "w": 18, "h": 17}, "rotated": true, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 18, "h": 17}, "sourceSize": {"w": 18, "h": 17}, "pivot": {"x": 0.5, "y": 0.5}}, "time": {"frame": {"x": 708, "y": 635, "w": 16, "h": 16}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 16, "h": 16}, "sourceSize": {"w": 16, "h": 16}, "pivot": {"x": 0.5, "y": 0.5}}, "highscore": {"frame": {"x": 230, "y": 697, "w": 15, "h": 15}, "rotated": false, "trimmed": false, "spriteSourceSize": {"x": 0, "y": 0, "w": 15, "h": 15}, "sourceSize": {"w": 15, "h": 15}, "pivot": {"x": 0.5, "y": 0.5}}}, "meta": {"app": "http://github.com/odrick/free-tex-packer-core", "version": "0.3.4", "image": "ui_atlas.png", "format": "RGBA8888", "size": {"w": 884, "h": 774}, "scale": 1}}