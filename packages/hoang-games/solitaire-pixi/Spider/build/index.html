<html>
    <head>
        <meta charset="UTF-8" />
        <title>@title@</title>
        <meta charset="utf-8" />
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <link rel="icon" href="./favicon.png?_t=@time@=" type="image/png" />
        <style>
            html {
                -ms-touch-action: none;
            }
            body,
            canvas,
            div {
                display: block;
                outline: none;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

                user-select: none;
                -moz-user-select: none;
                -webkit-user-select: none;
                -ms-user-select: none;
                -khtml-user-select: none;
                -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            }
            body {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                padding: 0;
                border: 0;
                margin: 0;

                cursor: default;
                color: #888;
                background-color: #333;

                text-align: center;
                font-family: Helvetica, Verdana, Arial, sans-serif;

                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                scroll-behavior: none;
                /* overflow: hidden; */
            }
            #shell {
                width: 100%;
            }
            #game {
                position: block;
                margin: auto;
                display: flex;
                width: 80%;
                max-width: 1100px;
                aspect-ratio: 1280/720; /* (1280 -> 1560)/720 */
                background-color: #acacac;
                align-items: center;
            }
            #game canvas {
                display: flex;
                width: 100%;
                height: 100%;
            }
            #btn {
                font-size: 20px;
                border-width: 2px;
                border-radius: 15px;
                width: 180px;
                height: 80px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        </style>
        <script>
            globalThis.jg = {
                gameConfig: {
                    STATIC_ROOT_PATH: '@StaticRootPath@'
                },
                getRootDiv: () => document.querySelector('#game') ?? document.body,
                // css_path: `styles.css?v${new Date().getTime()}`,
                js_path: `main.js?v${new Date().getTime()}`,
            };
            if(!'@isProd@') {
                setTimeout(() => { document.querySelector('#btn')?.click(); }, 200)
            }
        </script>
    </head>
    <body>
        <div id="shell">
            <div id="game"></div>
            <button id="btn" onclick="document.querySelector('#btn').remove(); globalThis.jg.load();">Start Game</button>
        </div>
    </body>
</html>
