import { defineConfig } from 'vite';
import preact from '@preact/preset-vite';
import { visualizer } from 'rollup-plugin-visualizer';
import pkg from './package.json' with { type: 'json' };

// https://vite.dev/config/
export default defineConfig(({ command }) => {
  const isProd = command === 'build';
  const isPublish = !!process.env.PUBLISH;

  return {
    define: {
      __IS_PROD__: isProd,
      __IS_PUBLISH__: isPublish,
      __VERSION__: JSON.stringify(pkg.version),
      __PACKAGE_NAME__: JSON.stringify(pkg.name),
    },
    plugins: [
      preact(),
      visualizer({
        filename: 'stats.html', // 报告文件名
        brotliSize: true, // 显示 brotli 大小
      }),
    ],
    ...(isPublish && {
      build: {
        assetsInlineLimit: 0,
        target: 'chrome96',
        lib: {
          entry: 'src/main.tsx',
          name: 'Maze',
          fileName: (format: string) =>
            format === 'es' ? 'maze.js' : `maze.${format}.js`,
          formats: ['es', 'umd', 'iife'],
        },
      },
    }),
  };
});
