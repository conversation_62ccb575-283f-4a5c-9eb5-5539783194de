import { GameConfig, LevelConfig } from '../types';

// Default Level configuration - adjust these values to change difficulty
export const LEVEL_CONFIG: LevelConfig[][] = [
  // Tier 1 (Levels 1-12) - Beginner
  [
    // debug
    // {
    //   size: 38,
    //   seed: 222,
    //   method: 'backtracking',
    //   wallWidth: 2,
    //   exitWallWidth: 22,
    // },
    {
      size: 8,
      seed: 12345,
      method: 'backtracking',
      wallWidth: 4,
      exitWallWidth: 6,
      exitDashArray: [8, 12],
    }, // Level 1 - Very easy, small maze
    {
      size: 8,
      seed: 23456,
      method: 'backtracking',
      exitColor: '#33f',
      exitWallWidth: 4,
      exitDashArray: [8, 16],
    }, // Level 2 - Very easy
    { size: 8, seed: 34567, method: 'prims' }, // Level 3 - Very easy
    { size: 8, seed: 45678, method: 'backtracking' }, // Level 4 - Easy
    { size: 9, seed: 56789, method: 'prims' }, // Level 5 - Easy
    { size: 9, seed: 67890, method: 'backtracking' }, // Level 6 - Easy
    { size: 9, seed: 78901, method: 'prims' }, // Level 7 - Easy
    { size: 9, seed: 89012, method: 'backtracking' }, // Level 8 - Easy-medium
    { size: 10, seed: 90123, method: 'prims' }, // Level 9 - Easy-medium
    { size: 11, seed: 11111, method: 'backtracking' }, // Level 10 - Easy-medium
    { size: 12, seed: 22222, method: 'prims' }, // Level 11 - Medium
    { size: 13, seed: 33333, method: 'backtracking' }, // Level 12 - Medium
  ],

  // Tier 2 (Levels 13-24) - Intermediate
  [
    { size: 12, seed: 44444, method: 'prims' }, // Level 13 - Medium
    { size: 12, seed: 55555, method: 'backtracking' }, // Level 14 - Medium
    { size: 12, seed: 66666, method: 'prims' }, // Level 15 - Medium
    { size: 12, seed: 77777, method: 'backtracking' }, // Level 16 - Medium-hard
    { size: 13, seed: 88888, method: 'prims' }, // Level 17 - Medium-hard
    { size: 13, seed: 99999, method: 'backtracking' }, // Level 18 - Medium-hard
    { size: 13, seed: 12121, method: 'prims' }, // Level 19 - Medium-hard
    { size: 14, seed: 23232, method: 'backtracking' }, // Level 20 - Medium-hard
    { size: 15, seed: 34343, method: 'prims' }, // Level 21 - Hard
    { size: 16, seed: 45454, method: 'backtracking' }, // Level 22 - Hard
    { size: 17, seed: 56565, method: 'prims' }, // Level 23 - Hard
    { size: 18, seed: 67676, method: 'backtracking' }, // Level 24 - Hard
  ],

  // Tier 3 (Levels 25-36) - Advanced
  [
    { size: 19, seed: 78787, method: 'prims' }, // Level 25 - Hard
    { size: 20, seed: 89898, method: 'backtracking' }, // Level 26 - Very hard
    { size: 21, seed: 90909, method: 'prims' }, // Level 27 - Very hard
    { size: 22, seed: 10101, method: 'backtracking' }, // Level 28 - Very hard
    { size: 23, seed: 20202, method: 'prims' }, // Level 29 - Very hard
    { size: 25, seed: 30303, method: 'backtracking' }, // Level 30 - Very hard
    { size: 27, seed: 40404, method: 'prims' }, // Level 31 - Very hard
    { size: 29, seed: 50505, method: 'backtracking' }, // Level 32 - Very hard
    { size: 31, seed: 60606, method: 'prims' }, // Level 33 - Very hard
    { size: 33, seed: 70707, method: 'backtracking' }, // Level 34 - Very hard
    { size: 37, seed: 80808, method: 'prims' }, // Level 35 - Very hard
    { size: 40, seed: 90909, method: 'backtracking' }, // Level 36 - Very hard
  ],

  // Tier 4 (Levels 37-48) - Expert
  [
    { size: 41, seed: 10111, method: 'prims' }, // Level 37 - Expert
    { size: 42, seed: 11121, method: 'backtracking' }, // Level 38 - Expert
    { size: 43, seed: 12131, method: 'prims' }, // Level 39 - Expert
    { size: 44, seed: 13141, method: 'backtracking' }, // Level 40 - Expert
    { size: 45, seed: 14151, method: 'prims' }, // Level 41 - Expert
    { size: 46, seed: 15161, method: 'backtracking' }, // Level 42 - Expert
    { size: 47, seed: 16171, method: 'prims' }, // Level 43 - Expert
    { size: 48, seed: 17181, method: 'backtracking' }, // Level 44 - Expert
    { size: 49, seed: 18191, method: 'prims' }, // Level 45 - Expert
    { size: 50, seed: 19201, method: 'backtracking' }, // Level 46 - Expert
    { size: 51, seed: 20211, method: 'prims' }, // Level 47 - Expert
    { size: 52, seed: 21221, method: 'backtracking' }, // Level 48 - Expert
  ],

  // Tier 5 (Levels 49-60) - Master
  [
    { size: 53, seed: 22231, method: 'prims' }, // Level 49 - Master
    { size: 54, seed: 23241, method: 'backtracking' }, // Level 50 - Master
    { size: 55, seed: 24251, method: 'prims' }, // Level 51 - Master
    { size: 56, seed: 25261, method: 'backtracking' }, // Level 52 - Master
    { size: 57, seed: 26271, method: 'prims' }, // Level 53 - Master
    { size: 58, seed: 27281, method: 'backtracking' }, // Level 54 - Master
    { size: 59, seed: 28291, method: 'prims' }, // Level 55 - Master
    { size: 60, seed: 29301, method: 'backtracking' }, // Level 56 - Master
    { size: 62, seed: 30311, method: 'prims' }, // Level 57 - Master
    { size: 64, seed: 31321, method: 'backtracking' }, // Level 58 - Master
    { size: 66, seed: 32331, method: 'prims' }, // Level 59 - Master
    { size: 68, seed: 33341, method: 'backtracking' }, // Level 60 - Master
  ],
];

export const defaultConfig: Partial<GameConfig> = {
  lcPrefix: 'maze-',
  i18n: {
    reset: 'Reset',
    resetTip: 'Reset all game data and highest score.',
    confirmReset: 'Confirm Reset',
    confirmResetMessage:
      'Are you sure you want to reset the game? All progress will be lost.',
    cancel: 'Cancel',
    close: 'Close',
    confirm: 'Confirm',
    resetDoneToast: 'Game reset! Starting from Level 1...',
    confirmClue: 'Confirm to turn on clue?',
    confirmClueMessage:
      'Are you sure you want to turn on clue? Your will lose 1 ★.',
    level: 'Level',
    tier: 'Tier',
    settings: 'Settings',
    clueOn: 'Clue: ON',
    clueOff: 'Clue: OFF',
    foodCollectedToast: 'Food collected! %NUM% remaining.',
    allFoodCollectedToast: 'All food collected! Head to the exit.',
    congratulations: 'Congratulations!',
    findYourExit: 'Find your exit!',
    withoutClue: 'Without clue!',
    withClue: 'You used the clue!',
    collectAllStars: 'Collect all stars!',
    notCollectAllStars: 'Not collect all stars.',
    nextLevel: 'Next Level ➤',
    retry: 'Retry',
    levelComplete: 'Level Complete',
    starsCollected: 'Stars Collected',
    avgStarPerLevel: 'Avg. Stars per Level',
    levelCompleteToast: 'Level completed! 🎉',
    tierUnlockedToast: 'Tier %TIER% unlocked! 🎉',
    levelGoToast: 'Level %LEVEL% - Go!',
    levelRestartToast: 'Level restarted',
    allLevelsCompleted: 'You completed all levels!',
    lastOK: 'OK',
    notCompleteLevel:
      'You still have levels not collected all stars! Try again to collect them all!',
    continueMessage:
      "You haven't collected all stars. You can click 'Continue' button to collect all stars.",
    continue: 'Continue',
    cluePathEnabled: 'Clue Path Enabled',
    cluePathDisabled: 'Clue Path Disabled',
    useArrowKeyTips:
      "Use arrow keys to navigate the maze. Press'C' to toggle path clue.",
  },
  renderConfig: {
    gridMoveSpeed: 0.07,
    padding: 10,
    wallWidth: 2,
    wallColor: '#000000',
    foodColor: '#4CAF50',
    clueColor: 'rgb(255,0,0)',
    backgroundColor: '#ffffff',
    playerColor: '#4CAF50',
    playerRadius: 0.2,
    // Exit
    exitColor: '#F44336',
    exitWallWidth: 4, // default exit wall width if not specified in level config, should always be a little wider than normal wallWidth
    exitDashArray: [8, 12], // default dash: 8px dash, 12px gap
  },
  levelConfig: LEVEL_CONFIG,
};
