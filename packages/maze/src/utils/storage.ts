import { GameProgress } from '../types';

export const createStorage = (key: string) => {
  return {
    saveGameData(data: GameProgress): void {
      try {
        localStorage.setItem(key, JSON.stringify(data));
      } catch (e) {
        console.error('Failed to save maze game data:', e);
      }
    },

    loadGameData(): GameProgress | null {
      try {
        const savedData = localStorage.getItem(key);
        if (!savedData) return null;

        return JSON.parse(savedData);
      } catch (e) {
        console.error('Failed to load maze game data:', e);
        return null;
      }
    },

    resetGameData(): void {
      try {
        localStorage.removeItem(key);
      } catch (e) {
        console.error('Failed to reset maze game data:', e);
      }
    },
  };
};
