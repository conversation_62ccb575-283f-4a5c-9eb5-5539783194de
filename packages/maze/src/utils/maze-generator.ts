import {
  MazeGenerationMethod,
  GridPos,
  GamePlayState,
  LevelConfig,
} from '../types';

// Seeded random number generator (Mulberry32)
function createSeededRandom(seed: number): () => number {
  return function () {
    let t = (seed += 0x6d2b79f5);
    t = Math.imul(t ^ (t >>> 15), t | 1);
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61);
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
  };
}

/**
 * Represents a single cell in the maze grid.
 */
export class MazeCell {
  visited = false;
  walls = { top: true, right: true, bottom: true, left: true };
  // initially all walls exit

  constructor(public row: number, public col: number) {}
}

/**
 * Represents the maze structure, holding the grid of cells and providing helper methods.
 * Also includes maze generation algorithms.
 */
export class Maze {
  readonly rows: number;
  readonly cols: number;
  readonly grid: Maze<PERSON>ell[][];
  readonly seed: number;
  readonly random: () => number;

  /**
   * Creates a new maze with the specified dimensions.
   *
   * @param rows Number of rows in the maze
   * @param cols Number of columns in the maze
   * @param method Optional generation algorithm ('backtracking' or 'prims')
   * @param seed Optional seed for random number generation
   * @param entry Optional entry cell position
   * @param exit Optional exit cell position
   */
  constructor(
    rows: number,
    cols: number,
    method: MazeGenerationMethod,
    seed?: number,
    entry: GridPos = { row: 0, col: 0 },
    exit?: GridPos
  ) {
    this.rows = rows;
    this.cols = cols;
    this.seed = seed ?? Math.floor(Math.random() * 1000000);
    this.random = createSeededRandom(this.seed);
    this.grid = this.initializeGrid();

    // Generate the maze
    this.generate(method, entry, exit);
  }

  // Initialize the grid with new MazeCell objects
  private initializeGrid(): MazeCell[][] {
    const grid: MazeCell[][] = [];
    for (let r = 0; r < this.rows; r++) {
      grid[r] = [];
      for (let c = 0; c < this.cols; c++) {
        grid[r][c] = new MazeCell(r, c);
      }
    }
    return grid;
  }

  // Check if coordinates are within the grid boundaries
  isValid(row: number, col: number): boolean {
    return row >= 0 && row < this.rows && col >= 0 && col < this.cols;
  }

  // Get all valid neighbors (top, right, bottom, left)
  private getNeighbors(row: number, col: number): GridPos[] {
    const neighbors: GridPos[] = [];
    const directions = [
      { r: -1, c: 0 }, // Top
      { r: 0, c: 1 }, // Right
      { r: 1, c: 0 }, // Bottom
      { r: 0, c: -1 }, // Left
    ];

    for (const dir of directions) {
      const nr = row + dir.r;
      const nc = col + dir.c;
      if (this.isValid(nr, nc)) {
        neighbors.push({ row: nr, col: nc });
      }
    }
    return neighbors;
  }

  // Get only neighbors that haven't been visited yet
  private getUnvisitedNeighbors(row: number, col: number): GridPos[] {
    return this.getNeighbors(row, col).filter(
      (n) => !this.grid[n.row][n.col].visited
    );
  }

  /**
   * Gets neighbors that have already been visited (part of the maze path).
   */
  private getVisitedNeighbors(row: number, col: number): GridPos[] {
    return this.getNeighbors(row, col).filter(
      (n) => this.grid[n.row][n.col].visited
    );
  }

  // Remove the wall between two adjacent cells
  private removeWall(r1: number, c1: number, r2: number, c2: number): void {
    const cell1 = this.grid[r1][c1];
    const cell2 = this.grid[r2][c2];

    if (r1 === r2 + 1) {
      // cell1 is below cell2
      cell1.walls.top = false;
      cell2.walls.bottom = false;
    } else if (r1 === r2 - 1) {
      // cell1 is above cell2
      cell1.walls.bottom = false;
      cell2.walls.top = false;
    } else if (c1 === c2 + 1) {
      // cell1 is right of cell2
      cell1.walls.left = false;
      cell2.walls.right = false;
    } else if (c1 === c2 - 1) {
      // cell1 is left of cell2
      cell1.walls.right = false;
      cell2.walls.left = false;
    }
  }

  // --- Generation Methods ---

  /**
   * Generates the maze using the specified algorithm.
   *
   * @param method The generation algorithm to use
   * @param entry The entry cell position
   * @param exit The exit cell position
   */
  generate(
    method: MazeGenerationMethod,
    entry: GridPos = { row: 0, col: 0 },
    exit?: GridPos
  ): void {
    // Run the selected algorithm
    switch (method) {
      case 'backtracking':
        this.generateWithBacktracking();
        break;
      case 'prims':
        this.generateWithPrims();
        break;
      default:
        throw new Error(`Unknown maze generation method: ${method}`);
    }

    // Add openings at entry and exit
    this.addOpenings(entry, exit);
  }

  // --- Backtracking Algorithm Implementation ---
  private generateWithBacktracking(): void {
    const stack: GridPos[] = [];
    const startRow = this.randomNumber(this.rows);
    const startCol = this.randomNumber(this.cols);

    let currentCellPos: GridPos = { row: startRow, col: startCol };
    this.grid[startRow][startCol].visited = true;
    stack.push(currentCellPos);

    while (stack.length > 0) {
      currentCellPos = stack[stack.length - 1];
      const { row, col } = currentCellPos;

      const unvisitedNeighbors = this.getUnvisitedNeighbors(row, col);

      if (unvisitedNeighbors.length > 0) {
        const nextNeighborPos = this.randomPick(unvisitedNeighbors);
        const { row: nextRow, col: nextCol } = nextNeighborPos;

        this.removeWall(row, col, nextRow, nextCol);
        this.grid[nextRow][nextCol].visited = true;
        stack.push(nextNeighborPos);
      } else {
        stack.pop();
      }
    }
  }

  // --- Prim's Algorithm Implementation ---
  private generateWithPrims(): void {
    const frontier: GridPos[] = [];

    // Add unvisited neighbors of a cell to the frontier
    const addNeighborsToFrontier = (r: number, c: number): void => {
      const potentialFrontier = this.getNeighbors(r, c);
      for (const neighbor of potentialFrontier) {
        if (
          !this.grid[neighbor.row][neighbor.col].visited &&
          !frontier.some(
            (f) => f.row === neighbor.row && f.col === neighbor.col
          )
        ) {
          frontier.push(neighbor);
        }
      }
    };

    // Start Prim's
    const startRow = this.randomNumber(this.rows);
    const startCol = this.randomNumber(this.cols);

    this.grid[startRow][startCol].visited = true;
    addNeighborsToFrontier(startRow, startCol);

    while (frontier.length > 0) {
      const randomIndex = this.randomNumber(frontier.length);
      const currentFrontierPos = frontier[randomIndex];
      const { row: fRow, col: fCol } = currentFrontierPos;

      const visitedNeighbors = this.getVisitedNeighbors(fRow, fCol);

      if (visitedNeighbors.length > 0) {
        const connectTo = this.randomPick(visitedNeighbors);
        this.removeWall(fRow, fCol, connectTo.row, connectTo.col);
        this.grid[fRow][fCol].visited = true;
        addNeighborsToFrontier(fRow, fCol);
      }

      frontier.splice(randomIndex, 1); // Remove processed cell
    }
  }

  // --- Helper Methods ---

  /**
   * Adds openings at the entry and exit points of the maze.
   */
  private addOpenings(entry: GridPos, exit?: GridPos): void {
    // Remove entry wall to allow player to enter
    this.grid[entry.row][entry.col].walls.top = false;

    // Remove exit wall to allow player to pass through
    // The exit will be detected when player center reaches the boundary
    if (exit && this.isValid(exit.row, exit.col)) {
      this.grid[exit.row][exit.col].walls.bottom = false;
    }
  }

  /**
   * Generates a random number between 0 and len-1.
   */
  private randomNumber(len: number): number {
    return Math.floor(this.random() * len);
  }

  /**
   * Randomly picks an item from a list.
   */
  private randomPick<T>(list: T[]): T {
    return list[Math.floor(this.random() * list.length)];
  }
}

// --- Helper Function ---

/**
 * Checks if a cell is part of the maze path (has at least one open wall).
 */
const isPath = (maze: Maze, r: number, c: number): boolean => {
  if (!maze.isValid(r, c)) return false;
  const cell = maze.grid[r][c];
  return (
    !cell.walls.top ||
    !cell.walls.right ||
    !cell.walls.bottom ||
    !cell.walls.left
  );
};

/**
 * Calculates the number of food items based on the level.
 */
const getFoodCountForLevel = (level: number): number => {
  if (level === 1) return 2;
  if (level >= 2 && level <= 5) return 3;
  if (level >= 6 && level <= 11) return 4;
  if (level >= 12 && level <= 15) return 5;
  return 6; // level 16+
};

/**
 * Generates a maze for a specific level using the level configuration.
 *
 * @param level The level number to generate
 * @param levelConfig Current level configurations
 * @returns Partial GameState with the generated maze and related data
 */
export const generateMazeForLevel = (
  level: number,
  levelConfig: LevelConfig | null
): Partial<GamePlayState> | null => {
  if (!levelConfig) {
    console.error(`Cannot find Level ${level}'s config.`);
    return null;
  }

  const { size, seed, method, entry, exit } = levelConfig;

  const rows = size;
  const cols = size;

  const entryCell: GridPos = entry || { row: 0, col: 0 };
  const exitCell: GridPos = exit || {
    row: rows - 1,
    col: cols - 1,
  };
  const maze = new Maze(rows, cols, method, seed, entryCell, exitCell);

  // generate food positions
  const foodCount = getFoodCountForLevel(level);
  const foodCells: GridPos[] = [];

  for (let i = 0; i < foodCount; i++) {
    let foodPos: GridPos;
    do {
      foodPos = {
        row: Math.floor(maze.random() * rows),
        col: Math.floor(maze.random() * cols),
      };
    } while (
      (foodPos.row === entryCell.row && foodPos.col === entryCell.col) ||
      (foodPos.row === exitCell.row && foodPos.col === exitCell.col) ||
      !isPath(maze, foodPos.row, foodPos.col) ||
      foodCells.some((fp) => fp.row === foodPos.row && fp.col === foodPos.col)
    );
    foodCells.push(foodPos);
  }

  return {
    isGameActive: true,
    levelTransitioning: false,

    maze,
    playerCell: entryCell,
    exitCell,
    foodCells,

    playerAtExit: false,
    foodCollected: 0,
    totalFoodCount: foodCount,
    showClue: false,
    playerPath: [entryCell],
    visitCounts: { [`${entryCell.row},${entryCell.col}`]: 1 },
  };
};
