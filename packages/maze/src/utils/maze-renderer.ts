import { GridP<PERSON>, Wall, GamePlayState, RenderOptions } from '../types';
import { Maze } from './maze-generator';

// --- Main Rendering Function ---

/**
 * Renders the entire maze state onto the canvas.
 */
export function renderMaze(
  ctx: CanvasRenderingContext2D,
  gamePlayState: GamePlayState,
  canvasSize: { canvasWidth: number; canvasHeight: number },
  cellSize: { cellWidth: number; cellHeight: number },
  renderConfig: RenderOptions
): void {
  if (!ctx || !gamePlayState.maze) return;
  const { canvasWidth, canvasHeight } = canvasSize;
  const { cellWidth, cellHeight } = cellSize;

  const {
    maze,
    playerCell, // row, col, but floating allowed
    exitCell,
    foodCells,
    showClue,
    playerPath,
    visitCounts,
  } = gamePlayState;

  const {
    padding,
    wallWidth, // need merge with level config when passed here
    wallColor,
    playerColor,
    playerRadius,
    foodColor,
    backgroundColor,
    clueColor,
    exitColor,
    exitWallWidth,
    exitDashArray,
  } = renderConfig;

  // clear the canvas
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
  // draw the background color
  ctx.save();
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);
  ctx.restore();

  // 1. Draw Clue Path (if enabled, drawn first to be underneath walls/player)
  if (showClue) {
    drawPlayerPath({
      ctx,
      playerPath,
      visitCounts,
      cellWidth,
      cellHeight,
      padding,
      clueColor,
    });
  }

  // 2. Draw Maze Walls (Lines)
  drawWalls({
    ctx,
    maze,
    cellWidth,
    cellHeight,
    padding,
    wallColor,
    wallWidth,
  });

  // 2.5. Draw Exit Walls (Dashed lines at exit position, bottom only)
  drawExitWalls({
    ctx,
    maze,
    exitCell,
    cellWidth,
    cellHeight,
    padding,
    exitColor,
    exitWallWidth,
    exitDashArray,
  });

  // 3. Draw Food
  drawFood({
    ctx,
    foodCells,
    cellWidth,
    cellHeight,
    padding,
    foodColor,
  });

  // 4. Exit marker is now drawn as dashed walls above, no need for separate marker

  // 5. Draw Player (drawn last to be on top)
  drawPlayer({
    ctx,
    playerCell,
    playerRadius,
    cellWidth,
    cellHeight,
    padding,
    playerColor,
  });
}

// --- Helper Drawing Functions ---

export function drawWalls({
  ctx,
  maze,
  cellWidth,
  cellHeight,
  padding,
  wallColor,
  wallWidth,
}: {
  ctx: CanvasRenderingContext2D;
  maze: Maze;
  cellWidth: number;
  cellHeight: number;
  padding: number;
  wallColor: string;
  wallWidth: number;
}): void {
  ctx.save();
  ctx.strokeStyle = wallColor;
  ctx.lineWidth = wallWidth;
  ctx.lineCap = 'round';

  ctx.beginPath(); // Start one path for all walls for potential performance gain

  const walls = getMazeWalls(maze, cellWidth, cellHeight, padding);
  walls.forEach((wall) => {
    ctx.moveTo(wall.x1, wall.y1);
    ctx.lineTo(wall.x2, wall.y2);
  });

  ctx.stroke(); // Draw all the wall lines added to the path
  ctx.restore();
}

export function drawPlayer({
  ctx,
  playerCell,
  playerRadius,
  cellWidth,
  cellHeight,
  padding,
  playerColor,
}: {
  ctx: CanvasRenderingContext2D;
  playerCell: GridPos; // row, col, but floating allowed
  playerRadius: number;
  cellWidth: number;
  cellHeight: number;
  padding: number;
  playerColor: string;
}): void {
  const { x, y } = gridToPixel(
    playerCell.row,
    playerCell.col,
    cellWidth,
    cellHeight,
    padding
  );

  // Calculate player radius in pixels to match collision detection exactly
  // playerRadius is in grid units (0.2), convert to pixels
  // In our coordinate system, 1 grid unit = cellWidth pixels
  const radius = playerRadius * Math.min(cellWidth, cellHeight);

  ctx.save();
  ctx.fillStyle = playerColor;
  ctx.beginPath();
  ctx.arc(x, y, radius, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
}

export function drawExitWalls({
  ctx,
  maze,
  exitCell,
  cellWidth,
  cellHeight,
  padding,
  exitColor,
  exitWallWidth,
  exitDashArray = [8, 12],
}: {
  ctx: CanvasRenderingContext2D;
  maze: Maze;
  exitCell: GridPos;
  cellWidth: number;
  cellHeight: number;
  padding: number;
  exitColor: string;
  exitWallWidth: number;
  exitDashArray: [number, number];
}): void {
  const { row, col } = exitCell;

  // Check if exit cell is valid
  if (!maze.isValid(row, col)) return;

  const x = padding + col * cellWidth;
  const y = padding + row * cellHeight;

  ctx.save();

  // First, erase any existing solid walls at exit position by drawing over them with background color
  ctx.globalCompositeOperation = 'destination-out'; // Erase mode
  ctx.lineWidth = exitWallWidth; // Slightly wider to ensure complete coverage
  ctx.lineCap = 'round';

  ctx.beginPath();
  // Always erase the bottom wall area at exit position (regardless of wall state)
  ctx.moveTo(x, y + cellHeight);
  ctx.lineTo(x + cellWidth, y + cellHeight);
  ctx.stroke();

  // Reset composite operation and draw dashed exit wall (only bottom)
  ctx.globalCompositeOperation = 'source-over'; // default move to draw
  ctx.strokeStyle = exitColor;
  ctx.lineWidth = exitWallWidth; // wider than normal walls to be more clear
  ctx.setLineDash(exitDashArray); // default is 8px dash, 12px gap

  ctx.beginPath();
  // Always draw the exit wall as dashed line (even if wall was removed for collision)
  ctx.moveTo(x, y + cellHeight);
  ctx.lineTo(x + cellWidth, y + cellHeight);

  ctx.stroke();
  ctx.restore();
}

export function drawFood({
  ctx,
  foodCells,
  cellWidth,
  cellHeight,
  padding,
  foodColor,
}: {
  ctx: CanvasRenderingContext2D;
  foodCells: GridPos[];
  cellWidth: number;
  cellHeight: number;
  padding: number;
  foodColor: string;
}): void {
  // set Food color
  ctx.fillStyle = foodColor;
  const radius = Math.min(cellWidth, cellHeight) * 0.25; // Size of food items

  foodCells.forEach((food) => {
    const { x: foodX, y: foodY } = gridToPixel(
      food.row,
      food.col,
      cellWidth,
      cellHeight,
      padding
    );

    drawStar(ctx, foodX, foodY, radius * 1.2, 5, radius * 0.6);
  });
}

function drawStar(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  radius: number,
  points: number,
  innerRadius: number
): void {
  ctx.save();
  ctx.beginPath();
  for (let i = 0; i < points * 2; i++) {
    const r = i % 2 === 0 ? radius : innerRadius;
    const angle = (Math.PI * i) / points;
    ctx.lineTo(x + r * Math.sin(angle), y + r * Math.cos(angle));
  }
  ctx.closePath();
  ctx.fill();
  ctx.restore();
}

export function drawPlayerPath({
  ctx,
  playerPath,
  visitCounts,
  cellWidth,
  cellHeight,
  padding,
  clueColor,
}: {
  ctx: CanvasRenderingContext2D;
  playerPath: GridPos[];
  visitCounts: Record<string, number>;
  cellWidth: number;
  cellHeight: number;
  padding: number;
  clueColor: string;
}): void {
  if (!playerPath || playerPath.length < 2 || !visitCounts) return;

  ctx.save();

  // Draw connecting lines
  for (let i = 1; i < playerPath.length; i++) {
    const fromPos = playerPath[i - 1];
    const toPos = playerPath[i];

    const key = `${fromPos.row},${fromPos.col}-${toPos.row},${toPos.col}`;
    const reverseKey = `${toPos.row},${toPos.col}-${fromPos.row},${fromPos.col}`;
    const count = (visitCounts[key] || 0) + (visitCounts[reverseKey] || 0);

    // Opacity increases slightly with visits, maxing out
    const opacity = Math.min(0.2 + count * 0.1, 0.9);
    // Width increases slightly with visits
    const width = Math.max(3, cellWidth * 0.1) + count * 0.5;

    try {
      ctx.strokeStyle = clueColor
        .replace(')', `, ${opacity})`)
        .replace('rgb(', 'rgba(');
    } catch {
      ctx.strokeStyle = `rgba(255, 0, 0, ${opacity})`;
    }
    ctx.lineWidth = width;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    const { x: startX, y: startY } = gridToPixel(
      fromPos.row,
      fromPos.col,
      cellWidth,
      cellHeight,
      padding
    );
    const { x: endX, y: endY } = gridToPixel(
      toPos.row,
      toPos.col,
      cellWidth,
      cellHeight,
      padding
    );

    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }

  ctx.restore();
}

// --- helper function ---

function getMazeWalls(
  maze: Maze,
  cellWidth: number,
  cellHeight: number,
  padding: number
): Wall[] {
  const walls: Wall[] = [];

  for (let r = 0; r < maze.rows; r++) {
    for (let c = 0; c < maze.cols; c++) {
      const cell = maze.grid[r][c];
      const x = padding + c * cellWidth;
      const y = padding + r * cellHeight;

      if (cell.walls.top) {
        walls.push({
          x1: x,
          y1: y,
          x2: x + cellWidth,
          y2: y,
        });
      }
      if (cell.walls.right) {
        walls.push({
          x1: x + cellWidth,
          y1: y,
          x2: x + cellWidth,
          y2: y + cellHeight,
        });
      }
      if (cell.walls.bottom) {
        walls.push({
          x1: x,
          y1: y + cellHeight,
          x2: x + cellWidth,
          y2: y + cellHeight,
        });
      }
      if (cell.walls.left) {
        walls.push({
          x1: x,
          y1: y,
          x2: x,
          y2: y + cellHeight,
        });
      }
    }
  }

  return walls;
}

function gridToPixel(
  row: number,
  col: number,
  cellWidth: number,
  cellHeight: number,
  padding: number
) {
  return {
    x: padding + col * cellWidth + cellWidth / 2,
    y: padding + row * cellHeight + cellHeight / 2,
  };
}
