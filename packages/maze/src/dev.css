body {
  margin: 0;
  padding: 0;
  background-color: #f0f0f0;
}

#app {
  width: 90%;
  max-width: 800px;
  min-height: 858px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.maze-usage {
  width: 95%;
  margin: 0 auto 30px;

  pre {
    white-space: pre-wrap;
    padding: 10px 12px;
    background-color: #291334;
    color: #d0cad3;
  }

  details>summary {
    cursor: pointer;
  }

  .copy-btn {
    position: absolute;
    top: 6px;
    right: 8px;
    padding: 4px 8px;
    background: #e0e0e0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
}

.maze-test-mode-toggle {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: #fff;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
}

.maze-test-mode-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;

  input[type=checkbox] {
    cursor: pointer;
  }
}

/* Maze Test Tool Styles */
.maze-test-tool {
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background-color: #f9f9f9;
}

.maze-test-tool__title {
  color: #333;
  font-size: 1.17em;
  font-weight: bold;
  cursor: pointer;
}

.maze-test-tool__content {
  display: flex;
  gap: 20px;
  flex-direction: column;
  margin-top: 20px;
}

.maze-test-tool__controls {
  width: 100%;
}

.maze-test-tool__form {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 10px;
  align-items: center;
}

.maze-test-tool__label {
  font-weight: 500;
  color: #555;
}

.maze-test-tool__input {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.maze-test-tool__input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.maze-test-tool__seed-group {
  display: flex;
  gap: 5px;
}

.maze-test-tool__seed-input {
  flex: 1;
}

.maze-test-tool__random-btn {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.maze-test-tool__random-btn:hover {
  background-color: #f5f5f5;
}

.maze-test-tool__dash-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.maze-test-tool__dash-input {
  flex: 1;
  min-width: 60px;
}

.maze-test-tool__dash-separator {
  color: #666;
  font-weight: bold;
  font-size: 16px;
  user-select: none;
}

.maze-test-tool__color-input {
  padding: 2px;
  border: 1px solid #ccc;
  border-radius: 4px;
  height: 32px;
  cursor: pointer;
}

.maze-test-tool__actions {
  margin-top: 20px;
}

.maze-test-tool__game-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.maze-test-tool__reset-btn {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.maze-test-tool__instructions {
  margin: 10px 0 0 0;
  color: #2196F3;
  font-size: 14px;
  font-weight: bold;
  background-color: #e3f2fd;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #2196F3;
}

.maze-test-tool__generate-btn {
  padding: 10px 20px;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.maze-test-tool__generate-btn--active {
  background-color: #4CAF50;
}

.maze-test-tool__generate-btn--active:hover {
  background-color: #45a049;
}

.maze-test-tool__generate-btn--disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.maze-test-tool__last-generated {
  margin: 10px 0 0 0;
  color: #666;
  font-size: 14px;
}

.maze-test-tool__config {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.maze-test-tool__config-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.maze-test-tool__config-json {
  background-color: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  overflow: auto;
  max-height: 150px;
  white-space: pre;
}

.maze-test-tool__preview {
  width: 100%;
  text-align: center;
}

.maze-test-tool__canvas {
  border-radius: 4px;
  background-color: #f5f5f5;
  width: 100%;
  height: auto;
  max-height: 80vh;
}

.maze-test-tool__canvas-label {
  margin: 10px 0 0 0;
  color: #666;
  font-size: 14px;
}
