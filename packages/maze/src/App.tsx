import { GameProvider } from './components/MazeContext';
import MazeG<PERSON> from './components/MazeGame';
import { GameConfig } from './types';
import { defaultConfig, LEVEL_CONFIG } from './utils/utils';

export default function App({ config = {} }: { config?: Partial<GameConfig> }) {
  const i18n = { ...defaultConfig.i18n, ...(config.i18n || {}) };
  const renderConfig = {
    ...defaultConfig.renderConfig,
    ...(config.renderConfig || {}),
  };
  const levelConfig = config.levelConfig || LEVEL_CONFIG;
  const fullConfig = {
    ...defaultConfig,
    ...config,
    i18n,
    levelConfig,
    renderConfig,
  } as GameConfig;

  return (
    <GameProvider config={fullConfig}>
      <MazeGame />
    </GameProvider>
  );
}
