import { GameProvider } from './components/MazeContext';
import MazeGame from './components/MazeGame';
import SingleMaze from './components/SingleMaze';
import { GameConfig, SingleGameConfig } from './types';
import {
  defaultConfig,
  LEVEL_CONFIG,
  defaultSingleConfig,
} from './utils/utils';

export function MazeGameApp({ config = {} }: { config?: Partial<GameConfig> }) {
  const i18n = { ...defaultConfig.i18n, ...(config.i18n || {}) };
  const renderConfig = {
    ...defaultConfig.renderConfig,
    ...(config.renderConfig || {}),
  };
  const levelConfig = config.levelConfig || LEVEL_CONFIG;
  const fullConfig = {
    ...defaultConfig,
    ...config,
    i18n,
    levelConfig,
    renderConfig,
  } as GameConfig;

  return (
    <GameProvider config={fullConfig}>
      <MazeGame />
    </GameProvider>
  );
}

export function SingleMazeApp({
  config = {},
}: {
  config?: Partial<SingleGameConfig>;
}) {
  const i18n = { ...defaultSingleConfig.i18n, ...(config.i18n || {}) };
  const renderConfig = {
    ...defaultSingleConfig.renderConfig,
    ...(config.renderConfig || {}),
  };
  const fullConfig = {
    ...defaultSingleConfig,
    ...config,
    i18n,
    renderConfig,
  } as SingleGameConfig;

  return <SingleMaze config={fullConfig} />;
}

// Default export for backward compatibility
export default MazeGameApp;
