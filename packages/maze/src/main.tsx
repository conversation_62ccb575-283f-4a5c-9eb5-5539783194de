import { render } from 'preact';
import { useState } from 'preact/hooks';
import './index.css';
import { MazeGameApp, SingleMazeApp } from './App';
import { defaultSingleConfig } from './utils/utils.ts';
import Usage from './components/Usage.tsx';
import MazeTestTool from './components/MazeTestTool.tsx';
import type { GameConfig, SingleGameConfig } from './types';

// Development wrapper component with mode toggles
function DevApp() {
  const [isTestMode, setIsTestMode] = useState(false);
  const [isSingleMode, setIsSingleMode] = useState(false);

  // Sample single maze config for development
  const singleMazeConfig = {
    levelConfig: {
      size: 8,
      seed: 12345,
      method: 'backtracking' as const,
      foodCount: 3,
      wallWidth: 3,
      exitColor: '#FF5722',
      exitWallWidth: 6,
      exitDashArray: [4, 8] as [number, number],
      gridMoveSpeed: 0.05,
    },
    successCallback: () => {
      alert('🎉 Single Maze Completed!');
      console.log('Single maze completed in dev mode');
    },
    failCallback: () => {
      console.log('Single maze failed in dev mode');
    },
  };

  return (
    <>
      {/* Mode Toggles */}
      <div className="maze-dev-toggles">
        <div className="maze-test-mode-toggle">
          <label className="maze-test-mode-label">
            <input
              type="checkbox"
              checked={isTestMode}
              onChange={(e) => setIsTestMode(e.currentTarget.checked)}
            />
            🔧 Test Mode
          </label>
        </div>

        <div className="maze-single-mode-toggle">
          <label className="maze-single-mode-label">
            <input
              type="checkbox"
              checked={isSingleMode}
              onChange={(e) => setIsSingleMode(e.currentTarget.checked)}
            />
            🎯 Single Maze Mode
          </label>
        </div>
      </div>

      <Usage />

      {/* Render different components based on mode */}
      {isTestMode ? (
        <MazeTestTool />
      ) : isSingleMode ? (
        <SingleMazeApp config={defaultSingleConfig} />
      ) : (
        <MazeGameApp />
      )}
    </>
  );
}

if (!__IS_PUBLISH__) {
  render(<DevApp />, document.getElementById('app')!);
}

const MazeGame = {
  async init(selector: Element | string, config: Partial<GameConfig>) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    let { root = location.origin } = config;
    if (root.at(-1) !== '/') {
      root += '/';
    }

    // default to show a loading indicator
    const loading = document.createElement('div');
    const bar = document.createElement('div');
    loading.style.cssText = `
      height: 30px;
      width: 100%;
      border: solid 2px #999;
      margin: 20% auto;
    `;
    bar.style.cssText = `
      width: 99%;
      height: 100%;
      background: #999;
      animation: loading 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
    `;
    loading.append(bar);
    el.append(loading);
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes loading {
      0% { width: 0%; }
      100% { width: 99%; }
    }`;
    document.head.appendChild(style);

    const loadCSS = async (url: string) => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'maze.css');

    render(<MazeGameApp config={config} />, el);
  },

  async initSingle(
    selector: Element | string,
    config: Partial<SingleGameConfig>
  ) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    let { root = location.origin } = config;
    if (root.at(-1) !== '/') {
      root += '/';
    }

    // default to show a loading indicator
    const loading = document.createElement('div');
    const bar = document.createElement('div');
    loading.style.cssText = `
      height: 30px;
      width: 100%;
      border: solid 2px #999;
      margin: 20% auto;
    `;
    bar.style.cssText = `
      width: 99%;
      height: 100%;
      background: #999;
      animation: loading 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
      transition: width 1.5s ease-in-out;
    `;
    loading.append(bar);
    el.append(loading);
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes loading {
      0% { width: 0%; }
      100% { width: 99%; }
    }`;
    document.head.appendChild(style);

    const loadCSS = async (url: string) => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'maze.css');

    render(<SingleMazeApp config={config} />, el);
  },
};

export default MazeGame;
