import type { <PERSON><PERSON><PERSON><PERSON>, Maze as MazeType } from './utils/maze-generator';

// grid, maze size=8, then grid is 8x8
// can be floating number
export interface GridPos {
  row: number;
  col: number;
}

export interface Wall {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface WallSegment {
  start: GridPos;
  end: GridPos;
}

export type MazeGenerationMethod = 'backtracking' | 'prims';

export type { MazeCell, MazeType as Maze };

export type LevelConfig = {
  size: number;
  seed: number;
  method: MazeGenerationMethod;
  entry?: GridPos;
  exit?: GridPos;
  wallWidth?: number;
  exitColor?: string;
  exitWallWidth?: number;
  exitDashArray?: [number, number];
};

export interface GameProgress {
  currentLevel: number;
  highestUnlockedLevel: number;
  currentTier: number;
  highestUnlockedTier: number;
  levelScores: Record<number, number>;
  totalScore: number;
}

// Scoring policy:
// 1. not using points number, but ⭐️
// 2. Each Level at most 3 ⭐️
//    - ⭐️: complete the level
//    - ⭐️: eat all foods
//    - ⭐️: no clue
// 3. Player get 3 ⭐️ if they complete the level without clue and eat all foods
// 4. Player get 2 ⭐️ if they complete the level without clue but not eat all foods
//                      or with clue on but eat all foods
// 5. Player get 1 ⭐️ if they complete the level with clue onbut not eat all foods
//
// currentScore can be calculated via useMemo [playerAtExit, foodCollected === totalFoodCount, showClue]

export interface GamePlayState {
  isGameActive: boolean; // Controls rendering loop and input handling (page visibility, level transitions)
  levelTransitioning: boolean; // Specific state for level completion dialog

  maze: MazeType | null;
  playerCell: GridPos; // row, col, can be floating number
  exitCell: GridPos;
  foodCells: GridPos[];

  playerAtExit: boolean;
  foodCollected: number;
  totalFoodCount: number;
  showClue: boolean;
  // record path, based on grid
  playerPath: GridPos[];
  visitCounts: Record<string, number>; // string: `${row},${col}`
}

export interface RenderOptions {
  gridMoveSpeed: number; // move X grid units per frame, default 0.03, center of one grid(cell) is the integer value (row, col)
  padding: number;
  wallWidth: number; // default wall width if not specified in level config
  wallColor: string;
  playerColor: string;
  playerRadius: number; // default 0.2 grid unit for player circle's radius, max 0.5 since over 0.5 will bigger than one cell
  foodColor: string;
  backgroundColor: string;
  clueColor: string; // Color for the clue path
  // Exit
  exitColor: string;
  exitWallWidth: number;
  exitDashArray: [number, number];
}

export interface GameConfig {
  root: string;
  lcPrefix: string;
  i18n: Record<string, string>;
  renderConfig: RenderOptions;
  levelConfig: LevelConfig[][];
}

export interface SingleGameConfig {
  root: string;
  i18n: Record<string, string>;
  size: number;
  seed: number;
  method: MazeGenerationMethod;
  foodCount: number;
  renderConfig: RenderOptions;
  timer: number;
  successCallback: () => void;
  failCallback: () => void;
}
