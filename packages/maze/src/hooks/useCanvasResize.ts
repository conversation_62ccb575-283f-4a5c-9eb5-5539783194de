import { RefObject } from 'preact';
import { useEffect, useRef, MutableRef } from 'preact/hooks';

/**
 * Hook to handle canvas size setup and updates.
 * Handles initial size setup, resize events, and DPI scaling.
 */
export const useCanvasResize = (
  canvasRef: RefObject<HTMLCanvasElement>,
  onResize?: () => void
): MutableRef<{
  canvasWidth: number;
  canvasHeight: number;
}> => {
  const canvasSizeRef = useRef({
    canvasWidth: 0,
    canvasHeight: 0,
  });

  useEffect(() => {
    const resize = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const parent = canvas.parentElement;
      if (!parent) return;

      // using parent width as the square size to draw the maze
      const containerWidth = parent.clientWidth;
      // const containerHeight = parent.clientHeight;

      // Handle high DPI displays
      const pixelRatio = window.devicePixelRatio || 1;

      // Set physical size (CSS pixels)
      canvas.style.width = `${containerWidth}px`;
      canvas.style.height = `${containerWidth}px`;

      // Set logical size (actual pixels)
      const canvasWidth = containerWidth * pixelRatio;
      const canvasHeight = containerWidth * pixelRatio;
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      canvasSizeRef.current = { canvasWidth, canvasHeight };

      // Scale the context to match the pixel ratio
      // const ctx = canvas.getContext('2d');
      // if (ctx) {
      //   ctx.scale(pixelRatio, pixelRatio);
      // }
      // seems no need to set scale as I already set canvas width and height with ratio
      onResize?.();
    };

    // Initial resize and event listener setup
    resize();

    let resizeObserver: ResizeObserver | null = null;
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(resize);
      resizeObserver.observe(canvasRef.current!.parentElement!);
    }

    window.addEventListener('resize', resize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', resize);
      resizeObserver?.disconnect();
    };
  }, [canvasRef, onResize]);

  return canvasSizeRef;
};
