import { useMemo } from 'preact/hooks';
import { LevelConfig } from '../types';

interface LevelConfigUtils {
  getTotalLevels: () => number;
  getTotalTiers: () => number;
  getTierLevelsRange: (tier: number) => [number, number];
  getTierByLevel: (level: number) => number;
  getFirstLevelOfTier: (tier: number) => number;
  getLevelConfig: (level: number) => LevelConfig | null;
  getTierConfig: (tier: number) => LevelConfig[] | null;
  isLevelUnlocked: (level: number, highestUnlockedLevel: number) => boolean;
  isTierUnlocked: (tier: number, highestUnlockedTier: number) => boolean;
  getNextLevelInfo: (currentLevel: number) => {
    nextLevel: number;
    nextTier: number;
    isTierChange: boolean;
  } | null;
  getCurrentTierProgress: (currentLevel: number, currentTier: number) => {
    current: number;
    total: number;
  };
  isLastLevelInTier: (level: number) => boolean;
  getPreviousLevelInfo: (currentLevel: number) => {
    prevLevel: number;
    prevTier: number;
    isTierChange: boolean;
  } | null;
}

export const useLevelConfig = (levelConfig: LevelConfig[][]): LevelConfigUtils => {
  return useMemo(() => {
    const tierLevelsCount = levelConfig.map(tier => tier.length);
    const totalLevels = tierLevelsCount.reduce((acc, curr) => acc + curr, 0);
    const totalTiers = levelConfig.length;

    const getTierByLevelInternal = (level: number): number => {
      if (level < 1 || level > totalLevels) return 0;

      let accumulator = 0;
      for (let i = 0; i < tierLevelsCount.length; i++) {
        accumulator += tierLevelsCount[i];
        if (level <= accumulator) return i + 1;
      }
      return 0; // Return 0 to indicate invalid level/tier
    };

    const getFirstLevelOfTierInternal = (tier: number): number => {
      if (tier < 1 || tier > totalTiers) return 0;

      return tierLevelsCount
        .slice(0, tier - 1)
        .reduce((acc, curr) => acc + curr, 0) + 1;
    };

    const utils: LevelConfigUtils = {
      getTotalLevels: () => totalLevels,

      getTotalTiers: () => totalTiers,

      getTierLevelsRange: (tier: number): [number, number] => {
        if (tier < 1 || tier > totalTiers) return [0, 0];

        const start = getFirstLevelOfTierInternal(tier);
        const end = start + tierLevelsCount[tier - 1] - 1;
        return [start, end];
      },

      getTierByLevel: getTierByLevelInternal,

      getFirstLevelOfTier: getFirstLevelOfTierInternal,

      getLevelConfig: (level: number): LevelConfig | null => {
        if (level < 1 || level > totalLevels) return null;

        const tier = getTierByLevelInternal(level);
        const tierStartLevel = getFirstLevelOfTierInternal(tier);
        const indexInTier = level - tierStartLevel;
        return levelConfig[tier - 1][indexInTier];
      },

      getTierConfig: (tier: number): LevelConfig[] | null => {
        if (tier < 1 || tier > totalTiers) return null;
        return levelConfig[tier - 1];
      },

      isLevelUnlocked: (level: number, highestUnlockedLevel: number): boolean => {
        return level <= highestUnlockedLevel;
      },

      isTierUnlocked: (tier: number, highestUnlockedTier: number): boolean => {
        return tier <= highestUnlockedTier;
      },

      getNextLevelInfo: (currentLevel: number) => {
        if (currentLevel >= totalLevels) return null;

        const nextLevel = currentLevel + 1;
        const currentTier = getTierByLevelInternal(currentLevel);
        const nextTier = getTierByLevelInternal(nextLevel);

        return {
          nextLevel,
          nextTier,
          isTierChange: nextTier > currentTier
        };
      },

      getCurrentTierProgress: (currentLevel: number, currentTier: number) => {
        const [startLevel, endLevel] = utils.getTierLevelsRange(currentTier);
        const current = currentLevel - startLevel + 1;
        const total = endLevel - startLevel + 1;
        return { current, total };
      },

      isLastLevelInTier: (level: number): boolean => {
        const tier = getTierByLevelInternal(level);
        const [, endLevel] = utils.getTierLevelsRange(tier);
        return level === endLevel;
      },

      getPreviousLevelInfo: (currentLevel: number) => {
        if (currentLevel <= 1) return null;

        const prevLevel = currentLevel - 1;
        const currentTier = getTierByLevelInternal(currentLevel);
        const prevTier = getTierByLevelInternal(prevLevel);

        return {
          prevLevel,
          prevTier,
          isTierChange: prevTier < currentTier
        };
      }
    };

    return utils;
  }, [levelConfig]);
};
