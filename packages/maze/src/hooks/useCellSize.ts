import { RefObject } from 'preact';
import { useEffect, useRef, MutableRef } from 'preact/hooks';
import { useGameContext } from '../components/MazeContext';

export const useCellSize = (
  canvasRef: RefObject<HTMLCanvasElement>
): MutableRef<{
  cellWidth: number;
  cellHeight: number;
}> => {
  const {
    config,
    gamePlayState: { maze },
  } = useGameContext();
  const cellSizeRef = useRef({ cellWidth: 0, cellHeight: 0 });

  useEffect(() => {
    const padding = config.renderConfig.padding!;

    const calculate = () => {
      const canvas = canvasRef.current;
      if (!canvas || !maze) return;

      const { rows, cols } = maze;
      if (rows <= 0 || cols <= 0) return;

      const cellWidth = (canvas.width - padding * 2) / cols;
      const cellHeight = (canvas.height - padding * 2) / rows;

      cellSizeRef.current = { cellWidth, cellHeight };
    };

    calculate();

    let resizeObserver: ResizeObserver | null = null;
    if (canvasRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(calculate);
      resizeObserver.observe(canvasRef.current);
    }

    return () => {
      if (canvasRef.current) {
        resizeObserver?.disconnect();
      }
    };
  }, [canvasRef, maze]);

  return cellSizeRef;
};
