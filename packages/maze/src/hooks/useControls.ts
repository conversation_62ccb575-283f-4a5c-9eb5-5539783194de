import { RefObject } from 'preact';
import { useEffect, useRef, useCallback, MutableRef } from 'preact/hooks';
import { GamePlayState, GridPos, Maze, WallSegment } from '../types';

interface SmoothControlsProps {
  containerRef: RefObject<HTMLDivElement>;
  gamePlayStateRef: MutableRef<GamePlayState>;
  setGamePlayState: (updater: (prev: GamePlayState) => GamePlayState) => void;
  moveSpeed: number;
  playerRadius: number;
  exitWallWidth?: number;
  getCellSizeInPixels?: () => number; // 获取当前 cell 的像素大小
  onFoodCollected?: () => void;
}

const directionMap = {
  ArrowUp: [-1, 0],
  ArrowDown: [1, 0],
  ArrowLeft: [0, -1],
  ArrowRight: [0, 1],
} as const;

type Direction = keyof typeof directionMap;

export const useSmoothControls = ({
  containerRef,
  gamePlayStateRef,
  setGamePlayState,
  moveSpeed, // move X grid units per frame, default 0.03
  playerRadius, // player radius in grid unit, default 0.2
  exitWallWidth,
  getCellSizeInPixels,
  onFoodCollected,
}: SmoothControlsProps) => {
  const isMovingRef = useRef(false);
  const activeDirectionsRef = useRef<Set<Direction>>(new Set());

  const canMoveToDirection = (
    direction: Direction,
    fromCell: GridPos
  ): { canMove: boolean; newCell: GridPos | null } => {
    const gamePlayState = gamePlayStateRef.current;
    if (!gamePlayState.maze) {
      return { canMove: false, newCell: null };
    }

    const [dr, dc] = directionMap[direction];
    const hasCollision = checkDirectionalCollision(
      fromCell,
      dr,
      dc,
      playerRadius,
      moveSpeed,
      gamePlayState.maze
    );

    if (hasCollision) {
      return { canMove: false, newCell: null };
    }

    const { row, col } = fromCell;

    const newRow = row + dr * moveSpeed;
    const newCol = col + dc * moveSpeed;

    const currentCellRow = Math.floor(row);
    const currentCellCol = Math.floor(col);
    const newCellRow = Math.floor(newRow);
    const newCellCol = Math.floor(newCol);

    const crossingBoundary =
      newCellRow !== currentCellRow || newCellCol !== currentCellCol;

    if (crossingBoundary) {
      // check if changing to new cell
      if (!gamePlayState.maze.isValid(newCellRow, newCellCol)) {
        // check boundary
        return { canMove: false, newCell: null };
      }

      return {
        canMove: true,
        newCell: { row: newCellRow, col: newCellCol },
      };
    }

    // moving within current cell, no new cell
    return { canMove: true, newCell: null };
  };

  const checkFoodAndExitCollision = () => {
    const gamePlayState = gamePlayStateRef.current;
    const { row, col } = gamePlayState.playerCell;
    const COLLISION_THRESHOLD = playerRadius * 2;

    const collidedFoodIndex = gamePlayState.foodCells.findIndex((foodCell) => {
      const distance = Math.sqrt(
        Math.pow(row - foodCell.row, 2) + Math.pow(col - foodCell.col, 2)
      );
      return distance < COLLISION_THRESHOLD;
    });

    const isFood = collidedFoodIndex !== -1;

    if (isFood && onFoodCollected) {
      onFoodCollected();

      // update food collecting process
      gamePlayStateRef.current = {
        ...gamePlayStateRef.current,
        foodCollected: gamePlayStateRef.current.foodCollected + 1,
        foodCells: gamePlayStateRef.current.foodCells.filter(
          (_, index) => index !== collidedFoodIndex
        ),
      };
    }

    // Check if player is colliding with exit walls (dashed walls)
    const isAtExit = checkExitWallCollision(
      gamePlayState.playerCell,
      gamePlayState.exitCell,
      gamePlayState.maze!,
      playerRadius,
      exitWallWidth,
      getCellSizeInPixels
    );

    if (isAtExit && !gamePlayStateRef.current.playerAtExit) {
      gamePlayStateRef.current = {
        ...gamePlayStateRef.current,
        playerAtExit: true,
        isGameActive: false, // pause render loop and controls
        levelTransitioning: true,
      };

      // if reached exit, need to update game state immediately
      requestIdleCallback(
        () => {
          setGamePlayState(() => ({ ...gamePlayStateRef.current }));
        },
        { timeout: 10 }
      );
    } else if (!isAtExit && gamePlayStateRef.current.playerAtExit) {
      // add this part to restore the state when continue to collect food from exit
      gamePlayStateRef.current = {
        ...gamePlayStateRef.current,
        playerAtExit: false,
        isGameActive: true,
        levelTransitioning: false,
      };
    }
  };

  const updatePath = (targetCell: GridPos) => {
    const gamePlayState = gamePlayStateRef.current;

    const targetCellRow = Math.round(targetCell.row);
    const targetCellCol = Math.round(targetCell.col);
    const lastPathPoint =
      gamePlayState.playerPath[gamePlayState.playerPath.length - 1];

    // check if new cell is the same as last path point
    const isSameCell =
      lastPathPoint.row === targetCellRow &&
      lastPathPoint.col === targetCellCol;

    // check if new cell is adjacent to last path point
    const rowDiff = Math.abs(lastPathPoint.row - targetCellRow);
    const colDiff = Math.abs(lastPathPoint.col - targetCellCol);
    const isAdjacent =
      (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);

    // only when new cell is adjacent to last path point and not the same as last path point
    if (isAdjacent && !isSameCell) {
      const pathKey = `${lastPathPoint.row},${lastPathPoint.col}-${targetCellRow},${targetCellCol}`;

      // update playerPath and visitCounts
      gamePlayStateRef.current = {
        ...gamePlayStateRef.current,
        playerPath: [
          ...gamePlayStateRef.current.playerPath,
          { row: targetCellRow, col: targetCellCol },
        ],
        visitCounts: {
          ...gamePlayStateRef.current.visitCounts,
          [pathKey]: (gamePlayStateRef.current.visitCounts[pathKey] || 0) + 1,
        },
      };
    }
  };

  const tryMove = () => {
    if (isMovingRef.current || activeDirectionsRef.current.size === 0) return;

    const gamePlayState = gamePlayStateRef.current;
    if (!gamePlayState.playerCell) return;

    // Check if any of the active directions can move
    const canMoveInAnyDirection = Array.from(activeDirectionsRef.current).some(
      (direction) => {
        const { canMove } = canMoveToDirection(
          direction,
          gamePlayState.playerCell
        );
        return canMove;
      }
    );

    if (!canMoveInAnyDirection) return;

    isMovingRef.current = true;
  };

  // handle key and touch event
  useEffect(() => {
    const preventArrowKeyScroll = (e: KeyboardEvent) => {
      if (
        e.key === 'ArrowUp' ||
        e.key === 'ArrowDown' ||
        e.key === 'ArrowLeft' ||
        e.key === 'ArrowRight' ||
        e.key === ' ' /* Space */
      ) {
        // Check if focus is on an editable element (input, textarea, contenteditable)
        const activeElement = document.activeElement;
        const isEditableElement =
          activeElement &&
          (activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.getAttribute('contenteditable') === 'true');

        // If user is editing text, don't prevent default behavior
        if (isEditableElement) return;

        e.preventDefault(); // prevent default browser behavior like page scroll
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      const key = e.key as Direction;
      if (!(key in directionMap)) return;

      // Check if focus is on an editable element (input, textarea, contenteditable)
      const activeElement = document.activeElement;
      const isEditableElement =
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.getAttribute('contenteditable') === 'true');

      // If user is editing text, don't interfere with arrow key navigation
      if (isEditableElement) return;

      e.preventDefault();

      // Check if game is active before processing input
      if (!gamePlayStateRef.current.isGameActive) return;

      // Add direction to active set if not already present
      if (!activeDirectionsRef.current.has(key)) {
        activeDirectionsRef.current.add(key);

        // If not currently moving, try to start moving
        if (!isMovingRef.current) {
          tryMove();
        }
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      const key = e.key as Direction;
      if (!(key in directionMap)) return;

      // Check if focus is on an editable element (input, textarea, contenteditable)
      const activeElement = document.activeElement;
      const isEditableElement =
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.getAttribute('contenteditable') === 'true');

      // If user is editing text, don't interfere with arrow key navigation
      if (isEditableElement) return;

      e.preventDefault();

      // Remove direction from active set
      activeDirectionsRef.current.delete(key);

      // If no directions are active, stop moving
      if (activeDirectionsRef.current.size === 0) {
        resetMovingState();
      }
    };

    const handleTouchStart = (e: TouchEvent) => {
      e.preventDefault();

      const gamePlayState = gamePlayStateRef.current;
      if (
        !gamePlayState.isGameActive ||
        !gamePlayState.maze ||
        isMovingRef.current
      )
        return;

      const touch = e.touches[0];
      const touchArea = document.elementFromPoint(touch.clientX, touch.clientY);

      let direction: Direction | null = null;

      if (touchArea) {
        if (touchArea.classList.contains('maze-up')) {
          direction = 'ArrowUp';
        } else if (touchArea.classList.contains('maze-down')) {
          direction = 'ArrowDown';
        } else if (touchArea.classList.contains('maze-left')) {
          direction = 'ArrowLeft';
        } else if (touchArea.classList.contains('maze-right')) {
          direction = 'ArrowRight';
        }
      }

      if (direction) {
        activeDirectionsRef.current.clear();
        activeDirectionsRef.current.add(direction);
        tryMove();
      }
    };

    const handleTouchEnd = () => {
      resetMovingState();
    };

    window.addEventListener('keydown', preventArrowKeyScroll, {
      capture: true,
    });

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    containerRef.current?.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    containerRef.current?.addEventListener('touchend', handleTouchEnd);

    return () => {
      window.removeEventListener('keydown', preventArrowKeyScroll, {
        capture: true,
      });
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      containerRef.current?.removeEventListener('touchstart', handleTouchStart);
      containerRef.current?.removeEventListener('touchend', handleTouchEnd);
    };
  }, []);

  const updatePlayerPos = useCallback((): GridPos => {
    if (!isMovingRef.current || activeDirectionsRef.current.size === 0) {
      return gamePlayStateRef.current.playerCell;
    }

    console.log('moveSpeed: ', moveSpeed);

    const currentCell = gamePlayStateRef.current.playerCell;
    let totalDr = 0;
    let totalDc = 0;
    let validDirections = 0;

    // Calculate combined movement vector from all active directions
    for (const direction of activeDirectionsRef.current) {
      const { canMove } = canMoveToDirection(direction, currentCell);

      if (canMove) {
        const [dr, dc] = directionMap[direction];
        totalDr += dr;
        totalDc += dc;
        validDirections++;
      }
    }

    // If no valid directions, stay at current position
    if (validDirections === 0) {
      return gamePlayStateRef.current.playerCell;
    }

    // Normalize movement speed for diagonal movement
    // When moving diagonally, we want the same speed as single direction
    const magnitude = Math.sqrt(totalDr * totalDr + totalDc * totalDc);
    const normalizedDr = magnitude > 0 ? totalDr / magnitude : 0;
    const normalizedDc = magnitude > 0 ? totalDc / magnitude : 0;

    const newRow = Number(
      (currentCell.row + normalizedDr * moveSpeed).toFixed(2)
    );
    const newCol = Number(
      (currentCell.col + normalizedDc * moveSpeed).toFixed(2)
    );

    // Final collision check for the combined movement
    // Create a virtual direction and speed for the combined movement
    const deltaRow = newRow - currentCell.row;
    const deltaCol = newCol - currentCell.col;
    const distance = Math.sqrt(deltaRow * deltaRow + deltaCol * deltaCol);

    if (distance > 0) {
      // Normalize the direction vector
      const normalizedDr = deltaRow / distance;
      const normalizedDc = deltaCol / distance;

      // Check collision using the existing function
      const hasCollision = checkDirectionalCollision(
        currentCell,
        normalizedDr,
        normalizedDc,
        playerRadius,
        distance,
        gamePlayStateRef.current.maze!
      );

      if (hasCollision) {
        return gamePlayStateRef.current.playerCell;
      }
    }

    // Update player position
    gamePlayStateRef.current.playerCell = {
      row: newRow,
      col: newCol,
    };

    checkFoodAndExitCollision(); // check on each frame

    const currentCellRow = Math.round(currentCell.row);
    const currentCellCol = Math.round(currentCell.col);
    const newCellRow = Math.round(newRow);
    const newCellCol = Math.round(newCol);

    const crossingBoundary =
      newCellRow !== currentCellRow || newCellCol !== currentCellCol;

    if (crossingBoundary) {
      // only when crossing to newCell, update path data, visit counts
      updatePath({ row: newCellRow, col: newCellCol });
    }

    return gamePlayStateRef.current.playerCell;
  }, [moveSpeed]);

  const resetMovingState = () => {
    // stop moving and clear all directions
    isMovingRef.current = false;
    activeDirectionsRef.current.clear();
  };

  return {
    updatePlayerPos,
    resetMovingState,
  };
};

function checkDirectionalCollision(
  currentCell: GridPos,
  directionRow: number,
  directionCol: number,
  playerRadius: number,
  moveDistance: number,
  maze: Maze
): boolean {
  // Calculate target position
  const targetRow = currentCell.row + directionRow * moveDistance;
  const targetCol = currentCell.col + directionCol * moveDistance;
  const targetCell = { row: targetRow, col: targetCol };

  // Get cells to check (current cell and its neighbors)
  const cellsToCheck: GridPos[] = [];
  const currentCellRow = Math.floor(currentCell.row);
  const currentCellCol = Math.floor(currentCell.col);
  cellsToCheck.push({ row: currentCellRow, col: currentCellCol });

  const offsets = [
    [-1, 0], // up
    [1, 0], // down
    [0, -1], // left
    [0, 1], // right, adjacent
    [-1, -1], // upper left
    [-1, 1], // upper right
    [1, -1], // lower left
    [1, 1], // lower right, diagonal
  ];

  // Get all valid neighbor
  for (const [rowOffset, colOffset] of offsets) {
    const neighborRow = currentCellRow + rowOffset;
    const neighborCol = currentCellCol + colOffset;
    if (maze.isValid(neighborRow, neighborCol)) {
      cellsToCheck.push({ row: neighborRow, col: neighborCol });
    }
  }

  // Check collision with walls
  for (const cell of cellsToCheck) {
    const wallSegments = getWallSegments(cell.row, cell.col, maze);

    for (const segment of wallSegments) {
      const distance = distanceFromPointToWallSegment(targetCell, segment);
      // if distance is less than player circle radius, collision
      if (distance < playerRadius) {
        return true;
      }
    }
  }

  return false;
}

function getWallSegments(row: number, col: number, maze: Maze): WallSegment[] {
  const segments: WallSegment[] = [];
  const cell = maze.grid[row][col];

  if (cell.walls.top) {
    segments.push({
      start: { row: row - 0.5, col: col - 0.5 },
      end: { row: row - 0.5, col: col + 0.5 },
    });
  }

  if (cell.walls.right) {
    segments.push({
      start: { row: row - 0.5, col: col + 0.5 },
      end: { row: row + 0.5, col: col + 0.5 },
    });
  }

  if (cell.walls.bottom) {
    segments.push({
      start: { row: row + 0.5, col: col - 0.5 },
      end: { row: row + 0.5, col: col + 0.5 },
    });
  }

  if (cell.walls.left) {
    segments.push({
      start: { row: row - 0.5, col: col - 0.5 },
      end: { row: row + 0.5, col: col - 0.5 },
    });
  }

  return segments;
}

function distanceFromPointToWallSegment(
  point: GridPos,
  segment: WallSegment
): number {
  const { start, end } = segment;

  // line vector
  const lineVectorX = end.col - start.col;
  const lineVectorY = end.row - start.row;

  // 点到线段起点的向量 vector from point to start of line
  const pointVectorX = point.col - start.col;
  const pointVectorY = point.row - start.row;

  const lineLengthSquared =
    lineVectorX * lineVectorX + lineVectorY * lineVectorY;

  // if line length is 0, return distance from point to start of line
  if (lineLengthSquared === 0) {
    return Math.sqrt(pointVectorX * pointVectorX + pointVectorY * pointVectorY);
  }

  // calculate projection ratio t (0-1 range means on the line, less than 0 means before the start, greater than 1 means after the end)
  const t = Math.max(
    0,
    Math.min(
      1,
      (pointVectorX * lineVectorX + pointVectorY * lineVectorY) /
        lineLengthSquared
    )
  );

  // calculate projection point
  const projectionX = start.col + t * lineVectorX;
  const projectionY = start.row + t * lineVectorY;

  // calculate distance from point to projection point
  const dx = point.col - projectionX;
  const dy = point.row - projectionY;

  return Math.sqrt(dx * dx + dy * dy);
}

function checkExitWallCollision(
  playerCell: GridPos,
  exitCell: GridPos,
  maze: Maze,
  playerRadius: number,
  exitWallWidth?: number,
  getCellSizeInPixels?: () => number
): boolean {
  // first ensure player is in the exit cell
  if (
    playerCell.row <= exitCell.row - 0.5 ||
    playerCell.row >= exitCell.row + 0.5 ||
    playerCell.col <= exitCell.col - 0.5 ||
    playerCell.col >= exitCell.col + 0.5
  ) {
    return false;
  }

  // Only check collision if player is in or very close to the exit cell
  const cellDistance = Math.sqrt(
    Math.pow(playerCell.row - exitCell.row, 2) +
      Math.pow(playerCell.col - exitCell.col, 2)
  );

  // If player is too far from exit cell center, no collision
  if (cellDistance > 1.5) return false;

  // Check if exit cell is valid
  if (!maze.isValid(exitCell.row, exitCell.col)) return false;

  const cell = maze.grid[exitCell.row][exitCell.col];

  // Only check collision with the bottom wall (the exit wall)
  if (cell.walls.bottom) {
    // Adjust wall segment position based on wall thickness
    // Thick walls extend inward into the cell, making them easier to reach
    let wallThickness = 0;
    if (getCellSizeInPixels && exitWallWidth) {
      const cellSizeInPixels = getCellSizeInPixels();
      wallThickness = exitWallWidth / cellSizeInPixels; // Accurate pixel to grid unit conversion
    } else {
      // Fallback to approximate conversion if cell size is not available
      wallThickness = (exitWallWidth || 3) / 100;
    }
    const wallOffset = wallThickness * 0.5; // Half thickness extends inward
    // subtract wallOffset in case the wallWidth is large and will be effect the collision check
    const exitWallSegment = {
      start: { row: exitCell.row + 0.5 - wallOffset, col: exitCell.col - 0.5 },
      end: { row: exitCell.row + 0.5 - wallOffset, col: exitCell.col + 0.5 },
    };

    const distance = distanceFromPointToWallSegment(
      playerCell,
      exitWallSegment
    );

    // If player circle touches the exit wall (bottom wall), it's a collision
    // Use simple and effective tolerance: 1.5 times player radius
    if (distance <= playerRadius * 1.5) {
      return true;
    }
  }

  return false;
}
