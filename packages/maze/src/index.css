.maze-game-container {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;

  button {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #45a049;
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }

  .link-button {
    background-color: transparent;
    color: #4CAF50;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s;

    &:hover {
      color: #45a049;
      background-color: transparent;
    }
  }

  .maze-canvas-container {
    position: relative;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
  }

  .maze-game-canvas {
    width: 100%;
    height: 100%;
    aspect-ratio: 1 / 1;
    background-color: white;
    transition: opacity 0.1s ease;
  }

  .maze-touch-area {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    /* background-color: red; */
  }

  .maze-up {
    clip-path: polygon(10% 0, 90% 0, 50% 40%);
    top: 0;
  }

  .maze-down {
    clip-path: polygon(10% 100%, 90% 100%, 50% 60%);
    bottom: 0;
  }

  .maze-left {
    clip-path: polygon(0 10%, 40% 50%, 0 90%);
    top: 0;
    left: 0;
  }

  .maze-right {
    clip-path: polygon(100% 10%, 60% 50%, 100% 90%);
    top: 0;
    right: 0;
  }

  /************** 🡣🡣🡣 Toast 🡣🡣🡣 **************/

  .toast-container {
    position: absolute;
    pointer-events: none;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    &.position-top {
      top: 10px;
      left: 0;
      right: 0;
    }

    &.position-center {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    &.position-bottom {
      /* make it below the maze canvas */
      bottom: -36px;
      left: 0;
      right: 0;
    }
  }

  .toast-message {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    width: fit-content;
    max-width: 90%;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translate(0, -10px, 0);
    pointer-events: none;
    will-change: opacity, transform;

    &.toast-visible {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }

  /************** 🡱🡱🡱 Toast 🡱🡱🡱 **************/

  /************** 🡣🡣🡣 GamePanel 🡣🡣🡣 **************/
  .game-panel-container {
    margin-bottom: 15px;
    font-size: 16px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 12px 15px 15px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .game-controls {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 12px;
  }

  .selectors-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 10px;
  }

  .tier-dropdown,
  .level-dropdown {
    padding: 8px 12px;
    border-radius: 8px;
    border: 2px solid #3498db;
    background-color: #f8f9fa;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23333%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 12px top 50%;
    background-size: 10px auto;
    min-width: 120px;
    transition: all 0.3s ease;
    outline: none;
  }

  .level-dropdown {
    border-color: #9b59b6;
    width: 150px;
  }

  .game-settings {
    display: flex;
    align-items: center;
    justify-content: center;

    .game-settings-icon {
      font-size: 2rem;
      cursor: pointer;
    }
  }

  .settings-stats {
    padding-top: 20px;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 6px;
    text-align: left;
  }

  .settings-item-box {
    padding-top: 20px;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 20px;
    text-align: left;
  }

  .settings-item-tip {
    color: #555;
  }

  .settings-reset-button,
  .settings-clue-button {
    background-color: #aaa;
    color: white;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    outline: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    &:hover {
      background-color: #d32f2f;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled+.settings-item-tip {
      color: #999;
    }
  }

  .settings-clue-button {
    &.clue-on {
      background-color: #FF9800;
    }

    &:hover {
      background-color: #FF9800;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }
  }

  /************** 🡱🡱🡱 GamePanel 🡱🡱🡱 **************/

  /************** 🡣🡣🡣 Level Complete Popup 🡣🡣🡣 **************/

  .level-complete-dialog {
    .maze-dialog-buttons {
      justify-content: space-between;
    }

    .maze-dialog-confirm {
      background-color: #ff9800;

      &:hover {
        background-color: #e67e00;
      }
    }

    .maze-continue-btn {
      background-color: #f1f1f1;
      color: #333;

      &:hover {
        background-color: #e1e1e1;
      }
    }
  }

  .maze-summary-message {
    width: 75%;
    margin: 30px auto;
    text-align: left;
  }

  .maze-go-next {
    margin: 30px auto;
  }

  .maze-go-next-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    gap: 6px;

    .spacebar-tip {
      font-size: 0.85rem;
    }
  }

  .all-levels-completed {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .not-all-stars-collected {
    padding: 10px;
    background-color: #FFF8E1;
    color: #FF9800;
    border-radius: 5px;
    text-align: center;
    font-size: 14px;
  }

  /************** 🡱🡱🡱 Level Complete Popup 🡱🡱🡱 **************/

  /************** 🡣🡣🡣 Dialog 🡣🡣🡣 **************/

  .maze-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;

    &.maze-dialog-visible {
      opacity: 1;
      visibility: visible;

      .maze-dialog {
        transform: translateY(0);
      }
    }
  }

  .maze-dialog {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    width: 300px;
    max-width: 90%;
    padding: 20px;
    text-align: center;
    transform: translateY(-20px);
    transition: transform 0.3s;
  }

  .maze-dialog-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }

  .maze-dialog-message {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }

  .maze-dialog-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
  }

  .maze-dialog-cancel {
    background-color: #f1f1f1;
    color: #333;
    font-size: 14px;

    &:hover {
      background-color: #e1e1e1;
    }
  }

  .maze-dialog-confirm {
    background-color: #f44336;
    color: white;
    font-size: 14px;

    &:hover {
      background-color: #d32f2f;
    }
  }

  /************** 🡱🡱🡱 Dialog 🡱🡱🡱 **************/

}

/* Single Maze Styles */
.single-maze-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.single-maze-canvas {
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.single-maze-loading,
.single-maze-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-family: Arial, sans-serif;
  color: #666;
}

.single-maze-error {
  color: #d32f2f;
}

/* Development Mode Toggles */
.maze-dev-toggles {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.maze-test-mode-toggle,
.maze-single-mode-toggle {
  background: white;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.maze-test-mode-label,
.maze-single-mode-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  margin: 0;
}

.maze-single-mode-toggle {
  background: #f8f9fa;
  border-color: #28a745;
}

.maze-single-mode-toggle input:checked {
  accent-color: #28a745;
}

.maze-single-mode-toggle:has(input:checked) .maze-single-mode-label {
  color: #28a745;
  font-weight: 500;
}
