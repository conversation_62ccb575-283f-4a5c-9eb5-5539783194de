export const NativeToast = {
  container: null as HTMLDivElement | null,

  // attach Toast container to element, only need to execute once
  attachTo(element: HTMLElement) {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    this.container = document.createElement('div');
    this.container.className = 'toast-container position-top';
    element.appendChild(this.container);

    return this.container;
  },

  // current toast element
  currentToast: null as HTMLDivElement | null,
  currentTimer: null as number | null,

  // use it in any component
  show(
    message: string,
    type: 'success' | 'info' | 'warning' | 'error' = 'info',
    duration: number = 2000
  ) {
    if (!this.container) {
      console.warn(
        'NativeToast: container not initialized, please call attachTo first'
      );
      return;
    }

    if (this.currentToast && this.container.contains(this.currentToast)) {
      this.container.removeChild(this.currentToast);
      this.currentToast = null;
    }

    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }

    const toast = document.createElement('div');
    toast.className = `toast-message type-${type} toast-visible`;
    toast.textContent = message;
    // append to container
    this.container.appendChild(toast);
    this.currentToast = toast;

    this.currentTimer = setTimeout(() => {
      toast.classList.remove('toast-visible');
      setTimeout(() => {
        if (this.container && this.container.contains(toast)) {
          this.container.removeChild(toast);
          if (this.currentToast === toast) {
            this.currentToast = null;
          }
        }
      }, 300);
      this.currentTimer = null;
    }, duration) as unknown as number;
  },

  clearAll() {
    if (this.container) {
      while (this.container.firstChild) {
        this.container.removeChild(this.container.firstChild);
      }
    }
  },
};
