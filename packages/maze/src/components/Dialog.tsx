import { useRef, useEffect } from 'preact/hooks';
import type { ComponentChildren } from 'preact';

interface DialogProps {
  isOpen: boolean;
  title: string;
  className?: string;
  message?: string;
  children?: ComponentChildren;
  noConfirm?: boolean;
  noCancel?: boolean;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const Dialog = ({
  isOpen,
  title,
  className = '',
  message,
  children,
  noConfirm = false,
  noCancel = false,
  confirmText = 'OK',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
}: DialogProps) => {
  const dialogRef = useRef<HTMLDivElement>(null);

  // ESC to close
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel?.();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onCancel]);

  return (
    <div
      className={`maze-dialog-overlay ${isOpen ? 'maze-dialog-visible' : ''}`}
    >
      <div className={'maze-dialog ' + className} ref={dialogRef}>
        <div className="maze-dialog-title">{title}</div>
        <div className="maze-dialog-content">
          {children || <div className="maze-dialog-message">{message}</div>}
        </div>
        {(!noCancel || !noConfirm) && (
          <div className="maze-dialog-buttons">
            {!noCancel && (
              <button
                type="button"
                className="maze-dialog-cancel"
                onClick={onCancel}
              >
                {cancelText}
              </button>
            )}
            {!noConfirm && (
              <button
                type="button"
                className="maze-dialog-confirm"
                onClick={onConfirm}
              >
                {confirmText}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dialog;
