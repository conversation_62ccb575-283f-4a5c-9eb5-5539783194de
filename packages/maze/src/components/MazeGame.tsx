import { useRef, useEffect } from 'preact/hooks';
import { LevelConfig } from '../types';
import GamePanel from './GamePanel';
import LevelCompleteDialog from './LevelCompleteDialog';
import { NativeToast } from './NativeToast';
import { useGameContext } from './MazeContext';
import { useLevelConfig } from '../hooks/useLevelConfig';
import { useCanvasResize } from '../hooks/useCanvasResize';
import { useCellSize } from '../hooks/useCellSize';
import { useSmoothControls } from '../hooks/useControls';
import { renderMaze } from '../utils/maze-renderer';
import { generateMazeForLevel } from '../utils/maze-generator';

export default function MazeGame() {
  const { setGamePlayState, gamePlayStateRef, gameProgress, config } =
    useGameContext();
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { i18n, levelConfig, renderConfig } = config;
  const levelConfigUtils = useLevelConfig(levelConfig);
  const curLevelConfigRef = useRef<LevelConfig | null>(
    levelConfigUtils.getLevelConfig(gameProgress.currentLevel)
  );

  useEffect(() => {
    if (containerRef.current) {
      NativeToast.attachTo(containerRef.current);
    }
  }, []);

  // Force re-render when canvas is resized
  const forceRender = useRef<() => void>();

  const canvasSizeRef = useCanvasResize(canvasRef, () => {
    // Trigger immediate render when canvas size changes
    forceRender.current?.();
  });
  const cellSizeRef = useCellSize(canvasRef);

  // update current level config
  useEffect(() => {
    curLevelConfigRef.current = levelConfigUtils.getLevelConfig(
      gameProgress.currentLevel
    );
  }, [gameProgress.currentLevel]);

  // generate maze when level changes
  const generate = () => {
    const newLevelState = generateMazeForLevel(
      gameProgress.currentLevel,
      curLevelConfigRef.current
    );
    if (!newLevelState) return;

    setGamePlayState((prev) => ({
      ...prev,
      ...newLevelState,
    }));
  };

  useEffect(() => {
    generate();
  }, [gameProgress.currentLevel]);

  const handleRetry = () => {
    generate();
  };

  const { updatePlayerPos, resetMovingState } = useSmoothControls({
    containerRef,
    gamePlayStateRef,
    setGamePlayState,
    playerRadius: renderConfig.playerRadius,
    moveSpeed: renderConfig.gridMoveSpeed,
    onFoodCollected: () => {
      const remaining =
        gamePlayStateRef.current.totalFoodCount -
        gamePlayStateRef.current.foodCollected -
        1; // subtract 1 because the state is not updated yet when this callback is executed

      if (remaining > 0) {
        NativeToast.show(
          i18n.foodCollectedToast.replace('%NUM%', remaining.toString()),
          3000
        );
      } else {
        NativeToast.show(i18n.allFoodCollectedToast);
      }
    },
  });

  useEffect(() => {
    resetMovingState();
  }, [gameProgress.currentLevel, resetMovingState]);

  // setting up render loop with visibility and game state control
  useEffect(() => {
    let animationFrameId: number;
    let isPageVisible = !document.hidden;

    const render = () => {
      const ctx = canvasRef.current?.getContext('2d');
      const gameState = gamePlayStateRef.current;

      // Check if we should render this frame
      const shouldRender =
        isPageVisible && gameState?.isGameActive && ctx && gameState?.maze;

      if (!shouldRender) {
        // If page is hidden, game is inactive, or not ready, skip rendering but keep the loop
        animationFrameId = requestAnimationFrame(render);
        return;
      }

      // Only update player position when game is active
      updatePlayerPos();

      // Use shared render function
      doRender();

      animationFrameId = requestAnimationFrame(render);
    };

    // Set up force render function for canvas resize
    const doRender = () => {
      const ctx = canvasRef.current?.getContext('2d');
      const gameState = gamePlayStateRef.current;

      if (ctx && gameState?.maze) {
        const wallWidth =
          curLevelConfigRef.current?.wallWidth || renderConfig?.wallWidth;
        const exitColor =
          curLevelConfigRef.current?.exitColor || renderConfig?.exitColor;
        const exitWallWidth =
          curLevelConfigRef.current?.exitWallWidth ||
          renderConfig?.exitWallWidth;
        const exitDashArray =
          curLevelConfigRef.current?.exitDashArray ||
          renderConfig?.exitDashArray;

        renderMaze(ctx, gameState, canvasSizeRef.current, cellSizeRef.current, {
          ...renderConfig,
          wallWidth,
          exitColor,
          exitWallWidth,
          exitDashArray,
        });
      }
    };

    forceRender.current = doRender;

    // Handle page visibility and focus changes (similar to Chrome Dino game)
    const handleVisibilityChange = (e?: Event) => {
      const wasVisible = isPageVisible;

      // Check multiple conditions for visibility/focus state
      const isCurrentlyVisible =
        !document.hidden &&
        document.visibilityState === 'visible' &&
        (!e || e.type !== 'blur');

      isPageVisible = isCurrentlyVisible;

      // If page/window becomes visible/focused and game was active, resume
      if (!wasVisible && isPageVisible) {
        if (gamePlayStateRef.current?.levelTransitioning === false) {
          // need to update state to keep state and ref in sync
          setGamePlayState({
            ...gamePlayStateRef.current,
            isGameActive: true,
          });
        }
      }
      // If page/window becomes hidden/blurred, pause game
      else if (wasVisible && !isPageVisible) {
        setGamePlayState({
          ...gamePlayStateRef.current,
          isGameActive: false,
        });
      }
    };

    // Add multiple event listeners for comprehensive coverage
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    // Start the render loop
    animationFrameId = requestAnimationFrame(render);

    return () => {
      cancelAnimationFrame(animationFrameId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, []);

  // show arrow tips once on loaded
  useEffect(() => {
    NativeToast.show(i18n.useArrowKeyTips, 3000);
  }, []);

  return (
    <div className="maze-game-container">
      <GamePanel />
      {/* Popup when complete one level, can choose to continue all stars, or click to go next level */}
      <LevelCompleteDialog onRetry={handleRetry} />

      <div className="maze-canvas-container" ref={containerRef}>
        <canvas ref={canvasRef} className="maze-game-canvas"></canvas>
        <div className="maze-touch-area maze-up"></div>
        <div className="maze-touch-area maze-down"></div>
        <div className="maze-touch-area maze-left"></div>
        <div className="maze-touch-area maze-right"></div>
      </div>
    </div>
  );
}
