import { useState, useEffect, useRef } from 'preact/hooks';
import type {
  SingleGameConfig,
  LevelConfig,
  GamePlayState,
  GridPos,
} from '../types';
import { Maze } from '../utils/maze-generator';
import { renderMaze } from '../utils/maze-renderer';
import { useSmoothControls } from '../hooks/useControls';

interface SingleMazeProps {
  config: SingleGameConfig;
}

export default function SingleMaze({ config }: SingleMazeProps) {
  const [gameState, setGameState] = useState<GamePlayState | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 400 });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gameStateRef = useRef<GamePlayState>({} as GamePlayState);
  const animationFrameRef = useRef<number>();

  // Generate game state from config
  const generateGamePlayStateFromConfig = (
    config: LevelConfig
  ): GamePlayState => {
    const { size, seed, method, entry, exit } = config;
    const rows = size;
    const cols = size;

    const entryCell = entry || { row: 0, col: 0 };
    const exitCell = exit || { row: rows - 1, col: cols - 1 };
    const maze = new Maze(rows, cols, method, seed, entryCell, exitCell);

    // Generate food positions if specified in config
    const foodCells: GridPos[] = [];
    if (config.foodPositions && config.foodPositions.length > 0) {
      foodCells.push(...config.foodPositions);
    } else {
      // Generate some default food positions
      const foodCount = Math.min(3, Math.floor(size / 3));
      for (let i = 0; i < foodCount; i++) {
        let foodPos: GridPos;
        let attempts = 0;
        do {
          foodPos = {
            row: Math.floor(Math.random() * rows),
            col: Math.floor(Math.random() * cols),
          };
          attempts++;
        } while (
          attempts < 50 &&
          ((foodPos.row === entryCell.row && foodPos.col === entryCell.col) ||
            (foodPos.row === exitCell.row && foodPos.col === exitCell.col) ||
            foodCells.some(
              (fp) => fp.row === foodPos.row && fp.col === foodPos.col
            ))
        );
        if (attempts < 50) {
          foodCells.push(foodPos);
        }
      }
    }

    return {
      isGameActive: true,
      levelTransitioning: false,
      maze,
      playerCell: entryCell,
      exitCell,
      foodCells,
      playerAtExit: false,
      foodCollected: 0,
      totalFoodCount: foodCells.length,
      showClue: config.showClue ?? false,
      playerPath: [entryCell],
      visitCounts: { [`${entryCell.row},${entryCell.col}`]: 1 },
    };
  };

  // Initialize maze when config changes
  useEffect(() => {
    const initializeMaze = async () => {
      setIsGenerating(true);
      try {
        const gamePlayState = generateGamePlayStateFromConfig(config);
        gameStateRef.current = gamePlayState;
        setGameState(gamePlayState);
      } catch (error) {
        console.error('Error generating single maze:', error);
      } finally {
        setIsGenerating(false);
      }
    };

    initializeMaze();
  }, [config]);

  // Set up smooth controls
  const { updatePlayerPos, resetMovingState } = useSmoothControls({
    containerRef,
    gamePlayStateRef: gameStateRef,
    setGamePlayState: (updater) => {
      if (gameStateRef.current) {
        const newState = updater(gameStateRef.current);
        gameStateRef.current = newState;
        setGameState({ ...newState });
      }
    },
    playerRadius: defaultRenderConfig.playerRadius,
    moveSpeed: config.gridMoveSpeed || defaultRenderConfig.gridMoveSpeed,
    onFoodCollected: () => {
      if (onFoodCollected) {
        onFoodCollected();
      }
    },
    onExitReached: () => {
      if (onComplete) {
        onComplete();
      }
    },
  });

  // Calculate canvas size based on container
  useEffect(() => {
    const updateCanvasSize = () => {
      const container = containerRef.current;
      if (!container || !gameState) return;

      const containerWidth = container.clientWidth;
      const maxCanvasSize = Math.min(containerWidth, window.innerHeight * 0.8);
      const cellSize = Math.floor(
        (maxCanvasSize - defaultRenderConfig.padding * 2) / config.size
      );
      const calculatedCanvasSize =
        config.size * cellSize + defaultRenderConfig.padding * 2;

      setCanvasSize({
        width: calculatedCanvasSize,
        height: calculatedCanvasSize,
      });
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [config.size, gameState]);

  // Rendering loop
  useEffect(() => {
    const render = () => {
      const canvas = canvasRef.current;
      const currentGameState = gameStateRef.current;

      if (!canvas || !currentGameState || !currentGameState.maze) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      // Update canvas size if needed
      if (
        canvas.width !== canvasSize.width ||
        canvas.height !== canvasSize.height
      ) {
        canvas.width = canvasSize.width;
        canvas.height = canvasSize.height;
      }

      // Update player position (smooth movement)
      if (currentGameState.isGameActive) {
        updatePlayerPos();
      }

      // Calculate cell size for rendering
      const cellSize = Math.floor(
        (canvasSize.width - defaultRenderConfig.padding * 2) / config.size
      );

      // Render maze
      renderMaze(
        ctx,
        currentGameState,
        { canvasWidth: canvasSize.width, canvasHeight: canvasSize.height },
        { cellWidth: cellSize, cellHeight: cellSize },
        {
          ...defaultRenderConfig,
          wallWidth: config.wallWidth || defaultRenderConfig.wallWidth,
          exitColor: config.exitColor || defaultRenderConfig.exitColor,
          exitWallWidth:
            config.exitWallWidth || defaultRenderConfig.exitWallWidth,
          exitDashArray:
            config.exitDashArray || defaultRenderConfig.exitDashArray,
        }
      );

      animationFrameRef.current = requestAnimationFrame(render);
    };

    if (gameState) {
      animationFrameRef.current = requestAnimationFrame(render);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [gameState, canvasSize, config]);

  // Reset moving state when config changes
  useEffect(() => {
    resetMovingState();
  }, [config, resetMovingState]);

  if (isGenerating) {
    return (
      <div className="single-maze-loading">
        <div>Generating maze...</div>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="single-maze-error">
        <div>Failed to generate maze</div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="single-maze-container">
      <canvas
        ref={canvasRef}
        className="single-maze-canvas"
        width={canvasSize.width}
        height={canvasSize.height}
      />
    </div>
  );
}
