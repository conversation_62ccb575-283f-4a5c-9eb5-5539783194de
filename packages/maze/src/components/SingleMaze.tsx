import { useState, useEffect, useRef } from 'preact/hooks';
import type { SingleGameConfig, GamePlayState } from '../types';
import { generateMaze } from '../utils/maze-generator';
import { renderMaze } from '../utils/maze-renderer';
import { useSmoothControls } from '../hooks/useControls';
import Dialog from './Dialog';

// Format time display (mm:ss)
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`;
};

export default function SingleMaze({ config }: { config: SingleGameConfig }) {
  const {
    size,
    seed,
    method,
    foodCount,
    renderConfig,
    successCallback,
    failCallback,
    timer,
    i18n,
  } = config;
  const [gameState, setGameState] = useState<GamePlayState | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 400 });
  const [timeLeft, setTimeLeft] = useState(timer);
  const [isGameCompleted, setIsGameCompleted] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [popupMessage, setPopupMessage] = useState('');
  const [hasStarted, setHasStarted] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gameStateRef = useRef<GamePlayState>({} as GamePlayState);
  const animationFrameRef = useRef<number>();
  const timerRef = useRef<NodeJS.Timeout>();

  // Timer functions
  const startTimer = () => {
    if (timerRef.current || hasStarted) {
      return; // Timer already started or game already started
    }

    setHasStarted(true);
    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          // Time's up!
          clearInterval(timerRef.current!);
          handleTimeUp();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const handleTimeUp = () => {
    setIsGameCompleted(true);
    setPopupMessage(`⏰ ${i18n.failMsg}`);
    setShowPopup(true);

    // Disable game controls
    if (gameStateRef.current) {
      gameStateRef.current.isGameActive = false;
    }

    // Call fail callback
    if (failCallback) {
      failCallback();
    }
  };

  const handleGameSuccess = () => {
    setIsGameCompleted(true);
    setPopupMessage(`🎉 ${i18n.successMsg}`);
    setShowPopup(true);
    stopTimer();

    // Call success callback
    if (successCallback) {
      successCallback();
    }
  };

  const restartGame = () => {
    // Stop current timer
    stopTimer();

    // Reset all game states
    setTimeLeft(timer);
    setIsGameCompleted(false);
    setShowPopup(false);
    setHasStarted(false);

    // Generate new maze with random seed for variety
    try {
      // const newSeed = Math.floor(Math.random() * 1000000);
      const gamePlayState = generateMaze({
        size,
        seed,
        method,
        foodCount,
      });
      gameStateRef.current = gamePlayState;
      setGameState(gamePlayState);
      // console.log(`🎮 New maze generated with seed: ${newSeed}`);
    } catch (error) {
      console.error('Error regenerating maze:', error);
    }

    // Reset movement states
    resetMovingState();
    resetFirstMoveState();
  };

  const closePopup = () => {
    setShowPopup(false);
    // Restart the game when closing popup
    restartGame();
  };

  // Initialize maze when config changes
  useEffect(() => {
    const initializeMaze = async () => {
      setIsGenerating(true);
      try {
        const gamePlayState = generateMaze({ size, seed, method, foodCount });
        gameStateRef.current = gamePlayState;
        setGameState(gamePlayState);

        // Reset timer and game state
        setTimeLeft(timer);
        setIsGameCompleted(false);
        setShowPopup(false);
        setHasStarted(false);

        // Timer will start when player first moves
      } catch (error) {
        console.error('Error generating single maze:', error);
      } finally {
        setIsGenerating(false);
      }
    };

    initializeMaze();

    // Cleanup timer on unmount or config change
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [config]);

  // Set up smooth controls
  const { updatePlayerPos, resetMovingState, resetFirstMoveState } =
    useSmoothControls({
      containerRef,
      gamePlayStateRef: gameStateRef,
      setGamePlayState: (updater) => {
        if (gameStateRef.current) {
          const newState = updater(gameStateRef.current);
          gameStateRef.current = newState;
          setGameState({ ...newState });
        }
      },
      playerRadius: renderConfig.playerRadius,
      moveSpeed: renderConfig.gridMoveSpeed,
      onFoodCollected: () => {
        // Handle food collection
        console.log('Food collected in single maze');
      },
      onExitReached: () => {
        // Handle exit reached - trigger success
        if (!isGameCompleted) {
          handleGameSuccess();
        }
      },
      onFirstMove: () => {
        // Start timer when player first moves
        startTimer();
      },
    });

  // Calculate canvas size based on container
  useEffect(() => {
    const updateCanvasSize = () => {
      const container = containerRef.current;
      if (!container || !gameState) return;

      const containerWidth = container.clientWidth;
      const maxCanvasSize = Math.min(containerWidth, window.innerHeight * 0.8);
      const cellSize = Math.floor(
        (maxCanvasSize - renderConfig.padding * 2) / size
      );
      const calculatedCanvasSize =
        size * cellSize + config.renderConfig.padding * 2;

      setCanvasSize({
        width: calculatedCanvasSize,
        height: calculatedCanvasSize,
      });
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [size, gameState]);

  // Rendering loop
  useEffect(() => {
    const render = () => {
      const canvas = canvasRef.current;
      const currentGameState = gameStateRef.current;

      if (!canvas || !currentGameState || !currentGameState.maze) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      // Update canvas size if needed
      if (
        canvas.width !== canvasSize.width ||
        canvas.height !== canvasSize.height
      ) {
        canvas.width = canvasSize.width;
        canvas.height = canvasSize.height;
      }

      // Update player position (smooth movement)
      if (currentGameState.isGameActive) {
        updatePlayerPos();
      }

      // Calculate cell size for rendering
      const cellSize = Math.floor(
        (canvasSize.width - config.renderConfig.padding * 2) / size
      );

      // Render maze
      renderMaze(
        ctx,
        currentGameState,
        { canvasWidth: canvasSize.width, canvasHeight: canvasSize.height },
        { cellWidth: cellSize, cellHeight: cellSize },
        {
          ...renderConfig,
          wallWidth: renderConfig.wallWidth,
          exitColor: renderConfig.exitColor,
          exitWallWidth: renderConfig.exitWallWidth,
          exitDashArray: renderConfig.exitDashArray,
        }
      );

      animationFrameRef.current = requestAnimationFrame(render);
    };

    if (gameState) {
      animationFrameRef.current = requestAnimationFrame(render);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [gameState, canvasSize, config]);

  // Reset moving state when config changes
  useEffect(() => {
    resetMovingState();
    resetFirstMoveState();
  }, [config, resetMovingState, resetFirstMoveState]);

  if (isGenerating) {
    return (
      <div className="single-maze-loading">
        <div>Generating maze...</div>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="single-maze-error">
        <div>Failed to generate maze</div>
      </div>
    );
  }

  return (
    <div className="maze-game-container">
      {/* Timer Display */}
      <div className="single-maze-timer">
        <span
          className={`maze-timer-text ${
            timeLeft <= 10 && hasStarted ? 'maze-timer-warning' : ''
          }`}
        >
          ⏱️ {formatTime(timeLeft)}
        </span>
      </div>

      {/* Canvas Container */}
      <div className="maze-canvas-container" ref={containerRef}>
        <canvas
          ref={canvasRef}
          className="maze-game-canvas"
          width={canvasSize.width}
          height={canvasSize.height}
        ></canvas>
        <div className="maze-touch-area maze-up"></div>
        <div className="maze-touch-area maze-down"></div>
        <div className="maze-touch-area maze-left"></div>
        <div className="maze-touch-area maze-right"></div>
      </div>

      <Dialog
        isOpen={showPopup}
        title={popupMessage}
        noCancel={true}
        confirmText={i18n.restart}
        onConfirm={closePopup}
      />
    </div>
  );
}
