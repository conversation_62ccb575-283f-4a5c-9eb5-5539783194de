import { useState, useEffect, useRef } from 'preact/hooks';
import type { SingleGameConfig, GamePlayState } from '../types';
import { generateMaze } from '../utils/maze-generator';
import { renderMaze } from '../utils/maze-renderer';
import { useSmoothControls } from '../hooks/useControls';

interface SingleMazeProps {
  config: SingleGameConfig;
}

export default function SingleMaze({ config }: SingleMazeProps) {
  const { size, seed, method, foodCount, renderConfig, successCallback } =
    config;
  const [gameState, setGameState] = useState<GamePlayState | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 400 });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gameStateRef = useRef<GamePlayState>({} as GamePlayState);
  const animationFrameRef = useRef<number>();

  // Initialize maze when config changes
  useEffect(() => {
    const initializeMaze = async () => {
      setIsGenerating(true);
      try {
        const gamePlayState = generateMaze({ size, seed, method, foodCount });
        gameStateRef.current = gamePlayState;
        setGameState(gamePlayState);
      } catch (error) {
        console.error('Error generating single maze:', error);
      } finally {
        setIsGenerating(false);
      }
    };

    initializeMaze();
  }, [config]);

  // Set up smooth controls
  const { updatePlayerPos, resetMovingState } = useSmoothControls({
    containerRef,
    gamePlayStateRef: gameStateRef,
    setGamePlayState: (updater) => {
      if (gameStateRef.current) {
        const newState = updater(gameStateRef.current);
        gameStateRef.current = newState;
        setGameState({ ...newState });
      }
    },
    playerRadius: renderConfig.playerRadius,
    moveSpeed: renderConfig.gridMoveSpeed,
    onFoodCollected: () => {
      // Handle food collection
      console.log('Food collected in single maze');
    },
    onExitReached: () => {
      // Handle exit reached
      successCallback?.();
    },
  });

  // Calculate canvas size based on container
  useEffect(() => {
    const updateCanvasSize = () => {
      const container = containerRef.current;
      if (!container || !gameState) return;

      const containerWidth = container.clientWidth;
      const maxCanvasSize = Math.min(containerWidth, window.innerHeight * 0.8);
      const cellSize = Math.floor(
        (maxCanvasSize - renderConfig.padding * 2) / size
      );
      const calculatedCanvasSize =
        size * cellSize + config.renderConfig.padding * 2;

      setCanvasSize({
        width: calculatedCanvasSize,
        height: calculatedCanvasSize,
      });
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [size, gameState]);

  // Rendering loop
  useEffect(() => {
    const render = () => {
      const canvas = canvasRef.current;
      const currentGameState = gameStateRef.current;

      if (!canvas || !currentGameState || !currentGameState.maze) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      // Update canvas size if needed
      if (
        canvas.width !== canvasSize.width ||
        canvas.height !== canvasSize.height
      ) {
        canvas.width = canvasSize.width;
        canvas.height = canvasSize.height;
      }

      // Update player position (smooth movement)
      if (currentGameState.isGameActive) {
        updatePlayerPos();
      }

      // Calculate cell size for rendering
      const cellSize = Math.floor(
        (canvasSize.width - config.renderConfig.padding * 2) / size
      );

      // Render maze
      renderMaze(
        ctx,
        currentGameState,
        { canvasWidth: canvasSize.width, canvasHeight: canvasSize.height },
        { cellWidth: cellSize, cellHeight: cellSize },
        {
          ...renderConfig,
          wallWidth: renderConfig.wallWidth,
          exitColor: renderConfig.exitColor,
          exitWallWidth: renderConfig.exitWallWidth,
          exitDashArray: renderConfig.exitDashArray,
        }
      );

      animationFrameRef.current = requestAnimationFrame(render);
    };

    if (gameState) {
      animationFrameRef.current = requestAnimationFrame(render);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [gameState, canvasSize, config]);

  // Reset moving state when config changes
  useEffect(() => {
    resetMovingState();
  }, [config, resetMovingState]);

  if (isGenerating) {
    return (
      <div className="single-maze-loading">
        <div>Generating maze...</div>
      </div>
    );
  }

  if (!gameState) {
    return (
      <div className="single-maze-error">
        <div>Failed to generate maze</div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className="single-maze-container">
      <canvas
        ref={canvasRef}
        className="single-maze-canvas"
        width={canvasSize.width}
        height={canvasSize.height}
      />
    </div>
  );
}
