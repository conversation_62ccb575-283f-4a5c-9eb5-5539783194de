import { useMemo } from 'preact/hooks';
import { useGameContext } from './MazeContext';
import { useLevelConfig } from '../hooks/useLevelConfig';

interface LevelSelectorProps {
  onLevelChange: (level: number) => void;
  disabled: boolean;
}

const LevelSelector = ({ onLevelChange, disabled }: LevelSelectorProps) => {
  const { gameProgress, config } = useGameContext();
  const { i18n, levelConfig } = config;
  const { currentTier, levelScores, currentLevel, highestUnlockedLevel } =
    gameProgress;
  const levelConfigUtils = useLevelConfig(levelConfig);

  const range = useMemo(() => {
    const [start, end] = levelConfigUtils.getTierLevelsRange(currentTier);

    return Array.from({ length: end - start + 1 }, (_, i) => {
      const level = start + i;
      const score = levelScores[level] || 0;
      return {
        level,
        score,
      };
    });
  }, [currentTier, levelScores]);

  return (
    <div className="level-selector">
      <select
        value={currentLevel}
        onChange={(e) => {
          if (e.target && (e.target as HTMLSelectElement).value) {
            onLevelChange(Number((e.target as HTMLSelectElement).value));
          }
        }}
        disabled={disabled}
        className="level-dropdown"
      >
        {range.map(({ level, score }) => (
          <option
            key={`level-${level}`}
            value={level}
            disabled={level > highestUnlockedLevel}
          >
            {`${i18n.level} ${level} ${'★'.repeat(score)}`}
          </option>
        ))}
      </select>
    </div>
  );
};

export default LevelSelector;
