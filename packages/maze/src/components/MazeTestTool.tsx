import { useState, useEffect, useRef } from 'preact/hooks';
import type {
  LevelConfig,
  MazeGenerationMethod,
  GamePlayState,
  GridPos,
} from '../types';
import { Maze } from '../utils/maze-generator';
import { renderMaze } from '../utils/maze-renderer';
import { useSmoothControls } from '../hooks/useControls';

const defaultConfig: LevelConfig = {
  size: 8,
  seed: 12345,
  method: 'backtracking',
  wallWidth: 2,
  exitColor: '#F44336',
  exitWallWidth: 4,
  exitDashArray: [8, 12],
};

const defaultRenderConfig = {
  gridMoveSpeed: 0.03,
  padding: 10,
  wallWidth: 2,
  wallColor: '#000000',
  foodColor: '#4CAF50',
  clueColor: 'rgb(255,0,0)',
  backgroundColor: '#ffffff',
  playerColor: '#4CAF50',
  playerRadius: 0.2,
  exitColor: '#F44336',
  exitWallWidth: 4,
  exitDashArray: [8, 12] as [number, number],
};

export default function MazeTestTool() {
  const [config, setConfig] = useState<LevelConfig>(defaultConfig);
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGenerated, setLastGenerated] = useState<string>('');
  const [hasGameState, setHasGameState] = useState(false);
  const [configText, setConfigText] = useState<string>(
    JSON.stringify({ ...defaultConfig, gridMoveSpeed: 0.03 }, null, 2)
  );
  const [copied, setCopied] = useState(false);
  const [moveSpeedSlider, setMoveSpeedSlider] = useState<number>(3); // Default to 3 (0.03 speed)

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gameStateRef = useRef<GamePlayState>({} as GamePlayState);
  const animationFrameRef = useRef<number>();

  const updateConfig = (
    key: keyof LevelConfig,
    value: string | number | MazeGenerationMethod | [number, number]
  ) => {
    setConfig((prev) => {
      const newConfig = {
        ...prev,
        [key]: value,
      };
      // Update configText when config changes, include gridMoveSpeed
      const configWithSpeed = {
        ...newConfig,
        gridMoveSpeed: moveSpeedSlider / 100,
      };
      setConfigText(JSON.stringify(configWithSpeed, null, 2));
      return newConfig;
    });
  };

  // Update config text when move speed changes
  const updateMoveSpeed = (newSpeed: number) => {
    setMoveSpeedSlider(newSpeed);
    // Update configText to include the new speed
    const configWithSpeed = {
      ...config,
      gridMoveSpeed: newSpeed / 100,
    };
    setConfigText(JSON.stringify(configWithSpeed, null, 2));
  };

  // Handle textarea config changes
  const handleConfigTextChange = (newText: string) => {
    setConfigText(newText);
    try {
      const parsedConfig = JSON.parse(newText);
      // Validate that it has the required properties
      if (parsedConfig && typeof parsedConfig === 'object') {
        // Extract gridMoveSpeed if present and update slider
        if (
          parsedConfig.gridMoveSpeed &&
          typeof parsedConfig.gridMoveSpeed === 'number'
        ) {
          setMoveSpeedSlider(Math.round(parsedConfig.gridMoveSpeed * 100));
        }
        // Remove gridMoveSpeed from config as it's handled separately
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { gridMoveSpeed, ...configWithoutSpeed } = parsedConfig;
        setConfig(configWithoutSpeed);
      }
    } catch (error) {
      // Invalid JSON, don't update config but keep the text
      console.warn('Invalid JSON in config textarea:', error);
    }
  };

  // Copy config to clipboard
  const copyConfig = async () => {
    try {
      await navigator.clipboard.writeText(configText);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 1500);
    } catch (error) {
      console.error('Failed to copy config:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = configText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 1500);
    }
  };

  // Reset config to default
  const resetConfig = () => {
    setConfig(defaultConfig);
    setMoveSpeedSlider(3); // Reset to default speed (0.03)
    const configWithSpeed = {
      ...defaultConfig,
      gridMoveSpeed: 0.03,
    };
    setConfigText(JSON.stringify(configWithSpeed, null, 2));
  };

  // Helper function to generate maze from config
  const generateGamePlayStateFromConfig = (
    config: LevelConfig
  ): GamePlayState => {
    const { size, seed, method, entry, exit } = config;
    const rows = size;
    const cols = size;

    const entryCell = entry || { row: 0, col: 0 };
    const exitCell = exit || { row: rows - 1, col: cols - 1 };
    const maze = new Maze(rows, cols, method, seed, entryCell);

    // Generate some food positions for testing
    const foodCells: GridPos[] = [];
    const foodCount = Math.min(3, Math.floor(size / 3)); // Simple food count

    for (let i = 0; i < foodCount; i++) {
      let foodPos: GridPos;
      let attempts = 0;
      do {
        foodPos = {
          row: Math.floor(Math.random() * rows),
          col: Math.floor(Math.random() * cols),
        };
        attempts++;
      } while (
        attempts < 50 &&
        ((foodPos.row === entryCell.row && foodPos.col === entryCell.col) ||
          (foodPos.row === exitCell.row && foodPos.col === exitCell.col) ||
          foodCells.some(
            (fp) => fp.row === foodPos.row && fp.col === foodPos.col
          ))
      );
      if (attempts < 50) {
        foodCells.push(foodPos);
      }
    }

    return {
      isGameActive: true,
      levelTransitioning: false,
      maze,
      playerCell: entryCell,
      exitCell,
      foodCells,
      playerAtExit: false,
      foodCollected: 0,
      totalFoodCount: foodCells.length,
      showClue: true, // default on when testing
      playerPath: [entryCell],
      visitCounts: { [`${entryCell.row},${entryCell.col}`]: 1 },
    };
  };

  const generateMaze = async () => {
    setIsGenerating(true);

    try {
      // Generate maze
      const gamePlayState = generateGamePlayStateFromConfig(config);

      // Set game state
      gameStateRef.current = gamePlayState;
      setHasGameState(true);

      setLastGenerated(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Error generating maze:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const randomizeSeed = () => {
    updateConfig('seed', Math.floor(Math.random() * 1000000));
  };

  // Create a setGamePlayState function for useSmoothControls
  const setGamePlayState = (
    updater: (prev: GamePlayState) => GamePlayState
  ) => {
    if (gameStateRef.current) {
      const newState = updater(gameStateRef.current);
      gameStateRef.current = newState;
    }
  };

  // Use the same smooth controls as the real game
  const { updatePlayerPos, resetMovingState } = useSmoothControls({
    containerRef,
    gamePlayStateRef: gameStateRef,
    setGamePlayState,
    playerRadius: defaultRenderConfig.playerRadius,
    moveSpeed: moveSpeedSlider / 100, // Convert slider value (1-100) to speed (0.01-1.0)
    exitWallWidth: config.exitWallWidth || defaultRenderConfig.exitWallWidth,
    getCellSizeInPixels: () => {
      // Calculate cell size based on current config and container
      const container = containerRef.current;
      if (!container) return 40; // fallback

      const containerWidth = container.clientWidth - 40;
      const maxCanvasSize = Math.min(containerWidth, window.innerHeight * 0.8);
      const cellSize = Math.floor(
        (maxCanvasSize - defaultRenderConfig.padding * 2) / config.size
      );
      return cellSize;
    },
    onFoodCollected: () => {
      // Optional: handle food collection in test tool
      console.log('Food collected in test mode');
    },
  });

  // Reset moving state when level changes or move speed changes
  useEffect(() => {
    resetMovingState();
  }, [config, moveSpeedSlider, resetMovingState]);

  // Rendering loop
  useEffect(() => {
    const render = () => {
      const canvas = canvasRef.current;
      const currentGameState = gameStateRef.current;

      if (!canvas || !currentGameState) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      // Calculate canvas size based on container width
      const container = canvas.parentElement;
      if (!container) {
        animationFrameRef.current = requestAnimationFrame(render);
        return;
      }

      const containerWidth = container.clientWidth - 40;
      const maxCanvasSize = Math.min(containerWidth, window.innerHeight * 0.8);
      const cellSize = Math.floor(
        (maxCanvasSize - defaultRenderConfig.padding * 2) / config.size
      );
      const canvasSize =
        config.size * cellSize + defaultRenderConfig.padding * 2;

      // Update canvas size if needed
      if (canvas.width !== canvasSize || canvas.height !== canvasSize) {
        canvas.width = canvasSize;
        canvas.height = canvasSize;
      }

      // Update player position (smooth movement)
      if (hasGameState && currentGameState.maze) {
        updatePlayerPos();
      }

      // Render maze
      renderMaze(
        ctx,
        currentGameState,
        { canvasWidth: canvasSize, canvasHeight: canvasSize },
        { cellWidth: cellSize, cellHeight: cellSize },
        {
          ...defaultRenderConfig,
          wallWidth: config.wallWidth || defaultRenderConfig.wallWidth,
          exitColor: config.exitColor || defaultRenderConfig.exitColor,
          exitWallWidth:
            config.exitWallWidth || defaultRenderConfig.exitWallWidth,
          exitDashArray:
            config.exitDashArray || defaultRenderConfig.exitDashArray,
        }
      );

      animationFrameRef.current = requestAnimationFrame(render);
    };

    if (hasGameState) {
      animationFrameRef.current = requestAnimationFrame(render);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [hasGameState, config]);

  // Reset player to starting position
  const resetPlayer = () => {
    const currentGameState = gameStateRef.current;
    if (currentGameState && currentGameState.maze) {
      const entryCell = config.entry || { row: 0, col: 0 };
      currentGameState.playerCell = entryCell;
      gameStateRef.current = currentGameState;
      resetMovingState(); // Reset smooth movement state
    }
  };

  return (
    <div className="maze-test-tool">
      <div className="maze-test-tool__title">🔧 Maze Test Tool (Dev Only)</div>

      <div className="maze-test-tool__content">
        {/* Controls */}
        <div className="maze-test-tool__controls">
          <div className="maze-test-tool__form">
            <label className="maze-test-tool__label">Size:</label>
            <input
              type="number"
              min="3"
              max="50"
              value={config.size}
              onChange={(e) =>
                updateConfig('size', parseInt(e.currentTarget.value) || 8)
              }
              className="maze-test-tool__input"
            />

            <label className="maze-test-tool__label">Seed:</label>
            <div className="maze-test-tool__seed-group">
              <input
                type="number"
                value={config.seed}
                onChange={(e) =>
                  updateConfig('seed', parseInt(e.currentTarget.value) || 12345)
                }
                className="maze-test-tool__input maze-test-tool__seed-input"
              />
              <button
                onClick={randomizeSeed}
                className="maze-test-tool__random-btn"
              >
                🎲
              </button>
            </div>

            <label className="maze-test-tool__label">Method:</label>
            <select
              value={config.method}
              onChange={(e) =>
                updateConfig(
                  'method',
                  e.currentTarget.value as MazeGenerationMethod
                )
              }
              className="maze-test-tool__input"
            >
              <option value="backtracking">Backtracking</option>
              <option value="prims">Prim's Algorithm</option>
            </select>

            <label className="maze-test-tool__label">Wall Width:</label>
            <input
              type="number"
              min="1"
              max="10"
              value={config.wallWidth}
              onChange={(e) =>
                updateConfig('wallWidth', parseInt(e.currentTarget.value) || 2)
              }
              className="maze-test-tool__input"
            />

            <label className="maze-test-tool__label">Move Speed:</label>
            <div className="maze-test-tool__speed-group">
              <input
                type="range"
                min="1"
                max="100"
                value={moveSpeedSlider}
                onChange={(e) =>
                  updateMoveSpeed(parseInt(e.currentTarget.value))
                }
                className="maze-test-tool__speed-slider"
              />
              <span className="maze-test-tool__speed-value">
                {moveSpeedSlider}% ({(moveSpeedSlider / 100).toFixed(2)})
              </span>
            </div>

            <label className="maze-test-tool__label">Exit Color:</label>
            <input
              type="color"
              value={config.exitColor}
              onChange={(e) => updateConfig('exitColor', e.currentTarget.value)}
              className="maze-test-tool__color-input"
            />

            <label className="maze-test-tool__label">Exit Wall Width:</label>
            <input
              type="number"
              min="1"
              max="10"
              value={config.exitWallWidth}
              onChange={(e) =>
                updateConfig(
                  'exitWallWidth',
                  parseInt(e.currentTarget.value) || 4
                )
              }
              className="maze-test-tool__input"
            />

            <label className="maze-test-tool__label">Exit Dash Pattern:</label>
            <div className="maze-test-tool__dash-group">
              <input
                type="number"
                min="1"
                max="20"
                value={config.exitDashArray?.[0] || 8}
                onChange={(e) => {
                  const newValue = parseInt(e.currentTarget.value) || 8;
                  const currentArray = config.exitDashArray || [8, 12];
                  updateConfig('exitDashArray', [newValue, currentArray[1]]);
                }}
                className="maze-test-tool__input maze-test-tool__dash-input"
                placeholder="Dash"
              />
              <span className="maze-test-tool__dash-separator">-</span>
              <input
                type="number"
                min="1"
                max="20"
                value={config.exitDashArray?.[1] || 12}
                onChange={(e) => {
                  const newValue = parseInt(e.currentTarget.value) || 12;
                  const currentArray = config.exitDashArray || [8, 12];
                  updateConfig('exitDashArray', [currentArray[0], newValue]);
                }}
                className="maze-test-tool__input maze-test-tool__dash-input"
                placeholder="Gap"
              />
            </div>
          </div>

          <div className="maze-test-tool__actions">
            <button
              onClick={generateMaze}
              disabled={isGenerating}
              className={`maze-test-tool__generate-btn ${
                isGenerating
                  ? 'maze-test-tool__generate-btn--disabled'
                  : 'maze-test-tool__generate-btn--active'
              }`}
            >
              {isGenerating ? 'Generating...' : '🎯 Generate Maze'}
            </button>

            {hasGameState && (
              <div className="maze-test-tool__game-controls">
                <button
                  onClick={resetPlayer}
                  className="maze-test-tool__reset-btn maze-test-tool__generate-btn--active"
                  disabled={!hasGameState}
                >
                  🔄 Reset Position
                </button>
              </div>
            )}

            {hasGameState && (
              <p className="maze-test-tool__instructions">
                Use arrow keys to move smoothly! Reach the exit (dashed line) to
                win!
              </p>
            )}

            {lastGenerated && (
              <p className="maze-test-tool__last-generated">
                Last generated: {lastGenerated}
              </p>
            )}
          </div>

          <div className="maze-test-tool__config">
            <p className="maze-test-tool__config-title">Level Config JSON:</p>
            <textarea
              className="maze-test-tool__config-textarea"
              value={configText}
              onChange={(e) => handleConfigTextChange(e.currentTarget.value)}
              rows={12}
              placeholder="Edit JSON configuration here..."
            />
            <div className="maze-test-tool__config-buttons">
              <button
                onClick={copyConfig}
                className="maze-test-tool__config-btn maze-test-tool__config-btn--copy"
              >
                {copied ? '✅' : '📋'} Copy
              </button>
              <button
                onClick={resetConfig}
                className="maze-test-tool__config-btn maze-test-tool__config-btn--reset"
              >
                🔄 Reset
              </button>
            </div>
          </div>
        </div>

        {/* Canvas */}
        <div ref={containerRef} className="maze-test-tool__preview">
          <canvas
            ref={canvasRef}
            id="test-maze-canvas"
            className="maze-test-tool__canvas"
            width="400"
            height="400"
          />
          <p className="maze-test-tool__canvas-label">
            {hasGameState
              ? 'Use arrow keys to move smoothly!'
              : 'Click Generate to create a playable maze'}
          </p>
        </div>
      </div>
    </div>
  );
}
