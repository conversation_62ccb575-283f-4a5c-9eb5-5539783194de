import { useGameContext } from './MazeContext';
import { useLevelConfig } from '../hooks/useLevelConfig';

interface TierSelectorProps {
  onTierChange: (tier: number) => void;
  disabled: boolean;
}

const TierSelector = ({ onTierChange, disabled }: TierSelectorProps) => {
  const { gameProgress, config } = useGameContext();
  const { i18n, levelConfig } = config;
  const { currentTier, highestUnlockedTier } = gameProgress;
  const levelConfigUtils = useLevelConfig(levelConfig);
  const totalTiers = levelConfigUtils.getTotalTiers();

  return (
    <div className="tier-selector">
      <select
        value={currentTier}
        onChange={(e) => {
          if (e.target && (e.target as HTMLSelectElement).value) {
            onTierChange(Number((e.target as HTMLSelectElement).value));
          }
        }}
        disabled={disabled}
        className="tier-dropdown"
      >
        {Array.from({ length: totalTiers }, (_, i) => i + 1).map((tier) => (
          <option
            key={`tier-${tier}`}
            value={tier}
            disabled={tier > highestUnlockedTier}
          >
            {i18n.tier + ' ' + tier}
          </option>
        ))}
      </select>
    </div>
  );
};

export default TierSelector;
