import { createContext, ComponentChildren } from 'preact';
import {
  Dispatch,
  StateUpdater,
  useEffect,
  useContext,
  useState,
  useMemo,
  useRef,
  MutableRef,
} from 'preact/hooks';

import { GameProgress, GamePlayState, GameConfig } from '../types';
import { createStorage } from '../utils/storage';

interface GameContextType {
  gameProgress: GameProgress;
  setGameProgress: Dispatch<StateUpdater<GameProgress>>;
  gamePlayState: GamePlayState;
  setGamePlayState: Dispatch<StateUpdater<GamePlayState>>;
  gamePlayStateRef: MutableRef<GamePlayState>;
  currentScore: [number, number, number]; // atExit, allFood, noClue
  config: GameConfig;
  resetGame: () => void;
}

const GameContext = createContext<GameContextType | undefined>(undefined);

interface GameProviderProps {
  children: ComponentChildren;
  config: GameConfig;
}

const initialGameProgress: GameProgress = {
  currentLevel: 1,
  highestUnlockedLevel: 1,
  currentTier: 1,
  highestUnlockedTier: 1,
  levelScores: {},
  totalScore: 0,
};

const initialGamePlayState: GamePlayState = {
  isGameActive: true,
  levelTransitioning: false,
  maze: null,
  playerCell: { row: 0, col: 0 },
  exitCell: { row: 0, col: 0 },
  foodCells: [],
  playerAtExit: false,
  foodCollected: 0,
  totalFoodCount: 0,
  showClue: false,
  playerPath: [],
  visitCounts: {},
};

export const GameProvider = ({ children, config }: GameProviderProps) => {
  const storage = createStorage(config.lcPrefix! + 'progress');
  const savedData = storage.loadGameData();
  const [gameProgress, setGameProgress] = useState<GameProgress>(() => {
    if (savedData) {
      return savedData;
    }
    return { ...initialGameProgress };
  });

  // update totalScore upon leveScores changes
  useEffect(() => {
    const totalScore = Object.values(gameProgress.levelScores).reduce(
      (a, b) => a + b,
      0
    );
    setGameProgress((prev) => ({
      ...prev,
      totalScore,
    }));
  }, [gameProgress.levelScores]);

  // save data anytime when the progress data changed
  useEffect(() => {
    storage.saveGameData(gameProgress);
  }, [
    gameProgress.currentLevel,
    gameProgress.highestUnlockedLevel,
    gameProgress.currentTier,
    gameProgress.highestUnlockedTier,
    gameProgress.levelScores,
    gameProgress.totalScore,
  ]);

  const [gamePlayState, setGamePlayState] = useState<GamePlayState>({
    ...initialGamePlayState,
  });
  const gamePlayStateRef = useRef(gamePlayState);

  useEffect(() => {
    gamePlayStateRef.current = gamePlayState;
  }, [gamePlayState]);

  const currentScore: [number, number, number] = useMemo(() => {
    return [
      gamePlayState.playerAtExit ? 1 : 0,
      gamePlayState.foodCollected === gamePlayState.totalFoodCount ? 1 : 0,
      gamePlayState.showClue ? 0 : 1,
    ];
  }, [
    gamePlayState.playerAtExit,
    gamePlayState.foodCollected,
    gamePlayState.totalFoodCount,
    gamePlayState.showClue,
  ]);

  const resetGame = () => {
    storage.resetGameData();
    setGameProgress({
      ...initialGameProgress,
    });
    setGamePlayState({
      ...initialGamePlayState,
    });
  };

  return (
    <GameContext.Provider
      value={{
        gameProgress,
        setGameProgress,
        gamePlayState,
        setGamePlayState,
        gamePlayStateRef,
        currentScore,
        config,
        resetGame,
      }}
    >
      {children}
    </GameContext.Provider>
  );
};

export const useGameContext = (): GameContextType => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGameContext must be used within a GameProvider');
  }
  return context;
};
