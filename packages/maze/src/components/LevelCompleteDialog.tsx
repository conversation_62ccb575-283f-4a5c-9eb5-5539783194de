import { useEffect, useMemo, useState } from 'preact/hooks';
import J<PERSON><PERSON><PERSON>tti from 'js-confetti';
import Dialog from './Dialog';
import { useGameContext } from './MazeContext';
import { useLevelConfig } from '../hooks/useLevelConfig';
import { NativeToast } from './NativeToast';

const jsConfetti = new JSConfetti();

const MAX_STARS_PER_LEVEL = 3;

const LevelCompleteDialog = ({ onRetry }: { onRetry: () => void }) => {
  const {
    config,
    currentScore,
    gamePlayState,
    setGamePlayState,
    gameProgress,
    setGameProgress,
  } = useGameContext();
  const { i18n, levelConfig } = config;
  const [atExit, allFood, noClue] = currentScore;
  const { foodCollected, totalFoodCount, playerAtExit, levelTransitioning } =
    gamePlayState;
  const { currentLevel } = gameProgress;
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (playerAtExit && levelTransitioning) {
      jsConfetti.addConfetti(); // show confetti each time on level conplete
      NativeToast.show(i18n.levelCompleteToast);
      setIsOpen(true);
    }
  }, [playerAtExit, levelTransitioning]);

  const levelConfigUtils = useLevelConfig(levelConfig);
  const totalLevels = levelConfigUtils.getTotalLevels();
  const isCurrentLevelLast = useMemo(() => {
    return currentLevel >= totalLevels;
  }, [currentLevel, totalLevels]);

  const doRetry = () => {
    setIsOpen(false);
    NativeToast.show(i18n.levelRestartToast);
    onRetry?.();
  };

  const doContinue = () => {
    setIsOpen(false);
    setGamePlayState((prev) => ({
      ...prev,
      isGameActive: true,
      levelTransitioning: false,
    }));
  };

  const doNextLevel = () => {
    setIsOpen(false);
    const nextLevelInfo = levelConfigUtils.getNextLevelInfo(currentLevel);
    if (!nextLevelInfo) return;

    const { nextLevel, nextTier, isTierChange } = nextLevelInfo;

    if (isTierChange) {
      NativeToast.show(
        i18n.tierUnlockedToast.replace('%TIER%', nextTier.toString())
      );
    }

    setGameProgress((prev) => ({
      ...prev,
      currentLevel: nextLevel,
      currentTier: nextTier,
      highestUnlockedLevel: Math.max(prev.highestUnlockedLevel, nextLevel),
      highestUnlockedTier: Math.max(prev.highestUnlockedTier, nextTier),
      levelScores: {
        ...prev.levelScores,
        [prev.currentLevel]: Math.max(
          Object.values(currentScore).filter(Boolean).length,
          prev.levelScores[prev.currentLevel] || 0
        ),
      },
    }));
  };

  const handleLastLevelCompletion = () => {
    setIsOpen(false);
    // update score and check not collected full level
    setGameProgress((prev) => {
      const currLevelScore = Math.max(
        Object.values(currentScore).filter(Boolean).length,
        prev.levelScores[prev.currentLevel] || 0
      );
      const newLevelScore = {
        ...prev.levelScores,
        [prev.currentLevel]: currLevelScore,
      };

      let prevLevel = 0;
      let prevTier = 0;
      const sortedLevelScores = Object.entries(newLevelScore).sort(
        (a, b) => Number(a[0]) - Number(b[0])
      ); // sort by level

      for (const [level, score] of sortedLevelScores) {
        if (score < MAX_STARS_PER_LEVEL) {
          prevLevel = Number(level);
          prevTier = levelConfigUtils.getTierByLevel(prevLevel);
          break;
        }
      }

      if (
        (prevLevel > 0 && prevLevel !== currentLevel) ||
        currLevelScore < MAX_STARS_PER_LEVEL
      ) {
        // prev level not collected full or current last level not collected full
        // show a tip
        NativeToast.show(i18n.notCompleteLevel);
      }

      if (prevLevel === 0 || currLevelScore < MAX_STARS_PER_LEVEL) {
        // all levels collected full, or only current last level not collected full, justy restart the current level
        onRetry?.();
      }

      return {
        ...prev,
        currentLevel: prevLevel || prev.currentLevel,
        currentTier: prevTier || prev.currentTier,
        levelScores: newLevelScore,
      };
    });
  };

  // Add keyboard shortcuts
  useEffect(() => {
    const container = document.querySelector('.maze-game-container');
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === ' ' || e.code === 'Space') {
        // Check if focus is within maze-game-container
        if (
          container &&
          (container.contains(document.activeElement) ||
            document.activeElement === document.body)
        ) {
          e.preventDefault();
          if (isCurrentLevelLast) {
            handleLastLevelCompletion();
          } else {
            doNextLevel();
          }
        }
      }
      if (e.key === 'r' || e.code === 'KeyR') {
        doRetry();
      }
      if (e.key === 'c' || e.code === 'KeyC') {
        doContinue();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, isCurrentLevelLast]);

  return (
    <Dialog
      className="level-complete-dialog"
      isOpen={isOpen}
      title={i18n.congratulations}
      cancelText={i18n.retry + ' (R)'}
      onCancel={doRetry}
      noConfirm={!!allFood}
      confirmText={i18n.continue + ' (C)'}
      onConfirm={doContinue}
      confirmClass="maze-continue-btn"
    >
      <div className="maze-summary-message">
        {!!atExit && <div>{'★ - ' + i18n.findYourExit}</div>}
        <div>{noClue ? '★ - ' + i18n.withoutClue : '☆ - ' + i18n.withClue}</div>
        <div>
          {(allFood
            ? '★ - ' + i18n.collectAllStars
            : '☆ - ' + i18n.notCollectAllStars) +
            ' ' +
            `(${foodCollected}/${totalFoodCount})`}
        </div>
      </div>
      {isCurrentLevelLast && (
        <div className="all-levels-completed">{i18n.allLevelsCompleted}</div>
      )}
      <div className="maze-go-next">
        {isCurrentLevelLast ? (
          <button
            className="maze-go-next-btn"
            type="button"
            onClick={handleLastLevelCompletion}
          >
            <span>{i18n.lastOK}</span>
            <span className="spacebar-tip">{`(${i18n.spacebarTip})`}</span>
          </button>
        ) : (
          <button
            className="maze-go-next-btn"
            type="button"
            onClick={doNextLevel}
          >
            <span>{i18n.nextLevel}</span>
            <span className="spacebar-tip">{`(${i18n.spacebarTip})`}</span>
          </button>
        )}
      </div>
      {!allFood && (
        <div className="not-all-stars-collected">{i18n.continueMessage}</div>
      )}
    </Dialog>
  );
};

export default LevelCompleteDialog;
