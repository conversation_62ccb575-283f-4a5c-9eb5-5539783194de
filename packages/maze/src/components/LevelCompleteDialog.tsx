import { useEffect, useMemo, useState } from 'preact/hooks';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'js-confetti';
import Dialog from './Dialog';
import { useGameContext } from './MazeContext';
import { useLevelConfig } from '../hooks/useLevelConfig';
import { NativeToast } from './NativeToast';

const jsConfetti = new JSConfetti();

const LevelCompleteDialog = ({ onRetry }: { onRetry: () => void }) => {
  const {
    config,
    currentScore,
    gamePlayState,
    setGamePlayState,
    gameProgress,
    setGameProgress,
  } = useGameContext();
  const { i18n, levelConfig } = config;
  const [atExit, allFood, noClue] = currentScore;
  const { foodCollected, totalFoodCount, playerAtExit, levelTransitioning } =
    gamePlayState;
  const { currentLevel } = gameProgress;
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (playerAtExit && levelTransitioning) {
      jsConfetti.addConfetti(); // show confetti each time on level conplete
      NativeToast.show(i18n.levelCompleteToast, 'success');
      setIsOpen(true);
    }
  }, [playerAtExit, levelTransitioning]);

  const levelConfigUtils = useLevelConfig(levelConfig);
  const totalLevels = levelConfigUtils.getTotalLevels();
  const isCurrentLevelLast = useMemo(() => {
    return currentLevel >= totalLevels;
  }, [currentLevel, totalLevels]);

  const doRetry = () => {
    setIsOpen(false);
    NativeToast.show(i18n.levelRestartToast, 'info');
    onRetry?.();
  };

  const doContinue = () => {
    setIsOpen(false);
    setGamePlayState((prev) => ({
      ...prev,
      isGameActive: true,
      levelTransitioning: false,
    }));
  };

  const doNextLevel = () => {
    setIsOpen(false);
    const nextLevelInfo = levelConfigUtils.getNextLevelInfo(currentLevel);
    if (!nextLevelInfo) return;

    const { nextLevel, nextTier, isTierChange } = nextLevelInfo;

    if (isTierChange) {
      NativeToast.show(
        i18n.tierUnlockedToast.replace('%TIER%', nextTier.toString()),
        'success'
      );
    }

    setGameProgress((prev) => ({
      ...prev,
      currentLevel: nextLevel,
      currentTier: nextTier,
      highestUnlockedLevel: Math.max(prev.highestUnlockedLevel, nextLevel),
      highestUnlockedTier: Math.max(prev.highestUnlockedTier, nextTier),
      levelScores: {
        ...prev.levelScores,
        [prev.currentLevel]: Math.max(
          Object.values(currentScore).filter(Boolean).length,
          prev.levelScores[prev.currentLevel] || 0
        ),
      },
    }));
  };

  return (
    <Dialog
      className="level-complete-dialog"
      isOpen={isOpen}
      title={i18n.congratulations}
      cancelText={i18n.retry}
      onCancel={doRetry}
      noConfirm={!!allFood}
      confirmText={i18n.continue}
      onConfirm={doContinue}
    >
      <div className="maze-summary-message">
        {!!atExit && <div>{'★ - ' + i18n.findYourExit}</div>}
        <div>{noClue ? '★ - ' + i18n.withoutClue : '☆ - ' + i18n.withClue}</div>
        <div>
          {(allFood
            ? '★ - ' + i18n.collectAllStars
            : '☆ - ' + i18n.notCollectAllStars) +
            ' ' +
            `(${foodCollected}/${totalFoodCount})`}
        </div>
      </div>
      {isCurrentLevelLast && (
        <div className="all-levels-completed">{i18n.allLevelsCompleted}</div>
      )}
      <div className="go-next-level">
        {isCurrentLevelLast ? (
          'OK'
        ) : (
          <button type="button" onClick={doNextLevel}>
            {i18n.nextLevel}
          </button>
        )}
      </div>
      {!allFood && (
        <div className="not-all-stars-collected">{i18n.continueMessage}</div>
      )}
    </Dialog>
  );
};

export default LevelCompleteDialog;
