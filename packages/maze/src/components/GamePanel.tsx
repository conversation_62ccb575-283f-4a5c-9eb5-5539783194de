import { useState } from 'preact/hooks';
import { useGameContext } from './MazeContext';
import TierSelector from './TierSelector';
import LevelSelector from './LevelSelector';
import Dialog from './Dialog';
import { NativeToast } from './NativeToast';
import { useLevelConfig } from '../hooks/useLevelConfig';

const GamePanel = () => {
  const {
    gameProgress,
    setGameProgress,
    gamePlayState,
    setGamePlayState,
    gamePlayStateRef,
    resetGame,
    config,
  } = useGameContext();
  const { i18n, levelConfig } = config;
  const levelConfigUtils = useLevelConfig(levelConfig);
  const { levelTransitioning, showClue } = gamePlayState;
  const { highestUnlockedLevel, totalScore } = gameProgress;
  const totalLevels = levelConfigUtils.getTotalLevels();

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isConfirmResetDialogOpen, setIsConfirmResetDialogOpen] =
    useState(false);
  const [isConfirmClueDialogOpen, setIsConfirmClueResetDialogOpen] =
    useState(false);

  const handleTierChange = (tier: number) => {
    const firstLevelInTier = levelConfigUtils.getFirstLevelOfTier(tier);
    if (
      levelConfigUtils.isLevelUnlocked(firstLevelInTier, highestUnlockedLevel)
    ) {
      setGameProgress((prev) => ({ ...prev, currentTier: tier }));
      handleLevelChange(firstLevelInTier);
    }
  };

  const handleLevelChange = (level: number) => {
    if (levelConfigUtils.isLevelUnlocked(level, highestUnlockedLevel)) {
      setGameProgress((prev) => ({ ...prev, currentLevel: level }));
      NativeToast.show(i18n.levelGoToast.replace('%LEVEL%', level.toString()));
    }
  };

  // Settings
  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
  };

  const handleSettingsClose = () => {
    setIsSettingsOpen(false);
  };
  // Clue
  const handleClueClick = () => {
    setIsConfirmClueResetDialogOpen(true);
  };

  const handleCancelClue = () => {
    setIsConfirmClueResetDialogOpen(false);
  };

  const handleConfirmClue = () => {
    NativeToast.show(showClue ? i18n.cluePathDisabled : i18n.cluePathEnabled);
    setGamePlayState(() => ({
      ...gamePlayStateRef.current, // need to sync with ref
      showClue: !gamePlayStateRef.current.showClue,
    }));
    setIsConfirmClueResetDialogOpen(false);
  };

  // Reset
  const handleResetClick = () => {
    setIsConfirmResetDialogOpen(true);
  };

  const handleConfirmReset = () => {
    resetGame();
    setIsConfirmResetDialogOpen(false);
    NativeToast.show(i18n.resetDoneToast);
  };

  const handleCancelReset = () => {
    setIsConfirmResetDialogOpen(false);
  };

  return (
    <div class="game-panel-container">
      <div className="game-controls">
        {/* Tier and Level Selectors */}
        <div className="selectors-container">
          <TierSelector
            onTierChange={handleTierChange}
            disabled={levelTransitioning}
          />

          <LevelSelector
            onLevelChange={handleLevelChange}
            disabled={levelTransitioning}
          />
        </div>
        {/* Clue Button */}
        <button
          className={`settings-clue-button ${
            showClue ? 'clue-on' : 'clue-off'
          }`}
          disabled={showClue}
          title="Toggle Clue"
          onClick={handleClueClick}
        >
          {showClue ? i18n.clueOn : i18n.clueOff}
        </button>
      </div>
      {/* Show Total Score, click to open Settings */}
      <div className="game-settings">
        <span className="game-settings-icon" onClick={handleSettingsClick}>
          {'★ ' + totalScore}
        </span>
      </div>
      {/* Settings Dialog */}
      <Dialog
        isOpen={isSettingsOpen}
        title={i18n.settings}
        noConfirm={true}
        cancelText={i18n.close}
        onCancel={handleSettingsClose}
      >
        {/* Show Stats */}
        <div className="settings-stats">
          <div>
            {i18n.levelComplete +
              ': ' +
              `${highestUnlockedLevel}/${totalLevels}`}
          </div>
          <div>{i18n.starsCollected + ': ' + totalScore}</div>
          <div>
            {i18n.avgStarPerLevel +
              ': ' +
              (totalScore / highestUnlockedLevel).toFixed(2)}
          </div>
        </div>
        <div className="settings-item-box">
          <button
            className="settings-reset-button"
            title="Reset Game"
            onClick={handleResetClick}
          >
            {i18n.reset}
          </button>
          <div className="settings-item-tip">{i18n.resetTip}</div>
        </div>
      </Dialog>
      {/* Confirm Reset Dialog */}
      <Dialog
        isOpen={isConfirmResetDialogOpen}
        title={i18n.confirmReset}
        message={i18n.confirmResetMessage}
        confirmText={i18n.reset}
        cancelText={i18n.cancel}
        onConfirm={handleConfirmReset}
        onCancel={handleCancelReset}
      />
      {/* Confirm Enable Clue Dialog */}
      <Dialog
        isOpen={isConfirmClueDialogOpen}
        title={i18n.confirmClue}
        message={i18n.confirmClueMessage}
        confirmText={i18n.confirm}
        cancelText={i18n.cancel}
        onConfirm={handleConfirmClue}
        onCancel={handleCancelClue}
      />
    </div>
  );
};

export default GamePanel;
