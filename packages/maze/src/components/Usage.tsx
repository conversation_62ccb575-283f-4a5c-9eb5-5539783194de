import { useState } from 'preact/hooks';
import { defaultConfig } from '../utils/utils';

const usageConfig = {
  root: `https://unpkg.com/${__PACKAGE_NAME__}@${__VERSION__}/dist/`,
  ...defaultConfig,
};
const scriptText = `<script src="https://unpkg.com/${__PACKAGE_NAME__}@${__VERSION__}/dist/maze.js"></script>`;
const initText = `Maze.init(
  document.querySelector('#maze-container'),  // DOM element or DOM selector
  ${JSON.stringify(usageConfig, null, 2)}
);`;

const singleMazeConfig = {
  size: 10,
  seed: 54321,
  method: 'prims',
  wallWidth: 3,
  exitColor: '#FF5722',
  exitWallWidth: 6,
  exitDashArray: [4, 8],
  gridMoveSpeed: 0.05,
  onComplete: () => {
    console.log('Maze completed!');
    alert('🎉 Congratulations!');
  },
  onFoodCollected: () => {
    console.log('Food collected!');
  },
};

const singleInitText = `Maze.initSingle(
  document.querySelector('#single-maze-container'),  // DOM element or DOM selector
  ${JSON.stringify(singleMazeConfig, null, 2)}
);`;

export default function Usage() {
  const [isCopiedScript, setIsCopiedScript] = useState(false);
  const [isCopiedInit, setIsCopiedInit] = useState(false);
  const [isCopiedSingle, setIsCopiedSingle] = useState(false);

  const handleCopy = (text: string, setIsCopied: (value: boolean) => void) => {
    navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => {
      setIsCopied(false);
    }, 1e3);
  };

  return (
    <div className="maze-usage">
      <p>
        First, embed the script. It will load the latest version of the module.
      </p>
      <pre style={{ position: 'relative' }}>
        <code>{scriptText}</code>
        <button
          type="button"
          className="copy-btn"
          onClick={() => handleCopy(scriptText, setIsCopiedScript)}
        >
          {isCopiedScript ? '✅ Copied!' : 'Copy'}
        </button>
      </pre>
      <p>
        Then, initialize the package with specified container element, and
        config options.
      </p>
      <details>
        <summary>Click to expand the code (Full Game)</summary>
        <pre style={{ position: 'relative' }}>
          <code>{initText}</code>
          <button
            type="button"
            className="copy-btn"
            onClick={() => handleCopy(initText, setIsCopiedInit)}
          >
            {isCopiedInit ? '✅ Copied!' : 'Copy'}
          </button>
        </pre>
      </details>

      <p>
        <strong>Alternative:</strong> For a single standalone maze, use{' '}
        <code>initSingle</code>:
      </p>
      <details>
        <summary>Click to expand the code (Single Maze)</summary>
        <pre style={{ position: 'relative' }}>
          <code>{singleInitText}</code>
          <button
            type="button"
            className="copy-btn"
            onClick={() => handleCopy(singleInitText, setIsCopiedSingle)}
          >
            {isCopiedSingle ? '✅ Copied!' : 'Copy'}
          </button>
        </pre>
      </details>
    </div>
  );
}
