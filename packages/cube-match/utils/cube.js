import { AssetCache } from "./asset-cache";
import CubeFace from "./cube-face";
import * as THREE from "three";

/**
 * Generates a game cube with specified dimensions and axis orientation.
 *
 * @param {number|array} pos - The position of the cube, can be a single number for uniform positioning or an array of three numbers for x, y, and z coordinates.
 * @param {number|array} dims - The dimensions of the cube, can be a single number for uniform dimensions or an array of three numbers for x, y, and z dimensions.
 * @param {string|number} [axis="px"] - The axis orientation of the cube, can be a string ("px", "nx", "py", "ny", "pz", "nz") or a number (0-5).
 * @param {string} [bgColor="#FFE800"] - The background color of the cube.
 * @return {THREE.Mesh} The generated game cube.
 */
export function makeCube(pos, dims, axis = 'px', bgColor = '#FFE800') {
  // Normalize input parameters
  if (typeof pos === 'number') pos = [pos, pos, pos];
  if (typeof dims === 'number') dims = [dims, dims, dims];

  // Generate background texture if not already generated
  if (!AssetCache.get(`cnvPixel${bgColor}`)) {
    const cnv = document.createElement('canvas');
    cnv.width = 250;
    cnv.height = 250;
    const ctx = cnv.getContext('2d');
    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, 250, 250);
    ctx.fillStyle = bgColor;
    ctx.fillRect(2, 2, 250 - 4, 250 - 4);
    AssetCache.set(`cnvPixel${bgColor}`, cnv);
  }

  const bgTexture = new THREE.CanvasTexture(
    AssetCache.get(`cnvPixel${bgColor}`)
  );

  // Normalize axis parameter
  if (typeof axis !== 'number') {
    const axes = ['px', 'nx', 'py', 'ny', 'pz', 'nz'];
    axis = axes.indexOf(axis);
  }

  // Define cube orientations
  const orientations = [
    [-1, -1, 0, 0, 0, 2],
    [-1, -1, 2, 2, 2, 0],
    //
    [3, 3, -1, -1, 3, 3],
    [1, 1, -1, -1, 1, 1],
    //
    [2, 0, 1, 3, -1, -1],
    [0, 2, 3, 1, -1, -1],
  ];

  // Generate cube geometry if not already generated
  const geoKey = `cube${dims[0]}${dims[1]}${dims[2]}`;
  const cachedGeometry = AssetCache.get(geoKey);

  let geometry;
  if (cachedGeometry) {
    geometry = cachedGeometry;
  } else {
    geometry = new THREE.BoxGeometry(...dims);
    AssetCache.set(geoKey, geometry);
  }

  // Generate cube face icons
  const iconTexes = [[], [], [], [], [], []];

  // Generate cube material if not already generated
  const matkey = bgColor + geoKey + axis;
  const cacheMat = AssetCache.get(matkey);

  const textureLoader = new THREE.TextureLoader();
  let material = new Array(6).fill('');
  material[axis - (axis % 2)] = bgColor;
  material[axis - (axis % 2) + 1] = bgColor;

  if (!cacheMat) {
    for (let i = 0; i < orientations.length; i++) {
      for (let j = 0; j < orientations[i].length; j++) {
        const r = orientations[i][j];

        /**
         * Calculate Res
         */
        const face = Math.floor(j / 2);
        const ab = dims.slice();
        ab.splice(face, 1);
        const [a, b] = ab;
        const resFactor = a > b ? b / a : a / b;
        const flip = a > b ? 0 : 1;
        let flipFactors = [0, 1, -1];
        const order = (a, b, c) => dims[a] >= dims[b] && dims[b] >= dims[c];
        if (order(2, 1, 0)) flipFactors = [0, -1, -1];
        if (order(0, 2, 1)) flipFactors = [1, 1, 1];
        if (order(0, 1, 2)) flipFactors = [0, 1, 1];
        if (order(2, 0, 1)) flipFactors = [0, -1, 1];
        if (order(1, 2, 0)) flipFactors = [0, -1, -1];

        const res = [512, 512];
        res[Math.ceil((flip + flipFactors[Math.floor(j / 2)]) / 2)] = resFactor;

        const img = new CubeFace('arrowR', res)
          .setParams((self) => {
            self.rotation = r * 90;
            self.padding = 50;
            self.bgColor = bgColor;

            self.resolution = res;
          })
          .refreshImage().canvas;

        iconTexes[i].push(img);
      }
    }

    /** @type {THREE.MeshStandardMaterialParameters} */
    const commonProps = {
      metalness: 0.9,
      roughness: 0.6,
    };

    material = material.map((el, i) => {
      if (!el.startsWith('#')) {
        return new THREE.MeshStandardMaterial({
          map: textureLoader.load(iconTexes[axis][i].toDataURL()),
          ...commonProps,
        });
      } else {
        return new THREE.MeshStandardMaterial({
          map: bgTexture,
          ...commonProps,
        });
      }
    });

    AssetCache.set(matkey, material);
  } else {
    material = cacheMat;
  }

  const cube = new THREE.Mesh(geometry, material);
  cube.userData = {
    axis: ['x', 'y', 'z'][Math.floor(axis / 2)],
    direction: [1, -1][axis % 2],
    pos,
    dims,
  };
  cube.position.set(...pos.map((el, i) => el + dims[i] / 2));

  return cube;
}

/**
 * Checks if a new cube collides with any of the existing cubes in the given array.
 *
 * @param {Object} newCube - The new cube to check for collision, with properties 'pos' and 'dims'.
 * @param {Array<Object>} cubeArray - An array of existing cubes, each with properties 'pos' and 'dims'.
 * @return {Boolean} True if the new cube collides with any of the existing cubes, false otherwise.
 */
export function isCollision (newCube, cubeArray) {
  const [newCubePos, newCubeDims] = Object.values(newCube);
  for (const cube of cubeArray) {
    const [cubePos, cubeDims] = Object.values(cube);

    // Calculate the boundaries of the new cube
    const newCubeXMin = newCubePos[0];
    const newCubeXMax = newCubePos[0] + newCubeDims[0];
    const newCubeYMin = newCubePos[1];
    const newCubeYMax = newCubePos[1] + newCubeDims[1];
    const newCubeZMin = newCubePos[2];
    const newCubeZMax = newCubePos[2] + newCubeDims[2];

    // Calculate the boundaries of the existing cube
    const cubeXMin = cubePos[0];
    const cubeXMax = cubePos[0] + cubeDims[0];
    const cubeYMin = cubePos[1];
    const cubeYMax = cubePos[1] + cubeDims[1];
    const cubeZMin = cubePos[2];
    const cubeZMax = cubePos[2] + cubeDims[2];

    // Check for overlap in all three dimensions
    if (
      newCubeXMin < cubeXMax &&
      newCubeXMax > cubeXMin &&
      newCubeYMin < cubeYMax &&
      newCubeYMax > cubeYMin &&
      newCubeZMin < cubeZMax &&
      newCubeZMax > cubeZMin
    ) {
      // Collision detected
      return true;
    }
  }

  // No collision detected
  return false;
}

const randomCube = (ac) => {
  const randomIndex = Math.floor(Math.random() * ac.length);
  return ac[randomIndex];
};

const create3DMatrix = (i, j, k) => {
  const matrix = [];
  for (let x = 0; x < i; x++) {
    matrix[x] = [];
    for (let y = 0; y < j; y++) {
      matrix[x][y] = [];
      for (let z = 0; z < k; z++) {
        matrix[x][y][z] = 0;
      }
    }
  }
  return matrix;
};

/**
 * Fills a 3D cuboid with smaller cubes of varying dimensions.
 *
 * @param {Array<Array<number>>} smallerCubes - A list of smaller cube dimensions.
 * @param {Array<number>} largerCuboid - The dimensions of the larger cuboid.
 * @return {Array<Object>} A list of cubes that fill the larger cuboid.
 */
export function fillCuboid (smallerCubes, largerCuboid) {
  const cubes = [];

  const auxMatrix = create3DMatrix(...largerCuboid);

  const fillCube = (pos, dims) => {
    const [x, y, z] = pos;
    const [X, Y, Z] = dims;
    for (let i = x; i < X; i++) {
      for (let j = y; j < Y; j++) {
        for (let k = z; k < Z; k++) {
          if (auxMatrix[i][j][k]) console.log('collision');
          auxMatrix[i][j][k] = 1;
        }
      }
    }
  };

  const getZSpace = (x, y, z) => {
    let space = 0;
    for (let k = z; k < largerCuboid[2]; k++) {
      if (auxMatrix[x][y][k] === 0) space += 1;
      else break;
    }
    return space;
  };

  const getYSpace = (x, y, z) => {
    let space = 0;
    for (let k = y; k < largerCuboid[1]; k++) {
      if (auxMatrix[x][k][z] === 0) space += 1;
      else break;
    }
    return space;
  };

  const getXSpace = (x, y, z) => {
    let space = 0;
    for (let k = x; k < largerCuboid[0]; k++) {
      if (auxMatrix[k][y][z] === 0) space += 1;
      else break;
    }
    return space;
  };

  for (let i = 0; i < largerCuboid[0]; i++) {
    for (let j = 0; j < largerCuboid[1]; j++) {
      for (let k = 0; k < largerCuboid[2]; k++) {
        if (auxMatrix[i][j][k] === 1) continue;

        const xSpace = getXSpace(i, j, k);
        const ySpace = getYSpace(i, j, k);
        const zSpace = getZSpace(i, j, k);
        const availableCubes = smallerCubes.filter(
          (el) => el[0] < xSpace && el[1] < ySpace && el[2] < zSpace
        );
        if (availableCubes.length === 0) availableCubes.push([1, 1, 1]);

        let selectedCube = {
          pos: [i, j, k],
          dims: randomCube(availableCubes),
        };

        if (isCollision(selectedCube, cubes)) selectedCube.dims = [1, 1, 1];
        if (isCollision(selectedCube, cubes)) continue;

        fillCube([i, j, k], selectedCube.dims);
        cubes.push(selectedCube);
      }
    }
  }

  return cubes;
}
