/**
 * Saving resources globally
 * @class
 */
class AssetCacheManager {
  constructor() {
    this._cache = {};
  }

  /**
   * @param {string} key
   */
  get(key) {
    return this._cache[key];
  }

  /**
   * @param {string} key
   * @param {*} value
   */
  set(key, value) {
    this._cache[key] = value;
  }

  clearCache() {
    this._cache = {};
  }
}

export const AssetCache = new AssetCacheManager();
