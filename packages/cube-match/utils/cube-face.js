import { AssetCache } from "./asset-cache";

/**
 * Represents an Image object with various operations.
 * @class
 */
export default class CubeFace {
  /**
   * Create a new ImageProcessor object.
   * @constructor
   * @param {string} name - The name of the image.
   * @param {number} resolution - The default resolution of the image.
   */
  constructor(name, resolution = [512, 512]) {
    this._cache = null;
    this._padding = [0, 0];
    this._rotation = 0;
    this._bgColor = "white";

    if (typeof resolution === "number") resolution = [resolution, resolution];
    if (resolution[1] <= 1) resolution[1] = resolution[0] * resolution[1];
    if (resolution[0] <= 1) resolution[0] = resolution[0] * resolution[1];
    this._resolution = resolution;

    this.canvas = document.createElement("canvas");
    this.canvas.width = this._resolution[0];
    this.canvas.height = this._resolution[1];
    this.context = this.canvas.getContext("2d");
    this.image = AssetCache.get(name);
  }

  setParams(callback) {
    callback(this);
    return this;
  }

  /**
   * @param {number | [number,number]} arg
   */
  set padding(arg) {
    if (typeof arg === "number") arg = [arg, arg];
    this._padding = arg;
    this._cache = null;
  }

  /**
   * You can also write an aspect ratio instead of a dimension
   * @param {number | [number,number]} arg
   */
  set resolution(arg) {
    if (typeof arg === "number") arg = [arg, arg];
    if (arg[1] <= 1) arg[1] = arg[0] * arg[1];
    if (arg[0] <= 1) arg[0] = arg[0] * arg[1];
    this._resolution = arg;
    this.canvas.width = arg[0];
    this.canvas.height = arg[1];
    this._cache = null;
  }

  /**
   * @param {string | null} color
   */
  set bgColor(color) {
    this._bgColor = color;
    this._cache = null;
  }

  /**
   * @param {number} deg
   */
  set rotation(deg) {
    this._rotation = (deg * Math.PI) / 180;
    this._cache = null;
  }

  refreshImage(borderSize = 5) {
    /** Draw Background */
    this.context.fillStyle = "black";
    this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);
    this.context.clearRect(
      borderSize,
      borderSize,
      this.canvas.width - 2 * borderSize,
      this.canvas.height - 2 * borderSize
    );
    if (this._bgColor) {
      this.context.fillStyle = this._bgColor;
      this.context.fillRect(
        borderSize,
        borderSize,
        this.canvas.width - 2 * borderSize,
        this.canvas.height - 2 * borderSize
      );
    }

    /** Calculate Positions and Dimensions */
    const dims = [
      this.canvas.width - this._padding[0],
      this.canvas.height - this._padding[1],
    ];

    const pos = [
      (this._padding[0] - this.canvas.width) / 2,
      (this._padding[1] - this.canvas.height) / 2,
    ];

    if (dims[0] > dims[1]) {
      pos[0] += (dims[0] - dims[1]) / 2;
      dims[0] = dims[1];
    }

    if (dims[1] > dims[0]) {
      pos[1] += (dims[1] - dims[0]) / 2;
      dims[1] = dims[0];
    }

    /**
     * Draw Rotated and Padded Image
     */
    this.context.save();
    this.context.translate(this.canvas.width / 2, this.canvas.height / 2);
    this.context.rotate(this._rotation);
    this.context.drawImage(this.image, ...pos, ...dims);
    this.context.restore();

    return this;
  }

  /**
   * Get the image data in WebP format.
   * @returns {Promise<string>} - A promise that resolves with the WebP data URL.
   */
  getWebP() {
    return new Promise((resolve) => {
      if (this._cache) {
        resolve(this._cache);
        return;
      }

      this.refreshImage();

      this.canvas.toBlob((blob) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          this._cache = reader.result;
          resolve(reader.result);
        };
        reader.readAsDataURL(blob);
      }, "image/webp");
    });
  }
}
