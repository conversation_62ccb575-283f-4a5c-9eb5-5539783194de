/**
 * Local Store Utility
 *
 * A utility that boilerplates some of the functionality of working
 * with localstorage for all JSON serializable objects
 *
 * @template T
 * @param {string} name The key to save our value by
 * @param {T} defaultValue the default to the value
 * @returns {[() => T, (val: T) => void, () => void]} A getter, A setter and a remover
 *
 * Use it like so,
 *
 * const [ getTheme, setTheme, clearTheme ] = localStore("theme", "dark");
 *
 * setTheme("light")
 *
 * getTheme()
 *
 * clearTheme()
 */
export const localStore = (name, defaultValue) => {
  /**
   * Serializes and saves the given value to localstorage
   * @param {T} value The value to save
   */
  const setter = (value) => {
    localStorage.setItem(name, JSON.stringify(value));
  };

  /**
   * Parses and returns the saved value,
   * saves and returns the default value if no value is set
   * @returns {T} the parsed saved or default value
   */
  const getter = () => {
    const item = localStorage.getItem(name);
    if (item === null) {
      setter(defaultValue);
      return defaultValue;
    }
    return JSON.parse(item);
  };

  /**
   * Removes the item from localstorage
   */
  const remover = () => {
    localStorage.removeItem(name);
  };

  return [getter, setter, remover];
};
