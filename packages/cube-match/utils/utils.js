import { AssetCache } from './asset-cache';

/**
 * @param {number} ms
 * @returns
 */
export const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const playSound = (key) => {
  const audio = new Audio(AssetCache.get(key));
  audio.play();
};

export const lerp = (a, b, t) => {
  if (typeof a !== typeof b)
    throw new Error("Can't interpolate different types");
  if (typeof a === 'number') return a * (1 - t) + b * t;

  if (Array.isArray(a)) {
    if (a.length !== b.length)
      throw new Error("Can't interpolate arrays with different dimensions");

    const lerped = [];
    for (let i = 0; i < a.length; i++) {
      const lerpResult = lerp(a[i], b[i], t);
      lerped.push(lerpResult);
    }

    return lerped;
  }

  throw new Error('Unsupported type for interpolation');
};

/**
 * @param {Function} func
 * @param {number} delay
 * @returns {Function} debounced version of the function
 */
export const debounce = (func, delay) => {
  let lastClickTime = 0;

  return function (...args) {
    const currentTime = Date.now();
    const elapsedTime = currentTime - lastClickTime;

    if (elapsedTime >= delay) func.apply(this, args);

    lastClickTime = currentTime;
  };
};

/**
 * Tells if a and b are close enough with a given sensitivity
 * @param {number} a
 * @param {number} b
 * @param {number} sensitivity
 * @returns {boolean}
 */
export const isCloseEnough = (a, b, sensitivity) => {
  return Math.abs(a - b) < sensitivity;
};

export function httpBuildQuery(obj) {
  const qs = new URLSearchParams(obj);
  return qs.toString();
}

export function formatTime(time, exact = true) {
  const ms = time % 1000;
  let sec = Math.floor(time / 1000);
  const min = Math.floor(sec / 60);
  sec = sec % 60;
  return `${min}'${String(sec).padStart(2, '0')}` + (exact ? `.${String(ms).padStart(3,'0')}` : ``);
}

export function formatScore(score) {
  if (score || score === 0) {
    return score.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  return '0';
}
