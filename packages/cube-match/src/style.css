/* @tailwind base; */
@tailwind components;
@tailwind utilities;

.cube-match-game-container {
  min-height: 550px;
  background-color: #4790FF;
  font-family: Montser<PERSON>, sans-serif;
  font-optical-sizing: auto;
  font-style: normal;

  svg {
    direction: inherit;
  }

  .luckiest-guy-font {
    font-family: "Luckiest Guy", cursive;
    font-weight: 400;
    font-style: normal;
  }

  *, ::before, ::after {
    box-sizing: border-box;
    /* border-width: 0;
    border-style: solid;
    border-color: #e5e7eb; */
  }
  button, [role="button"] {
    cursor: pointer;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
  }

  button, input:where([type='button']), input:where([type='reset']), input:where([type='submit']) {
    appearance: button;
    -webkit-appearance: button;
  }

  table {
    text-indent: 0;
    border-collapse: collapse;
    border-color: rgba(119,238,221, 0.2);
  }

  table * {
    border-style: solid;
    border-color: inherit;
    border-width: 0;
  }

  tbody tr {
    border-bottom-width: 1px;
  }

  .game-title-lower {
    font-family: Luckiest Guy, cursive;
    color: white;
    -webkit-text-stroke: 12px black;
  }

  .game-title-upper {
    font-family: Luckiest <PERSON>, cursive;
    color: white;
    -webkit-text-stroke: 4px black;
  }

  .play-icon path {
    fill: #188038;
    transition: ease-in-out .2s all;
    stroke: #188038;
    stroke-width: 12px;
  }

  .play-icon:hover path {
    fill: #fff;
  }

  .color-dark-green {
    color: #060;
  }

  .score-badge {
    background-color: #245db4;
  }

  .start-button {
    box-shadow: 0 7px 0 0 rgba(32, 68, 123, 0.3), inset 0 -7px 3px 1px #eeb60a;
  }

  .level-button {
    @apply px-1 sm:px-3 w-10 h-10 border-2 border-black border-solid disabled:border-none disabled:bg-white disabled:opacity-100;
    background-color: #00ffd9;
    font-family: Montserrat, sans-serif;
    font-weight: bold;
    font-size: 16px;

    &:hover {
      background-color: #05dcbb;
    }

    &.active {
      background-color: #ffe800;

      &:hover {
        background-color: #dac701;
      }
    }
  }

  .ops-button {
    @apply bg-[#00ffd9] py-1 border-2 border-solid border-black hover:bg-[#05dcbb];
    font-family: Montserrat, sans-serif;
    font-weight: bold;
    font-size: 12px;

    box-shadow: 0 2px 0 0 rgba(32, 68, 123, 0.3), inset 0 -2px 0 1px #0bc4a8;
  }

  .twitter-button {
    background-color: #009aeb;
    border: solid 1px #7ed3ff;

    &:hover {
      background-color: #1A8CD8;
    }
  }

  .fb-button {
    background-color: #0067fb;
    border: solid 1px #a2c9ff;

    &:hover {
      background-color: #166FE5;
    }
  }

  .scale-up {
    transform: scaleX(1.5) scaleY(1.5);
  }

  .scale-normal {
    transform: scaleX(1) scaleY(1);;
  }

  .fade-out {
    animation: fadeOut 0.2s cubic-bezier(0.4, 0, 0.6, 1) 1;
  }

  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
}

.cube-dialog-content {
  box-sizing: border-box;
  transform: translate(-50%, -50%);

  button, [role="button"] {
    cursor: pointer;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
    appearance: button;
    -webkit-appearance: button;
    background-color: #090;
    font-size: 1.125rem;
    &:hover {
      background-color: #0e0;
      color: #030;
    }
  }

  .input-reset-code {
    font-family: inherit;
    font-weight: inherit;
    letter-spacing: inherit;
    margin: 0;
    box-sizing: border-box;
    border-style: solid;
    font-size: 19px;
    -webkit-text-security: disc;
    padding: 9px;
    margin: 3px 0 9px;
    background-color: #dfd;

    &:focus {
      border-color: #090;
    }
  }

  .cancel-btn {
    filter: grayscale(1);
  }
}

@media (min-width: 640px) {
  .cube-match-game-container .translate-center {
    transform: translate(-50%, 0);
  }
}

@media (prefers-color-scheme: dark) {
  .cube-dialog-content {
    background-color: #030;
    color: #ffe;
  }
}

/* Progress bar */
.progress {
  @apply w-[45vw] sm:w-48 h-2.5 relative;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  overflow: hidden;
  border-radius: 1rem;
  background-color: white;
  border: 2px solid #000;

  &::-webkit-progress-bar {
    background-color: #fff;
  }

  &::-webkit-progress-value {
    background-color: #ffe800;
    border-right: 2px solid #000;
    border-radius: 1rem;
  }
}

html[dir="rtl"] .progress::-webkit-progress-value {
  border-right: none;
  border-left: 2px solid #000;
}

/* Radial Progress */
.radial-progress {
  @apply relative inline-grid h-[var(--size)] w-[var(--size)] place-content-center rounded-full bg-transparent;
  vertical-align: middle;
  box-sizing: content-box;
  --value: 0;
  --size: 2.75rem;
  --thickness: calc(var(--size) / 10)
}
.radial-progress::-moz-progress-bar {
  @apply appearance-none bg-transparent;
}
.radial-progress::-webkit-progress-value {
  @apply appearance-none bg-transparent;
}
.radial-progress::-webkit-progress-bar {
  @apply appearance-none bg-transparent;
}
.radial-progress:before,
.radial-progress:after {
  @apply absolute rounded-full;
  content: "";
}
.radial-progress:before {
  @apply inset-0;
  background:
    radial-gradient(farthest-side, currentColor 98%, #0000) top/var(--thickness) var(--thickness)
      no-repeat,
    conic-gradient(currentColor calc(var(--value) * 1%), #0000 0);
  -webkit-mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
  mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--thickness)),
    #000 calc(100% - var(--thickness))
  );
}
.radial-progress:after {
  inset: calc(50% - var(--thickness) / 2);
  transform: rotate(calc(var(--value) * 3.6deg - 90deg)) translate(calc(var(--size) / 2 - 50%));
  background-color: currentColor;
}
