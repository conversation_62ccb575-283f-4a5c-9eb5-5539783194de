import { useContext } from 'preact/hooks';
import { GameContext } from './context';
import { Button } from './components/ui/button';
import RunIcon from './components/ui/run-icon';
import SoundIcon from './components/ui/sound-icon';
import Reset from './components/reset';
import { formatScore } from '../utils/utils';

export default function Menu() {
  const { level, changeLevel, totalScore, maxLevel, maxLevelReached, setScreen, soundOn, setSoundOn, i18n } =
    useContext(GameContext);

  const doPlay = () => {
    setScreen('play');
  };

  const goSpeedrun = () => {
    setScreen('speedrun');
  };

  const switchSound = () => {
    setSoundOn((val) => !val);
  }

  return (
    <div className="menu absolute top-0 left-0 w-full h-full text-black">
      <div className="px-4 pt-4 pb-2 sm:px-8 sm:pt-12 sm:pb-4 h-full flex flex-col justify-between">
        <div className="flex flex-col justify-center items-center gap-4 sm:gap-6">
          <div className="relative mt-5">
            <div
              className="text-6xl sm:text-8xl font-bold w-full text-center game-title-lower"
              dangerouslySetInnerHTML={{ __html: i18n.name }}
            ></div>
            <div
              className="absolute top-0 text-6xl sm:text-8xl font-normal w-full text-center game-title-upper"
              dangerouslySetInnerHTML={{ __html: i18n.name }}
            ></div>
          </div>
          <div className="flex flex-row justify-center items-baseline gap-4">
            <div className="uppercase text-white font-bold text-lg">
              {i18n.yourScore}
            </div>
            <div className="score-badge text-[#ffe800] rounded-full px-8 py-1 text-xl font-bold">
              {formatScore(totalScore)}
            </div>
          </div>
        </div>
        <div className="flex justify-center" onClick={doPlay}>
          <div className="bg-[#ffe800] px-16 pt-5 pb-2 rounded-3xl border-4 border-black border-solid start-button cursor-pointer luckiest-guy-font text-3xl uppercase">
            {i18n.start}
          </div>
        </div>
        <div className="flex flex-col justify-center items-center gap-4">
          <div className="px-4 text-sm w-full font-semibold text-white">
            {i18n.availableLevels}
          </div>
          <div className="grid grid-cols-4 sm:grid-cols-8 gap-2">
            {Array.from({ length: maxLevel }).map((_, i) => (
              <Button
                key={i}
                variant="outline"
                className={
                  'w-10 h-10 level-button ' + (level === i ? 'active' : '')
                }
                disabled={i > maxLevelReached}
                onClick={() => changeLevel(i)}
              >
                {i + 1}
              </Button>
            ))}
          </div>
        </div>
        <div className="flex flex-wrap justify-between">
          <div className="flex flex-row gap-2 items-center">
            <Button
              variant="custom"
              size="custom"
              className="h-8 w-8 ops-button"
              onClick={switchSound}
            >
              <SoundIcon className="h-3.5 w-3.5" on={soundOn} />
            </Button>
            <div className="font-bold text-xs">
              {soundOn ? i18n.soundOn : i18n.soundOff}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Reset />
            <Button
              variant="custom"
              size="custom"
              className="ops-button"
              onClick={goSpeedrun}
            >
              <RunIcon className="h-3.5 w-3.5 me-3" />
              {i18n.speedrun}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
