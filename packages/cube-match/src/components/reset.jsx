import { useContext, useState, useEffect } from 'preact/hooks';
import { GameContext } from '../context';
import { Button } from './ui/button';
import { Input } from './ui/input';
import ResetIcon from './ui/reset-icon';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { localStore } from '../../utils/local-store';

export default function Reset() {
  const WAIT = 600;

  const { resetRecord, i18n, lcPrefix } = useContext(GameContext);
  const [open, setOpen] = useState(false);
  const [showResetCode, setShowResetCode] = useState(false);
  const [resetCodeGetter, resetCodeSetter] = localStore(lcPrefix + 'reset_code', '');
  const [resetCode, setResetCode] = useState(resetCodeGetter());
  const [inputCode, setInputCode] = useState('');
  const [wrongCode, setWrongCode] = useState(false);
  const [timer, setTimer] = useState(WAIT);

  let timerId;

  useEffect(() => {
    resetCodeSetter(resetCode);
  }, [resetCode]);

  useEffect(() => {
    if (wrongCode) {
      timerId = setInterval(() => {
        setTimer((t) => t - 1);
      }, 1000);
    }
    return () => {
      clearInterval(timerId);
    }
  }, [wrongCode]);

  useEffect(() => {
    if (timer === 0) {
      resetRecord();
      setResetCode('');
      setInputCode('');
      setWrongCode(false);
      clearInterval(timerId);
      setTimer(WAIT);
      setOpen(false);
    }
  }, [timer])

  const onCancel = () => {
    setInputCode('');
    setWrongCode(false);
    clearInterval(timerId);
    setTimer(WAIT);
    setOpen(false);
  }

  const doReset = () => {
    if (!resetCode) { // no code, just do reset
      resetRecord();
      setInputCode('');
      setOpen(false);
    } else if (resetCode && resetCode === inputCode) {
      // has code, and correct code, do reset and remove local code
      resetRecord();
      setResetCode('');
      setInputCode('');
      setOpen(false);
    } else {
      setWrongCode(true);
      // has code, but wrong code, need timer 600s before reset
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="custom"
          size="custom"
          className="ops-button"
        >
          <ResetIcon className="h-3.5 w-3.5 me-3" />
          {i18n.reset}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>{i18n.confirmReset}</DialogTitle>
        <DialogDescription>
          {resetCode && (
            <>
              <label className="block leading-none text-base mb-2">
                {i18n.enterResetCode}
              </label>
              <Input
                type="text"
                tabindex="-1"
                className="input-reset-code"
                value={inputCode}
                disabled={wrongCode}
                placeholder={i18n.enterCodePlaceholder}
                onChange={(e) => setInputCode(e.target.value)}
              />
              <div className="font-bold my-4">
                {wrongCode ? timer : '--:--'}
              </div>
              <div className="text-xs text-[#090] mb-4">
                {i18n.forgotResetCodeTip}
              </div>
            </>
          )}

          {!resetCode && !showResetCode && (
            <div
              className="underline text-black text-sm cursor-pointer dark:text-[#ffe] opacity-60 mb-4"
              onClick={() => setShowResetCode(true)}
            >
              {i18n.toggleResetCode}
            </div>
          )}
          {!resetCode && showResetCode && (
            <>
              <label className="block leading-none text-base mb-2">
                {i18n.createResetCode}
              </label>
              <Input
                type="text"
                className="input-reset-code"
                value={inputCode}
                placeholder={i18n.createCodePlaceholder}
                onChange={(e) => setInputCode(e.target.value)}
              />
              <Button
                size="lg"
                shape="round"
                className="mb-4 text-lg create-btn"
                onClick={() => {
                  setResetCode(inputCode);
                  setInputCode('');
                }}
              >
                {i18n.create}
              </Button>

              <div className="text-xs text-[#090] mb-2">
                {i18n.createCodeTip}
              </div>
              <div className="text-xs text-red-500 dark:text-[#ff0] font-bold mb-4">
                {i18n.createCodeWarning}
              </div>
            </>
          )}
        </DialogDescription>
        <DialogFooter>
          <Button
            size="lg"
            shape="round"
            className="cancel-btn mt-2 sm:mt-0"
            onClick={onCancel}
          >
            {i18n.cancel}
          </Button>
          <Button
            onClick={doReset}
            size="lg"
            shape="round"
            className="reset-btn"
            disabled={wrongCode}
          >
            {i18n.reset}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
