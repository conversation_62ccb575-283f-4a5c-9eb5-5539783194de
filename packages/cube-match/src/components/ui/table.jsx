import { forwardRef } from 'preact/compat';

const Table = forwardRef(({
  className = '', ...props
}, ref) => {
  return (
    <div className="relative w-full overflow-auto">
    <table
      ref={ref}
      className={"w-full caption-bottom text-right text-sm " + className}
      {...props}
    />
  </div>
  )
})

Table.displayName = "Table"

const TableHeader = forwardRef(({ className = '', ...props }, ref) => (
  <thead ref={ref} className={"[&_tr]:border-b " + className} {...props} />
))
TableHeader.displayName = "TableHeader"

const TableBody = forwardRef(({ className = '', ...props }, ref) => (
  <tbody
    ref={ref}
    className={"[&_tr:last-child]:border-0 " + className}
    {...props}
  />
))
TableBody.displayName = "TableBody"

const TableFooter = forwardRef(({ className = '', ...props }, ref) => (
  <tfoot
    ref={ref}
    className={"border-t bg-zinc-100/20 font-medium [&>tr]:last:border-b-0 " + className}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"

const TableRow = forwardRef(({ className = '', ...props }, ref) => (
  <tr
    ref={ref}
    className={"border-b transition-colors hover:bg-zinc-100/20 data-[state=selected]:bg-zinc-100 " + className}
    {...props}
  />
))
TableRow.displayName = "TableRow"

const TableHead = forwardRef(({ className = '', ...props }, ref) => (
  <th
    ref={ref}
    className={"h-12 px-4 align-middle font-semibold [&:has([role=checkbox])]:pe-0 " + className}
    {...props}
  />
))
TableHead.displayName = "TableHead"

const TableCell = forwardRef(({ className = '', ...props }, ref) => (
  <td
    ref={ref}
    className={"p-4 align-middle [&:has([role=checkbox])]:pe-0 " + className}
    {...props}
  />
))
TableCell.displayName = "TableCell"

const TableCaption = forwardRef(({ className = '', ...props }, ref) => (
  <caption
    ref={ref}
    className={"mt-4 text-sm text-zinc-500 " + className}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
