import { useEffect, useState } from 'preact/hooks';

export default function Combo({ count }) {
  const [isScalingUp, setIsScalingUp] = useState(false);
  const [isFadingOut, setIsFadingOut] = useState(false);
  const [pos, setPos] = useState(0);

  const position = {
    0: 'top-10',
    1: 'top-10 pe-1',
    2: 'top-10 ps-1',
    3: 'top-11 ps-1',
    4: 'top-11 pe-1',
    5: 'top-11',
  };

  useEffect(() => {
    if (isScalingUp) {
      const timer = setTimeout(() => setIsScalingUp(false), 50);
      return () => clearTimeout(timer);
    }
  }, [isScalingUp]);

  useEffect(() => {
    setIsFadingOut(false);
    if (count < 2) return;
    setPos(c => (c + 1) % 6);
    setIsScalingUp(true);
    const timer = setTimeout(() => {
      setIsFadingOut(true)
    }, 1800);
    return () => {
      clearTimeout(timer);
    }
  }, [count]);

  return (
    <div
      className={`combo absolute origin-center ${position[pos]} text-5xl ${
        isScalingUp ? 'scale-up' : 'scale-normal'
      } ${isFadingOut ? 'fade-out' : ''}`}
    >
      {count > 1 && count}
    </div>
  );
}
