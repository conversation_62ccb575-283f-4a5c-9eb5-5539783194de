import { useContext, useMemo } from 'preact/hooks';
import { GameContext } from '../context';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from './ui/dialog';
import { Button } from './ui/button';
import { formatScore, formatTime } from '../../utils/utils';

export default function Master({ open, setOpen }) {
  const { i18n, gameRecord, setScreen, totalScore } = useContext(GameContext);

  const totalDuration = useMemo(() => {
    return gameRecord.reduce((a, b) => a + (b?.time || 0), 0);
  }, [gameRecord]);

  const onOk = () => {
    setScreen('menu');
    setOpen(false);
  };

  return (
    <Dialog open={open}>
      <DialogContent>
        <DialogTitle>{i18n.masterDialogTitle}</DialogTitle>
        <DialogDescription>
          <div className="flex flex-row justify-around my-4">
            <div className="flex flex-col items-center">
              <div className="text-5xl font-semibold text-[#2c6414] dark:text-[#ffe]">
                {formatScore(totalScore)}
              </div>
              <div className="uppercase">{i18n.bestScore}</div>
            </div>
            <div className="flex flex-col items-center">
              <div className="text-5xl font-semibold text-[#2c6414] dark:text-[#ffe]">
                {formatTime(totalDuration, false)}
              </div>
              <div className="uppercase">{i18n.bestTime}</div>
            </div>
          </div>
          {i18n.masterDialogText}
        </DialogDescription>
        <DialogFooter>
          <Button
            shape="round"
            className="w-full"
            onClick={onOk}
          >
            {i18n.ok}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
