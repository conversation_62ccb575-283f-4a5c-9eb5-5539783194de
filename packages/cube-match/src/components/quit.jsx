import { useContext, useMemo } from 'preact/hooks';
import { GameContext } from '../context';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTitle,
} from './ui/dialog';
import { Button } from './ui/button';

export default function Quit({ open, onQuit, onCancel }) {
  const { level, i18n } = useContext(GameContext);

  const title = useMemo(() => {
    return i18n.quitDialogTitle.replace('%LEVEL%', level + 1);
  })

  return (
    <Dialog open={open}>
      <DialogContent>
        <DialogTitle>{title}</DialogTitle>
        <DialogFooter>
          <Button
            size="lg"
            shape="round"
            className="cancel-btn mt-2 sm:mt-0"
            onClick={onCancel}
          >
            {i18n.cancel}
          </Button>
          <Button
            size="lg"
            shape="round"
            onClick={onQuit}
          >
            {i18n.quit}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
