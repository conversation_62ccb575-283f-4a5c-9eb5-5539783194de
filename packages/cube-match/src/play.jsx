import { useContext, useState, useEffect } from 'preact/hooks';
import { GameContext } from './context';
import { Button } from './components/ui/button';
import BackIcon from './components/ui/back-icon';
import { ProgressBar } from './components/ui/progress';
import Quit from './components/quit';
import { formatTime } from '../utils/utils';

export default function Play() {
  const { level, maxLevel, progress, score, setScore, setScreen, gameRecord, timer, setTimer, i18n } =
    useContext(GameContext);

  const bestTime = gameRecord[level] ? (gameRecord[level]?.time || 0) : 0;
  const bestScore = gameRecord[level] ? (gameRecord[level]?.score || 0) : 0;

  const [elapsedTime, setElapsedTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [pausedTick, setPausedTick] = useState(0);
  const [pausedDuration, setPausedDuration] = useState(0);
  const [showQuit, setShowQuit] = useState(false);

  useEffect(() => {
    if (timer > 0 && !isPlaying) {
      setIsPlaying(true);
    } else if (isPlaying && !isPaused) {
      setIsPlaying(false);
      setElapsedTime(timer - pausedDuration - timer);
    } else {
      setElapsedTime(0);
      setTimer(0);
    }
  }, [timer]);

  let timerId = null;
  useEffect(() => {
    if (isPlaying && !isPaused) {
      timerId = setInterval(() => {
        setElapsedTime(Date.now() - pausedDuration - timer);
      }, 10);
    } else {
      clearInterval(timerId);
    }

    return () => {
      clearInterval(timerId);
    };
  }, [isPlaying, isPaused, pausedDuration]);

  const showQuitPopup = () => {
    setShowQuit(true);
    setIsPaused(true);
    setPausedTick(Date.now());
  }

  const onBeforeunload = (e) => {
    if (isPlaying) {
      e.preventDefault();
      e.returnValue = true;
    }
  }

  useEffect(() => {
    if (isPlaying) {
      window.addEventListener('beforeunload', onBeforeunload);
    }

    return () => {
      window.removeEventListener('beforeunload', onBeforeunload);
    };
  }, [isPlaying]);

  const goBack = () => {
    if (isPlaying) {
      showQuitPopup();
      return;
    }
    setScreen('menu');
  };

  const onQuit = () => {
    // should reset all the states on quit
    clearInterval(timerId);
    setTimer(0);
    setElapsedTime(0);
    setScore(0);
    setIsPlaying(false);
    setIsPaused(false);
    setPausedDuration(0);
    setPausedTick(0);
    setShowQuit(false);
    setScreen('menu');
  }

  const onCancelquit = () => {
    setShowQuit(false);
    setIsPaused(false);
    setPausedDuration(d => d + Date.now() - pausedTick);
    setPausedTick(0);
  };

  return (
    <>
      {/* top-left: back button + level */}
      <div className="absolute top-2 left-2 p-1.5 sm:p-2.5 inline-flex gap-4 items-center">
        <Button
          variant="custom"
          size="custom"
          className="h-8 w-8 ops-button"
          onClick={goBack}
        >
          <BackIcon className="h-3 w-3" />
        </Button>
        <span className="text-lg sm:text-xl font-bold text-white">
          {level + 1}/{maxLevel}
        </span>
      </div>
      {/* top-center: progress */}
      <div className="absolute top-2 right-2 sm:right-0 sm:left-1/2 ltr:translate-center p-1.5 sm:p-2.5 w-1/2 sm:w-fit inline-flex flex-row items-center gap-3">
        <ProgressBar progress={progress} />
        <div className="text-lg font-bold text-white">{progress}%</div>
      </div>
      {/* bottom-left: score */}
      <div className="absolute bottom-3 left-3 p-1.5 sm:px-2.5 flex flex-row bg-[#2b76e8] w-[36%] sm:w-40 rounded-lg">
        <div className="flex flex-row justify-between w-full">
          <div className="flex flex-col items-start">
            <span className="font-bold text-xs sm:text-sm text-white uppercase">
              {i18n.score}
            </span>
            <span className="text-[#ffe800] font-bold text-lg sm:text-xl leading-tight">
              {score}
            </span>
          </div>
          <div className="flex flex-col items-start opacity-60 text-white">
            <span className="uppercase font-bold text-xs sm:text-sm">
              {i18n.best}
            </span>
            <span className="font-bold text-lg sm:text-xl leading-tight">
              {Math.max(score, bestScore)}
            </span>
          </div>
        </div>
      </div>
      {/* bottom-right: time */}
      <div className="absolute bottom-3 right-3 p-1.5 sm:px-2.5 flex flex-row bg-[#2b76e8] w-[56%] sm:w-64 rounded-lg">
        <div className="flex flex-row justify-between w-full">
          <div className="flex flex-col items-start">
            <span className="font-bold text-xs sm:text-sm text-white uppercase">
              {i18n.time}
            </span>
            <span className="text-[#ffe800] font-bold text-lg sm:text-xl leading-tight font-mono">
              {formatTime(elapsedTime)}
            </span>
          </div>
          <div className="flex flex-col items-start opacity-60 text-white">
            <span className="uppercase font-bold text-xs sm:text-sm">
              {i18n.best}
            </span>
            <span className="font-bold text-lg sm:text-xl leading-tight font-mono">
              {formatTime(bestTime)}
            </span>
          </div>
        </div>
      </div>

      <Quit open={showQuit} onQuit={onQuit} onCancel={onCancelquit} />
    </>
  );
}
