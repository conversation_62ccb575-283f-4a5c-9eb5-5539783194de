import { render } from 'preact';
import './style.css';
import GameApp from './game';
import GameInit from './init';
import i18nDefault from './i18n.json';

if (!__IS_PUBLISH__) {
  // const allowedCubes = {
  //   1: [
  //     [1, 1, 1],
  //     [2, 2, 2],
  //   ],
  //   3: [[2, 3, 4]],
  // };
  // const levelSizes = {
  //   1: [5, 5, 5],
  //   3: [7, 7, 7],
  //   5: [9, 9, 11],
  // };
  render(
    <GameApp config={{ i18n: i18nDefault, root: '/' }} />,
    document.querySelector('.cube-dev-container'),
  );
}

const CubeMatch = {
  eventMap: {},
  on(event, fn) {
    this.eventMap[event] = fn;
  },
  async init(
    selector,
    config,
  ) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;

    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    config.root = config.root || '/';
    if (!config.i18n) {
      config.i18n = i18nDefault;
    } else {
      config.i18n = {
        ...i18nDefault,
        ...config.i18n,
      };
    }

    render(<GameInit config={config} />, el);

    const loadCSS = async (url) => {
      return new Promise((resolve, reject) => {
        // load google font
        const googleFont = document.createElement('link');
        googleFont.rel = 'stylesheet';
        googleFont.href = 'https://fonts.googleapis.com/css2?family=Luckiest+Guy&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap';
        document.head.appendChild(googleFont);

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(config.root + 'cube-match.css');

    render(<GameApp config={config} />, el);
  },
};

export default CubeMatch;
