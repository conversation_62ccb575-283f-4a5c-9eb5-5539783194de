import { defaultConfig } from '../utils/default-config';
export default function GameInit({ config = {} }) {
  config = {
    ...defaultConfig,
    ...config,
  };

  const i18n = config.i18n;

  return (
    <div style="min-height: 550px;height: 100%;display: flex;flex-direction: column;justify-content: center;align-items: center;user-select: none;position: relative;">
      <div
        style="color: #060;box-sizing: border-box;font-weight:700;font-size: 3rem;line-height: 1; margin-bottom: 2rem;"
        dangerouslySetInnerHTML={{ __html: i18n.name }}
      ></div>
      <div style="box-sizing: border-box;background-color: rgba(82,82,91,0.5);border-radius: 9999px;display:flex;justify-content: start;alien-items: center;width: 90%;height: 1rem;margin-left: 1rem;">
        <div style="width: 0%; box-sizing: border-box; height: 100%; background-color: #18181b; border-radius: 9999px;" />
      </div>
    </div>
  );
}
