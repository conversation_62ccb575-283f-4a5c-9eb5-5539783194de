import { AssetCache } from '../utils/asset-cache';

class AssetLoadingManager {
  constructor() {
    this.imagesUrls = {
      arrowR: 'assets/icons/arrowR.png',
    };
    this.soundUrls = {
      arrowShot: 'assets/sounds/arrowShot.webm',
      fastBlow: 'assets/sounds/fastBlow.webm',
    };
  }

  async load(prefix) {
    await this._loadImages(prefix);
    await this._loadSounds(prefix);
  }

  async _loadImages(prefix) {
    for (const key in this.imagesUrls) {
      const url = prefix + this.imagesUrls[key];
      const img = await this.loadImage(url);
      AssetCache.set(key, img);
    }
  }

  async _loadSounds(prefix) {
    for (const key in this.soundUrls) {
      const url = prefix + this.soundUrls[key];
      const response = await fetch(url);
      const blob = await response.blob();
      const audioUrl = URL.createObjectURL(blob);
      AssetCache.set(key, audioUrl);
    }
  }

  loadImage(url) {
    return new Promise((resolve) => {
      const image = new Image();
      image.onload = () => {
        resolve(image);
      };
      image.src = url;
      image.crossOrigin = 'anonymous';
      // need to add this to avoid error:
      // Failed to execute 'toDataURL' on 'HTMLCanvasElement': Tainted canvase may not be exported.
    });
  }
}

export const AssetLoader = new AssetLoadingManager();
