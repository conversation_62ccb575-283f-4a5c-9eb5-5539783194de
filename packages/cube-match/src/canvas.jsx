import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { EffectComposer } from 'three/addons/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/addons/postprocessing/RenderPass.js';
import { OutlinePass } from 'three/addons/postprocessing/OutlinePass.js';
import { OutputPass } from 'three/addons/postprocessing/OutputPass.js';

import { useRef, useEffect, useState } from 'preact/hooks';
import { useContext } from 'preact/hooks';
import { GameContext } from './context';
import { GameManager } from './manager';
import Combo from './components/combo';
import Master from './components/master';

export default function Canvas({ config }) {
  const {
    screen,
    level,
    changeLevel,
    setProgress,
    setScore,
    setTimer,
    setGameRecord,
  } = useContext(GameContext);
  const canvas = useRef(null);
  const [combo, setCombo] = useState(0);
  const [showMaster, setShowMaster] = useState(false);
  const { comboExpireTime = 2000 } = config;

  let timerId;
  useEffect(() => {
    if (combo > 1) {
      clearTimeout(timerId);
      // if over 2s no new combo, should reset and remove combo count
      timerId = setTimeout(() => setCombo(0), comboExpireTime);
    }

    return () => clearTimeout(timerId);
  }, [combo]);

  useEffect(() => {
    clearTimeout(timerId);
  }, [level]);

  useEffect(() => {
    const container = document.querySelector('.cube-match-game-container');
    let width = container.clientWidth;
    let height = container.clientHeight;

    let intersects = [];
    let manager;

    /**
     * Renderer
     */
    const renderer = new THREE.WebGLRenderer({
      canvas: canvas.current,
      alpha: true,
    });
    renderer.shadowMap.enabled = false;
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(width, height);

    /**
     * Scene
     */
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x4790ff); // blue background

    /**
     * Group
     */
    const group = new THREE.Group();
    scene.add(group);

    /**
     * Light
     */
    const ambientLight = new THREE.AmbientLight(0xffffff, 30);
    scene.add(ambientLight);

    /**
     * Camera
     */
    const camera = new THREE.PerspectiveCamera(60, width / height, 1.0, 200.0);
    camera.position.set(6, 6, 6);
    camera.lookAt(0, 0, 0);

    /**
     * Orbit Controls for Zoom/Pan
     */
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enablePan = true;
    controls.enableZoom = true;
    // controls.update();

    /**
     * Post processing
     */
    const composer = new EffectComposer(renderer); // make composer
    const renderPass = new RenderPass(scene, camera); // make render pass
    composer.addPass(renderPass); // add render pass

    const outlinePass = new OutlinePass(
      new THREE.Vector2(width, height),
      scene,
      camera
    );
    outlinePass.edgeStrength = 0.0;
    outlinePass.edgeGlow = 0.0;
    outlinePass.edgeThickness = 1.0;
    outlinePass.visibleEdgeColor.set('#FF8100');
    outlinePass.hiddenEdgeColor.set('#FF8100');
    composer.addPass(outlinePass);
    const outputPass = new OutputPass();
    composer.addPass(outputPass);

    /**
     * Raycaster for mouse hover and click controls
     */
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector3();

    let isDragging = false;
    let dragStartX = 0, dragStartY = 0;
    let lastClickTime = 0;

    const onPointerDown = (e) => {
      isDragging = false;
      dragStartX = e.clientX;
      dragStartY = e.clientY;
    };

    const onPointerMove = (e) => {
      const { width, height, left, top } = container.getBoundingClientRect();

      mouse.set(((e.clientX - left) / width) * 2 - 1, -((e.clientY - top) / height) * 2 + 1, 0);
      raycaster.setFromCamera(mouse, camera);
      intersects = raycaster.intersectObjects(group.children);

      // show cursor pointer when hover cube
      if (intersects.length) {
        document.body.style.cursor = 'pointer';
      } else {
        document.body.style.cursor = 'auto';
      }
      const { minDragDistance } = config;
      // check if is dragging
      if (!isDragging) {
        const dragDistance = Math.sqrt(
          Math.pow(e.clientX - dragStartX, 2) +
            Math.pow(e.clientY - dragStartY, 2)
        );
        if (dragDistance > minDragDistance) {
          isDragging = true;
        }
      }
    };

    const onClick = () => {
      if (intersects.length && manager) {
        lastClickTime = Date.now();
        // only consider first hit should be the click target
        const obj = intersects[0].object;
        manager.animateFly(obj);
      }
    };

    const onPointerUp = (e) => {
      if (Date.now() - lastClickTime < 50) {
        // gap of two clicks should be more than 50ms
        // if too fast, collision may happen
        return;
      }
      if (!isDragging) {
        const { width, height, left, top } = container.getBoundingClientRect();
        mouse.set(((e.clientX - left) / width) * 2 - 1, -((e.clientY - top) / height) * 2 + 1, 0);
        raycaster.setFromCamera(mouse, camera);
        intersects = raycaster.intersectObjects(group.children);
        onClick();
      }
      isDragging = false;
    };
    if (screen === 'play') {
      container.addEventListener('pointerdown', onPointerDown);
      container.addEventListener('pointermove', onPointerMove);
      container.addEventListener('pointerup', onPointerUp);
    }

    /**
     * Responsive
     */
    function resize() {
      if (screen === 'play') {
        width = container.clientWidth;
        height = container.clientHeight;
        camera.aspect = width / height;
        camera.updateProjectionMatrix();
        renderer.setSize(width, height);
      }
    }

    window.addEventListener('resize', resize);
    resize();

    /**
     * Render-loop, called 60-times/second
     */
    function animate() {
      requestAnimationFrame(animate);
      composer.render(scene, camera);
    }
    animate();

    /**
     * Play game, make level begin
     */
    const cubesMap = {};
    if (screen === 'play') {
      manager = new GameManager({
        config,
        group,
        camera,
        outlinePass,
        cubesMap,
        updateProgress: (progress) => setProgress(progress),
        updateLevel: (lv) => changeLevel(lv),
        updateCombo: (combo) => setCombo(combo),
        updateScore: (score) => setScore(score),
        updateTimer: (timer) => setTimer(timer),
        saveRecord: (level, time, score, valid, total) => {
          setGameRecord((prev) => {
            if (!prev[level]) {
              prev[level] = {
                time,
                score,
                valid,
                total,
              };
            } else {
              prev[level].time = Math.min(prev[level].time, time);
              prev[level].score = Math.max(prev[level].score, score);
              if (valid / total > prev[level].valid / prev[level].total) {
                prev[level].valid = valid;
                prev[level].total = total;
              }
            }
            return [...prev];
          });
        },
        onMasterClear: () => setShowMaster(true),
      });
      manager.setLevel(level);
      manager.makeLevel();
      canvas.current.style.visibility = 'visible';
    } else {
      canvas.current.style.visibility = 'hidden';
    }

    return () => {
      container.removeEventListener('pointerdown', onPointerDown);
      container.removeEventListener('pointermove', onPointerMove);
      container.removeEventListener('pointerup', onPointerUp);
      window.removeEventListener('resize', resize);
    }
  }, [canvas.current, screen]);

  return (
    <>
      <canvas
        id="cube-canvas"
        ref={canvas}
        style="visibility: hidden;"
      ></canvas>
      {screen === 'play' && <Combo count={combo} />}
      {screen === 'play' && (
        <Master open={showMaster} setOpen={setShowMaster} />
      )}
    </>
  );
}
