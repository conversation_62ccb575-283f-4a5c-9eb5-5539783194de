import * as THREE from 'three';
import { gsap } from 'gsap';

import { lerp, playSound } from '../utils/utils';
import { isCollision, fillCuboid, makeCube } from '../utils/cube';

export class GameManager {
  constructor({
    config = {},
    group,
    camera,
    outlinePass,
    cubesMap,
    updateProgress,
    updateLevel,
    saveRecord,
    updateCombo,
    updateScore,
    updateTimer,
    onMasterClear,
  }) {
    this.config = config;
    this._levelDimensions = this._inferLevelDimensions();
    this._level = 0;
    this._timer = 0;
    this._maxLevel = this._levelDimensions.length;

    this.group = group;
    const boundingBox = new THREE.Box3().setFromObject(this.group);
    const center = boundingBox.getCenter(new THREE.Vector3());
    this.group.position.copy(center);

    this.cubesMap = cubesMap;
    this.camera = camera;
    this.outlinePass = outlinePass;
    this.tweens = [];

    this.currentCubesCount = 0;
    this.totalCubesCount = 0;

    this._lastRemoveTime = 0;
    this._combo = 0;
    this._score = 0;
    this._validClick = 0;
    this._totalClick = 0; // accuracy = valid / total

    this.updateProgress = updateProgress;
    this.updateLevel = updateLevel;
    this.saveRecord = saveRecord;
    this.updateCombo = updateCombo;
    this.updateScore = updateScore;
    this.updateTimer = updateTimer;
    this.onMasterClear = onMasterClear;
  }

  setLevel(level) {
    this._level = level;
  }

  makeLevel() {
    this._emptyLevel();
    this._putCamera();
    this._animateIn();
    const arrangement = fillCuboid(this._levelAllowedBlocks, this._levelSize);

    console.time('generation took');
    for (let i = 0; i < arrangement.length; i++) {
      this.addCube(arrangement[i]);
    }
    console.timeEnd('generation took');

    this.totalCubesCount = this.group.children.length;
    this.currentCubesCount = this.group.children.length;
    this.updateProgress(0);
  }

  startTimer() {
    this._timer = Date.now();
    this.updateTimer(this._timer);
  }

  // seems not using
  cleanLevel() {
    this._animateOut(() => this._emptyLevel());
  }

  addCube({ pos, dims }) {
    const cube = makeCube(
      pos.map((el) => el * 1),
      dims,
      Math.floor(Math.random() * 3) * 2,
      // '#FFE800',
    );
    this.cubesMap[cube.uuid] = cube;
    this.group.add(cube);
  }

  removeCube(cube) {
    this.group.remove(cube);
    delete this.cubesMap[cube.uuid];
  }

  processForward() {
    this.currentCubesCount--;
    const progress = this.getProgress();
    this.updateProgress(progress);
    if (progress === 100) {
      this.stopTimer();
      setTimeout(() => {
        this.nextLevel();
      }, 800);
    }
  }

  nextLevel() {
    const next = this._level + 1;
    if (next === this._maxLevel) {
      this.onMasterClear();
      return;
    }
    this.setLevel(next);
    this.updateLevel(next);
    this.makeLevel();
    this._lastRemoveTime = 0;
    this._combo = 0;
    this._score = 0;
    this.updateCombo(0);
    this.updateScore(0);
    this.updateTimer(0);
    this._validClick = 0;
    this._totalClick = 0;
  }

  stopTimer() {
    if (!this._timer) return;
    const current = Date.now();
    const time = current - this._timer;
    this.updateTimer(current);
    this.saveRecord(this._level, time, this._score, this._validClick, this._totalClick);
    this._timer = 0;
  }

  animateFly(obj) {
    if (obj.userData.isMoving) return;
    if (this._timer === 0) {
      this.startTimer(); // start the timer when first cube is clicked
    }

    let cubes = this.group.children
      .filter(
        (el) =>
          !Object.values(this.cubesMap).includes(el.uuid) && !el.userData.isDead
      )
      .map((el) => ({ ...el.userData, uuid: el.uuid }))
      .slice();
    let idx = -1;

    for (let i = 0; i < cubes.length; i++) {
      if (cubes[i].uuid !== obj.uuid) continue;
      idx = i;
      break;
    }
    if (idx === -1) return;

    const pos = cubes[idx].pos.slice();
    const dims = cubes[idx].dims.slice();
    const axis = cubes[idx].axis;
    const direction = cubes[idx].direction;

    const axisI = { x: 0, y: 1, z: 2 }[axis];
    cubes.splice(idx, 1);
    cubes = cubes.map((el) => ({ pos: el.pos, dims: el.dims }));

    let collisionPoint = 0;
    for (let i = 0; i < this._levelSize[axisI] * 2; i++) {
      const newPos = pos;
      newPos[axisI] += (direction * i) / 2;

      const selectedCube = { pos: newPos, dims };
      if (isCollision(selectedCube, cubes)) {
        collisionPoint = Math.ceil(i / 2);
        break;
      }
    }

    obj.userData.isMoving = true;
    const cubeFlightSpeed = this.config?.cubeFlightSpeed || 0.0000001;

    if (collisionPoint) {
      if (this.isSoundOn()) {
        playSound('fastBlow');
      }
      let offset = collisionPoint * direction;
      this.outlinePass.selectedObjects = [
        ...this.outlinePass.selectedObjects,
        obj,
      ];
      obj.scale.set(0.99, 0.99, 0.99);

      this.outlinePass.edgeStrength = 5;

      // combo count broken
      this._lastRemoveTime = 0;
      this._combo = 0;

      // collision
      const t = gsap.to(obj.position, {
        onComplete: () => {
          t.reverse();
        },
        onReverseComplete: () => {
          obj.userData.isMoving = false;
          obj.scale.set(1, 1, 1);
          this.outlinePass.selectedObjects =
            this.outlinePass.selectedObjects.filter(
              (node) => node.id !== obj.id
            );
          if (!this.outlinePass.selectedObjects.length)
            this.outlinePass.edgeStrength = 0;
        },
        [obj.userData.axis]: obj.position[axis] + offset,
        duration: 0.115 / cubeFlightSpeed,
      });
    } else {
      if (this.isSoundOn()) {
        playSound('arrowShot');
      }
      obj.userData.isDead = true;
      let distance = obj.userData.direction * this._levelSize[axisI] * 2;
      distance = distance < 50 ? 50 : distance;
      const durationFactor = Math.abs(distance / 60);

      const currentTime = Date.now();
      if (!this._lastRemoveTime) {
        // init
        this._lastRemoveTime = currentTime;
        this._combo = 1;
      } else {
        const comboDuration = this.config?.comboExpireTime || 2000;
        if (currentTime - this._lastRemoveTime < comboDuration) {
          this._combo++; // within 2s => combo
        } else {
          this._combo = 1;
        }
        this._lastRemoveTime = currentTime;
      }

      // this cube can be removed
      gsap
        .to(obj.position, {
          [obj.userData.axis]: distance,
          ease: 'power2.inOut',
          duration: durationFactor / cubeFlightSpeed,
        })
        .then(() => {
          this.removeCube(obj);
        });

      const vol = obj.userData.dims.reduce((a, b) => a * b, 1); // volume of cube
      this._score += this._combo * vol;
      this.updateScore(this._score);
      this.processForward();
      this._validClick++;
    }

    this._totalClick++;

    this.updateCombo(this._combo);
  }

  isSoundOn() {
    const key = this.config.lcPrefix + 'cube-sound';
    const value = localStorage.getItem(key);
    return value === null ? true : value === '1';
  }

  getProgress() {
    return Math.floor(
      (1 - this.currentCubesCount / this.totalCubesCount) * 100
    );
  }

  _emptyLevel() {
    this.group.clear();
  }

  _putCamera() {
    const camera = this.camera;
    const targetPosition = new THREE.Vector3(1, 1, 1)
      .normalize()
      .multiplyScalar(Math.hypot(...this._levelSize) + 5);

    gsap.to(camera.position, {
      onUpdate: () => {
        camera.lookAt(0, 0, 0);
      },
      duration: 1,
      x: targetPosition.x,
      y: targetPosition.y,
      z: targetPosition.z,
    });
  }

  _animateIn() {
    const origin = { x: 0, y: 0, z: 0 };
    const duration = 2;
    const ease = 'power2.inOut';
    this.tweens = [];

    const t1 = gsap.fromTo(this.group.scale, origin, {
      x: 1,
      y: 1,
      z: 1,
      ease,
      duration,
    });

    const [x, y, z] = this._levelSize.map((el) => -el / 2);
    const t2 = gsap.fromTo(this.group.position, origin, {
      x,
      y,
      z,
      ease,
      duration,
    });

    setTimeout(() => {
      this.group.children.forEach((child) => {
        const og = child.position.clone();
        const out = og.cross(
          new THREE.Vector3(Math.random(), Math.random(), Math.random())
        );
        const t = gsap.to(child.position, {
          x: out.x,
          y: out.y,
          z: out.z,
          ease,
          duration: duration / 2,
        });

        t.eventCallback('onComplete', () => t.reverse());

        gsap.fromTo(child.scale, origin, {
          x: 1,
          y: 1,
          z: 1,
          ease,
          duration: duration - 0.1,
        });
      });
    }, 100);

    this.tweens.push(t1, t2);
  }

  _animateOut(cb) {
    let cT;
    for (const t of this.tweens) {
      t.reverse();
      cT = t;
    }

    if (cT) {
      cT.eventCallback('onComplete', () => {
        cb();
      });
    } else {
      cb();
    }
  }

  get _levelSize() {
    return this._levelDimensions[this._level];
  }

  get _levelAllowedBlocks() {
    return this._getLevelAllowedBlocks(this._level);
  }

  _inferLevelDimensions() {
    const levelSizeOpts = this.config?.levelSizes;
    const levelSizeOptKeys = Object.keys(levelSizeOpts);
    const dimensions = [];

    for (let i = 1; i < levelSizeOptKeys.length; i++) {
      const [a, b] = [levelSizeOptKeys[i - 1], levelSizeOptKeys[i]];
      const span = b - a;
      dimensions.push(levelSizeOpts[a]);
      for (let j = 1; j < span; j++) {
        const lvl = lerp(levelSizeOpts[a], levelSizeOpts[b], j / span);
        dimensions.push(lvl.map(Math.floor));
      }

      // last one
      if (i === levelSizeOptKeys.length - 1) {
        dimensions.push(levelSizeOpts[b]);
      }
    }

    return dimensions; // [[x,y,z], [x,y,z]....] increasing by a little each
  }

  _getLevelAllowedBlocks(level) {
    const allowedCubesOpts = this.config?.allowedCubes;
    const acKeys = Object.keys(allowedCubesOpts).filter(
      (el) => Number(el) <= level + 1
    ); // level start from 0

    let allowedCubes = [];

    for (let i = 0; i < acKeys.length; i++) {
      allowedCubes = [...allowedCubes, ...allowedCubesOpts[acKeys[i]]];
    }

    return allowedCubes;
  }
}
