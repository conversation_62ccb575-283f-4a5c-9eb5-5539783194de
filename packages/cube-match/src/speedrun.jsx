import { useContext, useState, useMemo } from 'preact/hooks';
import { Twitter, Facebook, Link, Share2, Check } from 'lucide-react';
import { GameContext } from './context';
import { Button } from './components/ui/button';
import BackIcon from './components/ui/back-icon';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './components/ui/table';
import { formatScore, formatTime, httpBuildQuery } from '../utils/utils';

export default function Speedrun() {
  const version = __VERSION__;
  const { setScreen, gameRecord, i18n, totalScore, socialShare } =
    useContext(GameContext);
  const [copied, setCopied] = useState(false);

  const completedLevels = useMemo(() => {
    return gameRecord.filter((r) => r.time > 0).length;
  }, [gameRecord]);

  const totalDuration = useMemo(() => {
    return gameRecord.reduce((a, b) => a + (b?.time || 0), 0);
  }, [gameRecord]);

  const totalAccuracy = useMemo(() => {
    const totalValid = gameRecord.reduce((a, b) => a + b.valid, 0);
    const totalCount = gameRecord.reduce((a, b) => a + b.total, 0);
    if (totalCount > 0) {
      return ((totalValid / totalCount) * 100).toFixed(1);
    }
    return 0;
  }, [gameRecord]);

  const url = location.href;
  const shareText = useMemo(() => {
    return (socialShare.text || i18n.shareText)
      .replace('%SCORE%', (totalScore || 0).toString())
      .replace('%LVL%', (completedLevels || 0).toString())
      .replace('%TIME%', formatTime(totalDuration))
      .replace('%ACC%', totalAccuracy + '%');
  }, [gameRecord]);

  const twLink = useMemo(() => {
    const tags = (Array.isArray(socialShare.hashtag) ? socialShare.hashtag.join(',') : socialShare.hashtag) || 'BlockDash';

    return (
      'https://twitter.com/intent/tweet?' +
      httpBuildQuery({
        text: shareText,
        hashtags: tags,
        url,
      })
    );
  }, [shareText]);

  async function doCopyLink() {
    if (copied) return;
    try {
      await navigator.clipboard.writeText(window.test.url);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 1e3);
    } catch (error) {
      console.error('Failed to copy: ', error);
    }
  }

  async function doShare() {
    try {
      await navigator.share({
        url,
        text: shareText,
        title: i18n.name,
      });
    } catch (error) {
      console.warn('Error: ', error);
    }
  }

  return (
    <div className="speedrun w-full h-full text-white">
      <div className="absolute top-2 left-2 p-2.5">
        <Button
          variant="custom"
          size="custom"
          className="h-8 w-8 ops-button"
          onClick={() => setScreen('menu')}
        >
          <BackIcon className="h-3 w-3" />
        </Button>
      </div>
      <div className="summary-data w-full flex flex-col p-6">
        <div className="flex flex-col justify-center items-center mb-10">
          <div className="text-7xl font-semibold">
            {formatScore(totalScore)}
          </div>
          <div className="uppercase">{i18n.score}</div>
        </div>
        <div className="flex flex-row justify-evenly items-center">
          <div className="flex flex-col justify-center items-center">
            <div className="text-3xl font-semibold">
              {formatTime(totalDuration, false)}
            </div>
            <div className="uppercase">{i18n.time}</div>
          </div>
          <div className="flex flex-col justify-center items-center">
            <div className="text-3xl font-semibold">{totalAccuracy}%</div>
            <div className="uppercase">{i18n.accuracy}</div>
          </div>
        </div>
      </div>
      <div className="w-full flex flex-row flex-wrap justify-center p-4 gap-4">
        <a href={twLink} target="_blank">
          <Button
            className="cursor-pointer twitter-button text-white"
            size="sm"
            variant="custom"
          >
            <Twitter
              className="h-4 w-4 me-1"
              fill="currentColor"
              strokeWidth="0"
            />
            {i18n.tweet}
          </Button>
        </a>
        <a
          href={'https://www.facebook.com/sharer.php?u=' + url}
          target="_blank"
        >
          <Button
            className="cursor-pointer fb-button text-white"
            size="sm"
            variant="custom"
          >
            <Facebook
              className="h-4 w-4 me-1"
              fill="currentColor"
              strokeWidth="0"
            />
            {i18n.facebook}
          </Button>
        </a>
        <Button
          size="sm"
          className="cursor-pointer"
          onClick={doCopyLink}
          variant="secondary"
        >
          {copied ? (
            <Check className="h-4 w-4 me-1" />
          ) : (
            <Link className="h-4 w-4 me-1" />
          )}
          {i18n.copyLink}
        </Button>
        <Button
          size="sm"
          className="cursor-pointer"
          onClick={doShare}
          variant="secondary"
        >
          <Share2 className="h-4 w-4 me-1" />
          {i18n.share}
        </Button>
      </div>
      <div className="w-full flex flex-col justify-center items-center py-4 px-6 sm:px-16 mt-12">
        <div className="text-xl font-medium mb-2">{i18n.levelBreakdown}</div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">{i18n.level}</TableHead>
              <TableHead>{i18n.bestTime}</TableHead>
              <TableHead>{i18n.bestScore}</TableHead>
              <TableHead>{i18n.accuracy}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {gameRecord.map((level, i) => (
              <TableRow key={i}>
                <TableCell className="font-medium">{i + 1}</TableCell>
                <TableCell>{formatTime(level?.time || 0)}</TableCell>
                <TableCell>{formatScore(level?.score || 0)}</TableCell>
                <TableCell>
                  {level.total > 0
                    ? ((level.valid / level.total) * 100).toFixed(1)
                    : 0}
                  %
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div className="font-mono p-8 text-sm text-sky-200">
        Version: {version}
      </div>
    </div>
  );
}
