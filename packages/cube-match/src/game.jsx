import { useState, useRef, useEffect, useMemo } from 'preact/hooks';
import { gsap } from 'gsap';
import { GameContext} from './context';
import { AssetLoader } from './loading';
import { localStore } from '../utils/local-store';

import Menu from './menu';
import Play from './play';
import Speedrun from './speedrun';
import Canvas from './canvas';
import { defaultConfig } from '../utils/default-config';

export default function GameApp({ config = {}}) {
  config = {
    ...defaultConfig,
    ...config,
  }

  const i18n = config.i18n
  const lcPrefix = config.lcPrefix
  const socialShare = config.social_share

  const maxLevel = 16; // change to fixed 16 levels
  const [getter, setter] = localStore(lcPrefix + 'cube-record', []);
  const [levelGetter, levelSetter] = localStore(lcPrefix + 'cube-level', 0);
  const [soundGetter, soundSetter] = localStore(lcPrefix + 'cube-sound', 1);
  const [totalScoreGetter, totalScoreSetter] = localStore('rd-v1cube-max', 0);

  const [loading, setLoading] = useState(true);
  const [screen, setScreen] = useState('menu'); // menu, play, paused
  const [level, setLevel] = useState(levelGetter() || 0); // show as level+1
  const [progress, setProgress] = useState(0);
  const [score, setScore] = useState(0);
  const [gameRecord, setGameRecord] = useState(getter() || []);
  const [totalScore, setTotalScore] = useState(totalScoreGetter() || 0);
  const [timer, setTimer] = useState(0);
  // eg: [{time: 3340, score: 36}, {time, score}, ...]
  const [soundOn, setSoundOn] = useState(!!soundGetter());

  const bar = useRef(null);
  const maxLevelReached = useMemo(() => gameRecord.length, [gameRecord]);
  let tween;

  useEffect(async () => {
    tween = gsap.to(bar.current.style, {
      width: '100%',
      duration: 10,
    });
    await AssetLoader.load(config.root);
    tween.duration(0.5);

    setTimeout(() => {
      setLoading(false);
    }, 500);
  }, []);

  const changeLevel = (lv) => {
    setLevel(lv);
    levelSetter(lv);
  };

  const resetRecord = () => {
    setGameRecord([]);
    changeLevel(0);
  };

  useEffect(() => {
    // save to local storage when laptimes updated
    setter(gameRecord);
    const totalScore = gameRecord.reduce((a, b) => a + (b?.score || 0), 0);
    totalScoreSetter(totalScore);
    setTotalScore(totalScore);
  }, [gameRecord]);

  useEffect(() => {
    soundSetter(soundOn ? 1 : 0);
  }, [soundOn])

  return (
    <GameContext.Provider
      value={{
        maxLevel,
        level,
        changeLevel,
        progress,
        setProgress,
        gameRecord,
        setGameRecord,
        score,
        setScore,
        timer,
        setTimer,
        soundOn,
        setSoundOn,
        maxLevelReached,
        screen,
        setScreen,
        resetRecord,
        i18n,
        lcPrefix,
        socialShare,
        totalScore,
      }}
    >
      <div className="cube-match-game-container flex flex-col justify-center relative items-center h-full select-none">
        {loading ? (
          <>
            <div className="mb-8">
              <div className="relative">
                <div
                  className="text-6xl sm:text-8xl font-bold w-full text-center game-title-lower"
                  dangerouslySetInnerHTML={{ __html: i18n.name }}
                ></div>
                <div
                  className="absolute top-0 text-6xl sm:text-8xl font-normal w-full text-center game-title-upper"
                  dangerouslySetInnerHTML={{ __html: i18n.name }}
                ></div>
              </div>
            </div>
            <div className="bg-white border-2 border-black border-solid rounded-full flex justify-start items-center h-4 w-[90%] me-4">
              <div
                ref={bar}
                className="rounded-full bg-[#ffe800] h-full border-l-0 border-t-0 border-b-0 border-r-2 border-r-black border-solid"
                style="width: 0%;"
              ></div>
            </div>
          </>
        ) : (
          <>
            {screen === 'menu' && <Menu />}
            {screen === 'play' && <Play />}
            {screen === 'speedrun' && <Speedrun />}
            {screen !== 'speedrun' && <Canvas config={config} />}
          </>
        )}
      </div>
    </GameContext.Provider>
  );
}
