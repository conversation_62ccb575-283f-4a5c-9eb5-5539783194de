# cube-match

A cube matching game made using vite and threejs. You can select unobstructed cubes to free them from the bundle, The goal is to free all the cubes!

## Build

### Removing Dev Styles

Some styles are included to make the test version look good, while others are essential to the functionality of the game.

The non essential styles are imported in `style.css` as `@import dev.css;` feel free to remove those entirely

### Customizing HTML

The html that is used in production is kept at `core/index.html`

Any change to it will reflect in the final build.

Meanwhile the `index.html` at root is the dev version, Changes to it will only reflect in dev mode.

### Building the game

The game can be built using `yarn build`

All the files will be built in the new `dist` folder


## Usage

```js
// example
import CubeMatch from 'https://unpkg.com/@roudanio/cube-match@latest/dist/cube-match.js';

// fist parameter is the element selector
// second parameter is a config object
CubeMatch.init('.cube_match_game_container', {
  root: 'https://unpkg.com/@roudanio/cube-match@latest/dist/',
});
```

The default config object is like this
```js
{
  root: location.origin,
  lcPrefix: 'mb-', // default local storage key prefix
  i18n: i18nDefault, // default i18n setting is in i18n.json
  cubeFlightSpeed: 1,
  minDragDistance: 5, // when pointer moving distance is over 5px，it will be considered as a drag, not a click
  comboExpireTime: 2000, // unit: ms, within this time, count as a combo
  allowedCubes: {
    1: [[1, 1, 1]],
    2: [
      [1, 1, 2],
      [2, 1, 1],
    ],
    3: [
      [2, 1, 2],
      [4, 3, 2],
      [2, 2, 1],
      [1, 2, 2],
      [1, 2, 1],
    ],
    5: [[4, 2, 4]],
  },
  levelSizes: {
    1: [2, 2, 2],
    2: [2, 3, 2],
    5: [5, 5, 5],
    12: [10, 12, 8],
    20: [12, 8, 10],
    100: [20, 20, 20],
  },
}
```

The default i18n is put in file [`./src/i18n.json`](./src/i18n.json).
The content is like this:

```json
{
  "name": "Block Dash",
  "availableLevels": "Available Levels",
  "reset": "Reset",
  "cancel": "Cancel",
  "ok": "OK",
  "quit": "Quit!",
  "quitDialogTitle": "You have made some progress of Lv %LEVEL%, do you really want to quit?",
  "soundOn": "Sound: ON",
  "soundOff": "Sound: OFF",
  "confirmReset": "Do you want to reset all the game data and start from scratch?",
  "toggleResetCode": "Protect your progress with a reset code",
  "enterResetCode": "Enter your reset code to reset instantly",
  "enterCodePlaceholder": "Enter your code...",
  "forgotResetCodeTip": "Forgot your code? Reset the game and wait for 10 minutes. :)",
  "createResetCode": "Create a reset code",
  "create": "Create",
  "createCodePlaceholder": "Start typing...",
  "createCodeTip": "Without the correct code, resetting will take 10 minutes and you must keep this window open.",
  "createCodeWarning": "WARNING: Do NOT use any passwords or personal info for the reset code. It's stored in your browser without encryption.",
  "back": "Back",
  "progress": "Progress",
  "level": "Level",
  "start": "Start",
  "play": "Play",
  "best": "Best",
  "current": "Current",
  "time": "Time",
  "bestTime": "Best Time",
  "yourScore": "Your Score:",
  "score": "Score",
  "bestScore": "Best Score",
  "speedrun": "Speedrun",
  "accuracy": "Accuracy",
  "levelBreakdown": "Level Breakdown",
  "tweet": "Tweet",
  "facebook": "Facebook",
  "copyLink": "Copy Link",
  "share": "Share",
  "shareText": "I have scored %SCORE% in Block Dash. I have completed %LVL% levels in %TIME% with accuracy of %ACC%.",
  "masterDialogTitle": "Congratulations on masterinf all the levels of Block Dash!",
  "masterDialogText": "Ready for another challenge? Replay earlier levels to improve your score and set new personal records!"
}

```
