{"name": "@roudanio/cube-match", "version": "0.3.9", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build:publish": "PUBLISH=1 vite build", "rezip": "rm -f /public/assets.zip && zip -r public/assets.zip assets/*"}, "devDependencies": {"@preact/preset-vite": "^2.10.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.17", "vite": "^6.3.4", "vite-plugin-static-copy": "^2.3.1"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.11", "class-variance-authority": "^0.7.1", "gsap": "^3.13.0", "lucide-react": "^0.473.0", "preact": "^10.26.5", "three": "^0.172.0"}}