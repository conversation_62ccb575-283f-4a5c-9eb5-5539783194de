{"name": "@roudanio/keyboard-masher", "version": "0.2.9", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:publish": "tsc && PUBLISH=1 vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --fix", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "animate.css": "^4.1.1", "class-variance-authority": "^0.7.1", "lucide-react": "^0.473.0", "preact": "^10.26.5"}, "devDependencies": {"@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "cssnano": "^7.0.6", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.3.4"}}