.keyboard-masher-container {
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  text-align: center;
  max-height: 500px;
  overflow: hidden;

  &.show-all {
    max-height: unset;
  }

  .game-title {
    font-family: "Silkscreen", sans-serif;
    text-align: center;
    font-weight: 400;
    font-size: 60px;
    line-height: 93%;
    color: #fff;
    /* width: 50%; */
    /* transform: translate3d(50%, 0, 0); */
  }

  .select-inset-shadow {
    box-shadow: inset 0 -4px 3px 2px #d9d9d9;
  }

  .time-tick {
    font-family: "Silkscreen", sans-serif;
    font-weight: 400;
    font-size: 2.5rem;
  }

  .times-up {
    font-weight: 900;
    color: #ff1d00;
    -webkit-text-stroke-color: #fff;
    -webkit-text-stroke-width: 6px;
    paint-order: stroke fill;
    font-size: 2.875rem;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .share-button {
    height: 44px;
    border: 2px solid #000;
    border-radius: 10px;
  }

  .share-button-yellow {
    box-shadow: 0 2px 0 0 #245945, inset 0 -3px 0 2px #f9b330;
    background: #ffda4f;

    &:hover {
      background-color: #ffc547;
    }
  }

  .share-button-twitter {
    box-shadow: 0 2px 0 0 #245945, inset 0 -3px 0 2px #2571b2;
    background: #51aeff;
  }

  .share-button-fb {
    box-shadow: 0 2px 0 0 #245945, inset 0 -3px 0 2px #1c50ac;
    background: #3377f1;
  }

  .share-button-white {
    box-shadow: 0 2px 0 0 #245945, inset 0 -3px 0 2px #dce9e4;
    background: #fff;
  }

  .play-btn {
    font-family: 'Silkscreen', sans-serif;
    font-size: 1.75rem;
    height: 4rem;
    border-radius: 24px;
    background-color: #ffe800;
    color: #000;
    border: 3px solid #000;
    box-shadow: 0 5px 0 0 rgba(32, 68, 123, 0.3), inset 0 -5px 3px 4px #eeb60a;

    &:hover {
      background-color: #ffc107;
    }
  }
}

input, button, [data-radix-popper-content-wrapper] * {
  box-sizing: border-box;
  border: 0 solid #e5e7eb
}

kbd + kbd {
  margin-left: 2px;
}
