import { useState, useEffect, useMemo } from 'react'
import { ChevronLeft, ChevronDown, Twitter, Facebook, Link, Share2, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import KbdIcon from '@/components/kbd-icon';
import { sleep } from "@/utils";
import './App.css'

enum GameStatus {
  Pre, // game 开始前，选择 timer 然后开始游戏
  Start, // 点了开始，但还没开始 typing
  Typing, // 开始了 typing，需要倒计时
  End, // timeup，显示结果以及排行榜+重新开始btn，
}

declare global {
  interface Window {
    gameOver?: (duration: number, score: number, whatTyped: string) => void;
  }
}

function httpBuildQuery(obj: Record<string, string>) {
  const qs = new URLSearchParams(obj);
  return qs.toString();
}

function App({
  onGameOver,
  root,
  i18n,
}: {
  duration?: number;
  onGameOver?: (duration: number, score: number, whatTyped: string) => void;
  root: HTMLElement;
  i18n: Record<string, string>,
}) {
  let timeout: string | number | NodeJS.Timeout | undefined, updateTimeout: string | number | NodeJS.Timeout | undefined;
  let apmTimer: string | number | NodeJS.Timeout | undefined;
  const kdbClass = 'px-2 py-1.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500';
  const timerOpts = [1, 3, 5, 10, 15, 30, 60, 180, 300];
  const apmDuration = 5; // 5s

  const [keyCombi, setKeyCombi] = useState('all');
  const [acceptableKeys, setAcceptableKeys] = useState<string[]>([]);
  const [recording, setRecording] = useState(false);
  const [durationOpts, setDurationOpts] = useState('10');
  const [duration, setDuration] = useState(10);
  const [status, setStatus] = useState<GameStatus>(GameStatus.Pre);
  const [score, setScore] = useState(0);
  const [start, setStart] = useState(0);
  const [whatTyped, setWhatTyped] = useState('');
  const [charFreq, setFreq] = useState({} as Record<string, number>);
  const [elapsed, setElapsed] = useState('0');
  const [hasOpenToggle, setHasOpenToggle] = useState(false);
  const [copied, setCopied] = useState(false);
  const [apm, setApm] = useState(0);
  const [apmRangeMax, setApmRangeMax] = useState(0);
  let apmTicks: number[] = [];
  const [apmPts, setApmPts] = useState<number[]>([]);

  useEffect(() => {
    function startTimer(e: KeyboardEvent) {
      e.preventDefault();

      setStart(Date.now());
      setApm(0);
      setApmPts(() => []);
      apmTicks.length = 0;
    }
    if (status === GameStatus.Pre) {
      setScore(0);
    } else if (status === GameStatus.Start && duration) {
      setElapsed(duration.toFixed(1));
      window.addEventListener('keydown', startTimer);
      return () => window.removeEventListener('keydown', startTimer);
    } else {
      window.removeEventListener('keydown', startTimer);
      if (status === GameStatus.End) {
        if (onGameOver) {
          onGameOver(duration!, score, whatTyped);
        }
      }
    }
  }, [status]);

  useEffect(() => {
    if (start > 0 && duration && duration > 0) {
      setHasOpenToggle(false);
      window.addEventListener('keydown', updateScore);
      updateTimeout = setInterval(updateTime, 100);
      apmTimer = setInterval(updateApm, 250);
      setStatus(GameStatus.Typing);
      timeout = setTimeout(async () => {
        setStatus(GameStatus.End);
        clearInterval(updateTimeout);
        clearInterval(apmTimer);
        clearTimeout(timeout);
        window.removeEventListener('keydown', updateScore);
        await sleep(30);
        if (root.scrollHeight > root.clientHeight) {
          setHasOpenToggle(true);
        }
      }, duration * 1000);
      return () => {
        clearInterval(updateTimeout);
        clearInterval(apmTimer);
        clearTimeout(timeout);
        window.removeEventListener('keydown', updateScore);
      }
    }
  }, [start, duration]);

  useEffect(() => {
    if (keyCombi === 'customize' && recording) {
      window.addEventListener('keydown', setCustomizeKeyCombi);
    }
    return () => {
      window.removeEventListener('keydown', setCustomizeKeyCombi);
    }
  }, [keyCombi, recording]);

  const freqChartData = useMemo(() => {
    return Object.entries(charFreq)
      .sort((a, b) => b[1] - a[1])
      .map(([char, freq]) => ({ char, count: freq }))
  }, [charFreq]);

  const maxTimes = useMemo<number>(() => {
    return freqChartData.length && freqChartData[0].count || 0;
  }, [freqChartData]);

  const url = location.href;
  const shareText = useMemo(() => {
    return i18n.shareText
      .replace('%SCORE%', score.toString())
      .replace('%DURATION%', duration.toString())
      .replace('%CPS%', (score / duration).toFixed(1))
      .replace('%FAV_KEY%', freqChartData[0]?.char || '');
  }, [score, duration, freqChartData]);
  const twLink = useMemo(() => {
    return 'https://twitter.com/intent/tweet?' + httpBuildQuery({
      text: shareText,
      hashtags: 'keyboardshasher',
      url,
    });
  }, [shareText]);

  async function doShare() {
    try {
      await navigator.share({
        url,
        text: shareText,
        title: 'Keyboard Smasher',
      });
    } catch (error) {
      console.warn('Error: ', error);
    }
  }

  async function doCopyLink() {
    if (copied) return;
    try {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      await navigator.clipboard.writeText(window.test.url);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 1e3);
    } catch (error) {
      console.error('Failed to copy: ', error);
    }
  }

  function updateApm() {
    const now = Date.now();

    const index = apmTicks.findIndex(time => time > now - apmDuration * 1000);
    if (index > 0) {
      apmTicks = apmTicks.slice(index);
    } else if (index === -1) {
      apmTicks.length = 0
    }
    const value = apmTicks.length / apmDuration * 60;
    setApm(Math.round(value));

    setApmPts(pts => {
      pts.push(value);
      if (pts.length > 256) {
        pts.shift();
      }
      return [...pts];
    });
  }

  useEffect(() => {
    setApmRangeMax(getRangeMax(Math.max(...apmPts)))
  }, [apmPts])

  function getRangeMax(max: number) {
    if (max < 100) {
      return 100;
    } else if (max < 1000) {
      return (Math.ceil(max / 100) + 1) * 100;
    } else {
      return 2000;
    }
  }

  function doExtendRoot(): void {
    root.classList.add('show-all');
    setHasOpenToggle(false);
  }

  function setCustomizeKeyCombi(e: KeyboardEvent) {
    setAcceptableKeys((acceptableKeys) => {
      if (!(acceptableKeys.includes(e.key))) {
        return [...acceptableKeys, e.key];
      }
      return acceptableKeys;
    });
  }

  function updateScore(e: KeyboardEvent) {
    // need to prevent spacebar scrolling or other page navi
    switch (e.key) {
      case ' ':
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
      case 'PageUp':
      case 'PageDown':
      case 'Home':
      case 'End':
        e.preventDefault();
        break;
    }

    apmTicks.push(Date.now());

    if (keyCombi === 'all' || acceptableKeys.includes(e.key)) {
      setWhatTyped((v) => {
        let key = e.key;
        switch (e.key) {
          case 'ArrowUp':
            key = '↑';
            break;
          case 'ArrowDown':
            key = '↓';
            break;
          case 'ArrowLeft':
            key = '←';
            break;
          case 'ArrowRight':
            key = '→';
            break;
        }
        return v + key;
      });
      setFreq((prev) => {
        prev[e.key] = (prev[e.key] ?? 0) + 1;
        return { ...prev };
      });
      setScore((score) => score + 1);
    }
  }

  function updateTime() {
    if (duration) {
      const diff = Date.now() - start;
      const elapsedStr = (duration - Math.floor(diff / 100) / 10).toFixed(1);
      setElapsed(elapsedStr);
    }
  }

  function reset() {
    setScore(0);
    setStart(0);
    setWhatTyped('');
    setFreq({});
    setApm(0);
    setApmPts(() => []);
    apmTicks.length = 0;
  }

  function restart() {
    setStatus(GameStatus.Start);
    reset();
  }

  function back() {
    setStatus(GameStatus.Pre);
    reset();
  }

  function onKeyCombiChange(val: string) {
    setAcceptableKeys([]);
    if (val === 'wasd') {
      setAcceptableKeys(['w', 'a', 's', 'd']);
    }
    if (val === 'udlr') {
      setAcceptableKeys(['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight']);
    }
    setKeyCombi(val);
  }

  function onChangeDuration(val: string) {
    if (val !== 'customize') {
      setDuration(Number(val));
    }
    setDurationOpts(val);
  }

  function onInputDuration(e: InputEvent) {
    setDuration(Number((e.target as HTMLInputElement)?.value));
  }

  return (
    <>
      <div className="game-title">{i18n.name}</div>
      {status === GameStatus.Pre ? (
        <div className="flex flex-col gap-4 py-6 px-52">
          <div className="text-left">
            <label className="block leading-none mb-2 text-white font-bold text-base">
              {i18n.keysCombination}
            </label>
            <Select defaultValue={keyCombi} onValueChange={onKeyCombiChange}>
              <SelectTrigger>
                <SelectValue placeholder="Keys Combination" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="undefined" disabled>
                  {i18n.selectKeysCombination}
                </SelectItem>
                <SelectItem value="wasd">
                  <kbd className={kdbClass}>W</kbd>
                  <kbd className={kdbClass}>A</kbd>
                  <kbd className={kdbClass}>S</kbd>
                  <kbd className={kdbClass}>D</kbd>
                </SelectItem>
                <SelectItem value="udlr">
                  <kbd className={kdbClass}>↑</kbd>
                  <kbd className={kdbClass}>↓</kbd>
                  <kbd className={kdbClass}>→</kbd>
                  <kbd className={kdbClass}>←</kbd>
                </SelectItem>
                <SelectItem value="all">{i18n.selectAllKeys}</SelectItem>
                <SelectItem value="customize">
                  {i18n.selectCustomizeKeys}
                </SelectItem>
              </SelectContent>
            </Select>
            {keyCombi === 'customize' && (
              <div>
                <label className="text-sm font-medium leading-none mb-2 text-white">
                  {i18n.hitKeys}
                </label>
                <div className="flex flex-wrap gap-1 min-h-7 border-dashed border-slate-200 border-slashed p-2 rounded-md">
                  {acceptableKeys.length === 0 && (
                    <span className="ps-2 text-sm text-slate-200">
                      {i18n.noKeySelected}
                    </span>
                  )}
                  {acceptableKeys.map((k) => (
                    <kbd className={kdbClass}>{k}</kbd>
                  ))}
                </div>
                <div className="mt-2">
                  {!recording && (
                    <Button
                      className="me-2"
                      variant="outline"
                      onClick={() => setRecording(true)}
                    >
                      {i18n.start}
                    </Button>
                  )}
                  {recording && (
                    <Button
                      className="me-2"
                      onClick={() => setRecording(false)}
                    >
                      {i18n.ok}
                    </Button>
                  )}
                  <Button
                    variant="secondary"
                    onClick={() => setAcceptableKeys([])}
                  >
                    {i18n.reset}
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="text-left">
            <label className="block leading-none mb-2 text-white font-bold text-base">
              {i18n.timerDuration}
            </label>
            <Select
              defaultValue={durationOpts}
              onValueChange={onChangeDuration}
            >
              <SelectTrigger>
                <SelectValue placeholder="Timer Duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="undefined" disabled>
                  {i18n.chooseTimerDuration}
                </SelectItem>
                {timerOpts.map((opt) => (
                  <SelectItem key={opt} value={String(opt)}>
                    {opt} {opt === 1 ? i18n.second : i18n.seconds}
                  </SelectItem>
                ))}
                <SelectItem value="customize">
                  {i18n.customizeTimerDuration}
                </SelectItem>
              </SelectContent>
            </Select>
            {durationOpts === 'customize' && (
              <Input
                className="mt-2"
                type="number"
                placeholder={i18n.setTimerDuration}
                onInput={onInputDuration}
                value={duration}
              />
            )}
          </div>
          <Button
            size="lg"
            className="cursor-pointer play-btn w-1/2 mx-auto mt-4 p-4"
            disabled={
              !duration ||
              (keyCombi === 'customize' && acceptableKeys.length === 0)
            }
            onClick={() => setStatus(GameStatus.Start)}
          >
            {i18n.play}
          </Button>
        </div>
      ) : (
        <div className="flex flex-col gap-4 py-6 px-28 select-none">
          <div className="flex flex-row justify-evenly items-center text-white text-lg font-semibold">
            <div className="flex flex-row justify-center items-center gap-2">
              <div>{i18n.score}:</div>
              <div
                id="score"
                className="bg-[#1a4434] rounded-2xl h-7 w-20 text-[#ffe800] text-center"
              >
                {score}
              </div>
            </div>
            <div className="flex flex-row justify-center items-center gap-2">
              <div>APM:</div>
              <div className="bg-[#1a4434] rounded-2xl h-7 w-20 text-[#ffe800] text-center">
                {apm}
              </div>
            </div>
          </div>

          {/*  */}
          <div className="flex flex-row justify-center items-center gap-6">
            <div className="apm flex items-center bg-white h-14 ps-2 pe-2 rounded-xl border-2 border-solid border-black">
              <svg className="w-56 h-14 -scale-y-1">
                <polyline
                  className="fill-none stroke-black stroke-2"
                  points={apmPts
                    .map((p, i) => `${i},${37 - (p / apmRangeMax) * 36}`)
                    .join(' ')}
                ></polyline>
              </svg>
            </div>
            {status === GameStatus.End && (
              <div className="text-2xl font-bold">
                <div>CPS</div>
                <div className="bg-[#1a4434] rounded-2xl h-7 w-20 text-[#ffe800] text-center text-lg">
                  {(score / duration).toFixed(1)}
                </div>
              </div>
            )}
          </div>
          <div className="text-lg min-h-24 flex flex-col gap-4">
            {status === GameStatus.Start && (
              <>
                <p className="text-[#ffe800] blink_me">{i18n.typeBegin}</p>
                <Button variant="outline" size="sm" onClick={back}>
                  <ChevronLeft className="h-4 w-4" />
                  {i18n.back}
                </Button>
              </>
            )}
            {status === GameStatus.Typing && (
              <>
                <p className="animate__animated animate__pulse animate__faster animate__infinite text-[#ffe800] font-black text-3xl my-2">
                  {i18n.gogogo}
                </p>
                <p className="break-all max-w-full my-2 text-white">
                  {whatTyped}
                </p>
              </>
            )}
            {status === GameStatus.End && (
              <>
                <p className="text-yellow-600 text-2xl times-up">
                  {i18n.timesUp}
                </p>
                <div className="flex flex-row flex-wrap gap-4 items-center justify-center">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={restart}
                    className="cursor-pointer share-button share-button-yellow"
                  >
                    {i18n.restart}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={back}
                    className=" cursor-pointer share-button share-button-yellow"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    {i18n.back}
                  </Button>
                  <a href={twLink} target="_blank">
                    <Button
                      className="cursor-pointer hover:bg-[#1A8CD8] text-white border-0 share-button share-button-twitter"
                      size="sm"
                      variant="custom"
                    >
                      <Twitter
                        className="h-4 w-4 me-1"
                        fill="currentColor"
                        strokeWidth="0"
                      />
                      {i18n.tweet}
                    </Button>
                  </a>
                  <a
                    href={'https://www.facebook.com/sharer.php?u=' + url}
                    target="_blank"
                  >
                    <Button
                      className="cursor-pointer hover:bg-[#166FE5] text-white border-0 share-button share-button-fb"
                      size="sm"
                      variant="custom"
                    >
                      <Facebook
                        className="h-4 w-4 me-1"
                        fill="currentColor"
                        strokeWidth="0"
                      />
                      {i18n.facebook}
                    </Button>
                  </a>
                  <Button
                    size="sm"
                    className="cursor-pointer share-button share-button-white"
                    onClick={doCopyLink}
                    variant="secondary"
                  >
                    {copied ? (
                      <Check className="h-4 w-4 me-1" />
                    ) : (
                      <Link className="h-4 w-4 me-1" />
                    )}
                    {i18n.copyLink}
                  </Button>
                  <Button
                    size="sm"
                    className="cursor-pointer share-button share-button-white"
                    onClick={doShare}
                    variant="secondary"
                  >
                    <Share2 className="h-4 w-4 me-1" />
                  </Button>
                </div>
                <p className="break-all max-w-full my-2 text-white">
                  {whatTyped}
                </p>
                <h2>{i18n.stat}</h2>
                <div
                  className="min-h-[200px] w-full grid grid-cols-2 gap-2 auto-rows-min"
                  style={{ gridTemplateColumns: '4rem 1fr' }}
                >
                  {freqChartData.map(({ char, count }) => (
                    <>
                      <div key={`${char}-key`}>
                        <kbd className="inline-flex items-center justify-center rounded-md border border-solid bg-gray-50 border-gray-200 px-2 border-b-2 min-h-9 min-w-9 text-nowrap capitalize">
                          <KbdIcon char={char} />
                        </kbd>
                      </div>
                      <div key={`${char}-count`}>
                        <progress
                          className="w-full"
                          max={maxTimes}
                          value={count}
                        />
                      </div>
                    </>
                  ))}
                </div>
                {hasOpenToggle && (
                  <Button
                    className="absolute left-0 bottom-0 right-0 z-10"
                    variant="outline"
                    size="sm"
                    onClick={doExtendRoot}
                  >
                    <ChevronDown className="w-4 h-4" />
                  </Button>
                )}
              </>
            )}
          </div>
          <div className="text-lg font-bold uppercase text-white flex justify-center items-start gap-3">
            {i18n.time} <span className="time-tick">{elapsed}</span>
          </div>
        </div>
      )}
    </>
  );
}

export default App
