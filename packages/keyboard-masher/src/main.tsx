import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App.tsx'
import i18nDefault from './i18n.json';
import './animate.css';
import './index.css'

const eventMap = {} as Record<string, (...args: unknown[]) => void>;
if (!__IS_PUBLISH__) {
  const el = document.getElementById('keyboard-masher-container');
  ReactDOM.createRoot(el!).render(
    <React.StrictMode>
      <App
        i18n={i18nDefault}
        onGameOver={(...args) => eventMap['gameOver']?.(...args)}
        root={el as HTMLElement}
      />
    </React.StrictMode>,
  )
}

const KeyboardMasher = {
  async init(selector: Element | string, {
    i18n = i18nDefault,
    root,
  }: { i18n: Record<string, string>, root: string }) {
    const el =
      typeof selector === 'string'
        ? document.querySelector(selector)
        : selector;
    if (!el) {
      console.warn('Cannot find the root element');
      const notice = document.createElement('div');
      notice.style.textAlign = 'center';
      notice.style.padding = '20px';
      notice.innerHTML = 'Cannot find the root element';
      document.body.append(notice);
      return;
    }

    // default to show a Loading text before css is loaded and app is rendered
    const loading = document.createElement('div');
    loading.style.display = 'flex';
    loading.style.justifyContent = 'center';
    loading.style.alignItems = 'center';
    loading.style.height = '500px';
    loading.innerHTML = 'Loading...';
    el.innerHTML = '';
    el.append(loading);

    const loadCSS = async (url: string) => {
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;
        link.onload = resolve;
        link.onerror = reject;
        document.head.appendChild(link);
      });
    };

    await loadCSS(root + 'style.css');

    ReactDOM.createRoot(el).render(
      <React.StrictMode>
        <App
          onGameOver={(...args) => eventMap['gameOver']?.(...args)}
          i18n={i18n}
          root={el as HTMLElement}
        />
      </React.StrictMode>,
    )
  },
  on(event: string, fn: (...args: unknown[]) => void) {
    eventMap[event] = fn
  }
}

export default KeyboardMasher;
