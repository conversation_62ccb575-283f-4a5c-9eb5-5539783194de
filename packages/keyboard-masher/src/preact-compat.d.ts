// https://gist.github.com/adamgreg/b4eecbbe764c76f068d661d2222ca893
import { ComponentClass, VNode } from "preact";
import "preact/compat";

declare module "preact/compat" {
  export type ElementType = JSX.ElementType;
  export type ComponentPropsWithoutRef<T extends ElementType> = PropsWithoutRef<
    ComponentProps<T>
  >;

  export type PropsWithRef<P> =
    // Just "P extends { ref?: infer R }" looks sufficient, but R will infer as {} if P is {}.
    "ref" extends keyof P
      ? P extends { ref?: infer R }
        ? string extends R ? PropsWithoutRef<P> & { ref?: Exclude<R, string> }
        : P
      : P
      : P;

  export type ComponentPropsWithRef<T extends ElementType> = T extends
    ComponentClass<infer P>
    ? PropsWithoutRef<P> & RefAttributes<InstanceType<T>>
    : PropsWithRef<ComponentProps<T>>;

  export type ElementRef<
    C extends
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    | ForwardRefExoticComponent<any>
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    | { new (props: any): Component<any> }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    | ((props: any, context?: any) => VNode)
    | keyof JSX.IntrinsicElements,
  > = NonNullable<ComponentPropsWithRef<C>["ref"]> extends preact.Ref<
    infer Instance
  > ? Instance
    : never;
}
