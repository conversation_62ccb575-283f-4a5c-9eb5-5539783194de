import {
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  CornerDownLeft,
  Space,
} from "lucide-react"

export default function KbdIcon({
  char
}: { char: string }) {
  switch(char) {
    case ' ':
      return <Space className='w-4 h-4' />;

    case 'Enter':
      return <CornerDownLeft className='w-4 h-4' />;

    case 'ArrowUp':
      return <ArrowUp className='w-4 h-4' />;

    case 'ArrowDown':
      return <ArrowDown className='w-4 h-4' />;

    case 'ArrowLeft':
      return <ArrowLeft className='w-4 h-4' />;

    case 'ArrowRight':
      return <ArrowRight className='w-4 h-4' />;

    default:
      return <span>{ char.length > 1 ? char : char.toUpperCase() }</span>;
  }
}
