import path from "path"
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const isProd = command === 'build';
  const isPublish = !!process.env.PUBLISH;

  return {
    define: {
      __IS_PROD__: isProd,
      __IS_PUBLISH__: isPublish,
    },
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "react": "preact/compat",
        "react/jsx-runtime": "preact/jsx-runtime",
        "react-dom": "preact/compat",
      },
    },
    ...isPublish && {
      build: {
        target: 'chrome96',
        lib: {
          entry: 'src/main.tsx',
          name: 'KeyboardMasher',
          fileName: (format: string) =>
            format === 'es' ? 'keyboard-masher.js' : `keyboard-masher.${format}.js`,
          formats: ['es', 'umd', 'iife'],
        },
      },
    },
  };
})
