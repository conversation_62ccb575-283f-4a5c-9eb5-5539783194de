# Keyboard Masher

Usage:

```js
import KeyboardMasher from "https://unpkg.com/@roudanio/keyboard-masher@latest/dist/keyboard-masher.js";

// fist parameter is the element selector
// second parameter is a config object
KeyboardMasher.init('#keyboard-masher-container', {
  root: 'https://unpkg.com/@roudanio/keyboard-masher@latest/dist/',
});
KeyboardMasher.on('gameOver', (...args) => {
  console.log('game over: ', args);
});
```

The default config object is like this:
```js
{
  root: location.origin,
  i18n: i18nDefault, // default i18n setting is in i18n.json
}
```

The default i18n is put in file [`./src/i18n.json`](./src/i18n.json).
The content is like this:

```json
{
  "name": "Keyboard Masher",
  "keysCombination": "Keys Combination",
  "selectKeysCombination": "Select the keys combination you like",
  "selectAllKeys": "All Keys",
  "selectCustomizeKeys": "Customize keys you like to smash",
  "hitKeys": "Hit the keys you want to smash:",
  "start": "Start",
  "ok": "OK",
  "reset": "Reset",
  "timerDuration": "Timer Duration",
  "chooseTimerDuration": "Choose the timer duration",
  "customizeTimerDuration": "Customize the timer duration",
  "setTimerDuration": "Set the seconds for the timer",
  "second": "second",
  "seconds": "seconds",
  "play": "Play!",
  "score": "Score",
  "back": "Back",
  "gogogo": "Go! Go! Go!",
  "typeBegin": "Type on your keyboard to begin.",
  "time": "Time: ",
  "timesUp": "Times up!",
  "restart": "Restart",
  "tweet": "Tweet",
  "facebook": "Facebook",
  "copyLink": "Copy Link",
  "stat": "Statistics",
  "shareText": "I have smashed %SCORE% keys in %DURATION% seconds! That's %CPS% keys per second! My favorite key is %FAV_KEY%. Can you smash your keyboard like this?"
}
```
